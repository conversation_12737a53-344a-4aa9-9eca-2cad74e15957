// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageUpdate is the builder for updating NotificationMessage entities.
type NotificationMessageUpdate struct {
	config
	hooks     []Hook
	mutation  *NotificationMessageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the NotificationMessageUpdate builder.
func (nmu *NotificationMessageUpdate) Where(ps ...predicate.NotificationMessage) *NotificationMessageUpdate {
	nmu.mutation.Where(ps...)
	return nmu
}

// SetUpdateTime sets the "update_time" field.
func (nmu *NotificationMessageUpdate) SetUpdateTime(t time.Time) *NotificationMessageUpdate {
	nmu.mutation.SetUpdateTime(t)
	return nmu
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableUpdateTime(t *time.Time) *NotificationMessageUpdate {
	if t != nil {
		nmu.SetUpdateTime(*t)
	}
	return nmu
}

// ClearUpdateTime clears the value of the "update_time" field.
func (nmu *NotificationMessageUpdate) ClearUpdateTime() *NotificationMessageUpdate {
	nmu.mutation.ClearUpdateTime()
	return nmu
}

// SetDeleteTime sets the "delete_time" field.
func (nmu *NotificationMessageUpdate) SetDeleteTime(t time.Time) *NotificationMessageUpdate {
	nmu.mutation.SetDeleteTime(t)
	return nmu
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableDeleteTime(t *time.Time) *NotificationMessageUpdate {
	if t != nil {
		nmu.SetDeleteTime(*t)
	}
	return nmu
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (nmu *NotificationMessageUpdate) ClearDeleteTime() *NotificationMessageUpdate {
	nmu.mutation.ClearDeleteTime()
	return nmu
}

// SetCreateBy sets the "create_by" field.
func (nmu *NotificationMessageUpdate) SetCreateBy(u uint32) *NotificationMessageUpdate {
	nmu.mutation.ResetCreateBy()
	nmu.mutation.SetCreateBy(u)
	return nmu
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableCreateBy(u *uint32) *NotificationMessageUpdate {
	if u != nil {
		nmu.SetCreateBy(*u)
	}
	return nmu
}

// AddCreateBy adds u to the "create_by" field.
func (nmu *NotificationMessageUpdate) AddCreateBy(u int32) *NotificationMessageUpdate {
	nmu.mutation.AddCreateBy(u)
	return nmu
}

// ClearCreateBy clears the value of the "create_by" field.
func (nmu *NotificationMessageUpdate) ClearCreateBy() *NotificationMessageUpdate {
	nmu.mutation.ClearCreateBy()
	return nmu
}

// SetUpdateBy sets the "update_by" field.
func (nmu *NotificationMessageUpdate) SetUpdateBy(u uint32) *NotificationMessageUpdate {
	nmu.mutation.ResetUpdateBy()
	nmu.mutation.SetUpdateBy(u)
	return nmu
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableUpdateBy(u *uint32) *NotificationMessageUpdate {
	if u != nil {
		nmu.SetUpdateBy(*u)
	}
	return nmu
}

// AddUpdateBy adds u to the "update_by" field.
func (nmu *NotificationMessageUpdate) AddUpdateBy(u int32) *NotificationMessageUpdate {
	nmu.mutation.AddUpdateBy(u)
	return nmu
}

// ClearUpdateBy clears the value of the "update_by" field.
func (nmu *NotificationMessageUpdate) ClearUpdateBy() *NotificationMessageUpdate {
	nmu.mutation.ClearUpdateBy()
	return nmu
}

// SetSubject sets the "subject" field.
func (nmu *NotificationMessageUpdate) SetSubject(s string) *NotificationMessageUpdate {
	nmu.mutation.SetSubject(s)
	return nmu
}

// SetNillableSubject sets the "subject" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableSubject(s *string) *NotificationMessageUpdate {
	if s != nil {
		nmu.SetSubject(*s)
	}
	return nmu
}

// ClearSubject clears the value of the "subject" field.
func (nmu *NotificationMessageUpdate) ClearSubject() *NotificationMessageUpdate {
	nmu.mutation.ClearSubject()
	return nmu
}

// SetContent sets the "content" field.
func (nmu *NotificationMessageUpdate) SetContent(s string) *NotificationMessageUpdate {
	nmu.mutation.SetContent(s)
	return nmu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableContent(s *string) *NotificationMessageUpdate {
	if s != nil {
		nmu.SetContent(*s)
	}
	return nmu
}

// ClearContent clears the value of the "content" field.
func (nmu *NotificationMessageUpdate) ClearContent() *NotificationMessageUpdate {
	nmu.mutation.ClearContent()
	return nmu
}

// SetCategoryID sets the "category_id" field.
func (nmu *NotificationMessageUpdate) SetCategoryID(u uint32) *NotificationMessageUpdate {
	nmu.mutation.ResetCategoryID()
	nmu.mutation.SetCategoryID(u)
	return nmu
}

// SetNillableCategoryID sets the "category_id" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableCategoryID(u *uint32) *NotificationMessageUpdate {
	if u != nil {
		nmu.SetCategoryID(*u)
	}
	return nmu
}

// AddCategoryID adds u to the "category_id" field.
func (nmu *NotificationMessageUpdate) AddCategoryID(u int32) *NotificationMessageUpdate {
	nmu.mutation.AddCategoryID(u)
	return nmu
}

// ClearCategoryID clears the value of the "category_id" field.
func (nmu *NotificationMessageUpdate) ClearCategoryID() *NotificationMessageUpdate {
	nmu.mutation.ClearCategoryID()
	return nmu
}

// SetStatus sets the "status" field.
func (nmu *NotificationMessageUpdate) SetStatus(n notificationmessage.Status) *NotificationMessageUpdate {
	nmu.mutation.SetStatus(n)
	return nmu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nmu *NotificationMessageUpdate) SetNillableStatus(n *notificationmessage.Status) *NotificationMessageUpdate {
	if n != nil {
		nmu.SetStatus(*n)
	}
	return nmu
}

// ClearStatus clears the value of the "status" field.
func (nmu *NotificationMessageUpdate) ClearStatus() *NotificationMessageUpdate {
	nmu.mutation.ClearStatus()
	return nmu
}

// Mutation returns the NotificationMessageMutation object of the builder.
func (nmu *NotificationMessageUpdate) Mutation() *NotificationMessageMutation {
	return nmu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (nmu *NotificationMessageUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, nmu.sqlSave, nmu.mutation, nmu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nmu *NotificationMessageUpdate) SaveX(ctx context.Context) int {
	affected, err := nmu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (nmu *NotificationMessageUpdate) Exec(ctx context.Context) error {
	_, err := nmu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmu *NotificationMessageUpdate) ExecX(ctx context.Context) {
	if err := nmu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmu *NotificationMessageUpdate) check() error {
	if v, ok := nmu.mutation.Status(); ok {
		if err := notificationmessage.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "NotificationMessage.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (nmu *NotificationMessageUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *NotificationMessageUpdate {
	nmu.modifiers = append(nmu.modifiers, modifiers...)
	return nmu
}

func (nmu *NotificationMessageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := nmu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationmessage.Table, notificationmessage.Columns, sqlgraph.NewFieldSpec(notificationmessage.FieldID, field.TypeUint32))
	if ps := nmu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if nmu.mutation.CreateTimeCleared() {
		_spec.ClearField(notificationmessage.FieldCreateTime, field.TypeTime)
	}
	if value, ok := nmu.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessage.FieldUpdateTime, field.TypeTime, value)
	}
	if nmu.mutation.UpdateTimeCleared() {
		_spec.ClearField(notificationmessage.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := nmu.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessage.FieldDeleteTime, field.TypeTime, value)
	}
	if nmu.mutation.DeleteTimeCleared() {
		_spec.ClearField(notificationmessage.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := nmu.mutation.CreateBy(); ok {
		_spec.SetField(notificationmessage.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := nmu.mutation.AddedCreateBy(); ok {
		_spec.AddField(notificationmessage.FieldCreateBy, field.TypeUint32, value)
	}
	if nmu.mutation.CreateByCleared() {
		_spec.ClearField(notificationmessage.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := nmu.mutation.UpdateBy(); ok {
		_spec.SetField(notificationmessage.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := nmu.mutation.AddedUpdateBy(); ok {
		_spec.AddField(notificationmessage.FieldUpdateBy, field.TypeUint32, value)
	}
	if nmu.mutation.UpdateByCleared() {
		_spec.ClearField(notificationmessage.FieldUpdateBy, field.TypeUint32)
	}
	if nmu.mutation.TenantIDCleared() {
		_spec.ClearField(notificationmessage.FieldTenantID, field.TypeUint32)
	}
	if value, ok := nmu.mutation.Subject(); ok {
		_spec.SetField(notificationmessage.FieldSubject, field.TypeString, value)
	}
	if nmu.mutation.SubjectCleared() {
		_spec.ClearField(notificationmessage.FieldSubject, field.TypeString)
	}
	if value, ok := nmu.mutation.Content(); ok {
		_spec.SetField(notificationmessage.FieldContent, field.TypeString, value)
	}
	if nmu.mutation.ContentCleared() {
		_spec.ClearField(notificationmessage.FieldContent, field.TypeString)
	}
	if value, ok := nmu.mutation.CategoryID(); ok {
		_spec.SetField(notificationmessage.FieldCategoryID, field.TypeUint32, value)
	}
	if value, ok := nmu.mutation.AddedCategoryID(); ok {
		_spec.AddField(notificationmessage.FieldCategoryID, field.TypeUint32, value)
	}
	if nmu.mutation.CategoryIDCleared() {
		_spec.ClearField(notificationmessage.FieldCategoryID, field.TypeUint32)
	}
	if value, ok := nmu.mutation.Status(); ok {
		_spec.SetField(notificationmessage.FieldStatus, field.TypeEnum, value)
	}
	if nmu.mutation.StatusCleared() {
		_spec.ClearField(notificationmessage.FieldStatus, field.TypeEnum)
	}
	_spec.AddModifiers(nmu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, nmu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationmessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	nmu.mutation.done = true
	return n, nil
}

// NotificationMessageUpdateOne is the builder for updating a single NotificationMessage entity.
type NotificationMessageUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *NotificationMessageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (nmuo *NotificationMessageUpdateOne) SetUpdateTime(t time.Time) *NotificationMessageUpdateOne {
	nmuo.mutation.SetUpdateTime(t)
	return nmuo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableUpdateTime(t *time.Time) *NotificationMessageUpdateOne {
	if t != nil {
		nmuo.SetUpdateTime(*t)
	}
	return nmuo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (nmuo *NotificationMessageUpdateOne) ClearUpdateTime() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearUpdateTime()
	return nmuo
}

// SetDeleteTime sets the "delete_time" field.
func (nmuo *NotificationMessageUpdateOne) SetDeleteTime(t time.Time) *NotificationMessageUpdateOne {
	nmuo.mutation.SetDeleteTime(t)
	return nmuo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableDeleteTime(t *time.Time) *NotificationMessageUpdateOne {
	if t != nil {
		nmuo.SetDeleteTime(*t)
	}
	return nmuo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (nmuo *NotificationMessageUpdateOne) ClearDeleteTime() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearDeleteTime()
	return nmuo
}

// SetCreateBy sets the "create_by" field.
func (nmuo *NotificationMessageUpdateOne) SetCreateBy(u uint32) *NotificationMessageUpdateOne {
	nmuo.mutation.ResetCreateBy()
	nmuo.mutation.SetCreateBy(u)
	return nmuo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableCreateBy(u *uint32) *NotificationMessageUpdateOne {
	if u != nil {
		nmuo.SetCreateBy(*u)
	}
	return nmuo
}

// AddCreateBy adds u to the "create_by" field.
func (nmuo *NotificationMessageUpdateOne) AddCreateBy(u int32) *NotificationMessageUpdateOne {
	nmuo.mutation.AddCreateBy(u)
	return nmuo
}

// ClearCreateBy clears the value of the "create_by" field.
func (nmuo *NotificationMessageUpdateOne) ClearCreateBy() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearCreateBy()
	return nmuo
}

// SetUpdateBy sets the "update_by" field.
func (nmuo *NotificationMessageUpdateOne) SetUpdateBy(u uint32) *NotificationMessageUpdateOne {
	nmuo.mutation.ResetUpdateBy()
	nmuo.mutation.SetUpdateBy(u)
	return nmuo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableUpdateBy(u *uint32) *NotificationMessageUpdateOne {
	if u != nil {
		nmuo.SetUpdateBy(*u)
	}
	return nmuo
}

// AddUpdateBy adds u to the "update_by" field.
func (nmuo *NotificationMessageUpdateOne) AddUpdateBy(u int32) *NotificationMessageUpdateOne {
	nmuo.mutation.AddUpdateBy(u)
	return nmuo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (nmuo *NotificationMessageUpdateOne) ClearUpdateBy() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearUpdateBy()
	return nmuo
}

// SetSubject sets the "subject" field.
func (nmuo *NotificationMessageUpdateOne) SetSubject(s string) *NotificationMessageUpdateOne {
	nmuo.mutation.SetSubject(s)
	return nmuo
}

// SetNillableSubject sets the "subject" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableSubject(s *string) *NotificationMessageUpdateOne {
	if s != nil {
		nmuo.SetSubject(*s)
	}
	return nmuo
}

// ClearSubject clears the value of the "subject" field.
func (nmuo *NotificationMessageUpdateOne) ClearSubject() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearSubject()
	return nmuo
}

// SetContent sets the "content" field.
func (nmuo *NotificationMessageUpdateOne) SetContent(s string) *NotificationMessageUpdateOne {
	nmuo.mutation.SetContent(s)
	return nmuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableContent(s *string) *NotificationMessageUpdateOne {
	if s != nil {
		nmuo.SetContent(*s)
	}
	return nmuo
}

// ClearContent clears the value of the "content" field.
func (nmuo *NotificationMessageUpdateOne) ClearContent() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearContent()
	return nmuo
}

// SetCategoryID sets the "category_id" field.
func (nmuo *NotificationMessageUpdateOne) SetCategoryID(u uint32) *NotificationMessageUpdateOne {
	nmuo.mutation.ResetCategoryID()
	nmuo.mutation.SetCategoryID(u)
	return nmuo
}

// SetNillableCategoryID sets the "category_id" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableCategoryID(u *uint32) *NotificationMessageUpdateOne {
	if u != nil {
		nmuo.SetCategoryID(*u)
	}
	return nmuo
}

// AddCategoryID adds u to the "category_id" field.
func (nmuo *NotificationMessageUpdateOne) AddCategoryID(u int32) *NotificationMessageUpdateOne {
	nmuo.mutation.AddCategoryID(u)
	return nmuo
}

// ClearCategoryID clears the value of the "category_id" field.
func (nmuo *NotificationMessageUpdateOne) ClearCategoryID() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearCategoryID()
	return nmuo
}

// SetStatus sets the "status" field.
func (nmuo *NotificationMessageUpdateOne) SetStatus(n notificationmessage.Status) *NotificationMessageUpdateOne {
	nmuo.mutation.SetStatus(n)
	return nmuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nmuo *NotificationMessageUpdateOne) SetNillableStatus(n *notificationmessage.Status) *NotificationMessageUpdateOne {
	if n != nil {
		nmuo.SetStatus(*n)
	}
	return nmuo
}

// ClearStatus clears the value of the "status" field.
func (nmuo *NotificationMessageUpdateOne) ClearStatus() *NotificationMessageUpdateOne {
	nmuo.mutation.ClearStatus()
	return nmuo
}

// Mutation returns the NotificationMessageMutation object of the builder.
func (nmuo *NotificationMessageUpdateOne) Mutation() *NotificationMessageMutation {
	return nmuo.mutation
}

// Where appends a list predicates to the NotificationMessageUpdate builder.
func (nmuo *NotificationMessageUpdateOne) Where(ps ...predicate.NotificationMessage) *NotificationMessageUpdateOne {
	nmuo.mutation.Where(ps...)
	return nmuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (nmuo *NotificationMessageUpdateOne) Select(field string, fields ...string) *NotificationMessageUpdateOne {
	nmuo.fields = append([]string{field}, fields...)
	return nmuo
}

// Save executes the query and returns the updated NotificationMessage entity.
func (nmuo *NotificationMessageUpdateOne) Save(ctx context.Context) (*NotificationMessage, error) {
	return withHooks(ctx, nmuo.sqlSave, nmuo.mutation, nmuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nmuo *NotificationMessageUpdateOne) SaveX(ctx context.Context) *NotificationMessage {
	node, err := nmuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (nmuo *NotificationMessageUpdateOne) Exec(ctx context.Context) error {
	_, err := nmuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmuo *NotificationMessageUpdateOne) ExecX(ctx context.Context) {
	if err := nmuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmuo *NotificationMessageUpdateOne) check() error {
	if v, ok := nmuo.mutation.Status(); ok {
		if err := notificationmessage.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "NotificationMessage.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (nmuo *NotificationMessageUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *NotificationMessageUpdateOne {
	nmuo.modifiers = append(nmuo.modifiers, modifiers...)
	return nmuo
}

func (nmuo *NotificationMessageUpdateOne) sqlSave(ctx context.Context) (_node *NotificationMessage, err error) {
	if err := nmuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationmessage.Table, notificationmessage.Columns, sqlgraph.NewFieldSpec(notificationmessage.FieldID, field.TypeUint32))
	id, ok := nmuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "NotificationMessage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := nmuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationmessage.FieldID)
		for _, f := range fields {
			if !notificationmessage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != notificationmessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := nmuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if nmuo.mutation.CreateTimeCleared() {
		_spec.ClearField(notificationmessage.FieldCreateTime, field.TypeTime)
	}
	if value, ok := nmuo.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessage.FieldUpdateTime, field.TypeTime, value)
	}
	if nmuo.mutation.UpdateTimeCleared() {
		_spec.ClearField(notificationmessage.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := nmuo.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessage.FieldDeleteTime, field.TypeTime, value)
	}
	if nmuo.mutation.DeleteTimeCleared() {
		_spec.ClearField(notificationmessage.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := nmuo.mutation.CreateBy(); ok {
		_spec.SetField(notificationmessage.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := nmuo.mutation.AddedCreateBy(); ok {
		_spec.AddField(notificationmessage.FieldCreateBy, field.TypeUint32, value)
	}
	if nmuo.mutation.CreateByCleared() {
		_spec.ClearField(notificationmessage.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := nmuo.mutation.UpdateBy(); ok {
		_spec.SetField(notificationmessage.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := nmuo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(notificationmessage.FieldUpdateBy, field.TypeUint32, value)
	}
	if nmuo.mutation.UpdateByCleared() {
		_spec.ClearField(notificationmessage.FieldUpdateBy, field.TypeUint32)
	}
	if nmuo.mutation.TenantIDCleared() {
		_spec.ClearField(notificationmessage.FieldTenantID, field.TypeUint32)
	}
	if value, ok := nmuo.mutation.Subject(); ok {
		_spec.SetField(notificationmessage.FieldSubject, field.TypeString, value)
	}
	if nmuo.mutation.SubjectCleared() {
		_spec.ClearField(notificationmessage.FieldSubject, field.TypeString)
	}
	if value, ok := nmuo.mutation.Content(); ok {
		_spec.SetField(notificationmessage.FieldContent, field.TypeString, value)
	}
	if nmuo.mutation.ContentCleared() {
		_spec.ClearField(notificationmessage.FieldContent, field.TypeString)
	}
	if value, ok := nmuo.mutation.CategoryID(); ok {
		_spec.SetField(notificationmessage.FieldCategoryID, field.TypeUint32, value)
	}
	if value, ok := nmuo.mutation.AddedCategoryID(); ok {
		_spec.AddField(notificationmessage.FieldCategoryID, field.TypeUint32, value)
	}
	if nmuo.mutation.CategoryIDCleared() {
		_spec.ClearField(notificationmessage.FieldCategoryID, field.TypeUint32)
	}
	if value, ok := nmuo.mutation.Status(); ok {
		_spec.SetField(notificationmessage.FieldStatus, field.TypeEnum, value)
	}
	if nmuo.mutation.StatusCleared() {
		_spec.ClearField(notificationmessage.FieldStatus, field.TypeEnum)
	}
	_spec.AddModifiers(nmuo.modifiers...)
	_node = &NotificationMessage{config: nmuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, nmuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationmessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	nmuo.mutation.done = true
	return _node, nil
}
