// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminOperationLogDelete is the builder for deleting a AdminOperationLog entity.
type AdminOperationLogDelete struct {
	config
	hooks    []Hook
	mutation *AdminOperationLogMutation
}

// Where appends a list predicates to the AdminOperationLogDelete builder.
func (aold *AdminOperationLogDelete) Where(ps ...predicate.AdminOperationLog) *AdminOperationLogDelete {
	aold.mutation.Where(ps...)
	return aold
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (aold *AdminOperationLogDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, aold.sqlExec, aold.mutation, aold.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (aold *AdminOperationLogDelete) ExecX(ctx context.Context) int {
	n, err := aold.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (aold *AdminOperationLogDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(adminoperationlog.Table, sqlgraph.NewFieldSpec(adminoperationlog.FieldID, field.TypeUint32))
	if ps := aold.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, aold.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	aold.mutation.done = true
	return affected, err
}

// AdminOperationLogDeleteOne is the builder for deleting a single AdminOperationLog entity.
type AdminOperationLogDeleteOne struct {
	aold *AdminOperationLogDelete
}

// Where appends a list predicates to the AdminOperationLogDelete builder.
func (aoldo *AdminOperationLogDeleteOne) Where(ps ...predicate.AdminOperationLog) *AdminOperationLogDeleteOne {
	aoldo.aold.mutation.Where(ps...)
	return aoldo
}

// Exec executes the deletion query.
func (aoldo *AdminOperationLogDeleteOne) Exec(ctx context.Context) error {
	n, err := aoldo.aold.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{adminoperationlog.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (aoldo *AdminOperationLogDeleteOne) ExecX(ctx context.Context) {
	if err := aoldo.Exec(ctx); err != nil {
		panic(err)
	}
}
