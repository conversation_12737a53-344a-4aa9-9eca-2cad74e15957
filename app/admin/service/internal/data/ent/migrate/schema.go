// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AdminLoginLogsColumns holds the columns for the "admin_login_logs" table.
	AdminLoginLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "login_ip", Type: field.TypeString, Nullable: true, Comment: "登录IP地址"},
		{Name: "login_mac", Type: field.TypeString, Nullable: true, Comment: "登录MAC地址"},
		{Name: "login_time", Type: field.TypeTime, Nullable: true, Comment: "登录时间"},
		{Name: "user_agent", Type: field.TypeString, Nullable: true, Comment: "浏览器的用户代理信息"},
		{Name: "browser_name", Type: field.TypeString, Nullable: true, Comment: "浏览器名称"},
		{Name: "browser_version", Type: field.TypeString, Nullable: true, Comment: "浏览器版本"},
		{Name: "client_id", Type: field.TypeString, Nullable: true, Comment: "客户端ID"},
		{Name: "client_name", Type: field.TypeString, Nullable: true, Comment: "客户端名称"},
		{Name: "os_name", Type: field.TypeString, Nullable: true, Comment: "操作系统名称"},
		{Name: "os_version", Type: field.TypeString, Nullable: true, Comment: "操作系统版本"},
		{Name: "user_id", Type: field.TypeUint32, Nullable: true, Comment: "操作者用户ID"},
		{Name: "username", Type: field.TypeString, Nullable: true, Comment: "操作者账号名"},
		{Name: "status_code", Type: field.TypeInt32, Nullable: true, Comment: "状态码"},
		{Name: "success", Type: field.TypeBool, Nullable: true, Comment: "操作成功"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Comment: "登录失败原因"},
		{Name: "location", Type: field.TypeString, Nullable: true, Comment: "登录地理位置"},
	}
	// AdminLoginLogsTable holds the schema information for the "admin_login_logs" table.
	AdminLoginLogsTable = &schema.Table{
		Name:       "admin_login_logs",
		Comment:    "后台登录日志表",
		Columns:    AdminLoginLogsColumns,
		PrimaryKey: []*schema.Column{AdminLoginLogsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "adminloginlog_id",
				Unique:  false,
				Columns: []*schema.Column{AdminLoginLogsColumns[0]},
			},
		},
	}
	// AdminLoginRestrictionsColumns holds the columns for the "admin_login_restrictions" table.
	AdminLoginRestrictionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "target_id", Type: field.TypeUint32, Nullable: true, Comment: "目标用户ID"},
		{Name: "value", Type: field.TypeString, Nullable: true, Comment: "限制值（如IP地址、MAC地址或地区代码）"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Comment: "限制原因"},
		{Name: "type", Type: field.TypeEnum, Nullable: true, Comment: "限制类型", Enums: []string{"BLACKLIST", "WHITELIST"}, Default: "BLACKLIST"},
		{Name: "method", Type: field.TypeEnum, Nullable: true, Comment: "限制方式", Enums: []string{"IP", "MAC", "REGION", "TIME", "DEVICE"}, Default: "IP"},
	}
	// AdminLoginRestrictionsTable holds the schema information for the "admin_login_restrictions" table.
	AdminLoginRestrictionsTable = &schema.Table{
		Name:       "admin_login_restrictions",
		Comment:    "后台登录限制表",
		Columns:    AdminLoginRestrictionsColumns,
		PrimaryKey: []*schema.Column{AdminLoginRestrictionsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "adminloginrestriction_id",
				Unique:  false,
				Columns: []*schema.Column{AdminLoginRestrictionsColumns[0]},
			},
			{
				Name:    "adminloginrestriction_target_id_type_method",
				Unique:  true,
				Columns: []*schema.Column{AdminLoginRestrictionsColumns[6], AdminLoginRestrictionsColumns[9], AdminLoginRestrictionsColumns[10]},
			},
		},
	}
	// AdminOperationLogsColumns holds the columns for the "admin_operation_logs" table.
	AdminOperationLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "request_id", Type: field.TypeString, Nullable: true, Comment: "请求ID"},
		{Name: "method", Type: field.TypeString, Nullable: true, Comment: "请求方法"},
		{Name: "operation", Type: field.TypeString, Nullable: true, Comment: "操作方法"},
		{Name: "path", Type: field.TypeString, Nullable: true, Comment: "请求路径"},
		{Name: "referer", Type: field.TypeString, Nullable: true, Comment: "请求源"},
		{Name: "request_uri", Type: field.TypeString, Nullable: true, Comment: "请求URI"},
		{Name: "request_body", Type: field.TypeString, Nullable: true, Comment: "请求体"},
		{Name: "request_header", Type: field.TypeString, Nullable: true, Comment: "请求头"},
		{Name: "response", Type: field.TypeString, Nullable: true, Comment: "响应信息"},
		{Name: "cost_time", Type: field.TypeFloat64, Nullable: true, Comment: "操作耗时"},
		{Name: "user_id", Type: field.TypeUint32, Nullable: true, Comment: "操作者用户ID"},
		{Name: "username", Type: field.TypeString, Nullable: true, Comment: "操作者账号名"},
		{Name: "client_ip", Type: field.TypeString, Nullable: true, Comment: "操作者IP"},
		{Name: "status_code", Type: field.TypeInt32, Nullable: true, Comment: "状态码"},
		{Name: "reason", Type: field.TypeString, Nullable: true, Comment: "操作失败原因"},
		{Name: "success", Type: field.TypeBool, Nullable: true, Comment: "操作成功"},
		{Name: "location", Type: field.TypeString, Nullable: true, Comment: "操作地理位置"},
		{Name: "user_agent", Type: field.TypeString, Nullable: true, Comment: "浏览器的用户代理信息"},
		{Name: "browser_name", Type: field.TypeString, Nullable: true, Comment: "浏览器名称"},
		{Name: "browser_version", Type: field.TypeString, Nullable: true, Comment: "浏览器版本"},
		{Name: "client_id", Type: field.TypeString, Nullable: true, Comment: "客户端ID"},
		{Name: "client_name", Type: field.TypeString, Nullable: true, Comment: "客户端名称"},
		{Name: "os_name", Type: field.TypeString, Nullable: true, Comment: "操作系统名称"},
		{Name: "os_version", Type: field.TypeString, Nullable: true, Comment: "操作系统版本"},
	}
	// AdminOperationLogsTable holds the schema information for the "admin_operation_logs" table.
	AdminOperationLogsTable = &schema.Table{
		Name:       "admin_operation_logs",
		Comment:    "后台操作日志表",
		Columns:    AdminOperationLogsColumns,
		PrimaryKey: []*schema.Column{AdminOperationLogsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "adminoperationlog_id",
				Unique:  false,
				Columns: []*schema.Column{AdminOperationLogsColumns[0]},
			},
		},
	}
	// SysAPIResourcesColumns holds the columns for the "sys_api_resources" table.
	SysAPIResourcesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "module", Type: field.TypeString, Nullable: true, Comment: "所属业务模块"},
		{Name: "module_description", Type: field.TypeString, Nullable: true, Comment: "业务模块描述"},
		{Name: "operation", Type: field.TypeString, Nullable: true, Comment: "接口操作名"},
		{Name: "path", Type: field.TypeString, Nullable: true, Comment: "接口路径"},
		{Name: "method", Type: field.TypeString, Nullable: true, Comment: "请求方法"},
	}
	// SysAPIResourcesTable holds the schema information for the "sys_api_resources" table.
	SysAPIResourcesTable = &schema.Table{
		Name:       "sys_api_resources",
		Comment:    "API资源表",
		Columns:    SysAPIResourcesColumns,
		PrimaryKey: []*schema.Column{SysAPIResourcesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "apiresource_id",
				Unique:  false,
				Columns: []*schema.Column{SysAPIResourcesColumns[0]},
			},
		},
	}
	// DepartmentsColumns holds the columns for the "departments" table.
	DepartmentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名字", Default: ""},
		{Name: "organization_id", Type: field.TypeUint32, Nullable: true, Comment: "所属组织ID"},
		{Name: "sort_id", Type: field.TypeInt32, Nullable: true, Comment: "排序ID", Default: 0},
		{Name: "parent_id", Type: field.TypeUint32, Nullable: true, Comment: "上一层部门ID", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
	}
	// DepartmentsTable holds the schema information for the "departments" table.
	DepartmentsTable = &schema.Table{
		Name:       "departments",
		Comment:    "部门表",
		Columns:    DepartmentsColumns,
		PrimaryKey: []*schema.Column{DepartmentsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "departments_departments_children",
				Columns:    []*schema.Column{DepartmentsColumns[12]},
				RefColumns: []*schema.Column{DepartmentsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "department_id",
				Unique:  false,
				Columns: []*schema.Column{DepartmentsColumns[0]},
			},
			{
				Name:    "department_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{DepartmentsColumns[8]},
			},
		},
	}
	// SysDictsColumns holds the columns for the "sys_dicts" table.
	SysDictsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "key", Type: field.TypeString, Unique: true, Nullable: true, Comment: "字典键"},
		{Name: "category", Type: field.TypeString, Nullable: true, Comment: "字典类型"},
		{Name: "category_desc", Type: field.TypeString, Nullable: true, Comment: "字典类型名称"},
		{Name: "value", Type: field.TypeString, Nullable: true, Comment: "字典值"},
		{Name: "value_desc", Type: field.TypeString, Nullable: true, Comment: "字典值名称"},
		{Name: "value_data_type", Type: field.TypeString, Nullable: true, Comment: "字典值数据类型"},
		{Name: "sort_id", Type: field.TypeInt32, Nullable: true, Comment: "排序ID", Default: 0},
	}
	// SysDictsTable holds the schema information for the "sys_dicts" table.
	SysDictsTable = &schema.Table{
		Name:       "sys_dicts",
		Comment:    "字典表",
		Columns:    SysDictsColumns,
		PrimaryKey: []*schema.Column{SysDictsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "dict_id",
				Unique:  false,
				Columns: []*schema.Column{SysDictsColumns[0]},
			},
			{
				Name:    "dict_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{SysDictsColumns[8]},
			},
		},
	}
	// FilesColumns holds the columns for the "files" table.
	FilesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "provider", Type: field.TypeEnum, Nullable: true, Comment: "OSS供应商", Enums: []string{"UNKNOWN", "MINIO", "ALIYUN", "QINIU", "TENCENT", "AWS", "GOOGLE", "AZURE", "BAIDU", "HUAWEI", "QCLOUD", "LOCAL"}},
		{Name: "bucket_name", Type: field.TypeString, Nullable: true, Comment: "存储桶名称"},
		{Name: "file_directory", Type: field.TypeString, Nullable: true, Comment: "文件目录"},
		{Name: "file_guid", Type: field.TypeString, Nullable: true, Comment: "文件Guid"},
		{Name: "save_file_name", Type: field.TypeString, Nullable: true, Comment: "保存文件名"},
		{Name: "file_name", Type: field.TypeString, Nullable: true, Comment: "文件名"},
		{Name: "extension", Type: field.TypeString, Nullable: true, Comment: "文件扩展名"},
		{Name: "size", Type: field.TypeUint64, Nullable: true, Comment: "文件字节长度"},
		{Name: "size_format", Type: field.TypeString, Nullable: true, Comment: "文件大小格式化"},
		{Name: "link_url", Type: field.TypeString, Nullable: true, Comment: "链接地址"},
		{Name: "md5", Type: field.TypeString, Nullable: true, Comment: "md5码，防止上传重复文件"},
	}
	// FilesTable holds the schema information for the "files" table.
	FilesTable = &schema.Table{
		Name:       "files",
		Comment:    "文件表",
		Columns:    FilesColumns,
		PrimaryKey: []*schema.Column{FilesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "file_id",
				Unique:  false,
				Columns: []*schema.Column{FilesColumns[0]},
			},
			{
				Name:    "file_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{FilesColumns[6]},
			},
		},
	}
	// SysMenusColumns holds the columns for the "sys_menus" table.
	SysMenusColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt32, Increment: true, Comment: "id"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "type", Type: field.TypeEnum, Nullable: true, Comment: "菜单类型 FOLDER: 目录 MENU: 菜单 BUTTON: 按钮", Enums: []string{"FOLDER", "MENU", "BUTTON"}, Default: "MENU"},
		{Name: "path", Type: field.TypeString, Nullable: true, Comment: "路径,当其类型为'按钮'的时候对应的数据操作名,例如:/user.service.v1.UserService/Login", Default: ""},
		{Name: "redirect", Type: field.TypeString, Nullable: true, Comment: "重定向地址"},
		{Name: "alias", Type: field.TypeString, Nullable: true, Comment: "路由别名"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。"},
		{Name: "component", Type: field.TypeString, Nullable: true, Comment: "前端页面组件", Default: ""},
		{Name: "meta", Type: field.TypeJSON, Nullable: true, Comment: "前端页面组件"},
		{Name: "parent_id", Type: field.TypeInt32, Nullable: true, Comment: "上一层菜单ID"},
	}
	// SysMenusTable holds the schema information for the "sys_menus" table.
	SysMenusTable = &schema.Table{
		Name:       "sys_menus",
		Comment:    "后台目录表",
		Columns:    SysMenusColumns,
		PrimaryKey: []*schema.Column{SysMenusColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_menus_sys_menus_children",
				Columns:    []*schema.Column{SysMenusColumns[15]},
				RefColumns: []*schema.Column{SysMenusColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// NotificationMessagesColumns holds the columns for the "notification_messages" table.
	NotificationMessagesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "subject", Type: field.TypeString, Nullable: true, Comment: "主题"},
		{Name: "content", Type: field.TypeString, Nullable: true, Comment: "内容"},
		{Name: "category_id", Type: field.TypeUint32, Nullable: true, Comment: "分类ID"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "消息状态", Enums: []string{"UNKNOWN", "DRAFT", "PUBLISHED", "SCHEDULED", "REVOKED", "ARCHIVED", "DELETED"}},
	}
	// NotificationMessagesTable holds the schema information for the "notification_messages" table.
	NotificationMessagesTable = &schema.Table{
		Name:       "notification_messages",
		Comment:    "站内信通知消息表",
		Columns:    NotificationMessagesColumns,
		PrimaryKey: []*schema.Column{NotificationMessagesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "notificationmessage_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationMessagesColumns[0]},
			},
			{
				Name:    "notificationmessage_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationMessagesColumns[6]},
			},
		},
	}
	// NotificationMessageCategoriesColumns holds the columns for the "notification_message_categories" table.
	NotificationMessageCategoriesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Comment: "编码"},
		{Name: "sort_id", Type: field.TypeInt32, Nullable: true, Comment: "排序编号"},
		{Name: "enable", Type: field.TypeBool, Nullable: true, Comment: "是否启用"},
		{Name: "parent_id", Type: field.TypeUint32, Nullable: true, Comment: "父节点ID", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
	}
	// NotificationMessageCategoriesTable holds the schema information for the "notification_message_categories" table.
	NotificationMessageCategoriesTable = &schema.Table{
		Name:       "notification_message_categories",
		Comment:    "站内信通知消息分类表",
		Columns:    NotificationMessageCategoriesColumns,
		PrimaryKey: []*schema.Column{NotificationMessageCategoriesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "notification_message_categories_notification_message_categories_children",
				Columns:    []*schema.Column{NotificationMessageCategoriesColumns[12]},
				RefColumns: []*schema.Column{NotificationMessageCategoriesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "notificationmessagecategory_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationMessageCategoriesColumns[0]},
			},
			{
				Name:    "notificationmessagecategory_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationMessageCategoriesColumns[7]},
			},
		},
	}
	// NotificationMessageRecipientsColumns holds the columns for the "notification_message_recipients" table.
	NotificationMessageRecipientsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "message_id", Type: field.TypeUint32, Nullable: true, Comment: "群发消息ID"},
		{Name: "recipient_id", Type: field.TypeUint32, Nullable: true, Comment: "接收者用户ID"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "消息状态", Enums: []string{"UNKNOWN", "RECEIVED", "READ", "ARCHIVED", "DELETED"}},
	}
	// NotificationMessageRecipientsTable holds the schema information for the "notification_message_recipients" table.
	NotificationMessageRecipientsTable = &schema.Table{
		Name:       "notification_message_recipients",
		Comment:    "站内信通知消息接收者表",
		Columns:    NotificationMessageRecipientsColumns,
		PrimaryKey: []*schema.Column{NotificationMessageRecipientsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "notificationmessagerecipient_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationMessageRecipientsColumns[0]},
			},
			{
				Name:    "notificationmessagerecipient_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationMessageRecipientsColumns[4]},
			},
		},
	}
	// OrganizationsColumns holds the columns for the "organizations" table.
	OrganizationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "名字", Default: ""},
		{Name: "sort_id", Type: field.TypeInt32, Nullable: true, Comment: "排序ID", Default: 0},
		{Name: "parent_id", Type: field.TypeUint32, Nullable: true, Comment: "上一层组织ID", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
	}
	// OrganizationsTable holds the schema information for the "organizations" table.
	OrganizationsTable = &schema.Table{
		Name:       "organizations",
		Comment:    "组织表",
		Columns:    OrganizationsColumns,
		PrimaryKey: []*schema.Column{OrganizationsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "organizations_organizations_children",
				Columns:    []*schema.Column{OrganizationsColumns[11]},
				RefColumns: []*schema.Column{OrganizationsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "organization_id",
				Unique:  false,
				Columns: []*schema.Column{OrganizationsColumns[0]},
			},
			{
				Name:    "organization_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{OrganizationsColumns[8]},
			},
		},
	}
	// PositionsColumns holds the columns for the "positions" table.
	PositionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "name", Type: field.TypeString, Size: 128, Comment: "职位名称", Default: ""},
		{Name: "code", Type: field.TypeString, Size: 128, Comment: "职位标识", Default: ""},
		{Name: "sort_id", Type: field.TypeInt32, Comment: "排序ID", Default: 0},
		{Name: "parent_id", Type: field.TypeUint32, Nullable: true, Comment: "上一层职位ID", Default: 0, SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
	}
	// PositionsTable holds the schema information for the "positions" table.
	PositionsTable = &schema.Table{
		Name:       "positions",
		Comment:    "职位表",
		Columns:    PositionsColumns,
		PrimaryKey: []*schema.Column{PositionsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "positions_positions_children",
				Columns:    []*schema.Column{PositionsColumns[12]},
				RefColumns: []*schema.Column{PositionsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "position_id",
				Unique:  false,
				Columns: []*schema.Column{PositionsColumns[0]},
			},
			{
				Name:    "position_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{PositionsColumns[8]},
			},
		},
	}
	// PrivateMessagesColumns holds the columns for the "private_messages" table.
	PrivateMessagesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "subject", Type: field.TypeString, Nullable: true, Comment: "主题"},
		{Name: "content", Type: field.TypeString, Nullable: true, Comment: "内容"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "消息状态", Enums: []string{"UNKNOWN", "DRAFT", "SENT", "RECEIVED", "READ", "ARCHIVED", "DELETED"}},
		{Name: "sender_id", Type: field.TypeUint32, Nullable: true, Comment: "发送者用户ID"},
		{Name: "receiver_id", Type: field.TypeUint32, Nullable: true, Comment: "接收者用户ID"},
	}
	// PrivateMessagesTable holds the schema information for the "private_messages" table.
	PrivateMessagesTable = &schema.Table{
		Name:       "private_messages",
		Comment:    "站内信私信消息表",
		Columns:    PrivateMessagesColumns,
		PrimaryKey: []*schema.Column{PrivateMessagesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "privatemessage_id",
				Unique:  false,
				Columns: []*schema.Column{PrivateMessagesColumns[0]},
			},
			{
				Name:    "privatemessage_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{PrivateMessagesColumns[4]},
			},
		},
	}
	// SysRolesColumns holds the columns for the "sys_roles" table.
	SysRolesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "name", Type: field.TypeString, Unique: true, Nullable: true, Size: 128, Comment: "角色名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Size: 128, Comment: "角色标识", Default: ""},
		{Name: "sort_id", Type: field.TypeInt32, Nullable: true, Comment: "排序ID", Default: 0},
		{Name: "menus", Type: field.TypeJSON, Nullable: true, Comment: "分配的菜单列表"},
		{Name: "apis", Type: field.TypeJSON, Nullable: true, Comment: "分配的API列表"},
		{Name: "parent_id", Type: field.TypeUint32, Nullable: true, Comment: "上一层角色ID", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
	}
	// SysRolesTable holds the schema information for the "sys_roles" table.
	SysRolesTable = &schema.Table{
		Name:       "sys_roles",
		Comment:    "角色表",
		Columns:    SysRolesColumns,
		PrimaryKey: []*schema.Column{SysRolesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sys_roles_sys_roles_children",
				Columns:    []*schema.Column{SysRolesColumns[14]},
				RefColumns: []*schema.Column{SysRolesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "role_id",
				Unique:  false,
				Columns: []*schema.Column{SysRolesColumns[0]},
			},
			{
				Name:    "role_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{SysRolesColumns[8]},
			},
			{
				Name:    "role_code",
				Unique:  false,
				Columns: []*schema.Column{SysRolesColumns[10]},
			},
		},
	}
	// SysTasksColumns holds the columns for the "sys_tasks" table.
	SysTasksColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "type", Type: field.TypeEnum, Nullable: true, Comment: "任务类型", Enums: []string{"PERIODIC", "DELAY", "WAIT_RESULT"}},
		{Name: "type_name", Type: field.TypeString, Unique: true, Nullable: true, Comment: "任务执行类型名"},
		{Name: "task_payload", Type: field.TypeString, Nullable: true, Comment: "任务数据", SchemaType: map[string]string{"mysql": "json", "postgres": "jsonb"}},
		{Name: "cron_spec", Type: field.TypeString, Nullable: true, Comment: "cron表达式"},
		{Name: "task_options", Type: field.TypeJSON, Nullable: true, Comment: "任务选项"},
		{Name: "enable", Type: field.TypeBool, Nullable: true, Comment: "启用/禁用任务"},
	}
	// SysTasksTable holds the schema information for the "sys_tasks" table.
	SysTasksTable = &schema.Table{
		Name:       "sys_tasks",
		Comment:    "任务表",
		Columns:    SysTasksColumns,
		PrimaryKey: []*schema.Column{SysTasksColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "task_id",
				Unique:  false,
				Columns: []*schema.Column{SysTasksColumns[0]},
			},
			{
				Name:    "task_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{SysTasksColumns[7]},
			},
		},
	}
	// TenantsColumns holds the columns for the "tenants" table.
	TenantsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "租户名称"},
		{Name: "code", Type: field.TypeString, Nullable: true, Size: 64, Comment: "租户编号"},
		{Name: "member_count", Type: field.TypeInt32, Nullable: true, Comment: "成员数", Default: 0},
		{Name: "subscription_at", Type: field.TypeTime, Nullable: true, Comment: "订阅时间"},
		{Name: "unsubscribe_at", Type: field.TypeTime, Nullable: true, Comment: "取消订阅时间"},
	}
	// TenantsTable holds the schema information for the "tenants" table.
	TenantsTable = &schema.Table{
		Name:       "tenants",
		Comment:    "租户表",
		Columns:    TenantsColumns,
		PrimaryKey: []*schema.Column{TenantsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "tenant_id",
				Unique:  false,
				Columns: []*schema.Column{TenantsColumns[0]},
			},
		},
	}
	// UsersColumns holds the columns for the "users" table.
	UsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_by", Type: field.TypeUint32, Nullable: true, Comment: "创建者ID"},
		{Name: "update_by", Type: field.TypeUint32, Nullable: true, Comment: "更新者ID"},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "备注", Default: ""},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "状态", Enums: []string{"OFF", "ON"}, Default: "ON"},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "username", Type: field.TypeString, Unique: true, Nullable: true, Comment: "用户名"},
		{Name: "nickname", Type: field.TypeString, Nullable: true, Size: 255, Comment: "昵称"},
		{Name: "realname", Type: field.TypeString, Nullable: true, Size: 255, Comment: "真实名字"},
		{Name: "email", Type: field.TypeString, Nullable: true, Size: 320, Comment: "电子邮箱"},
		{Name: "mobile", Type: field.TypeString, Nullable: true, Size: 255, Comment: "手机号码", Default: ""},
		{Name: "telephone", Type: field.TypeString, Nullable: true, Size: 255, Comment: "座机号码", Default: ""},
		{Name: "avatar", Type: field.TypeString, Nullable: true, Size: 1023, Comment: "头像"},
		{Name: "address", Type: field.TypeString, Nullable: true, Size: 2048, Comment: "地址", Default: ""},
		{Name: "region", Type: field.TypeString, Nullable: true, Size: 255, Comment: "国家地区", Default: ""},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 1023, Comment: "个人说明"},
		{Name: "gender", Type: field.TypeEnum, Nullable: true, Comment: "性别", Enums: []string{"SECRET", "MALE", "FEMALE"}},
		{Name: "authority", Type: field.TypeEnum, Nullable: true, Comment: "授权", Enums: []string{"SYS_ADMIN", "TENANT_ADMIN", "CUSTOMER_USER", "GUEST"}, Default: "CUSTOMER_USER"},
		{Name: "last_login_time", Type: field.TypeTime, Nullable: true, Comment: "最后一次登录的时间"},
		{Name: "last_login_ip", Type: field.TypeString, Nullable: true, Size: 64, Comment: "最后一次登录的IP", Default: ""},
		{Name: "org_id", Type: field.TypeUint32, Nullable: true, Comment: "部门ID"},
		{Name: "position_id", Type: field.TypeUint32, Nullable: true, Comment: "职位ID"},
		{Name: "work_id", Type: field.TypeUint32, Nullable: true, Comment: "员工工号"},
		{Name: "roles", Type: field.TypeJSON, Nullable: true, Comment: "多角色角色码列表"},
	}
	// UsersTable holds the schema information for the "users" table.
	UsersTable = &schema.Table{
		Name:       "users",
		Comment:    "用户表",
		Columns:    UsersColumns,
		PrimaryKey: []*schema.Column{UsersColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "user_id",
				Unique:  false,
				Columns: []*schema.Column{UsersColumns[0]},
			},
			{
				Name:    "user_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{UsersColumns[8]},
			},
		},
	}
	// UserCredentialsColumns holds the columns for the "user_credentials" table.
	UserCredentialsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint32, Increment: true, Comment: "id", SchemaType: map[string]string{"mysql": "int", "postgres": "serial"}},
		{Name: "create_time", Type: field.TypeTime, Nullable: true, Comment: "创建时间"},
		{Name: "update_time", Type: field.TypeTime, Nullable: true, Comment: "更新时间"},
		{Name: "delete_time", Type: field.TypeTime, Nullable: true, Comment: "删除时间"},
		{Name: "tenant_id", Type: field.TypeUint32, Nullable: true, Comment: "租户ID"},
		{Name: "user_id", Type: field.TypeUint32, Nullable: true, Comment: "关联主表的用户ID"},
		{Name: "identity_type", Type: field.TypeEnum, Nullable: true, Comment: "认证方式类型", Enums: []string{"USERNAME", "USERID", "EMAIL", "PHONE", "WECHAT", "QQ", "WEIBO", "DOUYIN", "KUAISHOU", "BAIDU", "ALIPAY", "TAOBAO", "JD", "MEITUAN", "DINGTALK", "BILIBILI", "XIAOHONGSHU", "GOOGLE", "FACEBOOK", "APPLE", "TELEGRAM", "TWITTER", "LINKEDIN", "GITHUB", "MICROSOFT", "DISCORD", "SLACK", "INSTAGRAM", "TIKTOK", "REDDIT", "YOUTUBE", "SPOTIFY", "PINTEREST", "SNAPCHAT", "TUMBLR", "YAHOO", "WHATSAPP", "LINE"}, Default: "USERNAME"},
		{Name: "identifier", Type: field.TypeString, Nullable: true, Comment: "身份唯一标识符"},
		{Name: "credential_type", Type: field.TypeEnum, Nullable: true, Comment: "凭证类型", Enums: []string{"PASSWORD_HASH", "ACCESS_TOKEN", "REFRESH_TOKEN", "EMAIL_VERIFICATION_CODE", "PHONE_VERIFICATION_CODE", "OAUTH_TOKEN", "API_KEY", "SSO_TOKEN", "JWT", "SAML_ASSERTION", "OPENID_CONNECT_ID_TOKEN", "SESSION_COOKIE", "TEMPORARY_CREDENTIAL", "CUSTOM_CREDENTIAL", "BIOMETRIC_DATA", "SECURITY_KEY", "OTP", "SMART_CARD", "CRYPTOGRAPHIC_CERTIFICATE", "BIOMETRIC_TOKEN", "DEVICE_FINGERPRINT", "HARDWARE_TOKEN", "SOFTWARE_TOKEN", "SECURITY_QUESTION", "SECURITY_PIN", "TWO_FACTOR_AUTHENTICATION", "MULTI_FACTOR_AUTHENTICATION", "PASSWORDLESS_AUTHENTICATION", "SOCIAL_LOGIN_TOKEN", "SSO_SESSION", "API_SECRET", "CUSTOM_TOKEN", "OAUTH2_CLIENT_CREDENTIALS", "OAUTH2_AUTHORIZATION_CODE", "OAUTH2_IMPLICIT_GRANT", "OAUTH2_PASSWORD_GRANT", "OAUTH2_REFRESH_GRANT"}, Default: "PASSWORD_HASH"},
		{Name: "credential", Type: field.TypeString, Nullable: true, Comment: "凭证"},
		{Name: "is_primary", Type: field.TypeBool, Nullable: true, Comment: "是否主认证方式", Default: false},
		{Name: "status", Type: field.TypeEnum, Nullable: true, Comment: "凭证状态", Enums: []string{"DISABLED", "ENABLED", "EXPIRED", "UNVERIFIED", "REMOVED", "BLOCKED", "TEMPORARY"}, Default: "ENABLED"},
		{Name: "extra_info", Type: field.TypeString, Nullable: true, Comment: "扩展信息", SchemaType: map[string]string{"mysql": "json", "postgres": "jsonb"}},
		{Name: "activate_token", Type: field.TypeString, Unique: true, Nullable: true, Size: 255, Comment: "激活账号用的令牌"},
		{Name: "reset_token", Type: field.TypeString, Unique: true, Nullable: true, Size: 255, Comment: "重置密码用的令牌"},
	}
	// UserCredentialsTable holds the schema information for the "user_credentials" table.
	UserCredentialsTable = &schema.Table{
		Name:       "user_credentials",
		Comment:    "用户认证信息表",
		Columns:    UserCredentialsColumns,
		PrimaryKey: []*schema.Column{UserCredentialsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "usercredential_id",
				Unique:  false,
				Columns: []*schema.Column{UserCredentialsColumns[0]},
			},
			{
				Name:    "usercredential_tenant_id",
				Unique:  false,
				Columns: []*schema.Column{UserCredentialsColumns[4]},
			},
			{
				Name:    "usercredential_user_id_identity_type_identifier",
				Unique:  true,
				Columns: []*schema.Column{UserCredentialsColumns[5], UserCredentialsColumns[6], UserCredentialsColumns[7]},
			},
			{
				Name:    "usercredential_identifier",
				Unique:  false,
				Columns: []*schema.Column{UserCredentialsColumns[7]},
			},
			{
				Name:    "usercredential_user_id",
				Unique:  false,
				Columns: []*schema.Column{UserCredentialsColumns[5]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AdminLoginLogsTable,
		AdminLoginRestrictionsTable,
		AdminOperationLogsTable,
		SysAPIResourcesTable,
		DepartmentsTable,
		SysDictsTable,
		FilesTable,
		SysMenusTable,
		NotificationMessagesTable,
		NotificationMessageCategoriesTable,
		NotificationMessageRecipientsTable,
		OrganizationsTable,
		PositionsTable,
		PrivateMessagesTable,
		SysRolesTable,
		SysTasksTable,
		TenantsTable,
		UsersTable,
		UserCredentialsTable,
	}
)

func init() {
	AdminLoginLogsTable.Annotation = &entsql.Annotation{
		Table:     "admin_login_logs",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	AdminLoginRestrictionsTable.Annotation = &entsql.Annotation{
		Table:     "admin_login_restrictions",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	AdminOperationLogsTable.Annotation = &entsql.Annotation{
		Table:     "admin_operation_logs",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	SysAPIResourcesTable.Annotation = &entsql.Annotation{
		Table:     "sys_api_resources",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	DepartmentsTable.ForeignKeys[0].RefTable = DepartmentsTable
	DepartmentsTable.Annotation = &entsql.Annotation{
		Table:     "departments",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	SysDictsTable.Annotation = &entsql.Annotation{
		Table:     "sys_dicts",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	FilesTable.Annotation = &entsql.Annotation{
		Table:     "files",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	SysMenusTable.ForeignKeys[0].RefTable = SysMenusTable
	SysMenusTable.Annotation = &entsql.Annotation{
		Table:     "sys_menus",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	NotificationMessagesTable.Annotation = &entsql.Annotation{
		Table:     "notification_messages",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	NotificationMessageCategoriesTable.ForeignKeys[0].RefTable = NotificationMessageCategoriesTable
	NotificationMessageCategoriesTable.Annotation = &entsql.Annotation{
		Table:     "notification_message_categories",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	NotificationMessageRecipientsTable.Annotation = &entsql.Annotation{
		Table:     "notification_message_recipients",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	OrganizationsTable.ForeignKeys[0].RefTable = OrganizationsTable
	OrganizationsTable.Annotation = &entsql.Annotation{
		Table:     "organizations",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	PositionsTable.ForeignKeys[0].RefTable = PositionsTable
	PositionsTable.Annotation = &entsql.Annotation{
		Table:     "positions",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	PrivateMessagesTable.Annotation = &entsql.Annotation{
		Table:     "private_messages",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	SysRolesTable.ForeignKeys[0].RefTable = SysRolesTable
	SysRolesTable.Annotation = &entsql.Annotation{
		Table:     "sys_roles",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	SysTasksTable.Annotation = &entsql.Annotation{
		Table:     "sys_tasks",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	TenantsTable.Annotation = &entsql.Annotation{
		Table:     "tenants",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	UsersTable.Annotation = &entsql.Annotation{
		Table:     "users",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
	UserCredentialsTable.Annotation = &entsql.Annotation{
		Table:     "user_credentials",
		Charset:   "utf8mb4",
		Collation: "utf8mb4_bin",
	}
}
