// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/apiresource"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ApiResourceCreate is the builder for creating a ApiResource entity.
type ApiResourceCreate struct {
	config
	mutation *ApiResourceMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (arc *ApiResourceCreate) SetCreateTime(t time.Time) *ApiResourceCreate {
	arc.mutation.SetCreateTime(t)
	return arc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableCreateTime(t *time.Time) *ApiResourceCreate {
	if t != nil {
		arc.SetCreateTime(*t)
	}
	return arc
}

// SetUpdateTime sets the "update_time" field.
func (arc *ApiResourceCreate) SetUpdateTime(t time.Time) *ApiResourceCreate {
	arc.mutation.SetUpdateTime(t)
	return arc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableUpdateTime(t *time.Time) *ApiResourceCreate {
	if t != nil {
		arc.SetUpdateTime(*t)
	}
	return arc
}

// SetDeleteTime sets the "delete_time" field.
func (arc *ApiResourceCreate) SetDeleteTime(t time.Time) *ApiResourceCreate {
	arc.mutation.SetDeleteTime(t)
	return arc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableDeleteTime(t *time.Time) *ApiResourceCreate {
	if t != nil {
		arc.SetDeleteTime(*t)
	}
	return arc
}

// SetCreateBy sets the "create_by" field.
func (arc *ApiResourceCreate) SetCreateBy(u uint32) *ApiResourceCreate {
	arc.mutation.SetCreateBy(u)
	return arc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableCreateBy(u *uint32) *ApiResourceCreate {
	if u != nil {
		arc.SetCreateBy(*u)
	}
	return arc
}

// SetUpdateBy sets the "update_by" field.
func (arc *ApiResourceCreate) SetUpdateBy(u uint32) *ApiResourceCreate {
	arc.mutation.SetUpdateBy(u)
	return arc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableUpdateBy(u *uint32) *ApiResourceCreate {
	if u != nil {
		arc.SetUpdateBy(*u)
	}
	return arc
}

// SetDescription sets the "description" field.
func (arc *ApiResourceCreate) SetDescription(s string) *ApiResourceCreate {
	arc.mutation.SetDescription(s)
	return arc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableDescription(s *string) *ApiResourceCreate {
	if s != nil {
		arc.SetDescription(*s)
	}
	return arc
}

// SetModule sets the "module" field.
func (arc *ApiResourceCreate) SetModule(s string) *ApiResourceCreate {
	arc.mutation.SetModule(s)
	return arc
}

// SetNillableModule sets the "module" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableModule(s *string) *ApiResourceCreate {
	if s != nil {
		arc.SetModule(*s)
	}
	return arc
}

// SetModuleDescription sets the "module_description" field.
func (arc *ApiResourceCreate) SetModuleDescription(s string) *ApiResourceCreate {
	arc.mutation.SetModuleDescription(s)
	return arc
}

// SetNillableModuleDescription sets the "module_description" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableModuleDescription(s *string) *ApiResourceCreate {
	if s != nil {
		arc.SetModuleDescription(*s)
	}
	return arc
}

// SetOperation sets the "operation" field.
func (arc *ApiResourceCreate) SetOperation(s string) *ApiResourceCreate {
	arc.mutation.SetOperation(s)
	return arc
}

// SetNillableOperation sets the "operation" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableOperation(s *string) *ApiResourceCreate {
	if s != nil {
		arc.SetOperation(*s)
	}
	return arc
}

// SetPath sets the "path" field.
func (arc *ApiResourceCreate) SetPath(s string) *ApiResourceCreate {
	arc.mutation.SetPath(s)
	return arc
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillablePath(s *string) *ApiResourceCreate {
	if s != nil {
		arc.SetPath(*s)
	}
	return arc
}

// SetMethod sets the "method" field.
func (arc *ApiResourceCreate) SetMethod(s string) *ApiResourceCreate {
	arc.mutation.SetMethod(s)
	return arc
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (arc *ApiResourceCreate) SetNillableMethod(s *string) *ApiResourceCreate {
	if s != nil {
		arc.SetMethod(*s)
	}
	return arc
}

// SetID sets the "id" field.
func (arc *ApiResourceCreate) SetID(u uint32) *ApiResourceCreate {
	arc.mutation.SetID(u)
	return arc
}

// Mutation returns the ApiResourceMutation object of the builder.
func (arc *ApiResourceCreate) Mutation() *ApiResourceMutation {
	return arc.mutation
}

// Save creates the ApiResource in the database.
func (arc *ApiResourceCreate) Save(ctx context.Context) (*ApiResource, error) {
	return withHooks(ctx, arc.sqlSave, arc.mutation, arc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (arc *ApiResourceCreate) SaveX(ctx context.Context) *ApiResource {
	v, err := arc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (arc *ApiResourceCreate) Exec(ctx context.Context) error {
	_, err := arc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (arc *ApiResourceCreate) ExecX(ctx context.Context) {
	if err := arc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (arc *ApiResourceCreate) check() error {
	if v, ok := arc.mutation.ID(); ok {
		if err := apiresource.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "ApiResource.id": %w`, err)}
		}
	}
	return nil
}

func (arc *ApiResourceCreate) sqlSave(ctx context.Context) (*ApiResource, error) {
	if err := arc.check(); err != nil {
		return nil, err
	}
	_node, _spec := arc.createSpec()
	if err := sqlgraph.CreateNode(ctx, arc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	arc.mutation.id = &_node.ID
	arc.mutation.done = true
	return _node, nil
}

func (arc *ApiResourceCreate) createSpec() (*ApiResource, *sqlgraph.CreateSpec) {
	var (
		_node = &ApiResource{config: arc.config}
		_spec = sqlgraph.NewCreateSpec(apiresource.Table, sqlgraph.NewFieldSpec(apiresource.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = arc.conflict
	if id, ok := arc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := arc.mutation.CreateTime(); ok {
		_spec.SetField(apiresource.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := arc.mutation.UpdateTime(); ok {
		_spec.SetField(apiresource.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := arc.mutation.DeleteTime(); ok {
		_spec.SetField(apiresource.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := arc.mutation.CreateBy(); ok {
		_spec.SetField(apiresource.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := arc.mutation.UpdateBy(); ok {
		_spec.SetField(apiresource.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := arc.mutation.Description(); ok {
		_spec.SetField(apiresource.FieldDescription, field.TypeString, value)
		_node.Description = &value
	}
	if value, ok := arc.mutation.Module(); ok {
		_spec.SetField(apiresource.FieldModule, field.TypeString, value)
		_node.Module = &value
	}
	if value, ok := arc.mutation.ModuleDescription(); ok {
		_spec.SetField(apiresource.FieldModuleDescription, field.TypeString, value)
		_node.ModuleDescription = &value
	}
	if value, ok := arc.mutation.Operation(); ok {
		_spec.SetField(apiresource.FieldOperation, field.TypeString, value)
		_node.Operation = &value
	}
	if value, ok := arc.mutation.Path(); ok {
		_spec.SetField(apiresource.FieldPath, field.TypeString, value)
		_node.Path = &value
	}
	if value, ok := arc.mutation.Method(); ok {
		_spec.SetField(apiresource.FieldMethod, field.TypeString, value)
		_node.Method = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ApiResource.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ApiResourceUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (arc *ApiResourceCreate) OnConflict(opts ...sql.ConflictOption) *ApiResourceUpsertOne {
	arc.conflict = opts
	return &ApiResourceUpsertOne{
		create: arc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ApiResource.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (arc *ApiResourceCreate) OnConflictColumns(columns ...string) *ApiResourceUpsertOne {
	arc.conflict = append(arc.conflict, sql.ConflictColumns(columns...))
	return &ApiResourceUpsertOne{
		create: arc,
	}
}

type (
	// ApiResourceUpsertOne is the builder for "upsert"-ing
	//  one ApiResource node.
	ApiResourceUpsertOne struct {
		create *ApiResourceCreate
	}

	// ApiResourceUpsert is the "OnConflict" setter.
	ApiResourceUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *ApiResourceUpsert) SetUpdateTime(v time.Time) *ApiResourceUpsert {
	u.Set(apiresource.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateUpdateTime() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *ApiResourceUpsert) ClearUpdateTime() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *ApiResourceUpsert) SetDeleteTime(v time.Time) *ApiResourceUpsert {
	u.Set(apiresource.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateDeleteTime() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *ApiResourceUpsert) ClearDeleteTime() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *ApiResourceUpsert) SetCreateBy(v uint32) *ApiResourceUpsert {
	u.Set(apiresource.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateCreateBy() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *ApiResourceUpsert) AddCreateBy(v uint32) *ApiResourceUpsert {
	u.Add(apiresource.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *ApiResourceUpsert) ClearCreateBy() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *ApiResourceUpsert) SetUpdateBy(v uint32) *ApiResourceUpsert {
	u.Set(apiresource.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateUpdateBy() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *ApiResourceUpsert) AddUpdateBy(v uint32) *ApiResourceUpsert {
	u.Add(apiresource.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *ApiResourceUpsert) ClearUpdateBy() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldUpdateBy)
	return u
}

// SetDescription sets the "description" field.
func (u *ApiResourceUpsert) SetDescription(v string) *ApiResourceUpsert {
	u.Set(apiresource.FieldDescription, v)
	return u
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateDescription() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldDescription)
	return u
}

// ClearDescription clears the value of the "description" field.
func (u *ApiResourceUpsert) ClearDescription() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldDescription)
	return u
}

// SetModule sets the "module" field.
func (u *ApiResourceUpsert) SetModule(v string) *ApiResourceUpsert {
	u.Set(apiresource.FieldModule, v)
	return u
}

// UpdateModule sets the "module" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateModule() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldModule)
	return u
}

// ClearModule clears the value of the "module" field.
func (u *ApiResourceUpsert) ClearModule() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldModule)
	return u
}

// SetModuleDescription sets the "module_description" field.
func (u *ApiResourceUpsert) SetModuleDescription(v string) *ApiResourceUpsert {
	u.Set(apiresource.FieldModuleDescription, v)
	return u
}

// UpdateModuleDescription sets the "module_description" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateModuleDescription() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldModuleDescription)
	return u
}

// ClearModuleDescription clears the value of the "module_description" field.
func (u *ApiResourceUpsert) ClearModuleDescription() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldModuleDescription)
	return u
}

// SetOperation sets the "operation" field.
func (u *ApiResourceUpsert) SetOperation(v string) *ApiResourceUpsert {
	u.Set(apiresource.FieldOperation, v)
	return u
}

// UpdateOperation sets the "operation" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateOperation() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldOperation)
	return u
}

// ClearOperation clears the value of the "operation" field.
func (u *ApiResourceUpsert) ClearOperation() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldOperation)
	return u
}

// SetPath sets the "path" field.
func (u *ApiResourceUpsert) SetPath(v string) *ApiResourceUpsert {
	u.Set(apiresource.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdatePath() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldPath)
	return u
}

// ClearPath clears the value of the "path" field.
func (u *ApiResourceUpsert) ClearPath() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldPath)
	return u
}

// SetMethod sets the "method" field.
func (u *ApiResourceUpsert) SetMethod(v string) *ApiResourceUpsert {
	u.Set(apiresource.FieldMethod, v)
	return u
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *ApiResourceUpsert) UpdateMethod() *ApiResourceUpsert {
	u.SetExcluded(apiresource.FieldMethod)
	return u
}

// ClearMethod clears the value of the "method" field.
func (u *ApiResourceUpsert) ClearMethod() *ApiResourceUpsert {
	u.SetNull(apiresource.FieldMethod)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ApiResource.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(apiresource.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ApiResourceUpsertOne) UpdateNewValues() *ApiResourceUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(apiresource.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(apiresource.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ApiResource.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ApiResourceUpsertOne) Ignore() *ApiResourceUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ApiResourceUpsertOne) DoNothing() *ApiResourceUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ApiResourceCreate.OnConflict
// documentation for more info.
func (u *ApiResourceUpsertOne) Update(set func(*ApiResourceUpsert)) *ApiResourceUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ApiResourceUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *ApiResourceUpsertOne) SetUpdateTime(v time.Time) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateUpdateTime() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *ApiResourceUpsertOne) ClearUpdateTime() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *ApiResourceUpsertOne) SetDeleteTime(v time.Time) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateDeleteTime() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *ApiResourceUpsertOne) ClearDeleteTime() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *ApiResourceUpsertOne) SetCreateBy(v uint32) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *ApiResourceUpsertOne) AddCreateBy(v uint32) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateCreateBy() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *ApiResourceUpsertOne) ClearCreateBy() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *ApiResourceUpsertOne) SetUpdateBy(v uint32) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *ApiResourceUpsertOne) AddUpdateBy(v uint32) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateUpdateBy() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *ApiResourceUpsertOne) ClearUpdateBy() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearUpdateBy()
	})
}

// SetDescription sets the "description" field.
func (u *ApiResourceUpsertOne) SetDescription(v string) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateDescription() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateDescription()
	})
}

// ClearDescription clears the value of the "description" field.
func (u *ApiResourceUpsertOne) ClearDescription() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearDescription()
	})
}

// SetModule sets the "module" field.
func (u *ApiResourceUpsertOne) SetModule(v string) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetModule(v)
	})
}

// UpdateModule sets the "module" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateModule() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateModule()
	})
}

// ClearModule clears the value of the "module" field.
func (u *ApiResourceUpsertOne) ClearModule() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearModule()
	})
}

// SetModuleDescription sets the "module_description" field.
func (u *ApiResourceUpsertOne) SetModuleDescription(v string) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetModuleDescription(v)
	})
}

// UpdateModuleDescription sets the "module_description" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateModuleDescription() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateModuleDescription()
	})
}

// ClearModuleDescription clears the value of the "module_description" field.
func (u *ApiResourceUpsertOne) ClearModuleDescription() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearModuleDescription()
	})
}

// SetOperation sets the "operation" field.
func (u *ApiResourceUpsertOne) SetOperation(v string) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetOperation(v)
	})
}

// UpdateOperation sets the "operation" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateOperation() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateOperation()
	})
}

// ClearOperation clears the value of the "operation" field.
func (u *ApiResourceUpsertOne) ClearOperation() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearOperation()
	})
}

// SetPath sets the "path" field.
func (u *ApiResourceUpsertOne) SetPath(v string) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdatePath() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdatePath()
	})
}

// ClearPath clears the value of the "path" field.
func (u *ApiResourceUpsertOne) ClearPath() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearPath()
	})
}

// SetMethod sets the "method" field.
func (u *ApiResourceUpsertOne) SetMethod(v string) *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *ApiResourceUpsertOne) UpdateMethod() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateMethod()
	})
}

// ClearMethod clears the value of the "method" field.
func (u *ApiResourceUpsertOne) ClearMethod() *ApiResourceUpsertOne {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearMethod()
	})
}

// Exec executes the query.
func (u *ApiResourceUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ApiResourceCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ApiResourceUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ApiResourceUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ApiResourceUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ApiResourceCreateBulk is the builder for creating many ApiResource entities in bulk.
type ApiResourceCreateBulk struct {
	config
	err      error
	builders []*ApiResourceCreate
	conflict []sql.ConflictOption
}

// Save creates the ApiResource entities in the database.
func (arcb *ApiResourceCreateBulk) Save(ctx context.Context) ([]*ApiResource, error) {
	if arcb.err != nil {
		return nil, arcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(arcb.builders))
	nodes := make([]*ApiResource, len(arcb.builders))
	mutators := make([]Mutator, len(arcb.builders))
	for i := range arcb.builders {
		func(i int, root context.Context) {
			builder := arcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ApiResourceMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, arcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = arcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, arcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, arcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (arcb *ApiResourceCreateBulk) SaveX(ctx context.Context) []*ApiResource {
	v, err := arcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (arcb *ApiResourceCreateBulk) Exec(ctx context.Context) error {
	_, err := arcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (arcb *ApiResourceCreateBulk) ExecX(ctx context.Context) {
	if err := arcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ApiResource.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ApiResourceUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (arcb *ApiResourceCreateBulk) OnConflict(opts ...sql.ConflictOption) *ApiResourceUpsertBulk {
	arcb.conflict = opts
	return &ApiResourceUpsertBulk{
		create: arcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ApiResource.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (arcb *ApiResourceCreateBulk) OnConflictColumns(columns ...string) *ApiResourceUpsertBulk {
	arcb.conflict = append(arcb.conflict, sql.ConflictColumns(columns...))
	return &ApiResourceUpsertBulk{
		create: arcb,
	}
}

// ApiResourceUpsertBulk is the builder for "upsert"-ing
// a bulk of ApiResource nodes.
type ApiResourceUpsertBulk struct {
	create *ApiResourceCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ApiResource.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(apiresource.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ApiResourceUpsertBulk) UpdateNewValues() *ApiResourceUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(apiresource.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(apiresource.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ApiResource.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ApiResourceUpsertBulk) Ignore() *ApiResourceUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ApiResourceUpsertBulk) DoNothing() *ApiResourceUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ApiResourceCreateBulk.OnConflict
// documentation for more info.
func (u *ApiResourceUpsertBulk) Update(set func(*ApiResourceUpsert)) *ApiResourceUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ApiResourceUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *ApiResourceUpsertBulk) SetUpdateTime(v time.Time) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateUpdateTime() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *ApiResourceUpsertBulk) ClearUpdateTime() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *ApiResourceUpsertBulk) SetDeleteTime(v time.Time) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateDeleteTime() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *ApiResourceUpsertBulk) ClearDeleteTime() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *ApiResourceUpsertBulk) SetCreateBy(v uint32) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *ApiResourceUpsertBulk) AddCreateBy(v uint32) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateCreateBy() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *ApiResourceUpsertBulk) ClearCreateBy() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *ApiResourceUpsertBulk) SetUpdateBy(v uint32) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *ApiResourceUpsertBulk) AddUpdateBy(v uint32) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateUpdateBy() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *ApiResourceUpsertBulk) ClearUpdateBy() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearUpdateBy()
	})
}

// SetDescription sets the "description" field.
func (u *ApiResourceUpsertBulk) SetDescription(v string) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateDescription() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateDescription()
	})
}

// ClearDescription clears the value of the "description" field.
func (u *ApiResourceUpsertBulk) ClearDescription() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearDescription()
	})
}

// SetModule sets the "module" field.
func (u *ApiResourceUpsertBulk) SetModule(v string) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetModule(v)
	})
}

// UpdateModule sets the "module" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateModule() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateModule()
	})
}

// ClearModule clears the value of the "module" field.
func (u *ApiResourceUpsertBulk) ClearModule() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearModule()
	})
}

// SetModuleDescription sets the "module_description" field.
func (u *ApiResourceUpsertBulk) SetModuleDescription(v string) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetModuleDescription(v)
	})
}

// UpdateModuleDescription sets the "module_description" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateModuleDescription() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateModuleDescription()
	})
}

// ClearModuleDescription clears the value of the "module_description" field.
func (u *ApiResourceUpsertBulk) ClearModuleDescription() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearModuleDescription()
	})
}

// SetOperation sets the "operation" field.
func (u *ApiResourceUpsertBulk) SetOperation(v string) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetOperation(v)
	})
}

// UpdateOperation sets the "operation" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateOperation() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateOperation()
	})
}

// ClearOperation clears the value of the "operation" field.
func (u *ApiResourceUpsertBulk) ClearOperation() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearOperation()
	})
}

// SetPath sets the "path" field.
func (u *ApiResourceUpsertBulk) SetPath(v string) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdatePath() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdatePath()
	})
}

// ClearPath clears the value of the "path" field.
func (u *ApiResourceUpsertBulk) ClearPath() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearPath()
	})
}

// SetMethod sets the "method" field.
func (u *ApiResourceUpsertBulk) SetMethod(v string) *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *ApiResourceUpsertBulk) UpdateMethod() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.UpdateMethod()
	})
}

// ClearMethod clears the value of the "method" field.
func (u *ApiResourceUpsertBulk) ClearMethod() *ApiResourceUpsertBulk {
	return u.Update(func(s *ApiResourceUpsert) {
		s.ClearMethod()
	})
}

// Exec executes the query.
func (u *ApiResourceUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ApiResourceCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ApiResourceCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ApiResourceUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
