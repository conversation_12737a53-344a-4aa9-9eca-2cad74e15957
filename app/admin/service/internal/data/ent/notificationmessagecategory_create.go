// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagecategory"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageCategoryCreate is the builder for creating a NotificationMessageCategory entity.
type NotificationMessageCategoryCreate struct {
	config
	mutation *NotificationMessageCategoryMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (nmcc *NotificationMessageCategoryCreate) SetCreateTime(t time.Time) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetCreateTime(t)
	return nmcc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableCreateTime(t *time.Time) *NotificationMessageCategoryCreate {
	if t != nil {
		nmcc.SetCreateTime(*t)
	}
	return nmcc
}

// SetUpdateTime sets the "update_time" field.
func (nmcc *NotificationMessageCategoryCreate) SetUpdateTime(t time.Time) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetUpdateTime(t)
	return nmcc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableUpdateTime(t *time.Time) *NotificationMessageCategoryCreate {
	if t != nil {
		nmcc.SetUpdateTime(*t)
	}
	return nmcc
}

// SetDeleteTime sets the "delete_time" field.
func (nmcc *NotificationMessageCategoryCreate) SetDeleteTime(t time.Time) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetDeleteTime(t)
	return nmcc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableDeleteTime(t *time.Time) *NotificationMessageCategoryCreate {
	if t != nil {
		nmcc.SetDeleteTime(*t)
	}
	return nmcc
}

// SetCreateBy sets the "create_by" field.
func (nmcc *NotificationMessageCategoryCreate) SetCreateBy(u uint32) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetCreateBy(u)
	return nmcc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableCreateBy(u *uint32) *NotificationMessageCategoryCreate {
	if u != nil {
		nmcc.SetCreateBy(*u)
	}
	return nmcc
}

// SetUpdateBy sets the "update_by" field.
func (nmcc *NotificationMessageCategoryCreate) SetUpdateBy(u uint32) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetUpdateBy(u)
	return nmcc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableUpdateBy(u *uint32) *NotificationMessageCategoryCreate {
	if u != nil {
		nmcc.SetUpdateBy(*u)
	}
	return nmcc
}

// SetRemark sets the "remark" field.
func (nmcc *NotificationMessageCategoryCreate) SetRemark(s string) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetRemark(s)
	return nmcc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableRemark(s *string) *NotificationMessageCategoryCreate {
	if s != nil {
		nmcc.SetRemark(*s)
	}
	return nmcc
}

// SetTenantID sets the "tenant_id" field.
func (nmcc *NotificationMessageCategoryCreate) SetTenantID(u uint32) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetTenantID(u)
	return nmcc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableTenantID(u *uint32) *NotificationMessageCategoryCreate {
	if u != nil {
		nmcc.SetTenantID(*u)
	}
	return nmcc
}

// SetName sets the "name" field.
func (nmcc *NotificationMessageCategoryCreate) SetName(s string) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetName(s)
	return nmcc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableName(s *string) *NotificationMessageCategoryCreate {
	if s != nil {
		nmcc.SetName(*s)
	}
	return nmcc
}

// SetCode sets the "code" field.
func (nmcc *NotificationMessageCategoryCreate) SetCode(s string) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetCode(s)
	return nmcc
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableCode(s *string) *NotificationMessageCategoryCreate {
	if s != nil {
		nmcc.SetCode(*s)
	}
	return nmcc
}

// SetSortID sets the "sort_id" field.
func (nmcc *NotificationMessageCategoryCreate) SetSortID(i int32) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetSortID(i)
	return nmcc
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableSortID(i *int32) *NotificationMessageCategoryCreate {
	if i != nil {
		nmcc.SetSortID(*i)
	}
	return nmcc
}

// SetEnable sets the "enable" field.
func (nmcc *NotificationMessageCategoryCreate) SetEnable(b bool) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetEnable(b)
	return nmcc
}

// SetNillableEnable sets the "enable" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableEnable(b *bool) *NotificationMessageCategoryCreate {
	if b != nil {
		nmcc.SetEnable(*b)
	}
	return nmcc
}

// SetParentID sets the "parent_id" field.
func (nmcc *NotificationMessageCategoryCreate) SetParentID(u uint32) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetParentID(u)
	return nmcc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (nmcc *NotificationMessageCategoryCreate) SetNillableParentID(u *uint32) *NotificationMessageCategoryCreate {
	if u != nil {
		nmcc.SetParentID(*u)
	}
	return nmcc
}

// SetID sets the "id" field.
func (nmcc *NotificationMessageCategoryCreate) SetID(u uint32) *NotificationMessageCategoryCreate {
	nmcc.mutation.SetID(u)
	return nmcc
}

// SetParent sets the "parent" edge to the NotificationMessageCategory entity.
func (nmcc *NotificationMessageCategoryCreate) SetParent(n *NotificationMessageCategory) *NotificationMessageCategoryCreate {
	return nmcc.SetParentID(n.ID)
}

// AddChildIDs adds the "children" edge to the NotificationMessageCategory entity by IDs.
func (nmcc *NotificationMessageCategoryCreate) AddChildIDs(ids ...uint32) *NotificationMessageCategoryCreate {
	nmcc.mutation.AddChildIDs(ids...)
	return nmcc
}

// AddChildren adds the "children" edges to the NotificationMessageCategory entity.
func (nmcc *NotificationMessageCategoryCreate) AddChildren(n ...*NotificationMessageCategory) *NotificationMessageCategoryCreate {
	ids := make([]uint32, len(n))
	for i := range n {
		ids[i] = n[i].ID
	}
	return nmcc.AddChildIDs(ids...)
}

// Mutation returns the NotificationMessageCategoryMutation object of the builder.
func (nmcc *NotificationMessageCategoryCreate) Mutation() *NotificationMessageCategoryMutation {
	return nmcc.mutation
}

// Save creates the NotificationMessageCategory in the database.
func (nmcc *NotificationMessageCategoryCreate) Save(ctx context.Context) (*NotificationMessageCategory, error) {
	nmcc.defaults()
	return withHooks(ctx, nmcc.sqlSave, nmcc.mutation, nmcc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (nmcc *NotificationMessageCategoryCreate) SaveX(ctx context.Context) *NotificationMessageCategory {
	v, err := nmcc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nmcc *NotificationMessageCategoryCreate) Exec(ctx context.Context) error {
	_, err := nmcc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmcc *NotificationMessageCategoryCreate) ExecX(ctx context.Context) {
	if err := nmcc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nmcc *NotificationMessageCategoryCreate) defaults() {
	if _, ok := nmcc.mutation.Remark(); !ok {
		v := notificationmessagecategory.DefaultRemark
		nmcc.mutation.SetRemark(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmcc *NotificationMessageCategoryCreate) check() error {
	if v, ok := nmcc.mutation.TenantID(); ok {
		if err := notificationmessagecategory.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageCategory.tenant_id": %w`, err)}
		}
	}
	if v, ok := nmcc.mutation.ID(); ok {
		if err := notificationmessagecategory.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageCategory.id": %w`, err)}
		}
	}
	return nil
}

func (nmcc *NotificationMessageCategoryCreate) sqlSave(ctx context.Context) (*NotificationMessageCategory, error) {
	if err := nmcc.check(); err != nil {
		return nil, err
	}
	_node, _spec := nmcc.createSpec()
	if err := sqlgraph.CreateNode(ctx, nmcc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	nmcc.mutation.id = &_node.ID
	nmcc.mutation.done = true
	return _node, nil
}

func (nmcc *NotificationMessageCategoryCreate) createSpec() (*NotificationMessageCategory, *sqlgraph.CreateSpec) {
	var (
		_node = &NotificationMessageCategory{config: nmcc.config}
		_spec = sqlgraph.NewCreateSpec(notificationmessagecategory.Table, sqlgraph.NewFieldSpec(notificationmessagecategory.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = nmcc.conflict
	if id, ok := nmcc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := nmcc.mutation.CreateTime(); ok {
		_spec.SetField(notificationmessagecategory.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := nmcc.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessagecategory.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := nmcc.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessagecategory.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := nmcc.mutation.CreateBy(); ok {
		_spec.SetField(notificationmessagecategory.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := nmcc.mutation.UpdateBy(); ok {
		_spec.SetField(notificationmessagecategory.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := nmcc.mutation.Remark(); ok {
		_spec.SetField(notificationmessagecategory.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := nmcc.mutation.TenantID(); ok {
		_spec.SetField(notificationmessagecategory.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := nmcc.mutation.Name(); ok {
		_spec.SetField(notificationmessagecategory.FieldName, field.TypeString, value)
		_node.Name = &value
	}
	if value, ok := nmcc.mutation.Code(); ok {
		_spec.SetField(notificationmessagecategory.FieldCode, field.TypeString, value)
		_node.Code = &value
	}
	if value, ok := nmcc.mutation.SortID(); ok {
		_spec.SetField(notificationmessagecategory.FieldSortID, field.TypeInt32, value)
		_node.SortID = &value
	}
	if value, ok := nmcc.mutation.Enable(); ok {
		_spec.SetField(notificationmessagecategory.FieldEnable, field.TypeBool, value)
		_node.Enable = &value
	}
	if nodes := nmcc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   notificationmessagecategory.ParentTable,
			Columns: []string{notificationmessagecategory.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notificationmessagecategory.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := nmcc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   notificationmessagecategory.ChildrenTable,
			Columns: []string{notificationmessagecategory.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(notificationmessagecategory.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.NotificationMessageCategory.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.NotificationMessageCategoryUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (nmcc *NotificationMessageCategoryCreate) OnConflict(opts ...sql.ConflictOption) *NotificationMessageCategoryUpsertOne {
	nmcc.conflict = opts
	return &NotificationMessageCategoryUpsertOne{
		create: nmcc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.NotificationMessageCategory.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (nmcc *NotificationMessageCategoryCreate) OnConflictColumns(columns ...string) *NotificationMessageCategoryUpsertOne {
	nmcc.conflict = append(nmcc.conflict, sql.ConflictColumns(columns...))
	return &NotificationMessageCategoryUpsertOne{
		create: nmcc,
	}
}

type (
	// NotificationMessageCategoryUpsertOne is the builder for "upsert"-ing
	//  one NotificationMessageCategory node.
	NotificationMessageCategoryUpsertOne struct {
		create *NotificationMessageCategoryCreate
	}

	// NotificationMessageCategoryUpsert is the "OnConflict" setter.
	NotificationMessageCategoryUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageCategoryUpsert) SetUpdateTime(v time.Time) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateUpdateTime() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageCategoryUpsert) ClearUpdateTime() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageCategoryUpsert) SetDeleteTime(v time.Time) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateDeleteTime() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageCategoryUpsert) ClearDeleteTime() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *NotificationMessageCategoryUpsert) SetCreateBy(v uint32) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateCreateBy() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *NotificationMessageCategoryUpsert) AddCreateBy(v uint32) *NotificationMessageCategoryUpsert {
	u.Add(notificationmessagecategory.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *NotificationMessageCategoryUpsert) ClearCreateBy() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *NotificationMessageCategoryUpsert) SetUpdateBy(v uint32) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateUpdateBy() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *NotificationMessageCategoryUpsert) AddUpdateBy(v uint32) *NotificationMessageCategoryUpsert {
	u.Add(notificationmessagecategory.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *NotificationMessageCategoryUpsert) ClearUpdateBy() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *NotificationMessageCategoryUpsert) SetRemark(v string) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateRemark() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *NotificationMessageCategoryUpsert) ClearRemark() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldRemark)
	return u
}

// SetName sets the "name" field.
func (u *NotificationMessageCategoryUpsert) SetName(v string) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateName() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *NotificationMessageCategoryUpsert) ClearName() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *NotificationMessageCategoryUpsert) SetCode(v string) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateCode() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldCode)
	return u
}

// ClearCode clears the value of the "code" field.
func (u *NotificationMessageCategoryUpsert) ClearCode() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldCode)
	return u
}

// SetSortID sets the "sort_id" field.
func (u *NotificationMessageCategoryUpsert) SetSortID(v int32) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldSortID, v)
	return u
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateSortID() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldSortID)
	return u
}

// AddSortID adds v to the "sort_id" field.
func (u *NotificationMessageCategoryUpsert) AddSortID(v int32) *NotificationMessageCategoryUpsert {
	u.Add(notificationmessagecategory.FieldSortID, v)
	return u
}

// ClearSortID clears the value of the "sort_id" field.
func (u *NotificationMessageCategoryUpsert) ClearSortID() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldSortID)
	return u
}

// SetEnable sets the "enable" field.
func (u *NotificationMessageCategoryUpsert) SetEnable(v bool) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldEnable, v)
	return u
}

// UpdateEnable sets the "enable" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateEnable() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldEnable)
	return u
}

// ClearEnable clears the value of the "enable" field.
func (u *NotificationMessageCategoryUpsert) ClearEnable() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldEnable)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *NotificationMessageCategoryUpsert) SetParentID(v uint32) *NotificationMessageCategoryUpsert {
	u.Set(notificationmessagecategory.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsert) UpdateParentID() *NotificationMessageCategoryUpsert {
	u.SetExcluded(notificationmessagecategory.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *NotificationMessageCategoryUpsert) ClearParentID() *NotificationMessageCategoryUpsert {
	u.SetNull(notificationmessagecategory.FieldParentID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.NotificationMessageCategory.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(notificationmessagecategory.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *NotificationMessageCategoryUpsertOne) UpdateNewValues() *NotificationMessageCategoryUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(notificationmessagecategory.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(notificationmessagecategory.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(notificationmessagecategory.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.NotificationMessageCategory.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *NotificationMessageCategoryUpsertOne) Ignore() *NotificationMessageCategoryUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *NotificationMessageCategoryUpsertOne) DoNothing() *NotificationMessageCategoryUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the NotificationMessageCategoryCreate.OnConflict
// documentation for more info.
func (u *NotificationMessageCategoryUpsertOne) Update(set func(*NotificationMessageCategoryUpsert)) *NotificationMessageCategoryUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&NotificationMessageCategoryUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageCategoryUpsertOne) SetUpdateTime(v time.Time) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateUpdateTime() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageCategoryUpsertOne) ClearUpdateTime() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageCategoryUpsertOne) SetDeleteTime(v time.Time) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateDeleteTime() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageCategoryUpsertOne) ClearDeleteTime() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *NotificationMessageCategoryUpsertOne) SetCreateBy(v uint32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *NotificationMessageCategoryUpsertOne) AddCreateBy(v uint32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateCreateBy() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *NotificationMessageCategoryUpsertOne) ClearCreateBy() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *NotificationMessageCategoryUpsertOne) SetUpdateBy(v uint32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *NotificationMessageCategoryUpsertOne) AddUpdateBy(v uint32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateUpdateBy() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *NotificationMessageCategoryUpsertOne) ClearUpdateBy() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *NotificationMessageCategoryUpsertOne) SetRemark(v string) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateRemark() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *NotificationMessageCategoryUpsertOne) ClearRemark() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *NotificationMessageCategoryUpsertOne) SetName(v string) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateName() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *NotificationMessageCategoryUpsertOne) ClearName() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *NotificationMessageCategoryUpsertOne) SetCode(v string) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateCode() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateCode()
	})
}

// ClearCode clears the value of the "code" field.
func (u *NotificationMessageCategoryUpsertOne) ClearCode() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearCode()
	})
}

// SetSortID sets the "sort_id" field.
func (u *NotificationMessageCategoryUpsertOne) SetSortID(v int32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *NotificationMessageCategoryUpsertOne) AddSortID(v int32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateSortID() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *NotificationMessageCategoryUpsertOne) ClearSortID() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearSortID()
	})
}

// SetEnable sets the "enable" field.
func (u *NotificationMessageCategoryUpsertOne) SetEnable(v bool) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetEnable(v)
	})
}

// UpdateEnable sets the "enable" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateEnable() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateEnable()
	})
}

// ClearEnable clears the value of the "enable" field.
func (u *NotificationMessageCategoryUpsertOne) ClearEnable() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearEnable()
	})
}

// SetParentID sets the "parent_id" field.
func (u *NotificationMessageCategoryUpsertOne) SetParentID(v uint32) *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertOne) UpdateParentID() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *NotificationMessageCategoryUpsertOne) ClearParentID() *NotificationMessageCategoryUpsertOne {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearParentID()
	})
}

// Exec executes the query.
func (u *NotificationMessageCategoryUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for NotificationMessageCategoryCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *NotificationMessageCategoryUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *NotificationMessageCategoryUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *NotificationMessageCategoryUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// NotificationMessageCategoryCreateBulk is the builder for creating many NotificationMessageCategory entities in bulk.
type NotificationMessageCategoryCreateBulk struct {
	config
	err      error
	builders []*NotificationMessageCategoryCreate
	conflict []sql.ConflictOption
}

// Save creates the NotificationMessageCategory entities in the database.
func (nmccb *NotificationMessageCategoryCreateBulk) Save(ctx context.Context) ([]*NotificationMessageCategory, error) {
	if nmccb.err != nil {
		return nil, nmccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(nmccb.builders))
	nodes := make([]*NotificationMessageCategory, len(nmccb.builders))
	mutators := make([]Mutator, len(nmccb.builders))
	for i := range nmccb.builders {
		func(i int, root context.Context) {
			builder := nmccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotificationMessageCategoryMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, nmccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = nmccb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, nmccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, nmccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (nmccb *NotificationMessageCategoryCreateBulk) SaveX(ctx context.Context) []*NotificationMessageCategory {
	v, err := nmccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nmccb *NotificationMessageCategoryCreateBulk) Exec(ctx context.Context) error {
	_, err := nmccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmccb *NotificationMessageCategoryCreateBulk) ExecX(ctx context.Context) {
	if err := nmccb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.NotificationMessageCategory.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.NotificationMessageCategoryUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (nmccb *NotificationMessageCategoryCreateBulk) OnConflict(opts ...sql.ConflictOption) *NotificationMessageCategoryUpsertBulk {
	nmccb.conflict = opts
	return &NotificationMessageCategoryUpsertBulk{
		create: nmccb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.NotificationMessageCategory.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (nmccb *NotificationMessageCategoryCreateBulk) OnConflictColumns(columns ...string) *NotificationMessageCategoryUpsertBulk {
	nmccb.conflict = append(nmccb.conflict, sql.ConflictColumns(columns...))
	return &NotificationMessageCategoryUpsertBulk{
		create: nmccb,
	}
}

// NotificationMessageCategoryUpsertBulk is the builder for "upsert"-ing
// a bulk of NotificationMessageCategory nodes.
type NotificationMessageCategoryUpsertBulk struct {
	create *NotificationMessageCategoryCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.NotificationMessageCategory.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(notificationmessagecategory.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *NotificationMessageCategoryUpsertBulk) UpdateNewValues() *NotificationMessageCategoryUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(notificationmessagecategory.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(notificationmessagecategory.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(notificationmessagecategory.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.NotificationMessageCategory.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *NotificationMessageCategoryUpsertBulk) Ignore() *NotificationMessageCategoryUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *NotificationMessageCategoryUpsertBulk) DoNothing() *NotificationMessageCategoryUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the NotificationMessageCategoryCreateBulk.OnConflict
// documentation for more info.
func (u *NotificationMessageCategoryUpsertBulk) Update(set func(*NotificationMessageCategoryUpsert)) *NotificationMessageCategoryUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&NotificationMessageCategoryUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageCategoryUpsertBulk) SetUpdateTime(v time.Time) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateUpdateTime() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearUpdateTime() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageCategoryUpsertBulk) SetDeleteTime(v time.Time) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateDeleteTime() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearDeleteTime() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *NotificationMessageCategoryUpsertBulk) SetCreateBy(v uint32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *NotificationMessageCategoryUpsertBulk) AddCreateBy(v uint32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateCreateBy() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearCreateBy() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *NotificationMessageCategoryUpsertBulk) SetUpdateBy(v uint32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *NotificationMessageCategoryUpsertBulk) AddUpdateBy(v uint32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateUpdateBy() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearUpdateBy() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *NotificationMessageCategoryUpsertBulk) SetRemark(v string) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateRemark() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearRemark() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *NotificationMessageCategoryUpsertBulk) SetName(v string) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateName() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearName() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *NotificationMessageCategoryUpsertBulk) SetCode(v string) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateCode() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateCode()
	})
}

// ClearCode clears the value of the "code" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearCode() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearCode()
	})
}

// SetSortID sets the "sort_id" field.
func (u *NotificationMessageCategoryUpsertBulk) SetSortID(v int32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *NotificationMessageCategoryUpsertBulk) AddSortID(v int32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateSortID() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearSortID() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearSortID()
	})
}

// SetEnable sets the "enable" field.
func (u *NotificationMessageCategoryUpsertBulk) SetEnable(v bool) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetEnable(v)
	})
}

// UpdateEnable sets the "enable" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateEnable() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateEnable()
	})
}

// ClearEnable clears the value of the "enable" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearEnable() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearEnable()
	})
}

// SetParentID sets the "parent_id" field.
func (u *NotificationMessageCategoryUpsertBulk) SetParentID(v uint32) *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *NotificationMessageCategoryUpsertBulk) UpdateParentID() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *NotificationMessageCategoryUpsertBulk) ClearParentID() *NotificationMessageCategoryUpsertBulk {
	return u.Update(func(s *NotificationMessageCategoryUpsert) {
		s.ClearParentID()
	})
}

// Exec executes the query.
func (u *NotificationMessageCategoryUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the NotificationMessageCategoryCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for NotificationMessageCategoryCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *NotificationMessageCategoryUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
