// Code generated by ent, DO NOT EDIT.

package apiresource

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldDeleteTime, v))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldCreateBy, v))
}

// UpdateBy applies equality check predicate on the "update_by" field. It's identical to UpdateByEQ.
func UpdateBy(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldUpdateBy, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldDescription, v))
}

// Module applies equality check predicate on the "module" field. It's identical to ModuleEQ.
func Module(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldModule, v))
}

// ModuleDescription applies equality check predicate on the "module_description" field. It's identical to ModuleDescriptionEQ.
func ModuleDescription(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldModuleDescription, v))
}

// Operation applies equality check predicate on the "operation" field. It's identical to OperationEQ.
func Operation(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldOperation, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldPath, v))
}

// Method applies equality check predicate on the "method" field. It's identical to MethodEQ.
func Method(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldMethod, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldDeleteTime))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldCreateBy))
}

// UpdateByEQ applies the EQ predicate on the "update_by" field.
func UpdateByEQ(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldUpdateBy, v))
}

// UpdateByNEQ applies the NEQ predicate on the "update_by" field.
func UpdateByNEQ(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldUpdateBy, v))
}

// UpdateByIn applies the In predicate on the "update_by" field.
func UpdateByIn(vs ...uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldUpdateBy, vs...))
}

// UpdateByNotIn applies the NotIn predicate on the "update_by" field.
func UpdateByNotIn(vs ...uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldUpdateBy, vs...))
}

// UpdateByGT applies the GT predicate on the "update_by" field.
func UpdateByGT(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldUpdateBy, v))
}

// UpdateByGTE applies the GTE predicate on the "update_by" field.
func UpdateByGTE(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldUpdateBy, v))
}

// UpdateByLT applies the LT predicate on the "update_by" field.
func UpdateByLT(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldUpdateBy, v))
}

// UpdateByLTE applies the LTE predicate on the "update_by" field.
func UpdateByLTE(v uint32) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldUpdateBy, v))
}

// UpdateByIsNil applies the IsNil predicate on the "update_by" field.
func UpdateByIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldUpdateBy))
}

// UpdateByNotNil applies the NotNil predicate on the "update_by" field.
func UpdateByNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldUpdateBy))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContainsFold(FieldDescription, v))
}

// ModuleEQ applies the EQ predicate on the "module" field.
func ModuleEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldModule, v))
}

// ModuleNEQ applies the NEQ predicate on the "module" field.
func ModuleNEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldModule, v))
}

// ModuleIn applies the In predicate on the "module" field.
func ModuleIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldModule, vs...))
}

// ModuleNotIn applies the NotIn predicate on the "module" field.
func ModuleNotIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldModule, vs...))
}

// ModuleGT applies the GT predicate on the "module" field.
func ModuleGT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldModule, v))
}

// ModuleGTE applies the GTE predicate on the "module" field.
func ModuleGTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldModule, v))
}

// ModuleLT applies the LT predicate on the "module" field.
func ModuleLT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldModule, v))
}

// ModuleLTE applies the LTE predicate on the "module" field.
func ModuleLTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldModule, v))
}

// ModuleContains applies the Contains predicate on the "module" field.
func ModuleContains(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContains(FieldModule, v))
}

// ModuleHasPrefix applies the HasPrefix predicate on the "module" field.
func ModuleHasPrefix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasPrefix(FieldModule, v))
}

// ModuleHasSuffix applies the HasSuffix predicate on the "module" field.
func ModuleHasSuffix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasSuffix(FieldModule, v))
}

// ModuleIsNil applies the IsNil predicate on the "module" field.
func ModuleIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldModule))
}

// ModuleNotNil applies the NotNil predicate on the "module" field.
func ModuleNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldModule))
}

// ModuleEqualFold applies the EqualFold predicate on the "module" field.
func ModuleEqualFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEqualFold(FieldModule, v))
}

// ModuleContainsFold applies the ContainsFold predicate on the "module" field.
func ModuleContainsFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContainsFold(FieldModule, v))
}

// ModuleDescriptionEQ applies the EQ predicate on the "module_description" field.
func ModuleDescriptionEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldModuleDescription, v))
}

// ModuleDescriptionNEQ applies the NEQ predicate on the "module_description" field.
func ModuleDescriptionNEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldModuleDescription, v))
}

// ModuleDescriptionIn applies the In predicate on the "module_description" field.
func ModuleDescriptionIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldModuleDescription, vs...))
}

// ModuleDescriptionNotIn applies the NotIn predicate on the "module_description" field.
func ModuleDescriptionNotIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldModuleDescription, vs...))
}

// ModuleDescriptionGT applies the GT predicate on the "module_description" field.
func ModuleDescriptionGT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldModuleDescription, v))
}

// ModuleDescriptionGTE applies the GTE predicate on the "module_description" field.
func ModuleDescriptionGTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldModuleDescription, v))
}

// ModuleDescriptionLT applies the LT predicate on the "module_description" field.
func ModuleDescriptionLT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldModuleDescription, v))
}

// ModuleDescriptionLTE applies the LTE predicate on the "module_description" field.
func ModuleDescriptionLTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldModuleDescription, v))
}

// ModuleDescriptionContains applies the Contains predicate on the "module_description" field.
func ModuleDescriptionContains(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContains(FieldModuleDescription, v))
}

// ModuleDescriptionHasPrefix applies the HasPrefix predicate on the "module_description" field.
func ModuleDescriptionHasPrefix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasPrefix(FieldModuleDescription, v))
}

// ModuleDescriptionHasSuffix applies the HasSuffix predicate on the "module_description" field.
func ModuleDescriptionHasSuffix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasSuffix(FieldModuleDescription, v))
}

// ModuleDescriptionIsNil applies the IsNil predicate on the "module_description" field.
func ModuleDescriptionIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldModuleDescription))
}

// ModuleDescriptionNotNil applies the NotNil predicate on the "module_description" field.
func ModuleDescriptionNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldModuleDescription))
}

// ModuleDescriptionEqualFold applies the EqualFold predicate on the "module_description" field.
func ModuleDescriptionEqualFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEqualFold(FieldModuleDescription, v))
}

// ModuleDescriptionContainsFold applies the ContainsFold predicate on the "module_description" field.
func ModuleDescriptionContainsFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContainsFold(FieldModuleDescription, v))
}

// OperationEQ applies the EQ predicate on the "operation" field.
func OperationEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldOperation, v))
}

// OperationNEQ applies the NEQ predicate on the "operation" field.
func OperationNEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldOperation, v))
}

// OperationIn applies the In predicate on the "operation" field.
func OperationIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldOperation, vs...))
}

// OperationNotIn applies the NotIn predicate on the "operation" field.
func OperationNotIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldOperation, vs...))
}

// OperationGT applies the GT predicate on the "operation" field.
func OperationGT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldOperation, v))
}

// OperationGTE applies the GTE predicate on the "operation" field.
func OperationGTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldOperation, v))
}

// OperationLT applies the LT predicate on the "operation" field.
func OperationLT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldOperation, v))
}

// OperationLTE applies the LTE predicate on the "operation" field.
func OperationLTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldOperation, v))
}

// OperationContains applies the Contains predicate on the "operation" field.
func OperationContains(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContains(FieldOperation, v))
}

// OperationHasPrefix applies the HasPrefix predicate on the "operation" field.
func OperationHasPrefix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasPrefix(FieldOperation, v))
}

// OperationHasSuffix applies the HasSuffix predicate on the "operation" field.
func OperationHasSuffix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasSuffix(FieldOperation, v))
}

// OperationIsNil applies the IsNil predicate on the "operation" field.
func OperationIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldOperation))
}

// OperationNotNil applies the NotNil predicate on the "operation" field.
func OperationNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldOperation))
}

// OperationEqualFold applies the EqualFold predicate on the "operation" field.
func OperationEqualFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEqualFold(FieldOperation, v))
}

// OperationContainsFold applies the ContainsFold predicate on the "operation" field.
func OperationContainsFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContainsFold(FieldOperation, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasSuffix(FieldPath, v))
}

// PathIsNil applies the IsNil predicate on the "path" field.
func PathIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldPath))
}

// PathNotNil applies the NotNil predicate on the "path" field.
func PathNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldPath))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContainsFold(FieldPath, v))
}

// MethodEQ applies the EQ predicate on the "method" field.
func MethodEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEQ(FieldMethod, v))
}

// MethodNEQ applies the NEQ predicate on the "method" field.
func MethodNEQ(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNEQ(FieldMethod, v))
}

// MethodIn applies the In predicate on the "method" field.
func MethodIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIn(FieldMethod, vs...))
}

// MethodNotIn applies the NotIn predicate on the "method" field.
func MethodNotIn(vs ...string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotIn(FieldMethod, vs...))
}

// MethodGT applies the GT predicate on the "method" field.
func MethodGT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGT(FieldMethod, v))
}

// MethodGTE applies the GTE predicate on the "method" field.
func MethodGTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldGTE(FieldMethod, v))
}

// MethodLT applies the LT predicate on the "method" field.
func MethodLT(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLT(FieldMethod, v))
}

// MethodLTE applies the LTE predicate on the "method" field.
func MethodLTE(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldLTE(FieldMethod, v))
}

// MethodContains applies the Contains predicate on the "method" field.
func MethodContains(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContains(FieldMethod, v))
}

// MethodHasPrefix applies the HasPrefix predicate on the "method" field.
func MethodHasPrefix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasPrefix(FieldMethod, v))
}

// MethodHasSuffix applies the HasSuffix predicate on the "method" field.
func MethodHasSuffix(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldHasSuffix(FieldMethod, v))
}

// MethodIsNil applies the IsNil predicate on the "method" field.
func MethodIsNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldIsNull(FieldMethod))
}

// MethodNotNil applies the NotNil predicate on the "method" field.
func MethodNotNil() predicate.ApiResource {
	return predicate.ApiResource(sql.FieldNotNull(FieldMethod))
}

// MethodEqualFold applies the EqualFold predicate on the "method" field.
func MethodEqualFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldEqualFold(FieldMethod, v))
}

// MethodContainsFold applies the ContainsFold predicate on the "method" field.
func MethodContainsFold(v string) predicate.ApiResource {
	return predicate.ApiResource(sql.FieldContainsFold(FieldMethod, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ApiResource) predicate.ApiResource {
	return predicate.ApiResource(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ApiResource) predicate.ApiResource {
	return predicate.ApiResource(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ApiResource) predicate.ApiResource {
	return predicate.ApiResource(sql.NotPredicates(p))
}
