// Code generated by ent, DO NOT EDIT.

package apiresource

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the apiresource type in the database.
	Label = "api_resource"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldDeleteTime holds the string denoting the delete_time field in the database.
	FieldDeleteTime = "delete_time"
	// FieldCreateBy holds the string denoting the create_by field in the database.
	FieldCreateBy = "create_by"
	// FieldUpdateBy holds the string denoting the update_by field in the database.
	FieldUpdateBy = "update_by"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldModule holds the string denoting the module field in the database.
	FieldModule = "module"
	// FieldModuleDescription holds the string denoting the module_description field in the database.
	FieldModuleDescription = "module_description"
	// FieldOperation holds the string denoting the operation field in the database.
	FieldOperation = "operation"
	// FieldPath holds the string denoting the path field in the database.
	FieldPath = "path"
	// FieldMethod holds the string denoting the method field in the database.
	FieldMethod = "method"
	// Table holds the table name of the apiresource in the database.
	Table = "sys_api_resources"
)

// Columns holds all SQL columns for apiresource fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldDeleteTime,
	FieldCreateBy,
	FieldUpdateBy,
	FieldDescription,
	FieldModule,
	FieldModuleDescription,
	FieldOperation,
	FieldPath,
	FieldMethod,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(uint32) error
)

// OrderOption defines the ordering options for the ApiResource queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByDeleteTime orders the results by the delete_time field.
func ByDeleteTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeleteTime, opts...).ToFunc()
}

// ByCreateBy orders the results by the create_by field.
func ByCreateBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateBy, opts...).ToFunc()
}

// ByUpdateBy orders the results by the update_by field.
func ByUpdateBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateBy, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByModule orders the results by the module field.
func ByModule(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModule, opts...).ToFunc()
}

// ByModuleDescription orders the results by the module_description field.
func ByModuleDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModuleDescription, opts...).ToFunc()
}

// ByOperation orders the results by the operation field.
func ByOperation(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOperation, opts...).ToFunc()
}

// ByPath orders the results by the path field.
func ByPath(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPath, opts...).ToFunc()
}

// ByMethod orders the results by the method field.
func ByMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMethod, opts...).ToFunc()
}
