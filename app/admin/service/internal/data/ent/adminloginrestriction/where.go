// Code generated by ent, DO NOT EDIT.

package adminloginrestriction

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldDeleteTime, v))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldCreateBy, v))
}

// UpdateBy applies equality check predicate on the "update_by" field. It's identical to UpdateByEQ.
func UpdateBy(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldUpdateBy, v))
}

// TargetID applies equality check predicate on the "target_id" field. It's identical to TargetIDEQ.
func TargetID(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldTargetID, v))
}

// Value applies equality check predicate on the "value" field. It's identical to ValueEQ.
func Value(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldValue, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldReason, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldDeleteTime))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldCreateBy))
}

// UpdateByEQ applies the EQ predicate on the "update_by" field.
func UpdateByEQ(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldUpdateBy, v))
}

// UpdateByNEQ applies the NEQ predicate on the "update_by" field.
func UpdateByNEQ(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldUpdateBy, v))
}

// UpdateByIn applies the In predicate on the "update_by" field.
func UpdateByIn(vs ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldUpdateBy, vs...))
}

// UpdateByNotIn applies the NotIn predicate on the "update_by" field.
func UpdateByNotIn(vs ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldUpdateBy, vs...))
}

// UpdateByGT applies the GT predicate on the "update_by" field.
func UpdateByGT(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldUpdateBy, v))
}

// UpdateByGTE applies the GTE predicate on the "update_by" field.
func UpdateByGTE(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldUpdateBy, v))
}

// UpdateByLT applies the LT predicate on the "update_by" field.
func UpdateByLT(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldUpdateBy, v))
}

// UpdateByLTE applies the LTE predicate on the "update_by" field.
func UpdateByLTE(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldUpdateBy, v))
}

// UpdateByIsNil applies the IsNil predicate on the "update_by" field.
func UpdateByIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldUpdateBy))
}

// UpdateByNotNil applies the NotNil predicate on the "update_by" field.
func UpdateByNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldUpdateBy))
}

// TargetIDEQ applies the EQ predicate on the "target_id" field.
func TargetIDEQ(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldTargetID, v))
}

// TargetIDNEQ applies the NEQ predicate on the "target_id" field.
func TargetIDNEQ(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldTargetID, v))
}

// TargetIDIn applies the In predicate on the "target_id" field.
func TargetIDIn(vs ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldTargetID, vs...))
}

// TargetIDNotIn applies the NotIn predicate on the "target_id" field.
func TargetIDNotIn(vs ...uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldTargetID, vs...))
}

// TargetIDGT applies the GT predicate on the "target_id" field.
func TargetIDGT(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldTargetID, v))
}

// TargetIDGTE applies the GTE predicate on the "target_id" field.
func TargetIDGTE(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldTargetID, v))
}

// TargetIDLT applies the LT predicate on the "target_id" field.
func TargetIDLT(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldTargetID, v))
}

// TargetIDLTE applies the LTE predicate on the "target_id" field.
func TargetIDLTE(v uint32) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldTargetID, v))
}

// TargetIDIsNil applies the IsNil predicate on the "target_id" field.
func TargetIDIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldTargetID))
}

// TargetIDNotNil applies the NotNil predicate on the "target_id" field.
func TargetIDNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldTargetID))
}

// ValueEQ applies the EQ predicate on the "value" field.
func ValueEQ(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldValue, v))
}

// ValueNEQ applies the NEQ predicate on the "value" field.
func ValueNEQ(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldValue, v))
}

// ValueIn applies the In predicate on the "value" field.
func ValueIn(vs ...string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldValue, vs...))
}

// ValueNotIn applies the NotIn predicate on the "value" field.
func ValueNotIn(vs ...string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldValue, vs...))
}

// ValueGT applies the GT predicate on the "value" field.
func ValueGT(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldValue, v))
}

// ValueGTE applies the GTE predicate on the "value" field.
func ValueGTE(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldValue, v))
}

// ValueLT applies the LT predicate on the "value" field.
func ValueLT(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldValue, v))
}

// ValueLTE applies the LTE predicate on the "value" field.
func ValueLTE(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldValue, v))
}

// ValueContains applies the Contains predicate on the "value" field.
func ValueContains(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldContains(FieldValue, v))
}

// ValueHasPrefix applies the HasPrefix predicate on the "value" field.
func ValueHasPrefix(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldHasPrefix(FieldValue, v))
}

// ValueHasSuffix applies the HasSuffix predicate on the "value" field.
func ValueHasSuffix(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldHasSuffix(FieldValue, v))
}

// ValueIsNil applies the IsNil predicate on the "value" field.
func ValueIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldValue))
}

// ValueNotNil applies the NotNil predicate on the "value" field.
func ValueNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldValue))
}

// ValueEqualFold applies the EqualFold predicate on the "value" field.
func ValueEqualFold(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEqualFold(FieldValue, v))
}

// ValueContainsFold applies the ContainsFold predicate on the "value" field.
func ValueContainsFold(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldContainsFold(FieldValue, v))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldContainsFold(FieldReason, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v Type) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v Type) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...Type) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...Type) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldType, vs...))
}

// TypeIsNil applies the IsNil predicate on the "type" field.
func TypeIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldType))
}

// TypeNotNil applies the NotNil predicate on the "type" field.
func TypeNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldType))
}

// MethodEQ applies the EQ predicate on the "method" field.
func MethodEQ(v Method) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldEQ(FieldMethod, v))
}

// MethodNEQ applies the NEQ predicate on the "method" field.
func MethodNEQ(v Method) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNEQ(FieldMethod, v))
}

// MethodIn applies the In predicate on the "method" field.
func MethodIn(vs ...Method) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIn(FieldMethod, vs...))
}

// MethodNotIn applies the NotIn predicate on the "method" field.
func MethodNotIn(vs ...Method) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotIn(FieldMethod, vs...))
}

// MethodIsNil applies the IsNil predicate on the "method" field.
func MethodIsNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldIsNull(FieldMethod))
}

// MethodNotNil applies the NotNil predicate on the "method" field.
func MethodNotNil() predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.FieldNotNull(FieldMethod))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AdminLoginRestriction) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AdminLoginRestriction) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AdminLoginRestriction) predicate.AdminLoginRestriction {
	return predicate.AdminLoginRestriction(sql.NotPredicates(p))
}
