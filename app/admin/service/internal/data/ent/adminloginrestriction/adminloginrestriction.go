// Code generated by ent, DO NOT EDIT.

package adminloginrestriction

import (
	"fmt"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the adminloginrestriction type in the database.
	Label = "admin_login_restriction"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldDeleteTime holds the string denoting the delete_time field in the database.
	FieldDeleteTime = "delete_time"
	// FieldCreateBy holds the string denoting the create_by field in the database.
	FieldCreateBy = "create_by"
	// FieldUpdateBy holds the string denoting the update_by field in the database.
	FieldUpdateBy = "update_by"
	// FieldTargetID holds the string denoting the target_id field in the database.
	FieldTargetID = "target_id"
	// FieldValue holds the string denoting the value field in the database.
	FieldValue = "value"
	// FieldReason holds the string denoting the reason field in the database.
	FieldReason = "reason"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldMethod holds the string denoting the method field in the database.
	FieldMethod = "method"
	// Table holds the table name of the adminloginrestriction in the database.
	Table = "admin_login_restrictions"
)

// Columns holds all SQL columns for adminloginrestriction fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldDeleteTime,
	FieldCreateBy,
	FieldUpdateBy,
	FieldTargetID,
	FieldValue,
	FieldReason,
	FieldType,
	FieldMethod,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(uint32) error
)

// Type defines the type for the "type" enum field.
type Type string

// TypeBlacklist is the default value of the Type enum.
const DefaultType = TypeBlacklist

// Type values.
const (
	TypeBlacklist Type = "BLACKLIST"
	TypeWhitelist Type = "WHITELIST"
)

func (_type Type) String() string {
	return string(_type)
}

// TypeValidator is a validator for the "type" field enum values. It is called by the builders before save.
func TypeValidator(_type Type) error {
	switch _type {
	case TypeBlacklist, TypeWhitelist:
		return nil
	default:
		return fmt.Errorf("adminloginrestriction: invalid enum value for type field: %q", _type)
	}
}

// Method defines the type for the "method" enum field.
type Method string

// MethodIp is the default value of the Method enum.
const DefaultMethod = MethodIp

// Method values.
const (
	MethodIp     Method = "IP"
	MethodMac    Method = "MAC"
	MethodRegion Method = "REGION"
	MethodTime   Method = "TIME"
	MethodDevice Method = "DEVICE"
)

func (m Method) String() string {
	return string(m)
}

// MethodValidator is a validator for the "method" field enum values. It is called by the builders before save.
func MethodValidator(m Method) error {
	switch m {
	case MethodIp, MethodMac, MethodRegion, MethodTime, MethodDevice:
		return nil
	default:
		return fmt.Errorf("adminloginrestriction: invalid enum value for method field: %q", m)
	}
}

// OrderOption defines the ordering options for the AdminLoginRestriction queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByDeleteTime orders the results by the delete_time field.
func ByDeleteTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeleteTime, opts...).ToFunc()
}

// ByCreateBy orders the results by the create_by field.
func ByCreateBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateBy, opts...).ToFunc()
}

// ByUpdateBy orders the results by the update_by field.
func ByUpdateBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateBy, opts...).ToFunc()
}

// ByTargetID orders the results by the target_id field.
func ByTargetID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTargetID, opts...).ToFunc()
}

// ByValue orders the results by the value field.
func ByValue(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldValue, opts...).ToFunc()
}

// ByReason orders the results by the reason field.
func ByReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReason, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByMethod orders the results by the method field.
func ByMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMethod, opts...).ToFunc()
}
