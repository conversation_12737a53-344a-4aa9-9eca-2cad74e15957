// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminLoginRestrictionUpdate is the builder for updating AdminLoginRestriction entities.
type AdminLoginRestrictionUpdate struct {
	config
	hooks     []Hook
	mutation  *AdminLoginRestrictionMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AdminLoginRestrictionUpdate builder.
func (alru *AdminLoginRestrictionUpdate) Where(ps ...predicate.AdminLoginRestriction) *AdminLoginRestrictionUpdate {
	alru.mutation.Where(ps...)
	return alru
}

// SetUpdateTime sets the "update_time" field.
func (alru *AdminLoginRestrictionUpdate) SetUpdateTime(t time.Time) *AdminLoginRestrictionUpdate {
	alru.mutation.SetUpdateTime(t)
	return alru
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableUpdateTime(t *time.Time) *AdminLoginRestrictionUpdate {
	if t != nil {
		alru.SetUpdateTime(*t)
	}
	return alru
}

// ClearUpdateTime clears the value of the "update_time" field.
func (alru *AdminLoginRestrictionUpdate) ClearUpdateTime() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearUpdateTime()
	return alru
}

// SetDeleteTime sets the "delete_time" field.
func (alru *AdminLoginRestrictionUpdate) SetDeleteTime(t time.Time) *AdminLoginRestrictionUpdate {
	alru.mutation.SetDeleteTime(t)
	return alru
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableDeleteTime(t *time.Time) *AdminLoginRestrictionUpdate {
	if t != nil {
		alru.SetDeleteTime(*t)
	}
	return alru
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (alru *AdminLoginRestrictionUpdate) ClearDeleteTime() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearDeleteTime()
	return alru
}

// SetCreateBy sets the "create_by" field.
func (alru *AdminLoginRestrictionUpdate) SetCreateBy(u uint32) *AdminLoginRestrictionUpdate {
	alru.mutation.ResetCreateBy()
	alru.mutation.SetCreateBy(u)
	return alru
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableCreateBy(u *uint32) *AdminLoginRestrictionUpdate {
	if u != nil {
		alru.SetCreateBy(*u)
	}
	return alru
}

// AddCreateBy adds u to the "create_by" field.
func (alru *AdminLoginRestrictionUpdate) AddCreateBy(u int32) *AdminLoginRestrictionUpdate {
	alru.mutation.AddCreateBy(u)
	return alru
}

// ClearCreateBy clears the value of the "create_by" field.
func (alru *AdminLoginRestrictionUpdate) ClearCreateBy() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearCreateBy()
	return alru
}

// SetUpdateBy sets the "update_by" field.
func (alru *AdminLoginRestrictionUpdate) SetUpdateBy(u uint32) *AdminLoginRestrictionUpdate {
	alru.mutation.ResetUpdateBy()
	alru.mutation.SetUpdateBy(u)
	return alru
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableUpdateBy(u *uint32) *AdminLoginRestrictionUpdate {
	if u != nil {
		alru.SetUpdateBy(*u)
	}
	return alru
}

// AddUpdateBy adds u to the "update_by" field.
func (alru *AdminLoginRestrictionUpdate) AddUpdateBy(u int32) *AdminLoginRestrictionUpdate {
	alru.mutation.AddUpdateBy(u)
	return alru
}

// ClearUpdateBy clears the value of the "update_by" field.
func (alru *AdminLoginRestrictionUpdate) ClearUpdateBy() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearUpdateBy()
	return alru
}

// SetTargetID sets the "target_id" field.
func (alru *AdminLoginRestrictionUpdate) SetTargetID(u uint32) *AdminLoginRestrictionUpdate {
	alru.mutation.ResetTargetID()
	alru.mutation.SetTargetID(u)
	return alru
}

// SetNillableTargetID sets the "target_id" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableTargetID(u *uint32) *AdminLoginRestrictionUpdate {
	if u != nil {
		alru.SetTargetID(*u)
	}
	return alru
}

// AddTargetID adds u to the "target_id" field.
func (alru *AdminLoginRestrictionUpdate) AddTargetID(u int32) *AdminLoginRestrictionUpdate {
	alru.mutation.AddTargetID(u)
	return alru
}

// ClearTargetID clears the value of the "target_id" field.
func (alru *AdminLoginRestrictionUpdate) ClearTargetID() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearTargetID()
	return alru
}

// SetValue sets the "value" field.
func (alru *AdminLoginRestrictionUpdate) SetValue(s string) *AdminLoginRestrictionUpdate {
	alru.mutation.SetValue(s)
	return alru
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableValue(s *string) *AdminLoginRestrictionUpdate {
	if s != nil {
		alru.SetValue(*s)
	}
	return alru
}

// ClearValue clears the value of the "value" field.
func (alru *AdminLoginRestrictionUpdate) ClearValue() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearValue()
	return alru
}

// SetReason sets the "reason" field.
func (alru *AdminLoginRestrictionUpdate) SetReason(s string) *AdminLoginRestrictionUpdate {
	alru.mutation.SetReason(s)
	return alru
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableReason(s *string) *AdminLoginRestrictionUpdate {
	if s != nil {
		alru.SetReason(*s)
	}
	return alru
}

// ClearReason clears the value of the "reason" field.
func (alru *AdminLoginRestrictionUpdate) ClearReason() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearReason()
	return alru
}

// SetType sets the "type" field.
func (alru *AdminLoginRestrictionUpdate) SetType(a adminloginrestriction.Type) *AdminLoginRestrictionUpdate {
	alru.mutation.SetType(a)
	return alru
}

// SetNillableType sets the "type" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableType(a *adminloginrestriction.Type) *AdminLoginRestrictionUpdate {
	if a != nil {
		alru.SetType(*a)
	}
	return alru
}

// ClearType clears the value of the "type" field.
func (alru *AdminLoginRestrictionUpdate) ClearType() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearType()
	return alru
}

// SetMethod sets the "method" field.
func (alru *AdminLoginRestrictionUpdate) SetMethod(a adminloginrestriction.Method) *AdminLoginRestrictionUpdate {
	alru.mutation.SetMethod(a)
	return alru
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (alru *AdminLoginRestrictionUpdate) SetNillableMethod(a *adminloginrestriction.Method) *AdminLoginRestrictionUpdate {
	if a != nil {
		alru.SetMethod(*a)
	}
	return alru
}

// ClearMethod clears the value of the "method" field.
func (alru *AdminLoginRestrictionUpdate) ClearMethod() *AdminLoginRestrictionUpdate {
	alru.mutation.ClearMethod()
	return alru
}

// Mutation returns the AdminLoginRestrictionMutation object of the builder.
func (alru *AdminLoginRestrictionUpdate) Mutation() *AdminLoginRestrictionMutation {
	return alru.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (alru *AdminLoginRestrictionUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, alru.sqlSave, alru.mutation, alru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (alru *AdminLoginRestrictionUpdate) SaveX(ctx context.Context) int {
	affected, err := alru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (alru *AdminLoginRestrictionUpdate) Exec(ctx context.Context) error {
	_, err := alru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (alru *AdminLoginRestrictionUpdate) ExecX(ctx context.Context) {
	if err := alru.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (alru *AdminLoginRestrictionUpdate) check() error {
	if v, ok := alru.mutation.GetType(); ok {
		if err := adminloginrestriction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.type": %w`, err)}
		}
	}
	if v, ok := alru.mutation.Method(); ok {
		if err := adminloginrestriction.MethodValidator(v); err != nil {
			return &ValidationError{Name: "method", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.method": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (alru *AdminLoginRestrictionUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AdminLoginRestrictionUpdate {
	alru.modifiers = append(alru.modifiers, modifiers...)
	return alru
}

func (alru *AdminLoginRestrictionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := alru.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(adminloginrestriction.Table, adminloginrestriction.Columns, sqlgraph.NewFieldSpec(adminloginrestriction.FieldID, field.TypeUint32))
	if ps := alru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if alru.mutation.CreateTimeCleared() {
		_spec.ClearField(adminloginrestriction.FieldCreateTime, field.TypeTime)
	}
	if value, ok := alru.mutation.UpdateTime(); ok {
		_spec.SetField(adminloginrestriction.FieldUpdateTime, field.TypeTime, value)
	}
	if alru.mutation.UpdateTimeCleared() {
		_spec.ClearField(adminloginrestriction.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := alru.mutation.DeleteTime(); ok {
		_spec.SetField(adminloginrestriction.FieldDeleteTime, field.TypeTime, value)
	}
	if alru.mutation.DeleteTimeCleared() {
		_spec.ClearField(adminloginrestriction.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := alru.mutation.CreateBy(); ok {
		_spec.SetField(adminloginrestriction.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := alru.mutation.AddedCreateBy(); ok {
		_spec.AddField(adminloginrestriction.FieldCreateBy, field.TypeUint32, value)
	}
	if alru.mutation.CreateByCleared() {
		_spec.ClearField(adminloginrestriction.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := alru.mutation.UpdateBy(); ok {
		_spec.SetField(adminloginrestriction.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := alru.mutation.AddedUpdateBy(); ok {
		_spec.AddField(adminloginrestriction.FieldUpdateBy, field.TypeUint32, value)
	}
	if alru.mutation.UpdateByCleared() {
		_spec.ClearField(adminloginrestriction.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := alru.mutation.TargetID(); ok {
		_spec.SetField(adminloginrestriction.FieldTargetID, field.TypeUint32, value)
	}
	if value, ok := alru.mutation.AddedTargetID(); ok {
		_spec.AddField(adminloginrestriction.FieldTargetID, field.TypeUint32, value)
	}
	if alru.mutation.TargetIDCleared() {
		_spec.ClearField(adminloginrestriction.FieldTargetID, field.TypeUint32)
	}
	if value, ok := alru.mutation.Value(); ok {
		_spec.SetField(adminloginrestriction.FieldValue, field.TypeString, value)
	}
	if alru.mutation.ValueCleared() {
		_spec.ClearField(adminloginrestriction.FieldValue, field.TypeString)
	}
	if value, ok := alru.mutation.Reason(); ok {
		_spec.SetField(adminloginrestriction.FieldReason, field.TypeString, value)
	}
	if alru.mutation.ReasonCleared() {
		_spec.ClearField(adminloginrestriction.FieldReason, field.TypeString)
	}
	if value, ok := alru.mutation.GetType(); ok {
		_spec.SetField(adminloginrestriction.FieldType, field.TypeEnum, value)
	}
	if alru.mutation.TypeCleared() {
		_spec.ClearField(adminloginrestriction.FieldType, field.TypeEnum)
	}
	if value, ok := alru.mutation.Method(); ok {
		_spec.SetField(adminloginrestriction.FieldMethod, field.TypeEnum, value)
	}
	if alru.mutation.MethodCleared() {
		_spec.ClearField(adminloginrestriction.FieldMethod, field.TypeEnum)
	}
	_spec.AddModifiers(alru.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, alru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{adminloginrestriction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	alru.mutation.done = true
	return n, nil
}

// AdminLoginRestrictionUpdateOne is the builder for updating a single AdminLoginRestriction entity.
type AdminLoginRestrictionUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AdminLoginRestrictionMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetUpdateTime(t time.Time) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.SetUpdateTime(t)
	return alruo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableUpdateTime(t *time.Time) *AdminLoginRestrictionUpdateOne {
	if t != nil {
		alruo.SetUpdateTime(*t)
	}
	return alruo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearUpdateTime() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearUpdateTime()
	return alruo
}

// SetDeleteTime sets the "delete_time" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetDeleteTime(t time.Time) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.SetDeleteTime(t)
	return alruo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableDeleteTime(t *time.Time) *AdminLoginRestrictionUpdateOne {
	if t != nil {
		alruo.SetDeleteTime(*t)
	}
	return alruo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearDeleteTime() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearDeleteTime()
	return alruo
}

// SetCreateBy sets the "create_by" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetCreateBy(u uint32) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ResetCreateBy()
	alruo.mutation.SetCreateBy(u)
	return alruo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableCreateBy(u *uint32) *AdminLoginRestrictionUpdateOne {
	if u != nil {
		alruo.SetCreateBy(*u)
	}
	return alruo
}

// AddCreateBy adds u to the "create_by" field.
func (alruo *AdminLoginRestrictionUpdateOne) AddCreateBy(u int32) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.AddCreateBy(u)
	return alruo
}

// ClearCreateBy clears the value of the "create_by" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearCreateBy() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearCreateBy()
	return alruo
}

// SetUpdateBy sets the "update_by" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetUpdateBy(u uint32) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ResetUpdateBy()
	alruo.mutation.SetUpdateBy(u)
	return alruo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableUpdateBy(u *uint32) *AdminLoginRestrictionUpdateOne {
	if u != nil {
		alruo.SetUpdateBy(*u)
	}
	return alruo
}

// AddUpdateBy adds u to the "update_by" field.
func (alruo *AdminLoginRestrictionUpdateOne) AddUpdateBy(u int32) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.AddUpdateBy(u)
	return alruo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearUpdateBy() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearUpdateBy()
	return alruo
}

// SetTargetID sets the "target_id" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetTargetID(u uint32) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ResetTargetID()
	alruo.mutation.SetTargetID(u)
	return alruo
}

// SetNillableTargetID sets the "target_id" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableTargetID(u *uint32) *AdminLoginRestrictionUpdateOne {
	if u != nil {
		alruo.SetTargetID(*u)
	}
	return alruo
}

// AddTargetID adds u to the "target_id" field.
func (alruo *AdminLoginRestrictionUpdateOne) AddTargetID(u int32) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.AddTargetID(u)
	return alruo
}

// ClearTargetID clears the value of the "target_id" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearTargetID() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearTargetID()
	return alruo
}

// SetValue sets the "value" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetValue(s string) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.SetValue(s)
	return alruo
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableValue(s *string) *AdminLoginRestrictionUpdateOne {
	if s != nil {
		alruo.SetValue(*s)
	}
	return alruo
}

// ClearValue clears the value of the "value" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearValue() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearValue()
	return alruo
}

// SetReason sets the "reason" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetReason(s string) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.SetReason(s)
	return alruo
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableReason(s *string) *AdminLoginRestrictionUpdateOne {
	if s != nil {
		alruo.SetReason(*s)
	}
	return alruo
}

// ClearReason clears the value of the "reason" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearReason() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearReason()
	return alruo
}

// SetType sets the "type" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetType(a adminloginrestriction.Type) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.SetType(a)
	return alruo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableType(a *adminloginrestriction.Type) *AdminLoginRestrictionUpdateOne {
	if a != nil {
		alruo.SetType(*a)
	}
	return alruo
}

// ClearType clears the value of the "type" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearType() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearType()
	return alruo
}

// SetMethod sets the "method" field.
func (alruo *AdminLoginRestrictionUpdateOne) SetMethod(a adminloginrestriction.Method) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.SetMethod(a)
	return alruo
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (alruo *AdminLoginRestrictionUpdateOne) SetNillableMethod(a *adminloginrestriction.Method) *AdminLoginRestrictionUpdateOne {
	if a != nil {
		alruo.SetMethod(*a)
	}
	return alruo
}

// ClearMethod clears the value of the "method" field.
func (alruo *AdminLoginRestrictionUpdateOne) ClearMethod() *AdminLoginRestrictionUpdateOne {
	alruo.mutation.ClearMethod()
	return alruo
}

// Mutation returns the AdminLoginRestrictionMutation object of the builder.
func (alruo *AdminLoginRestrictionUpdateOne) Mutation() *AdminLoginRestrictionMutation {
	return alruo.mutation
}

// Where appends a list predicates to the AdminLoginRestrictionUpdate builder.
func (alruo *AdminLoginRestrictionUpdateOne) Where(ps ...predicate.AdminLoginRestriction) *AdminLoginRestrictionUpdateOne {
	alruo.mutation.Where(ps...)
	return alruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (alruo *AdminLoginRestrictionUpdateOne) Select(field string, fields ...string) *AdminLoginRestrictionUpdateOne {
	alruo.fields = append([]string{field}, fields...)
	return alruo
}

// Save executes the query and returns the updated AdminLoginRestriction entity.
func (alruo *AdminLoginRestrictionUpdateOne) Save(ctx context.Context) (*AdminLoginRestriction, error) {
	return withHooks(ctx, alruo.sqlSave, alruo.mutation, alruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (alruo *AdminLoginRestrictionUpdateOne) SaveX(ctx context.Context) *AdminLoginRestriction {
	node, err := alruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (alruo *AdminLoginRestrictionUpdateOne) Exec(ctx context.Context) error {
	_, err := alruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (alruo *AdminLoginRestrictionUpdateOne) ExecX(ctx context.Context) {
	if err := alruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (alruo *AdminLoginRestrictionUpdateOne) check() error {
	if v, ok := alruo.mutation.GetType(); ok {
		if err := adminloginrestriction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.type": %w`, err)}
		}
	}
	if v, ok := alruo.mutation.Method(); ok {
		if err := adminloginrestriction.MethodValidator(v); err != nil {
			return &ValidationError{Name: "method", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.method": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (alruo *AdminLoginRestrictionUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AdminLoginRestrictionUpdateOne {
	alruo.modifiers = append(alruo.modifiers, modifiers...)
	return alruo
}

func (alruo *AdminLoginRestrictionUpdateOne) sqlSave(ctx context.Context) (_node *AdminLoginRestriction, err error) {
	if err := alruo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(adminloginrestriction.Table, adminloginrestriction.Columns, sqlgraph.NewFieldSpec(adminloginrestriction.FieldID, field.TypeUint32))
	id, ok := alruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AdminLoginRestriction.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := alruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, adminloginrestriction.FieldID)
		for _, f := range fields {
			if !adminloginrestriction.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != adminloginrestriction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := alruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if alruo.mutation.CreateTimeCleared() {
		_spec.ClearField(adminloginrestriction.FieldCreateTime, field.TypeTime)
	}
	if value, ok := alruo.mutation.UpdateTime(); ok {
		_spec.SetField(adminloginrestriction.FieldUpdateTime, field.TypeTime, value)
	}
	if alruo.mutation.UpdateTimeCleared() {
		_spec.ClearField(adminloginrestriction.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := alruo.mutation.DeleteTime(); ok {
		_spec.SetField(adminloginrestriction.FieldDeleteTime, field.TypeTime, value)
	}
	if alruo.mutation.DeleteTimeCleared() {
		_spec.ClearField(adminloginrestriction.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := alruo.mutation.CreateBy(); ok {
		_spec.SetField(adminloginrestriction.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := alruo.mutation.AddedCreateBy(); ok {
		_spec.AddField(adminloginrestriction.FieldCreateBy, field.TypeUint32, value)
	}
	if alruo.mutation.CreateByCleared() {
		_spec.ClearField(adminloginrestriction.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := alruo.mutation.UpdateBy(); ok {
		_spec.SetField(adminloginrestriction.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := alruo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(adminloginrestriction.FieldUpdateBy, field.TypeUint32, value)
	}
	if alruo.mutation.UpdateByCleared() {
		_spec.ClearField(adminloginrestriction.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := alruo.mutation.TargetID(); ok {
		_spec.SetField(adminloginrestriction.FieldTargetID, field.TypeUint32, value)
	}
	if value, ok := alruo.mutation.AddedTargetID(); ok {
		_spec.AddField(adminloginrestriction.FieldTargetID, field.TypeUint32, value)
	}
	if alruo.mutation.TargetIDCleared() {
		_spec.ClearField(adminloginrestriction.FieldTargetID, field.TypeUint32)
	}
	if value, ok := alruo.mutation.Value(); ok {
		_spec.SetField(adminloginrestriction.FieldValue, field.TypeString, value)
	}
	if alruo.mutation.ValueCleared() {
		_spec.ClearField(adminloginrestriction.FieldValue, field.TypeString)
	}
	if value, ok := alruo.mutation.Reason(); ok {
		_spec.SetField(adminloginrestriction.FieldReason, field.TypeString, value)
	}
	if alruo.mutation.ReasonCleared() {
		_spec.ClearField(adminloginrestriction.FieldReason, field.TypeString)
	}
	if value, ok := alruo.mutation.GetType(); ok {
		_spec.SetField(adminloginrestriction.FieldType, field.TypeEnum, value)
	}
	if alruo.mutation.TypeCleared() {
		_spec.ClearField(adminloginrestriction.FieldType, field.TypeEnum)
	}
	if value, ok := alruo.mutation.Method(); ok {
		_spec.SetField(adminloginrestriction.FieldMethod, field.TypeEnum, value)
	}
	if alruo.mutation.MethodCleared() {
		_spec.ClearField(adminloginrestriction.FieldMethod, field.TypeEnum)
	}
	_spec.AddModifiers(alruo.modifiers...)
	_node = &AdminLoginRestriction{config: alruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, alruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{adminloginrestriction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	alruo.mutation.done = true
	return _node, nil
}
