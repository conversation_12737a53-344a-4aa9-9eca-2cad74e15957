// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserCreate is the builder for creating a User entity.
type UserCreate struct {
	config
	mutation *UserMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateBy sets the "create_by" field.
func (uc *UserCreate) SetCreateBy(u uint32) *UserCreate {
	uc.mutation.SetCreateBy(u)
	return uc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (uc *UserCreate) SetNillableCreateBy(u *uint32) *UserCreate {
	if u != nil {
		uc.SetCreateBy(*u)
	}
	return uc
}

// SetUpdateBy sets the "update_by" field.
func (uc *UserCreate) SetUpdateBy(u uint32) *UserCreate {
	uc.mutation.SetUpdateBy(u)
	return uc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (uc *UserCreate) SetNillableUpdateBy(u *uint32) *UserCreate {
	if u != nil {
		uc.SetUpdateBy(*u)
	}
	return uc
}

// SetCreateTime sets the "create_time" field.
func (uc *UserCreate) SetCreateTime(t time.Time) *UserCreate {
	uc.mutation.SetCreateTime(t)
	return uc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (uc *UserCreate) SetNillableCreateTime(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetCreateTime(*t)
	}
	return uc
}

// SetUpdateTime sets the "update_time" field.
func (uc *UserCreate) SetUpdateTime(t time.Time) *UserCreate {
	uc.mutation.SetUpdateTime(t)
	return uc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (uc *UserCreate) SetNillableUpdateTime(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetUpdateTime(*t)
	}
	return uc
}

// SetDeleteTime sets the "delete_time" field.
func (uc *UserCreate) SetDeleteTime(t time.Time) *UserCreate {
	uc.mutation.SetDeleteTime(t)
	return uc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (uc *UserCreate) SetNillableDeleteTime(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetDeleteTime(*t)
	}
	return uc
}

// SetRemark sets the "remark" field.
func (uc *UserCreate) SetRemark(s string) *UserCreate {
	uc.mutation.SetRemark(s)
	return uc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (uc *UserCreate) SetNillableRemark(s *string) *UserCreate {
	if s != nil {
		uc.SetRemark(*s)
	}
	return uc
}

// SetStatus sets the "status" field.
func (uc *UserCreate) SetStatus(u user.Status) *UserCreate {
	uc.mutation.SetStatus(u)
	return uc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uc *UserCreate) SetNillableStatus(u *user.Status) *UserCreate {
	if u != nil {
		uc.SetStatus(*u)
	}
	return uc
}

// SetTenantID sets the "tenant_id" field.
func (uc *UserCreate) SetTenantID(u uint32) *UserCreate {
	uc.mutation.SetTenantID(u)
	return uc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (uc *UserCreate) SetNillableTenantID(u *uint32) *UserCreate {
	if u != nil {
		uc.SetTenantID(*u)
	}
	return uc
}

// SetUsername sets the "username" field.
func (uc *UserCreate) SetUsername(s string) *UserCreate {
	uc.mutation.SetUsername(s)
	return uc
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uc *UserCreate) SetNillableUsername(s *string) *UserCreate {
	if s != nil {
		uc.SetUsername(*s)
	}
	return uc
}

// SetNickname sets the "nickname" field.
func (uc *UserCreate) SetNickname(s string) *UserCreate {
	uc.mutation.SetNickname(s)
	return uc
}

// SetNillableNickname sets the "nickname" field if the given value is not nil.
func (uc *UserCreate) SetNillableNickname(s *string) *UserCreate {
	if s != nil {
		uc.SetNickname(*s)
	}
	return uc
}

// SetRealname sets the "realname" field.
func (uc *UserCreate) SetRealname(s string) *UserCreate {
	uc.mutation.SetRealname(s)
	return uc
}

// SetNillableRealname sets the "realname" field if the given value is not nil.
func (uc *UserCreate) SetNillableRealname(s *string) *UserCreate {
	if s != nil {
		uc.SetRealname(*s)
	}
	return uc
}

// SetEmail sets the "email" field.
func (uc *UserCreate) SetEmail(s string) *UserCreate {
	uc.mutation.SetEmail(s)
	return uc
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uc *UserCreate) SetNillableEmail(s *string) *UserCreate {
	if s != nil {
		uc.SetEmail(*s)
	}
	return uc
}

// SetMobile sets the "mobile" field.
func (uc *UserCreate) SetMobile(s string) *UserCreate {
	uc.mutation.SetMobile(s)
	return uc
}

// SetNillableMobile sets the "mobile" field if the given value is not nil.
func (uc *UserCreate) SetNillableMobile(s *string) *UserCreate {
	if s != nil {
		uc.SetMobile(*s)
	}
	return uc
}

// SetTelephone sets the "telephone" field.
func (uc *UserCreate) SetTelephone(s string) *UserCreate {
	uc.mutation.SetTelephone(s)
	return uc
}

// SetNillableTelephone sets the "telephone" field if the given value is not nil.
func (uc *UserCreate) SetNillableTelephone(s *string) *UserCreate {
	if s != nil {
		uc.SetTelephone(*s)
	}
	return uc
}

// SetAvatar sets the "avatar" field.
func (uc *UserCreate) SetAvatar(s string) *UserCreate {
	uc.mutation.SetAvatar(s)
	return uc
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (uc *UserCreate) SetNillableAvatar(s *string) *UserCreate {
	if s != nil {
		uc.SetAvatar(*s)
	}
	return uc
}

// SetAddress sets the "address" field.
func (uc *UserCreate) SetAddress(s string) *UserCreate {
	uc.mutation.SetAddress(s)
	return uc
}

// SetNillableAddress sets the "address" field if the given value is not nil.
func (uc *UserCreate) SetNillableAddress(s *string) *UserCreate {
	if s != nil {
		uc.SetAddress(*s)
	}
	return uc
}

// SetRegion sets the "region" field.
func (uc *UserCreate) SetRegion(s string) *UserCreate {
	uc.mutation.SetRegion(s)
	return uc
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (uc *UserCreate) SetNillableRegion(s *string) *UserCreate {
	if s != nil {
		uc.SetRegion(*s)
	}
	return uc
}

// SetDescription sets the "description" field.
func (uc *UserCreate) SetDescription(s string) *UserCreate {
	uc.mutation.SetDescription(s)
	return uc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (uc *UserCreate) SetNillableDescription(s *string) *UserCreate {
	if s != nil {
		uc.SetDescription(*s)
	}
	return uc
}

// SetGender sets the "gender" field.
func (uc *UserCreate) SetGender(u user.Gender) *UserCreate {
	uc.mutation.SetGender(u)
	return uc
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uc *UserCreate) SetNillableGender(u *user.Gender) *UserCreate {
	if u != nil {
		uc.SetGender(*u)
	}
	return uc
}

// SetAuthority sets the "authority" field.
func (uc *UserCreate) SetAuthority(u user.Authority) *UserCreate {
	uc.mutation.SetAuthority(u)
	return uc
}

// SetNillableAuthority sets the "authority" field if the given value is not nil.
func (uc *UserCreate) SetNillableAuthority(u *user.Authority) *UserCreate {
	if u != nil {
		uc.SetAuthority(*u)
	}
	return uc
}

// SetLastLoginTime sets the "last_login_time" field.
func (uc *UserCreate) SetLastLoginTime(t time.Time) *UserCreate {
	uc.mutation.SetLastLoginTime(t)
	return uc
}

// SetNillableLastLoginTime sets the "last_login_time" field if the given value is not nil.
func (uc *UserCreate) SetNillableLastLoginTime(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetLastLoginTime(*t)
	}
	return uc
}

// SetLastLoginIP sets the "last_login_ip" field.
func (uc *UserCreate) SetLastLoginIP(s string) *UserCreate {
	uc.mutation.SetLastLoginIP(s)
	return uc
}

// SetNillableLastLoginIP sets the "last_login_ip" field if the given value is not nil.
func (uc *UserCreate) SetNillableLastLoginIP(s *string) *UserCreate {
	if s != nil {
		uc.SetLastLoginIP(*s)
	}
	return uc
}

// SetOrgID sets the "org_id" field.
func (uc *UserCreate) SetOrgID(u uint32) *UserCreate {
	uc.mutation.SetOrgID(u)
	return uc
}

// SetNillableOrgID sets the "org_id" field if the given value is not nil.
func (uc *UserCreate) SetNillableOrgID(u *uint32) *UserCreate {
	if u != nil {
		uc.SetOrgID(*u)
	}
	return uc
}

// SetPositionID sets the "position_id" field.
func (uc *UserCreate) SetPositionID(u uint32) *UserCreate {
	uc.mutation.SetPositionID(u)
	return uc
}

// SetNillablePositionID sets the "position_id" field if the given value is not nil.
func (uc *UserCreate) SetNillablePositionID(u *uint32) *UserCreate {
	if u != nil {
		uc.SetPositionID(*u)
	}
	return uc
}

// SetWorkID sets the "work_id" field.
func (uc *UserCreate) SetWorkID(u uint32) *UserCreate {
	uc.mutation.SetWorkID(u)
	return uc
}

// SetNillableWorkID sets the "work_id" field if the given value is not nil.
func (uc *UserCreate) SetNillableWorkID(u *uint32) *UserCreate {
	if u != nil {
		uc.SetWorkID(*u)
	}
	return uc
}

// SetRoles sets the "roles" field.
func (uc *UserCreate) SetRoles(s []string) *UserCreate {
	uc.mutation.SetRoles(s)
	return uc
}

// SetID sets the "id" field.
func (uc *UserCreate) SetID(u uint32) *UserCreate {
	uc.mutation.SetID(u)
	return uc
}

// Mutation returns the UserMutation object of the builder.
func (uc *UserCreate) Mutation() *UserMutation {
	return uc.mutation
}

// Save creates the User in the database.
func (uc *UserCreate) Save(ctx context.Context) (*User, error) {
	uc.defaults()
	return withHooks(ctx, uc.sqlSave, uc.mutation, uc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uc *UserCreate) SaveX(ctx context.Context) *User {
	v, err := uc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uc *UserCreate) Exec(ctx context.Context) error {
	_, err := uc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uc *UserCreate) ExecX(ctx context.Context) {
	if err := uc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uc *UserCreate) defaults() {
	if _, ok := uc.mutation.Remark(); !ok {
		v := user.DefaultRemark
		uc.mutation.SetRemark(v)
	}
	if _, ok := uc.mutation.Status(); !ok {
		v := user.DefaultStatus
		uc.mutation.SetStatus(v)
	}
	if _, ok := uc.mutation.Mobile(); !ok {
		v := user.DefaultMobile
		uc.mutation.SetMobile(v)
	}
	if _, ok := uc.mutation.Telephone(); !ok {
		v := user.DefaultTelephone
		uc.mutation.SetTelephone(v)
	}
	if _, ok := uc.mutation.Address(); !ok {
		v := user.DefaultAddress
		uc.mutation.SetAddress(v)
	}
	if _, ok := uc.mutation.Region(); !ok {
		v := user.DefaultRegion
		uc.mutation.SetRegion(v)
	}
	if _, ok := uc.mutation.Authority(); !ok {
		v := user.DefaultAuthority
		uc.mutation.SetAuthority(v)
	}
	if _, ok := uc.mutation.LastLoginIP(); !ok {
		v := user.DefaultLastLoginIP
		uc.mutation.SetLastLoginIP(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uc *UserCreate) check() error {
	if v, ok := uc.mutation.Status(); ok {
		if err := user.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "User.status": %w`, err)}
		}
	}
	if v, ok := uc.mutation.TenantID(); ok {
		if err := user.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "User.tenant_id": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Username(); ok {
		if err := user.UsernameValidator(v); err != nil {
			return &ValidationError{Name: "username", err: fmt.Errorf(`ent: validator failed for field "User.username": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Nickname(); ok {
		if err := user.NicknameValidator(v); err != nil {
			return &ValidationError{Name: "nickname", err: fmt.Errorf(`ent: validator failed for field "User.nickname": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Realname(); ok {
		if err := user.RealnameValidator(v); err != nil {
			return &ValidationError{Name: "realname", err: fmt.Errorf(`ent: validator failed for field "User.realname": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Mobile(); ok {
		if err := user.MobileValidator(v); err != nil {
			return &ValidationError{Name: "mobile", err: fmt.Errorf(`ent: validator failed for field "User.mobile": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Telephone(); ok {
		if err := user.TelephoneValidator(v); err != nil {
			return &ValidationError{Name: "telephone", err: fmt.Errorf(`ent: validator failed for field "User.telephone": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Avatar(); ok {
		if err := user.AvatarValidator(v); err != nil {
			return &ValidationError{Name: "avatar", err: fmt.Errorf(`ent: validator failed for field "User.avatar": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Address(); ok {
		if err := user.AddressValidator(v); err != nil {
			return &ValidationError{Name: "address", err: fmt.Errorf(`ent: validator failed for field "User.address": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Region(); ok {
		if err := user.RegionValidator(v); err != nil {
			return &ValidationError{Name: "region", err: fmt.Errorf(`ent: validator failed for field "User.region": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Description(); ok {
		if err := user.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "User.description": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Gender(); ok {
		if err := user.GenderValidator(v); err != nil {
			return &ValidationError{Name: "gender", err: fmt.Errorf(`ent: validator failed for field "User.gender": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Authority(); ok {
		if err := user.AuthorityValidator(v); err != nil {
			return &ValidationError{Name: "authority", err: fmt.Errorf(`ent: validator failed for field "User.authority": %w`, err)}
		}
	}
	if v, ok := uc.mutation.LastLoginIP(); ok {
		if err := user.LastLoginIPValidator(v); err != nil {
			return &ValidationError{Name: "last_login_ip", err: fmt.Errorf(`ent: validator failed for field "User.last_login_ip": %w`, err)}
		}
	}
	if v, ok := uc.mutation.ID(); ok {
		if err := user.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "User.id": %w`, err)}
		}
	}
	return nil
}

func (uc *UserCreate) sqlSave(ctx context.Context) (*User, error) {
	if err := uc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	uc.mutation.id = &_node.ID
	uc.mutation.done = true
	return _node, nil
}

func (uc *UserCreate) createSpec() (*User, *sqlgraph.CreateSpec) {
	var (
		_node = &User{config: uc.config}
		_spec = sqlgraph.NewCreateSpec(user.Table, sqlgraph.NewFieldSpec(user.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = uc.conflict
	if id, ok := uc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := uc.mutation.CreateBy(); ok {
		_spec.SetField(user.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := uc.mutation.UpdateBy(); ok {
		_spec.SetField(user.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := uc.mutation.CreateTime(); ok {
		_spec.SetField(user.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := uc.mutation.UpdateTime(); ok {
		_spec.SetField(user.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := uc.mutation.DeleteTime(); ok {
		_spec.SetField(user.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := uc.mutation.Remark(); ok {
		_spec.SetField(user.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := uc.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := uc.mutation.TenantID(); ok {
		_spec.SetField(user.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := uc.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
		_node.Username = &value
	}
	if value, ok := uc.mutation.Nickname(); ok {
		_spec.SetField(user.FieldNickname, field.TypeString, value)
		_node.Nickname = &value
	}
	if value, ok := uc.mutation.Realname(); ok {
		_spec.SetField(user.FieldRealname, field.TypeString, value)
		_node.Realname = &value
	}
	if value, ok := uc.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
		_node.Email = &value
	}
	if value, ok := uc.mutation.Mobile(); ok {
		_spec.SetField(user.FieldMobile, field.TypeString, value)
		_node.Mobile = &value
	}
	if value, ok := uc.mutation.Telephone(); ok {
		_spec.SetField(user.FieldTelephone, field.TypeString, value)
		_node.Telephone = &value
	}
	if value, ok := uc.mutation.Avatar(); ok {
		_spec.SetField(user.FieldAvatar, field.TypeString, value)
		_node.Avatar = &value
	}
	if value, ok := uc.mutation.Address(); ok {
		_spec.SetField(user.FieldAddress, field.TypeString, value)
		_node.Address = &value
	}
	if value, ok := uc.mutation.Region(); ok {
		_spec.SetField(user.FieldRegion, field.TypeString, value)
		_node.Region = &value
	}
	if value, ok := uc.mutation.Description(); ok {
		_spec.SetField(user.FieldDescription, field.TypeString, value)
		_node.Description = &value
	}
	if value, ok := uc.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeEnum, value)
		_node.Gender = &value
	}
	if value, ok := uc.mutation.Authority(); ok {
		_spec.SetField(user.FieldAuthority, field.TypeEnum, value)
		_node.Authority = &value
	}
	if value, ok := uc.mutation.LastLoginTime(); ok {
		_spec.SetField(user.FieldLastLoginTime, field.TypeTime, value)
		_node.LastLoginTime = &value
	}
	if value, ok := uc.mutation.LastLoginIP(); ok {
		_spec.SetField(user.FieldLastLoginIP, field.TypeString, value)
		_node.LastLoginIP = &value
	}
	if value, ok := uc.mutation.OrgID(); ok {
		_spec.SetField(user.FieldOrgID, field.TypeUint32, value)
		_node.OrgID = &value
	}
	if value, ok := uc.mutation.PositionID(); ok {
		_spec.SetField(user.FieldPositionID, field.TypeUint32, value)
		_node.PositionID = &value
	}
	if value, ok := uc.mutation.WorkID(); ok {
		_spec.SetField(user.FieldWorkID, field.TypeUint32, value)
		_node.WorkID = &value
	}
	if value, ok := uc.mutation.Roles(); ok {
		_spec.SetField(user.FieldRoles, field.TypeJSON, value)
		_node.Roles = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.User.Create().
//		SetCreateBy(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UserUpsert) {
//			SetCreateBy(v+v).
//		}).
//		Exec(ctx)
func (uc *UserCreate) OnConflict(opts ...sql.ConflictOption) *UserUpsertOne {
	uc.conflict = opts
	return &UserUpsertOne{
		create: uc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (uc *UserCreate) OnConflictColumns(columns ...string) *UserUpsertOne {
	uc.conflict = append(uc.conflict, sql.ConflictColumns(columns...))
	return &UserUpsertOne{
		create: uc,
	}
}

type (
	// UserUpsertOne is the builder for "upsert"-ing
	//  one User node.
	UserUpsertOne struct {
		create *UserCreate
	}

	// UserUpsert is the "OnConflict" setter.
	UserUpsert struct {
		*sql.UpdateSet
	}
)

// SetCreateBy sets the "create_by" field.
func (u *UserUpsert) SetCreateBy(v uint32) *UserUpsert {
	u.Set(user.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *UserUpsert) UpdateCreateBy() *UserUpsert {
	u.SetExcluded(user.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *UserUpsert) AddCreateBy(v uint32) *UserUpsert {
	u.Add(user.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *UserUpsert) ClearCreateBy() *UserUpsert {
	u.SetNull(user.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *UserUpsert) SetUpdateBy(v uint32) *UserUpsert {
	u.Set(user.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *UserUpsert) UpdateUpdateBy() *UserUpsert {
	u.SetExcluded(user.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *UserUpsert) AddUpdateBy(v uint32) *UserUpsert {
	u.Add(user.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *UserUpsert) ClearUpdateBy() *UserUpsert {
	u.SetNull(user.FieldUpdateBy)
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *UserUpsert) SetUpdateTime(v time.Time) *UserUpsert {
	u.Set(user.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *UserUpsert) UpdateUpdateTime() *UserUpsert {
	u.SetExcluded(user.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *UserUpsert) ClearUpdateTime() *UserUpsert {
	u.SetNull(user.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *UserUpsert) SetDeleteTime(v time.Time) *UserUpsert {
	u.Set(user.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *UserUpsert) UpdateDeleteTime() *UserUpsert {
	u.SetExcluded(user.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *UserUpsert) ClearDeleteTime() *UserUpsert {
	u.SetNull(user.FieldDeleteTime)
	return u
}

// SetRemark sets the "remark" field.
func (u *UserUpsert) SetRemark(v string) *UserUpsert {
	u.Set(user.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *UserUpsert) UpdateRemark() *UserUpsert {
	u.SetExcluded(user.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *UserUpsert) ClearRemark() *UserUpsert {
	u.SetNull(user.FieldRemark)
	return u
}

// SetStatus sets the "status" field.
func (u *UserUpsert) SetStatus(v user.Status) *UserUpsert {
	u.Set(user.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *UserUpsert) UpdateStatus() *UserUpsert {
	u.SetExcluded(user.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *UserUpsert) ClearStatus() *UserUpsert {
	u.SetNull(user.FieldStatus)
	return u
}

// SetNickname sets the "nickname" field.
func (u *UserUpsert) SetNickname(v string) *UserUpsert {
	u.Set(user.FieldNickname, v)
	return u
}

// UpdateNickname sets the "nickname" field to the value that was provided on create.
func (u *UserUpsert) UpdateNickname() *UserUpsert {
	u.SetExcluded(user.FieldNickname)
	return u
}

// ClearNickname clears the value of the "nickname" field.
func (u *UserUpsert) ClearNickname() *UserUpsert {
	u.SetNull(user.FieldNickname)
	return u
}

// SetRealname sets the "realname" field.
func (u *UserUpsert) SetRealname(v string) *UserUpsert {
	u.Set(user.FieldRealname, v)
	return u
}

// UpdateRealname sets the "realname" field to the value that was provided on create.
func (u *UserUpsert) UpdateRealname() *UserUpsert {
	u.SetExcluded(user.FieldRealname)
	return u
}

// ClearRealname clears the value of the "realname" field.
func (u *UserUpsert) ClearRealname() *UserUpsert {
	u.SetNull(user.FieldRealname)
	return u
}

// SetEmail sets the "email" field.
func (u *UserUpsert) SetEmail(v string) *UserUpsert {
	u.Set(user.FieldEmail, v)
	return u
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *UserUpsert) UpdateEmail() *UserUpsert {
	u.SetExcluded(user.FieldEmail)
	return u
}

// ClearEmail clears the value of the "email" field.
func (u *UserUpsert) ClearEmail() *UserUpsert {
	u.SetNull(user.FieldEmail)
	return u
}

// SetMobile sets the "mobile" field.
func (u *UserUpsert) SetMobile(v string) *UserUpsert {
	u.Set(user.FieldMobile, v)
	return u
}

// UpdateMobile sets the "mobile" field to the value that was provided on create.
func (u *UserUpsert) UpdateMobile() *UserUpsert {
	u.SetExcluded(user.FieldMobile)
	return u
}

// ClearMobile clears the value of the "mobile" field.
func (u *UserUpsert) ClearMobile() *UserUpsert {
	u.SetNull(user.FieldMobile)
	return u
}

// SetTelephone sets the "telephone" field.
func (u *UserUpsert) SetTelephone(v string) *UserUpsert {
	u.Set(user.FieldTelephone, v)
	return u
}

// UpdateTelephone sets the "telephone" field to the value that was provided on create.
func (u *UserUpsert) UpdateTelephone() *UserUpsert {
	u.SetExcluded(user.FieldTelephone)
	return u
}

// ClearTelephone clears the value of the "telephone" field.
func (u *UserUpsert) ClearTelephone() *UserUpsert {
	u.SetNull(user.FieldTelephone)
	return u
}

// SetAvatar sets the "avatar" field.
func (u *UserUpsert) SetAvatar(v string) *UserUpsert {
	u.Set(user.FieldAvatar, v)
	return u
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *UserUpsert) UpdateAvatar() *UserUpsert {
	u.SetExcluded(user.FieldAvatar)
	return u
}

// ClearAvatar clears the value of the "avatar" field.
func (u *UserUpsert) ClearAvatar() *UserUpsert {
	u.SetNull(user.FieldAvatar)
	return u
}

// SetAddress sets the "address" field.
func (u *UserUpsert) SetAddress(v string) *UserUpsert {
	u.Set(user.FieldAddress, v)
	return u
}

// UpdateAddress sets the "address" field to the value that was provided on create.
func (u *UserUpsert) UpdateAddress() *UserUpsert {
	u.SetExcluded(user.FieldAddress)
	return u
}

// ClearAddress clears the value of the "address" field.
func (u *UserUpsert) ClearAddress() *UserUpsert {
	u.SetNull(user.FieldAddress)
	return u
}

// SetRegion sets the "region" field.
func (u *UserUpsert) SetRegion(v string) *UserUpsert {
	u.Set(user.FieldRegion, v)
	return u
}

// UpdateRegion sets the "region" field to the value that was provided on create.
func (u *UserUpsert) UpdateRegion() *UserUpsert {
	u.SetExcluded(user.FieldRegion)
	return u
}

// ClearRegion clears the value of the "region" field.
func (u *UserUpsert) ClearRegion() *UserUpsert {
	u.SetNull(user.FieldRegion)
	return u
}

// SetDescription sets the "description" field.
func (u *UserUpsert) SetDescription(v string) *UserUpsert {
	u.Set(user.FieldDescription, v)
	return u
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *UserUpsert) UpdateDescription() *UserUpsert {
	u.SetExcluded(user.FieldDescription)
	return u
}

// ClearDescription clears the value of the "description" field.
func (u *UserUpsert) ClearDescription() *UserUpsert {
	u.SetNull(user.FieldDescription)
	return u
}

// SetGender sets the "gender" field.
func (u *UserUpsert) SetGender(v user.Gender) *UserUpsert {
	u.Set(user.FieldGender, v)
	return u
}

// UpdateGender sets the "gender" field to the value that was provided on create.
func (u *UserUpsert) UpdateGender() *UserUpsert {
	u.SetExcluded(user.FieldGender)
	return u
}

// ClearGender clears the value of the "gender" field.
func (u *UserUpsert) ClearGender() *UserUpsert {
	u.SetNull(user.FieldGender)
	return u
}

// SetAuthority sets the "authority" field.
func (u *UserUpsert) SetAuthority(v user.Authority) *UserUpsert {
	u.Set(user.FieldAuthority, v)
	return u
}

// UpdateAuthority sets the "authority" field to the value that was provided on create.
func (u *UserUpsert) UpdateAuthority() *UserUpsert {
	u.SetExcluded(user.FieldAuthority)
	return u
}

// ClearAuthority clears the value of the "authority" field.
func (u *UserUpsert) ClearAuthority() *UserUpsert {
	u.SetNull(user.FieldAuthority)
	return u
}

// SetLastLoginTime sets the "last_login_time" field.
func (u *UserUpsert) SetLastLoginTime(v time.Time) *UserUpsert {
	u.Set(user.FieldLastLoginTime, v)
	return u
}

// UpdateLastLoginTime sets the "last_login_time" field to the value that was provided on create.
func (u *UserUpsert) UpdateLastLoginTime() *UserUpsert {
	u.SetExcluded(user.FieldLastLoginTime)
	return u
}

// ClearLastLoginTime clears the value of the "last_login_time" field.
func (u *UserUpsert) ClearLastLoginTime() *UserUpsert {
	u.SetNull(user.FieldLastLoginTime)
	return u
}

// SetLastLoginIP sets the "last_login_ip" field.
func (u *UserUpsert) SetLastLoginIP(v string) *UserUpsert {
	u.Set(user.FieldLastLoginIP, v)
	return u
}

// UpdateLastLoginIP sets the "last_login_ip" field to the value that was provided on create.
func (u *UserUpsert) UpdateLastLoginIP() *UserUpsert {
	u.SetExcluded(user.FieldLastLoginIP)
	return u
}

// ClearLastLoginIP clears the value of the "last_login_ip" field.
func (u *UserUpsert) ClearLastLoginIP() *UserUpsert {
	u.SetNull(user.FieldLastLoginIP)
	return u
}

// SetOrgID sets the "org_id" field.
func (u *UserUpsert) SetOrgID(v uint32) *UserUpsert {
	u.Set(user.FieldOrgID, v)
	return u
}

// UpdateOrgID sets the "org_id" field to the value that was provided on create.
func (u *UserUpsert) UpdateOrgID() *UserUpsert {
	u.SetExcluded(user.FieldOrgID)
	return u
}

// AddOrgID adds v to the "org_id" field.
func (u *UserUpsert) AddOrgID(v uint32) *UserUpsert {
	u.Add(user.FieldOrgID, v)
	return u
}

// ClearOrgID clears the value of the "org_id" field.
func (u *UserUpsert) ClearOrgID() *UserUpsert {
	u.SetNull(user.FieldOrgID)
	return u
}

// SetPositionID sets the "position_id" field.
func (u *UserUpsert) SetPositionID(v uint32) *UserUpsert {
	u.Set(user.FieldPositionID, v)
	return u
}

// UpdatePositionID sets the "position_id" field to the value that was provided on create.
func (u *UserUpsert) UpdatePositionID() *UserUpsert {
	u.SetExcluded(user.FieldPositionID)
	return u
}

// AddPositionID adds v to the "position_id" field.
func (u *UserUpsert) AddPositionID(v uint32) *UserUpsert {
	u.Add(user.FieldPositionID, v)
	return u
}

// ClearPositionID clears the value of the "position_id" field.
func (u *UserUpsert) ClearPositionID() *UserUpsert {
	u.SetNull(user.FieldPositionID)
	return u
}

// SetWorkID sets the "work_id" field.
func (u *UserUpsert) SetWorkID(v uint32) *UserUpsert {
	u.Set(user.FieldWorkID, v)
	return u
}

// UpdateWorkID sets the "work_id" field to the value that was provided on create.
func (u *UserUpsert) UpdateWorkID() *UserUpsert {
	u.SetExcluded(user.FieldWorkID)
	return u
}

// AddWorkID adds v to the "work_id" field.
func (u *UserUpsert) AddWorkID(v uint32) *UserUpsert {
	u.Add(user.FieldWorkID, v)
	return u
}

// ClearWorkID clears the value of the "work_id" field.
func (u *UserUpsert) ClearWorkID() *UserUpsert {
	u.SetNull(user.FieldWorkID)
	return u
}

// SetRoles sets the "roles" field.
func (u *UserUpsert) SetRoles(v []string) *UserUpsert {
	u.Set(user.FieldRoles, v)
	return u
}

// UpdateRoles sets the "roles" field to the value that was provided on create.
func (u *UserUpsert) UpdateRoles() *UserUpsert {
	u.SetExcluded(user.FieldRoles)
	return u
}

// ClearRoles clears the value of the "roles" field.
func (u *UserUpsert) ClearRoles() *UserUpsert {
	u.SetNull(user.FieldRoles)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(user.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *UserUpsertOne) UpdateNewValues() *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(user.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(user.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(user.FieldTenantID)
		}
		if _, exists := u.create.mutation.Username(); exists {
			s.SetIgnore(user.FieldUsername)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.User.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *UserUpsertOne) Ignore() *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UserUpsertOne) DoNothing() *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UserCreate.OnConflict
// documentation for more info.
func (u *UserUpsertOne) Update(set func(*UserUpsert)) *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UserUpsert{UpdateSet: update})
	}))
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *UserUpsertOne) SetCreateBy(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *UserUpsertOne) AddCreateBy(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateCreateBy() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *UserUpsertOne) ClearCreateBy() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *UserUpsertOne) SetUpdateBy(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *UserUpsertOne) AddUpdateBy(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateUpdateBy() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *UserUpsertOne) ClearUpdateBy() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearUpdateBy()
	})
}

// SetUpdateTime sets the "update_time" field.
func (u *UserUpsertOne) SetUpdateTime(v time.Time) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateUpdateTime() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *UserUpsertOne) ClearUpdateTime() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *UserUpsertOne) SetDeleteTime(v time.Time) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateDeleteTime() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *UserUpsertOne) ClearDeleteTime() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearDeleteTime()
	})
}

// SetRemark sets the "remark" field.
func (u *UserUpsertOne) SetRemark(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateRemark() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *UserUpsertOne) ClearRemark() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearRemark()
	})
}

// SetStatus sets the "status" field.
func (u *UserUpsertOne) SetStatus(v user.Status) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateStatus() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *UserUpsertOne) ClearStatus() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearStatus()
	})
}

// SetNickname sets the "nickname" field.
func (u *UserUpsertOne) SetNickname(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetNickname(v)
	})
}

// UpdateNickname sets the "nickname" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateNickname() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateNickname()
	})
}

// ClearNickname clears the value of the "nickname" field.
func (u *UserUpsertOne) ClearNickname() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearNickname()
	})
}

// SetRealname sets the "realname" field.
func (u *UserUpsertOne) SetRealname(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetRealname(v)
	})
}

// UpdateRealname sets the "realname" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateRealname() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRealname()
	})
}

// ClearRealname clears the value of the "realname" field.
func (u *UserUpsertOne) ClearRealname() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearRealname()
	})
}

// SetEmail sets the "email" field.
func (u *UserUpsertOne) SetEmail(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetEmail(v)
	})
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateEmail() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateEmail()
	})
}

// ClearEmail clears the value of the "email" field.
func (u *UserUpsertOne) ClearEmail() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearEmail()
	})
}

// SetMobile sets the "mobile" field.
func (u *UserUpsertOne) SetMobile(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetMobile(v)
	})
}

// UpdateMobile sets the "mobile" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateMobile() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateMobile()
	})
}

// ClearMobile clears the value of the "mobile" field.
func (u *UserUpsertOne) ClearMobile() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearMobile()
	})
}

// SetTelephone sets the "telephone" field.
func (u *UserUpsertOne) SetTelephone(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetTelephone(v)
	})
}

// UpdateTelephone sets the "telephone" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateTelephone() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateTelephone()
	})
}

// ClearTelephone clears the value of the "telephone" field.
func (u *UserUpsertOne) ClearTelephone() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearTelephone()
	})
}

// SetAvatar sets the "avatar" field.
func (u *UserUpsertOne) SetAvatar(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateAvatar() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAvatar()
	})
}

// ClearAvatar clears the value of the "avatar" field.
func (u *UserUpsertOne) ClearAvatar() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearAvatar()
	})
}

// SetAddress sets the "address" field.
func (u *UserUpsertOne) SetAddress(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetAddress(v)
	})
}

// UpdateAddress sets the "address" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateAddress() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAddress()
	})
}

// ClearAddress clears the value of the "address" field.
func (u *UserUpsertOne) ClearAddress() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearAddress()
	})
}

// SetRegion sets the "region" field.
func (u *UserUpsertOne) SetRegion(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetRegion(v)
	})
}

// UpdateRegion sets the "region" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateRegion() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRegion()
	})
}

// ClearRegion clears the value of the "region" field.
func (u *UserUpsertOne) ClearRegion() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearRegion()
	})
}

// SetDescription sets the "description" field.
func (u *UserUpsertOne) SetDescription(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateDescription() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDescription()
	})
}

// ClearDescription clears the value of the "description" field.
func (u *UserUpsertOne) ClearDescription() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearDescription()
	})
}

// SetGender sets the "gender" field.
func (u *UserUpsertOne) SetGender(v user.Gender) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetGender(v)
	})
}

// UpdateGender sets the "gender" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateGender() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateGender()
	})
}

// ClearGender clears the value of the "gender" field.
func (u *UserUpsertOne) ClearGender() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearGender()
	})
}

// SetAuthority sets the "authority" field.
func (u *UserUpsertOne) SetAuthority(v user.Authority) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetAuthority(v)
	})
}

// UpdateAuthority sets the "authority" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateAuthority() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAuthority()
	})
}

// ClearAuthority clears the value of the "authority" field.
func (u *UserUpsertOne) ClearAuthority() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearAuthority()
	})
}

// SetLastLoginTime sets the "last_login_time" field.
func (u *UserUpsertOne) SetLastLoginTime(v time.Time) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetLastLoginTime(v)
	})
}

// UpdateLastLoginTime sets the "last_login_time" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateLastLoginTime() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateLastLoginTime()
	})
}

// ClearLastLoginTime clears the value of the "last_login_time" field.
func (u *UserUpsertOne) ClearLastLoginTime() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearLastLoginTime()
	})
}

// SetLastLoginIP sets the "last_login_ip" field.
func (u *UserUpsertOne) SetLastLoginIP(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetLastLoginIP(v)
	})
}

// UpdateLastLoginIP sets the "last_login_ip" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateLastLoginIP() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateLastLoginIP()
	})
}

// ClearLastLoginIP clears the value of the "last_login_ip" field.
func (u *UserUpsertOne) ClearLastLoginIP() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearLastLoginIP()
	})
}

// SetOrgID sets the "org_id" field.
func (u *UserUpsertOne) SetOrgID(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetOrgID(v)
	})
}

// AddOrgID adds v to the "org_id" field.
func (u *UserUpsertOne) AddOrgID(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.AddOrgID(v)
	})
}

// UpdateOrgID sets the "org_id" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateOrgID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateOrgID()
	})
}

// ClearOrgID clears the value of the "org_id" field.
func (u *UserUpsertOne) ClearOrgID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearOrgID()
	})
}

// SetPositionID sets the "position_id" field.
func (u *UserUpsertOne) SetPositionID(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetPositionID(v)
	})
}

// AddPositionID adds v to the "position_id" field.
func (u *UserUpsertOne) AddPositionID(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.AddPositionID(v)
	})
}

// UpdatePositionID sets the "position_id" field to the value that was provided on create.
func (u *UserUpsertOne) UpdatePositionID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdatePositionID()
	})
}

// ClearPositionID clears the value of the "position_id" field.
func (u *UserUpsertOne) ClearPositionID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearPositionID()
	})
}

// SetWorkID sets the "work_id" field.
func (u *UserUpsertOne) SetWorkID(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetWorkID(v)
	})
}

// AddWorkID adds v to the "work_id" field.
func (u *UserUpsertOne) AddWorkID(v uint32) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.AddWorkID(v)
	})
}

// UpdateWorkID sets the "work_id" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateWorkID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateWorkID()
	})
}

// ClearWorkID clears the value of the "work_id" field.
func (u *UserUpsertOne) ClearWorkID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearWorkID()
	})
}

// SetRoles sets the "roles" field.
func (u *UserUpsertOne) SetRoles(v []string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetRoles(v)
	})
}

// UpdateRoles sets the "roles" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateRoles() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRoles()
	})
}

// ClearRoles clears the value of the "roles" field.
func (u *UserUpsertOne) ClearRoles() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearRoles()
	})
}

// Exec executes the query.
func (u *UserUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for UserCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UserUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *UserUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *UserUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// UserCreateBulk is the builder for creating many User entities in bulk.
type UserCreateBulk struct {
	config
	err      error
	builders []*UserCreate
	conflict []sql.ConflictOption
}

// Save creates the User entities in the database.
func (ucb *UserCreateBulk) Save(ctx context.Context) ([]*User, error) {
	if ucb.err != nil {
		return nil, ucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ucb.builders))
	nodes := make([]*User, len(ucb.builders))
	mutators := make([]Mutator, len(ucb.builders))
	for i := range ucb.builders {
		func(i int, root context.Context) {
			builder := ucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = ucb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ucb *UserCreateBulk) SaveX(ctx context.Context) []*User {
	v, err := ucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucb *UserCreateBulk) Exec(ctx context.Context) error {
	_, err := ucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucb *UserCreateBulk) ExecX(ctx context.Context) {
	if err := ucb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.User.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UserUpsert) {
//			SetCreateBy(v+v).
//		}).
//		Exec(ctx)
func (ucb *UserCreateBulk) OnConflict(opts ...sql.ConflictOption) *UserUpsertBulk {
	ucb.conflict = opts
	return &UserUpsertBulk{
		create: ucb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ucb *UserCreateBulk) OnConflictColumns(columns ...string) *UserUpsertBulk {
	ucb.conflict = append(ucb.conflict, sql.ConflictColumns(columns...))
	return &UserUpsertBulk{
		create: ucb,
	}
}

// UserUpsertBulk is the builder for "upsert"-ing
// a bulk of User nodes.
type UserUpsertBulk struct {
	create *UserCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(user.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *UserUpsertBulk) UpdateNewValues() *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(user.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(user.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(user.FieldTenantID)
			}
			if _, exists := b.mutation.Username(); exists {
				s.SetIgnore(user.FieldUsername)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *UserUpsertBulk) Ignore() *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UserUpsertBulk) DoNothing() *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UserCreateBulk.OnConflict
// documentation for more info.
func (u *UserUpsertBulk) Update(set func(*UserUpsert)) *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UserUpsert{UpdateSet: update})
	}))
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *UserUpsertBulk) SetCreateBy(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *UserUpsertBulk) AddCreateBy(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateCreateBy() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *UserUpsertBulk) ClearCreateBy() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *UserUpsertBulk) SetUpdateBy(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *UserUpsertBulk) AddUpdateBy(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateUpdateBy() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *UserUpsertBulk) ClearUpdateBy() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearUpdateBy()
	})
}

// SetUpdateTime sets the "update_time" field.
func (u *UserUpsertBulk) SetUpdateTime(v time.Time) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateUpdateTime() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *UserUpsertBulk) ClearUpdateTime() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *UserUpsertBulk) SetDeleteTime(v time.Time) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateDeleteTime() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *UserUpsertBulk) ClearDeleteTime() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearDeleteTime()
	})
}

// SetRemark sets the "remark" field.
func (u *UserUpsertBulk) SetRemark(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateRemark() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *UserUpsertBulk) ClearRemark() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearRemark()
	})
}

// SetStatus sets the "status" field.
func (u *UserUpsertBulk) SetStatus(v user.Status) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateStatus() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *UserUpsertBulk) ClearStatus() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearStatus()
	})
}

// SetNickname sets the "nickname" field.
func (u *UserUpsertBulk) SetNickname(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetNickname(v)
	})
}

// UpdateNickname sets the "nickname" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateNickname() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateNickname()
	})
}

// ClearNickname clears the value of the "nickname" field.
func (u *UserUpsertBulk) ClearNickname() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearNickname()
	})
}

// SetRealname sets the "realname" field.
func (u *UserUpsertBulk) SetRealname(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetRealname(v)
	})
}

// UpdateRealname sets the "realname" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateRealname() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRealname()
	})
}

// ClearRealname clears the value of the "realname" field.
func (u *UserUpsertBulk) ClearRealname() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearRealname()
	})
}

// SetEmail sets the "email" field.
func (u *UserUpsertBulk) SetEmail(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetEmail(v)
	})
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateEmail() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateEmail()
	})
}

// ClearEmail clears the value of the "email" field.
func (u *UserUpsertBulk) ClearEmail() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearEmail()
	})
}

// SetMobile sets the "mobile" field.
func (u *UserUpsertBulk) SetMobile(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetMobile(v)
	})
}

// UpdateMobile sets the "mobile" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateMobile() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateMobile()
	})
}

// ClearMobile clears the value of the "mobile" field.
func (u *UserUpsertBulk) ClearMobile() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearMobile()
	})
}

// SetTelephone sets the "telephone" field.
func (u *UserUpsertBulk) SetTelephone(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetTelephone(v)
	})
}

// UpdateTelephone sets the "telephone" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateTelephone() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateTelephone()
	})
}

// ClearTelephone clears the value of the "telephone" field.
func (u *UserUpsertBulk) ClearTelephone() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearTelephone()
	})
}

// SetAvatar sets the "avatar" field.
func (u *UserUpsertBulk) SetAvatar(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateAvatar() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAvatar()
	})
}

// ClearAvatar clears the value of the "avatar" field.
func (u *UserUpsertBulk) ClearAvatar() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearAvatar()
	})
}

// SetAddress sets the "address" field.
func (u *UserUpsertBulk) SetAddress(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetAddress(v)
	})
}

// UpdateAddress sets the "address" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateAddress() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAddress()
	})
}

// ClearAddress clears the value of the "address" field.
func (u *UserUpsertBulk) ClearAddress() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearAddress()
	})
}

// SetRegion sets the "region" field.
func (u *UserUpsertBulk) SetRegion(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetRegion(v)
	})
}

// UpdateRegion sets the "region" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateRegion() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRegion()
	})
}

// ClearRegion clears the value of the "region" field.
func (u *UserUpsertBulk) ClearRegion() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearRegion()
	})
}

// SetDescription sets the "description" field.
func (u *UserUpsertBulk) SetDescription(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateDescription() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDescription()
	})
}

// ClearDescription clears the value of the "description" field.
func (u *UserUpsertBulk) ClearDescription() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearDescription()
	})
}

// SetGender sets the "gender" field.
func (u *UserUpsertBulk) SetGender(v user.Gender) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetGender(v)
	})
}

// UpdateGender sets the "gender" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateGender() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateGender()
	})
}

// ClearGender clears the value of the "gender" field.
func (u *UserUpsertBulk) ClearGender() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearGender()
	})
}

// SetAuthority sets the "authority" field.
func (u *UserUpsertBulk) SetAuthority(v user.Authority) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetAuthority(v)
	})
}

// UpdateAuthority sets the "authority" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateAuthority() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAuthority()
	})
}

// ClearAuthority clears the value of the "authority" field.
func (u *UserUpsertBulk) ClearAuthority() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearAuthority()
	})
}

// SetLastLoginTime sets the "last_login_time" field.
func (u *UserUpsertBulk) SetLastLoginTime(v time.Time) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetLastLoginTime(v)
	})
}

// UpdateLastLoginTime sets the "last_login_time" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateLastLoginTime() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateLastLoginTime()
	})
}

// ClearLastLoginTime clears the value of the "last_login_time" field.
func (u *UserUpsertBulk) ClearLastLoginTime() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearLastLoginTime()
	})
}

// SetLastLoginIP sets the "last_login_ip" field.
func (u *UserUpsertBulk) SetLastLoginIP(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetLastLoginIP(v)
	})
}

// UpdateLastLoginIP sets the "last_login_ip" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateLastLoginIP() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateLastLoginIP()
	})
}

// ClearLastLoginIP clears the value of the "last_login_ip" field.
func (u *UserUpsertBulk) ClearLastLoginIP() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearLastLoginIP()
	})
}

// SetOrgID sets the "org_id" field.
func (u *UserUpsertBulk) SetOrgID(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetOrgID(v)
	})
}

// AddOrgID adds v to the "org_id" field.
func (u *UserUpsertBulk) AddOrgID(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.AddOrgID(v)
	})
}

// UpdateOrgID sets the "org_id" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateOrgID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateOrgID()
	})
}

// ClearOrgID clears the value of the "org_id" field.
func (u *UserUpsertBulk) ClearOrgID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearOrgID()
	})
}

// SetPositionID sets the "position_id" field.
func (u *UserUpsertBulk) SetPositionID(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetPositionID(v)
	})
}

// AddPositionID adds v to the "position_id" field.
func (u *UserUpsertBulk) AddPositionID(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.AddPositionID(v)
	})
}

// UpdatePositionID sets the "position_id" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdatePositionID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdatePositionID()
	})
}

// ClearPositionID clears the value of the "position_id" field.
func (u *UserUpsertBulk) ClearPositionID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearPositionID()
	})
}

// SetWorkID sets the "work_id" field.
func (u *UserUpsertBulk) SetWorkID(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetWorkID(v)
	})
}

// AddWorkID adds v to the "work_id" field.
func (u *UserUpsertBulk) AddWorkID(v uint32) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.AddWorkID(v)
	})
}

// UpdateWorkID sets the "work_id" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateWorkID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateWorkID()
	})
}

// ClearWorkID clears the value of the "work_id" field.
func (u *UserUpsertBulk) ClearWorkID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearWorkID()
	})
}

// SetRoles sets the "roles" field.
func (u *UserUpsertBulk) SetRoles(v []string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetRoles(v)
	})
}

// UpdateRoles sets the "roles" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateRoles() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateRoles()
	})
}

// ClearRoles clears the value of the "roles" field.
func (u *UserUpsertBulk) ClearRoles() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearRoles()
	})
}

// Exec executes the query.
func (u *UserUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the UserCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for UserCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UserUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
