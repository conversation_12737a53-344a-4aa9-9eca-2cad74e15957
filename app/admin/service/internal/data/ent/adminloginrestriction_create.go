// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminLoginRestrictionCreate is the builder for creating a AdminLoginRestriction entity.
type AdminLoginRestrictionCreate struct {
	config
	mutation *AdminLoginRestrictionMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (alrc *AdminLoginRestrictionCreate) SetCreateTime(t time.Time) *AdminLoginRestrictionCreate {
	alrc.mutation.SetCreateTime(t)
	return alrc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableCreateTime(t *time.Time) *AdminLoginRestrictionCreate {
	if t != nil {
		alrc.SetCreateTime(*t)
	}
	return alrc
}

// SetUpdateTime sets the "update_time" field.
func (alrc *AdminLoginRestrictionCreate) SetUpdateTime(t time.Time) *AdminLoginRestrictionCreate {
	alrc.mutation.SetUpdateTime(t)
	return alrc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableUpdateTime(t *time.Time) *AdminLoginRestrictionCreate {
	if t != nil {
		alrc.SetUpdateTime(*t)
	}
	return alrc
}

// SetDeleteTime sets the "delete_time" field.
func (alrc *AdminLoginRestrictionCreate) SetDeleteTime(t time.Time) *AdminLoginRestrictionCreate {
	alrc.mutation.SetDeleteTime(t)
	return alrc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableDeleteTime(t *time.Time) *AdminLoginRestrictionCreate {
	if t != nil {
		alrc.SetDeleteTime(*t)
	}
	return alrc
}

// SetCreateBy sets the "create_by" field.
func (alrc *AdminLoginRestrictionCreate) SetCreateBy(u uint32) *AdminLoginRestrictionCreate {
	alrc.mutation.SetCreateBy(u)
	return alrc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableCreateBy(u *uint32) *AdminLoginRestrictionCreate {
	if u != nil {
		alrc.SetCreateBy(*u)
	}
	return alrc
}

// SetUpdateBy sets the "update_by" field.
func (alrc *AdminLoginRestrictionCreate) SetUpdateBy(u uint32) *AdminLoginRestrictionCreate {
	alrc.mutation.SetUpdateBy(u)
	return alrc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableUpdateBy(u *uint32) *AdminLoginRestrictionCreate {
	if u != nil {
		alrc.SetUpdateBy(*u)
	}
	return alrc
}

// SetTargetID sets the "target_id" field.
func (alrc *AdminLoginRestrictionCreate) SetTargetID(u uint32) *AdminLoginRestrictionCreate {
	alrc.mutation.SetTargetID(u)
	return alrc
}

// SetNillableTargetID sets the "target_id" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableTargetID(u *uint32) *AdminLoginRestrictionCreate {
	if u != nil {
		alrc.SetTargetID(*u)
	}
	return alrc
}

// SetValue sets the "value" field.
func (alrc *AdminLoginRestrictionCreate) SetValue(s string) *AdminLoginRestrictionCreate {
	alrc.mutation.SetValue(s)
	return alrc
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableValue(s *string) *AdminLoginRestrictionCreate {
	if s != nil {
		alrc.SetValue(*s)
	}
	return alrc
}

// SetReason sets the "reason" field.
func (alrc *AdminLoginRestrictionCreate) SetReason(s string) *AdminLoginRestrictionCreate {
	alrc.mutation.SetReason(s)
	return alrc
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableReason(s *string) *AdminLoginRestrictionCreate {
	if s != nil {
		alrc.SetReason(*s)
	}
	return alrc
}

// SetType sets the "type" field.
func (alrc *AdminLoginRestrictionCreate) SetType(a adminloginrestriction.Type) *AdminLoginRestrictionCreate {
	alrc.mutation.SetType(a)
	return alrc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableType(a *adminloginrestriction.Type) *AdminLoginRestrictionCreate {
	if a != nil {
		alrc.SetType(*a)
	}
	return alrc
}

// SetMethod sets the "method" field.
func (alrc *AdminLoginRestrictionCreate) SetMethod(a adminloginrestriction.Method) *AdminLoginRestrictionCreate {
	alrc.mutation.SetMethod(a)
	return alrc
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (alrc *AdminLoginRestrictionCreate) SetNillableMethod(a *adminloginrestriction.Method) *AdminLoginRestrictionCreate {
	if a != nil {
		alrc.SetMethod(*a)
	}
	return alrc
}

// SetID sets the "id" field.
func (alrc *AdminLoginRestrictionCreate) SetID(u uint32) *AdminLoginRestrictionCreate {
	alrc.mutation.SetID(u)
	return alrc
}

// Mutation returns the AdminLoginRestrictionMutation object of the builder.
func (alrc *AdminLoginRestrictionCreate) Mutation() *AdminLoginRestrictionMutation {
	return alrc.mutation
}

// Save creates the AdminLoginRestriction in the database.
func (alrc *AdminLoginRestrictionCreate) Save(ctx context.Context) (*AdminLoginRestriction, error) {
	alrc.defaults()
	return withHooks(ctx, alrc.sqlSave, alrc.mutation, alrc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (alrc *AdminLoginRestrictionCreate) SaveX(ctx context.Context) *AdminLoginRestriction {
	v, err := alrc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (alrc *AdminLoginRestrictionCreate) Exec(ctx context.Context) error {
	_, err := alrc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (alrc *AdminLoginRestrictionCreate) ExecX(ctx context.Context) {
	if err := alrc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (alrc *AdminLoginRestrictionCreate) defaults() {
	if _, ok := alrc.mutation.GetType(); !ok {
		v := adminloginrestriction.DefaultType
		alrc.mutation.SetType(v)
	}
	if _, ok := alrc.mutation.Method(); !ok {
		v := adminloginrestriction.DefaultMethod
		alrc.mutation.SetMethod(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (alrc *AdminLoginRestrictionCreate) check() error {
	if v, ok := alrc.mutation.GetType(); ok {
		if err := adminloginrestriction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.type": %w`, err)}
		}
	}
	if v, ok := alrc.mutation.Method(); ok {
		if err := adminloginrestriction.MethodValidator(v); err != nil {
			return &ValidationError{Name: "method", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.method": %w`, err)}
		}
	}
	if v, ok := alrc.mutation.ID(); ok {
		if err := adminloginrestriction.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "AdminLoginRestriction.id": %w`, err)}
		}
	}
	return nil
}

func (alrc *AdminLoginRestrictionCreate) sqlSave(ctx context.Context) (*AdminLoginRestriction, error) {
	if err := alrc.check(); err != nil {
		return nil, err
	}
	_node, _spec := alrc.createSpec()
	if err := sqlgraph.CreateNode(ctx, alrc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	alrc.mutation.id = &_node.ID
	alrc.mutation.done = true
	return _node, nil
}

func (alrc *AdminLoginRestrictionCreate) createSpec() (*AdminLoginRestriction, *sqlgraph.CreateSpec) {
	var (
		_node = &AdminLoginRestriction{config: alrc.config}
		_spec = sqlgraph.NewCreateSpec(adminloginrestriction.Table, sqlgraph.NewFieldSpec(adminloginrestriction.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = alrc.conflict
	if id, ok := alrc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := alrc.mutation.CreateTime(); ok {
		_spec.SetField(adminloginrestriction.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := alrc.mutation.UpdateTime(); ok {
		_spec.SetField(adminloginrestriction.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := alrc.mutation.DeleteTime(); ok {
		_spec.SetField(adminloginrestriction.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := alrc.mutation.CreateBy(); ok {
		_spec.SetField(adminloginrestriction.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := alrc.mutation.UpdateBy(); ok {
		_spec.SetField(adminloginrestriction.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := alrc.mutation.TargetID(); ok {
		_spec.SetField(adminloginrestriction.FieldTargetID, field.TypeUint32, value)
		_node.TargetID = &value
	}
	if value, ok := alrc.mutation.Value(); ok {
		_spec.SetField(adminloginrestriction.FieldValue, field.TypeString, value)
		_node.Value = &value
	}
	if value, ok := alrc.mutation.Reason(); ok {
		_spec.SetField(adminloginrestriction.FieldReason, field.TypeString, value)
		_node.Reason = &value
	}
	if value, ok := alrc.mutation.GetType(); ok {
		_spec.SetField(adminloginrestriction.FieldType, field.TypeEnum, value)
		_node.Type = &value
	}
	if value, ok := alrc.mutation.Method(); ok {
		_spec.SetField(adminloginrestriction.FieldMethod, field.TypeEnum, value)
		_node.Method = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AdminLoginRestriction.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AdminLoginRestrictionUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (alrc *AdminLoginRestrictionCreate) OnConflict(opts ...sql.ConflictOption) *AdminLoginRestrictionUpsertOne {
	alrc.conflict = opts
	return &AdminLoginRestrictionUpsertOne{
		create: alrc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AdminLoginRestriction.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (alrc *AdminLoginRestrictionCreate) OnConflictColumns(columns ...string) *AdminLoginRestrictionUpsertOne {
	alrc.conflict = append(alrc.conflict, sql.ConflictColumns(columns...))
	return &AdminLoginRestrictionUpsertOne{
		create: alrc,
	}
}

type (
	// AdminLoginRestrictionUpsertOne is the builder for "upsert"-ing
	//  one AdminLoginRestriction node.
	AdminLoginRestrictionUpsertOne struct {
		create *AdminLoginRestrictionCreate
	}

	// AdminLoginRestrictionUpsert is the "OnConflict" setter.
	AdminLoginRestrictionUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *AdminLoginRestrictionUpsert) SetUpdateTime(v time.Time) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateUpdateTime() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *AdminLoginRestrictionUpsert) ClearUpdateTime() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *AdminLoginRestrictionUpsert) SetDeleteTime(v time.Time) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateDeleteTime() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *AdminLoginRestrictionUpsert) ClearDeleteTime() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *AdminLoginRestrictionUpsert) SetCreateBy(v uint32) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateCreateBy() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *AdminLoginRestrictionUpsert) AddCreateBy(v uint32) *AdminLoginRestrictionUpsert {
	u.Add(adminloginrestriction.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *AdminLoginRestrictionUpsert) ClearCreateBy() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *AdminLoginRestrictionUpsert) SetUpdateBy(v uint32) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateUpdateBy() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *AdminLoginRestrictionUpsert) AddUpdateBy(v uint32) *AdminLoginRestrictionUpsert {
	u.Add(adminloginrestriction.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *AdminLoginRestrictionUpsert) ClearUpdateBy() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldUpdateBy)
	return u
}

// SetTargetID sets the "target_id" field.
func (u *AdminLoginRestrictionUpsert) SetTargetID(v uint32) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldTargetID, v)
	return u
}

// UpdateTargetID sets the "target_id" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateTargetID() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldTargetID)
	return u
}

// AddTargetID adds v to the "target_id" field.
func (u *AdminLoginRestrictionUpsert) AddTargetID(v uint32) *AdminLoginRestrictionUpsert {
	u.Add(adminloginrestriction.FieldTargetID, v)
	return u
}

// ClearTargetID clears the value of the "target_id" field.
func (u *AdminLoginRestrictionUpsert) ClearTargetID() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldTargetID)
	return u
}

// SetValue sets the "value" field.
func (u *AdminLoginRestrictionUpsert) SetValue(v string) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldValue, v)
	return u
}

// UpdateValue sets the "value" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateValue() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldValue)
	return u
}

// ClearValue clears the value of the "value" field.
func (u *AdminLoginRestrictionUpsert) ClearValue() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldValue)
	return u
}

// SetReason sets the "reason" field.
func (u *AdminLoginRestrictionUpsert) SetReason(v string) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldReason, v)
	return u
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateReason() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldReason)
	return u
}

// ClearReason clears the value of the "reason" field.
func (u *AdminLoginRestrictionUpsert) ClearReason() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldReason)
	return u
}

// SetType sets the "type" field.
func (u *AdminLoginRestrictionUpsert) SetType(v adminloginrestriction.Type) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateType() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldType)
	return u
}

// ClearType clears the value of the "type" field.
func (u *AdminLoginRestrictionUpsert) ClearType() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldType)
	return u
}

// SetMethod sets the "method" field.
func (u *AdminLoginRestrictionUpsert) SetMethod(v adminloginrestriction.Method) *AdminLoginRestrictionUpsert {
	u.Set(adminloginrestriction.FieldMethod, v)
	return u
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsert) UpdateMethod() *AdminLoginRestrictionUpsert {
	u.SetExcluded(adminloginrestriction.FieldMethod)
	return u
}

// ClearMethod clears the value of the "method" field.
func (u *AdminLoginRestrictionUpsert) ClearMethod() *AdminLoginRestrictionUpsert {
	u.SetNull(adminloginrestriction.FieldMethod)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AdminLoginRestriction.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(adminloginrestriction.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AdminLoginRestrictionUpsertOne) UpdateNewValues() *AdminLoginRestrictionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(adminloginrestriction.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(adminloginrestriction.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AdminLoginRestriction.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AdminLoginRestrictionUpsertOne) Ignore() *AdminLoginRestrictionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AdminLoginRestrictionUpsertOne) DoNothing() *AdminLoginRestrictionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AdminLoginRestrictionCreate.OnConflict
// documentation for more info.
func (u *AdminLoginRestrictionUpsertOne) Update(set func(*AdminLoginRestrictionUpsert)) *AdminLoginRestrictionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AdminLoginRestrictionUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *AdminLoginRestrictionUpsertOne) SetUpdateTime(v time.Time) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateUpdateTime() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *AdminLoginRestrictionUpsertOne) ClearUpdateTime() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *AdminLoginRestrictionUpsertOne) SetDeleteTime(v time.Time) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateDeleteTime() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *AdminLoginRestrictionUpsertOne) ClearDeleteTime() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *AdminLoginRestrictionUpsertOne) SetCreateBy(v uint32) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *AdminLoginRestrictionUpsertOne) AddCreateBy(v uint32) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateCreateBy() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *AdminLoginRestrictionUpsertOne) ClearCreateBy() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *AdminLoginRestrictionUpsertOne) SetUpdateBy(v uint32) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *AdminLoginRestrictionUpsertOne) AddUpdateBy(v uint32) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateUpdateBy() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *AdminLoginRestrictionUpsertOne) ClearUpdateBy() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearUpdateBy()
	})
}

// SetTargetID sets the "target_id" field.
func (u *AdminLoginRestrictionUpsertOne) SetTargetID(v uint32) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetTargetID(v)
	})
}

// AddTargetID adds v to the "target_id" field.
func (u *AdminLoginRestrictionUpsertOne) AddTargetID(v uint32) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.AddTargetID(v)
	})
}

// UpdateTargetID sets the "target_id" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateTargetID() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateTargetID()
	})
}

// ClearTargetID clears the value of the "target_id" field.
func (u *AdminLoginRestrictionUpsertOne) ClearTargetID() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearTargetID()
	})
}

// SetValue sets the "value" field.
func (u *AdminLoginRestrictionUpsertOne) SetValue(v string) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetValue(v)
	})
}

// UpdateValue sets the "value" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateValue() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateValue()
	})
}

// ClearValue clears the value of the "value" field.
func (u *AdminLoginRestrictionUpsertOne) ClearValue() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearValue()
	})
}

// SetReason sets the "reason" field.
func (u *AdminLoginRestrictionUpsertOne) SetReason(v string) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateReason() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateReason()
	})
}

// ClearReason clears the value of the "reason" field.
func (u *AdminLoginRestrictionUpsertOne) ClearReason() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearReason()
	})
}

// SetType sets the "type" field.
func (u *AdminLoginRestrictionUpsertOne) SetType(v adminloginrestriction.Type) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateType() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateType()
	})
}

// ClearType clears the value of the "type" field.
func (u *AdminLoginRestrictionUpsertOne) ClearType() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearType()
	})
}

// SetMethod sets the "method" field.
func (u *AdminLoginRestrictionUpsertOne) SetMethod(v adminloginrestriction.Method) *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertOne) UpdateMethod() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateMethod()
	})
}

// ClearMethod clears the value of the "method" field.
func (u *AdminLoginRestrictionUpsertOne) ClearMethod() *AdminLoginRestrictionUpsertOne {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearMethod()
	})
}

// Exec executes the query.
func (u *AdminLoginRestrictionUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AdminLoginRestrictionCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AdminLoginRestrictionUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AdminLoginRestrictionUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AdminLoginRestrictionUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AdminLoginRestrictionCreateBulk is the builder for creating many AdminLoginRestriction entities in bulk.
type AdminLoginRestrictionCreateBulk struct {
	config
	err      error
	builders []*AdminLoginRestrictionCreate
	conflict []sql.ConflictOption
}

// Save creates the AdminLoginRestriction entities in the database.
func (alrcb *AdminLoginRestrictionCreateBulk) Save(ctx context.Context) ([]*AdminLoginRestriction, error) {
	if alrcb.err != nil {
		return nil, alrcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(alrcb.builders))
	nodes := make([]*AdminLoginRestriction, len(alrcb.builders))
	mutators := make([]Mutator, len(alrcb.builders))
	for i := range alrcb.builders {
		func(i int, root context.Context) {
			builder := alrcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AdminLoginRestrictionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, alrcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = alrcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, alrcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, alrcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (alrcb *AdminLoginRestrictionCreateBulk) SaveX(ctx context.Context) []*AdminLoginRestriction {
	v, err := alrcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (alrcb *AdminLoginRestrictionCreateBulk) Exec(ctx context.Context) error {
	_, err := alrcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (alrcb *AdminLoginRestrictionCreateBulk) ExecX(ctx context.Context) {
	if err := alrcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AdminLoginRestriction.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AdminLoginRestrictionUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (alrcb *AdminLoginRestrictionCreateBulk) OnConflict(opts ...sql.ConflictOption) *AdminLoginRestrictionUpsertBulk {
	alrcb.conflict = opts
	return &AdminLoginRestrictionUpsertBulk{
		create: alrcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AdminLoginRestriction.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (alrcb *AdminLoginRestrictionCreateBulk) OnConflictColumns(columns ...string) *AdminLoginRestrictionUpsertBulk {
	alrcb.conflict = append(alrcb.conflict, sql.ConflictColumns(columns...))
	return &AdminLoginRestrictionUpsertBulk{
		create: alrcb,
	}
}

// AdminLoginRestrictionUpsertBulk is the builder for "upsert"-ing
// a bulk of AdminLoginRestriction nodes.
type AdminLoginRestrictionUpsertBulk struct {
	create *AdminLoginRestrictionCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AdminLoginRestriction.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(adminloginrestriction.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AdminLoginRestrictionUpsertBulk) UpdateNewValues() *AdminLoginRestrictionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(adminloginrestriction.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(adminloginrestriction.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AdminLoginRestriction.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AdminLoginRestrictionUpsertBulk) Ignore() *AdminLoginRestrictionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AdminLoginRestrictionUpsertBulk) DoNothing() *AdminLoginRestrictionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AdminLoginRestrictionCreateBulk.OnConflict
// documentation for more info.
func (u *AdminLoginRestrictionUpsertBulk) Update(set func(*AdminLoginRestrictionUpsert)) *AdminLoginRestrictionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AdminLoginRestrictionUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *AdminLoginRestrictionUpsertBulk) SetUpdateTime(v time.Time) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateUpdateTime() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearUpdateTime() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *AdminLoginRestrictionUpsertBulk) SetDeleteTime(v time.Time) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateDeleteTime() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearDeleteTime() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *AdminLoginRestrictionUpsertBulk) SetCreateBy(v uint32) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *AdminLoginRestrictionUpsertBulk) AddCreateBy(v uint32) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateCreateBy() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearCreateBy() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *AdminLoginRestrictionUpsertBulk) SetUpdateBy(v uint32) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *AdminLoginRestrictionUpsertBulk) AddUpdateBy(v uint32) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateUpdateBy() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearUpdateBy() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearUpdateBy()
	})
}

// SetTargetID sets the "target_id" field.
func (u *AdminLoginRestrictionUpsertBulk) SetTargetID(v uint32) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetTargetID(v)
	})
}

// AddTargetID adds v to the "target_id" field.
func (u *AdminLoginRestrictionUpsertBulk) AddTargetID(v uint32) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.AddTargetID(v)
	})
}

// UpdateTargetID sets the "target_id" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateTargetID() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateTargetID()
	})
}

// ClearTargetID clears the value of the "target_id" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearTargetID() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearTargetID()
	})
}

// SetValue sets the "value" field.
func (u *AdminLoginRestrictionUpsertBulk) SetValue(v string) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetValue(v)
	})
}

// UpdateValue sets the "value" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateValue() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateValue()
	})
}

// ClearValue clears the value of the "value" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearValue() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearValue()
	})
}

// SetReason sets the "reason" field.
func (u *AdminLoginRestrictionUpsertBulk) SetReason(v string) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateReason() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateReason()
	})
}

// ClearReason clears the value of the "reason" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearReason() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearReason()
	})
}

// SetType sets the "type" field.
func (u *AdminLoginRestrictionUpsertBulk) SetType(v adminloginrestriction.Type) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateType() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateType()
	})
}

// ClearType clears the value of the "type" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearType() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearType()
	})
}

// SetMethod sets the "method" field.
func (u *AdminLoginRestrictionUpsertBulk) SetMethod(v adminloginrestriction.Method) *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *AdminLoginRestrictionUpsertBulk) UpdateMethod() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.UpdateMethod()
	})
}

// ClearMethod clears the value of the "method" field.
func (u *AdminLoginRestrictionUpsertBulk) ClearMethod() *AdminLoginRestrictionUpsertBulk {
	return u.Update(func(s *AdminLoginRestrictionUpsert) {
		s.ClearMethod()
	})
}

// Exec executes the query.
func (u *AdminLoginRestrictionUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AdminLoginRestrictionCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AdminLoginRestrictionCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AdminLoginRestrictionUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
