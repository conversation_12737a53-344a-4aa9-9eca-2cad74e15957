// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagecategory"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageCategoryQuery is the builder for querying NotificationMessageCategory entities.
type NotificationMessageCategoryQuery struct {
	config
	ctx          *QueryContext
	order        []notificationmessagecategory.OrderOption
	inters       []Interceptor
	predicates   []predicate.NotificationMessageCategory
	withParent   *NotificationMessageCategoryQuery
	withChildren *NotificationMessageCategoryQuery
	modifiers    []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NotificationMessageCategoryQuery builder.
func (nmcq *NotificationMessageCategoryQuery) Where(ps ...predicate.NotificationMessageCategory) *NotificationMessageCategoryQuery {
	nmcq.predicates = append(nmcq.predicates, ps...)
	return nmcq
}

// Limit the number of records to be returned by this query.
func (nmcq *NotificationMessageCategoryQuery) Limit(limit int) *NotificationMessageCategoryQuery {
	nmcq.ctx.Limit = &limit
	return nmcq
}

// Offset to start from.
func (nmcq *NotificationMessageCategoryQuery) Offset(offset int) *NotificationMessageCategoryQuery {
	nmcq.ctx.Offset = &offset
	return nmcq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (nmcq *NotificationMessageCategoryQuery) Unique(unique bool) *NotificationMessageCategoryQuery {
	nmcq.ctx.Unique = &unique
	return nmcq
}

// Order specifies how the records should be ordered.
func (nmcq *NotificationMessageCategoryQuery) Order(o ...notificationmessagecategory.OrderOption) *NotificationMessageCategoryQuery {
	nmcq.order = append(nmcq.order, o...)
	return nmcq
}

// QueryParent chains the current query on the "parent" edge.
func (nmcq *NotificationMessageCategoryQuery) QueryParent() *NotificationMessageCategoryQuery {
	query := (&NotificationMessageCategoryClient{config: nmcq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := nmcq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := nmcq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(notificationmessagecategory.Table, notificationmessagecategory.FieldID, selector),
			sqlgraph.To(notificationmessagecategory.Table, notificationmessagecategory.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, notificationmessagecategory.ParentTable, notificationmessagecategory.ParentColumn),
		)
		fromU = sqlgraph.SetNeighbors(nmcq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryChildren chains the current query on the "children" edge.
func (nmcq *NotificationMessageCategoryQuery) QueryChildren() *NotificationMessageCategoryQuery {
	query := (&NotificationMessageCategoryClient{config: nmcq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := nmcq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := nmcq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(notificationmessagecategory.Table, notificationmessagecategory.FieldID, selector),
			sqlgraph.To(notificationmessagecategory.Table, notificationmessagecategory.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, notificationmessagecategory.ChildrenTable, notificationmessagecategory.ChildrenColumn),
		)
		fromU = sqlgraph.SetNeighbors(nmcq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first NotificationMessageCategory entity from the query.
// Returns a *NotFoundError when no NotificationMessageCategory was found.
func (nmcq *NotificationMessageCategoryQuery) First(ctx context.Context) (*NotificationMessageCategory, error) {
	nodes, err := nmcq.Limit(1).All(setContextOp(ctx, nmcq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{notificationmessagecategory.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) FirstX(ctx context.Context) *NotificationMessageCategory {
	node, err := nmcq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first NotificationMessageCategory ID from the query.
// Returns a *NotFoundError when no NotificationMessageCategory ID was found.
func (nmcq *NotificationMessageCategoryQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = nmcq.Limit(1).IDs(setContextOp(ctx, nmcq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{notificationmessagecategory.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := nmcq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single NotificationMessageCategory entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one NotificationMessageCategory entity is found.
// Returns a *NotFoundError when no NotificationMessageCategory entities are found.
func (nmcq *NotificationMessageCategoryQuery) Only(ctx context.Context) (*NotificationMessageCategory, error) {
	nodes, err := nmcq.Limit(2).All(setContextOp(ctx, nmcq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{notificationmessagecategory.Label}
	default:
		return nil, &NotSingularError{notificationmessagecategory.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) OnlyX(ctx context.Context) *NotificationMessageCategory {
	node, err := nmcq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only NotificationMessageCategory ID in the query.
// Returns a *NotSingularError when more than one NotificationMessageCategory ID is found.
// Returns a *NotFoundError when no entities are found.
func (nmcq *NotificationMessageCategoryQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = nmcq.Limit(2).IDs(setContextOp(ctx, nmcq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{notificationmessagecategory.Label}
	default:
		err = &NotSingularError{notificationmessagecategory.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := nmcq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of NotificationMessageCategories.
func (nmcq *NotificationMessageCategoryQuery) All(ctx context.Context) ([]*NotificationMessageCategory, error) {
	ctx = setContextOp(ctx, nmcq.ctx, ent.OpQueryAll)
	if err := nmcq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*NotificationMessageCategory, *NotificationMessageCategoryQuery]()
	return withInterceptors[[]*NotificationMessageCategory](ctx, nmcq, qr, nmcq.inters)
}

// AllX is like All, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) AllX(ctx context.Context) []*NotificationMessageCategory {
	nodes, err := nmcq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of NotificationMessageCategory IDs.
func (nmcq *NotificationMessageCategoryQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if nmcq.ctx.Unique == nil && nmcq.path != nil {
		nmcq.Unique(true)
	}
	ctx = setContextOp(ctx, nmcq.ctx, ent.OpQueryIDs)
	if err = nmcq.Select(notificationmessagecategory.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := nmcq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (nmcq *NotificationMessageCategoryQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, nmcq.ctx, ent.OpQueryCount)
	if err := nmcq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, nmcq, querierCount[*NotificationMessageCategoryQuery](), nmcq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) CountX(ctx context.Context) int {
	count, err := nmcq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (nmcq *NotificationMessageCategoryQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, nmcq.ctx, ent.OpQueryExist)
	switch _, err := nmcq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (nmcq *NotificationMessageCategoryQuery) ExistX(ctx context.Context) bool {
	exist, err := nmcq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NotificationMessageCategoryQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (nmcq *NotificationMessageCategoryQuery) Clone() *NotificationMessageCategoryQuery {
	if nmcq == nil {
		return nil
	}
	return &NotificationMessageCategoryQuery{
		config:       nmcq.config,
		ctx:          nmcq.ctx.Clone(),
		order:        append([]notificationmessagecategory.OrderOption{}, nmcq.order...),
		inters:       append([]Interceptor{}, nmcq.inters...),
		predicates:   append([]predicate.NotificationMessageCategory{}, nmcq.predicates...),
		withParent:   nmcq.withParent.Clone(),
		withChildren: nmcq.withChildren.Clone(),
		// clone intermediate query.
		sql:       nmcq.sql.Clone(),
		path:      nmcq.path,
		modifiers: append([]func(*sql.Selector){}, nmcq.modifiers...),
	}
}

// WithParent tells the query-builder to eager-load the nodes that are connected to
// the "parent" edge. The optional arguments are used to configure the query builder of the edge.
func (nmcq *NotificationMessageCategoryQuery) WithParent(opts ...func(*NotificationMessageCategoryQuery)) *NotificationMessageCategoryQuery {
	query := (&NotificationMessageCategoryClient{config: nmcq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	nmcq.withParent = query
	return nmcq
}

// WithChildren tells the query-builder to eager-load the nodes that are connected to
// the "children" edge. The optional arguments are used to configure the query builder of the edge.
func (nmcq *NotificationMessageCategoryQuery) WithChildren(opts ...func(*NotificationMessageCategoryQuery)) *NotificationMessageCategoryQuery {
	query := (&NotificationMessageCategoryClient{config: nmcq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	nmcq.withChildren = query
	return nmcq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.NotificationMessageCategory.Query().
//		GroupBy(notificationmessagecategory.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (nmcq *NotificationMessageCategoryQuery) GroupBy(field string, fields ...string) *NotificationMessageCategoryGroupBy {
	nmcq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NotificationMessageCategoryGroupBy{build: nmcq}
	grbuild.flds = &nmcq.ctx.Fields
	grbuild.label = notificationmessagecategory.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.NotificationMessageCategory.Query().
//		Select(notificationmessagecategory.FieldCreateTime).
//		Scan(ctx, &v)
func (nmcq *NotificationMessageCategoryQuery) Select(fields ...string) *NotificationMessageCategorySelect {
	nmcq.ctx.Fields = append(nmcq.ctx.Fields, fields...)
	sbuild := &NotificationMessageCategorySelect{NotificationMessageCategoryQuery: nmcq}
	sbuild.label = notificationmessagecategory.Label
	sbuild.flds, sbuild.scan = &nmcq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NotificationMessageCategorySelect configured with the given aggregations.
func (nmcq *NotificationMessageCategoryQuery) Aggregate(fns ...AggregateFunc) *NotificationMessageCategorySelect {
	return nmcq.Select().Aggregate(fns...)
}

func (nmcq *NotificationMessageCategoryQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range nmcq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, nmcq); err != nil {
				return err
			}
		}
	}
	for _, f := range nmcq.ctx.Fields {
		if !notificationmessagecategory.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if nmcq.path != nil {
		prev, err := nmcq.path(ctx)
		if err != nil {
			return err
		}
		nmcq.sql = prev
	}
	return nil
}

func (nmcq *NotificationMessageCategoryQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*NotificationMessageCategory, error) {
	var (
		nodes       = []*NotificationMessageCategory{}
		_spec       = nmcq.querySpec()
		loadedTypes = [2]bool{
			nmcq.withParent != nil,
			nmcq.withChildren != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*NotificationMessageCategory).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &NotificationMessageCategory{config: nmcq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(nmcq.modifiers) > 0 {
		_spec.Modifiers = nmcq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, nmcq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := nmcq.withParent; query != nil {
		if err := nmcq.loadParent(ctx, query, nodes, nil,
			func(n *NotificationMessageCategory, e *NotificationMessageCategory) { n.Edges.Parent = e }); err != nil {
			return nil, err
		}
	}
	if query := nmcq.withChildren; query != nil {
		if err := nmcq.loadChildren(ctx, query, nodes,
			func(n *NotificationMessageCategory) { n.Edges.Children = []*NotificationMessageCategory{} },
			func(n *NotificationMessageCategory, e *NotificationMessageCategory) {
				n.Edges.Children = append(n.Edges.Children, e)
			}); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (nmcq *NotificationMessageCategoryQuery) loadParent(ctx context.Context, query *NotificationMessageCategoryQuery, nodes []*NotificationMessageCategory, init func(*NotificationMessageCategory), assign func(*NotificationMessageCategory, *NotificationMessageCategory)) error {
	ids := make([]uint32, 0, len(nodes))
	nodeids := make(map[uint32][]*NotificationMessageCategory)
	for i := range nodes {
		if nodes[i].ParentID == nil {
			continue
		}
		fk := *nodes[i].ParentID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(notificationmessagecategory.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "parent_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (nmcq *NotificationMessageCategoryQuery) loadChildren(ctx context.Context, query *NotificationMessageCategoryQuery, nodes []*NotificationMessageCategory, init func(*NotificationMessageCategory), assign func(*NotificationMessageCategory, *NotificationMessageCategory)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[uint32]*NotificationMessageCategory)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(notificationmessagecategory.FieldParentID)
	}
	query.Where(predicate.NotificationMessageCategory(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(notificationmessagecategory.ChildrenColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ParentID
		if fk == nil {
			return fmt.Errorf(`foreign-key "parent_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "parent_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (nmcq *NotificationMessageCategoryQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := nmcq.querySpec()
	if len(nmcq.modifiers) > 0 {
		_spec.Modifiers = nmcq.modifiers
	}
	_spec.Node.Columns = nmcq.ctx.Fields
	if len(nmcq.ctx.Fields) > 0 {
		_spec.Unique = nmcq.ctx.Unique != nil && *nmcq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, nmcq.driver, _spec)
}

func (nmcq *NotificationMessageCategoryQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(notificationmessagecategory.Table, notificationmessagecategory.Columns, sqlgraph.NewFieldSpec(notificationmessagecategory.FieldID, field.TypeUint32))
	_spec.From = nmcq.sql
	if unique := nmcq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if nmcq.path != nil {
		_spec.Unique = true
	}
	if fields := nmcq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationmessagecategory.FieldID)
		for i := range fields {
			if fields[i] != notificationmessagecategory.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if nmcq.withParent != nil {
			_spec.Node.AddColumnOnce(notificationmessagecategory.FieldParentID)
		}
	}
	if ps := nmcq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := nmcq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := nmcq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := nmcq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (nmcq *NotificationMessageCategoryQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(nmcq.driver.Dialect())
	t1 := builder.Table(notificationmessagecategory.Table)
	columns := nmcq.ctx.Fields
	if len(columns) == 0 {
		columns = notificationmessagecategory.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if nmcq.sql != nil {
		selector = nmcq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if nmcq.ctx.Unique != nil && *nmcq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range nmcq.modifiers {
		m(selector)
	}
	for _, p := range nmcq.predicates {
		p(selector)
	}
	for _, p := range nmcq.order {
		p(selector)
	}
	if offset := nmcq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := nmcq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (nmcq *NotificationMessageCategoryQuery) ForUpdate(opts ...sql.LockOption) *NotificationMessageCategoryQuery {
	if nmcq.driver.Dialect() == dialect.Postgres {
		nmcq.Unique(false)
	}
	nmcq.modifiers = append(nmcq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return nmcq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (nmcq *NotificationMessageCategoryQuery) ForShare(opts ...sql.LockOption) *NotificationMessageCategoryQuery {
	if nmcq.driver.Dialect() == dialect.Postgres {
		nmcq.Unique(false)
	}
	nmcq.modifiers = append(nmcq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return nmcq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (nmcq *NotificationMessageCategoryQuery) Modify(modifiers ...func(s *sql.Selector)) *NotificationMessageCategorySelect {
	nmcq.modifiers = append(nmcq.modifiers, modifiers...)
	return nmcq.Select()
}

// NotificationMessageCategoryGroupBy is the group-by builder for NotificationMessageCategory entities.
type NotificationMessageCategoryGroupBy struct {
	selector
	build *NotificationMessageCategoryQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (nmcgb *NotificationMessageCategoryGroupBy) Aggregate(fns ...AggregateFunc) *NotificationMessageCategoryGroupBy {
	nmcgb.fns = append(nmcgb.fns, fns...)
	return nmcgb
}

// Scan applies the selector query and scans the result into the given value.
func (nmcgb *NotificationMessageCategoryGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nmcgb.build.ctx, ent.OpQueryGroupBy)
	if err := nmcgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationMessageCategoryQuery, *NotificationMessageCategoryGroupBy](ctx, nmcgb.build, nmcgb, nmcgb.build.inters, v)
}

func (nmcgb *NotificationMessageCategoryGroupBy) sqlScan(ctx context.Context, root *NotificationMessageCategoryQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(nmcgb.fns))
	for _, fn := range nmcgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*nmcgb.flds)+len(nmcgb.fns))
		for _, f := range *nmcgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*nmcgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nmcgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NotificationMessageCategorySelect is the builder for selecting fields of NotificationMessageCategory entities.
type NotificationMessageCategorySelect struct {
	*NotificationMessageCategoryQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (nmcs *NotificationMessageCategorySelect) Aggregate(fns ...AggregateFunc) *NotificationMessageCategorySelect {
	nmcs.fns = append(nmcs.fns, fns...)
	return nmcs
}

// Scan applies the selector query and scans the result into the given value.
func (nmcs *NotificationMessageCategorySelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nmcs.ctx, ent.OpQuerySelect)
	if err := nmcs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationMessageCategoryQuery, *NotificationMessageCategorySelect](ctx, nmcs.NotificationMessageCategoryQuery, nmcs, nmcs.inters, v)
}

func (nmcs *NotificationMessageCategorySelect) sqlScan(ctx context.Context, root *NotificationMessageCategoryQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(nmcs.fns))
	for _, fn := range nmcs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*nmcs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nmcs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (nmcs *NotificationMessageCategorySelect) Modify(modifiers ...func(s *sql.Selector)) *NotificationMessageCategorySelect {
	nmcs.modifiers = append(nmcs.modifiers, modifiers...)
	return nmcs
}
