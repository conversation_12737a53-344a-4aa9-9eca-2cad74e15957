// Code generated by ent, DO NOT EDIT.

package file

import (
	"fmt"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the file type in the database.
	Label = "file"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldDeleteTime holds the string denoting the delete_time field in the database.
	FieldDeleteTime = "delete_time"
	// FieldCreateBy holds the string denoting the create_by field in the database.
	FieldCreateBy = "create_by"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldProvider holds the string denoting the provider field in the database.
	FieldProvider = "provider"
	// FieldBucketName holds the string denoting the bucket_name field in the database.
	FieldBucketName = "bucket_name"
	// FieldFileDirectory holds the string denoting the file_directory field in the database.
	FieldFileDirectory = "file_directory"
	// FieldFileGUID holds the string denoting the file_guid field in the database.
	FieldFileGUID = "file_guid"
	// FieldSaveFileName holds the string denoting the save_file_name field in the database.
	FieldSaveFileName = "save_file_name"
	// FieldFileName holds the string denoting the file_name field in the database.
	FieldFileName = "file_name"
	// FieldExtension holds the string denoting the extension field in the database.
	FieldExtension = "extension"
	// FieldSize holds the string denoting the size field in the database.
	FieldSize = "size"
	// FieldSizeFormat holds the string denoting the size_format field in the database.
	FieldSizeFormat = "size_format"
	// FieldLinkURL holds the string denoting the link_url field in the database.
	FieldLinkURL = "link_url"
	// FieldMd5 holds the string denoting the md5 field in the database.
	FieldMd5 = "md5"
	// Table holds the table name of the file in the database.
	Table = "files"
)

// Columns holds all SQL columns for file fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldDeleteTime,
	FieldCreateBy,
	FieldRemark,
	FieldTenantID,
	FieldProvider,
	FieldBucketName,
	FieldFileDirectory,
	FieldFileGUID,
	FieldSaveFileName,
	FieldFileName,
	FieldExtension,
	FieldSize,
	FieldSizeFormat,
	FieldLinkURL,
	FieldMd5,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultRemark holds the default value on creation for the "remark" field.
	DefaultRemark string
	// TenantIDValidator is a validator for the "tenant_id" field. It is called by the builders before save.
	TenantIDValidator func(uint32) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(uint32) error
)

// Provider defines the type for the "provider" enum field.
type Provider string

// Provider values.
const (
	ProviderUnknown Provider = "UNKNOWN"
	ProviderMinIO   Provider = "MINIO"
	ProviderAliyun  Provider = "ALIYUN"
	ProviderQiniu   Provider = "QINIU"
	ProviderTencent Provider = "TENCENT"
	ProviderAWS     Provider = "AWS"
	ProviderGoogle  Provider = "GOOGLE"
	ProviderAzure   Provider = "AZURE"
	ProviderBaidu   Provider = "BAIDU"
	ProviderHuawei  Provider = "HUAWEI"
	ProviderQCloud  Provider = "QCLOUD"
	ProviderLocal   Provider = "LOCAL"
)

func (pr Provider) String() string {
	return string(pr)
}

// ProviderValidator is a validator for the "provider" field enum values. It is called by the builders before save.
func ProviderValidator(pr Provider) error {
	switch pr {
	case ProviderUnknown, ProviderMinIO, ProviderAliyun, ProviderQiniu, ProviderTencent, ProviderAWS, ProviderGoogle, ProviderAzure, ProviderBaidu, ProviderHuawei, ProviderQCloud, ProviderLocal:
		return nil
	default:
		return fmt.Errorf("file: invalid enum value for provider field: %q", pr)
	}
}

// OrderOption defines the ordering options for the File queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByDeleteTime orders the results by the delete_time field.
func ByDeleteTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeleteTime, opts...).ToFunc()
}

// ByCreateBy orders the results by the create_by field.
func ByCreateBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateBy, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByProvider orders the results by the provider field.
func ByProvider(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProvider, opts...).ToFunc()
}

// ByBucketName orders the results by the bucket_name field.
func ByBucketName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBucketName, opts...).ToFunc()
}

// ByFileDirectory orders the results by the file_directory field.
func ByFileDirectory(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileDirectory, opts...).ToFunc()
}

// ByFileGUID orders the results by the file_guid field.
func ByFileGUID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileGUID, opts...).ToFunc()
}

// BySaveFileName orders the results by the save_file_name field.
func BySaveFileName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSaveFileName, opts...).ToFunc()
}

// ByFileName orders the results by the file_name field.
func ByFileName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileName, opts...).ToFunc()
}

// ByExtension orders the results by the extension field.
func ByExtension(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExtension, opts...).ToFunc()
}

// BySize orders the results by the size field.
func BySize(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSize, opts...).ToFunc()
}

// BySizeFormat orders the results by the size_format field.
func BySizeFormat(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSizeFormat, opts...).ToFunc()
}

// ByLinkURL orders the results by the link_url field.
func ByLinkURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLinkURL, opts...).ToFunc()
}

// ByMd5 orders the results by the md5 field.
func ByMd5(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMd5, opts...).ToFunc()
}
