// Code generated by ent, DO NOT EDIT.

package file

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.File {
	return predicate.File(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.File {
	return predicate.File(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.File {
	return predicate.File(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.File {
	return predicate.File(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.File {
	return predicate.File(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.File {
	return predicate.File(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.File {
	return predicate.File(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.File {
	return predicate.File(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.File {
	return predicate.File(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.File {
	return predicate.File(sql.FieldEQ(FieldDeleteTime, v))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.File {
	return predicate.File(sql.FieldEQ(FieldCreateBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldRemark, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v uint32) predicate.File {
	return predicate.File(sql.FieldEQ(FieldTenantID, v))
}

// BucketName applies equality check predicate on the "bucket_name" field. It's identical to BucketNameEQ.
func BucketName(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldBucketName, v))
}

// FileDirectory applies equality check predicate on the "file_directory" field. It's identical to FileDirectoryEQ.
func FileDirectory(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldFileDirectory, v))
}

// FileGUID applies equality check predicate on the "file_guid" field. It's identical to FileGUIDEQ.
func FileGUID(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldFileGUID, v))
}

// SaveFileName applies equality check predicate on the "save_file_name" field. It's identical to SaveFileNameEQ.
func SaveFileName(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldSaveFileName, v))
}

// FileName applies equality check predicate on the "file_name" field. It's identical to FileNameEQ.
func FileName(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldFileName, v))
}

// Extension applies equality check predicate on the "extension" field. It's identical to ExtensionEQ.
func Extension(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldExtension, v))
}

// Size applies equality check predicate on the "size" field. It's identical to SizeEQ.
func Size(v uint64) predicate.File {
	return predicate.File(sql.FieldEQ(FieldSize, v))
}

// SizeFormat applies equality check predicate on the "size_format" field. It's identical to SizeFormatEQ.
func SizeFormat(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldSizeFormat, v))
}

// LinkURL applies equality check predicate on the "link_url" field. It's identical to LinkURLEQ.
func LinkURL(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldLinkURL, v))
}

// Md5 applies equality check predicate on the "md5" field. It's identical to Md5EQ.
func Md5(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldMd5, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.File {
	return predicate.File(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.File {
	return predicate.File(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.File {
	return predicate.File(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.File {
	return predicate.File(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.File {
	return predicate.File(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.File {
	return predicate.File(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.File {
	return predicate.File(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.File {
	return predicate.File(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.File {
	return predicate.File(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.File {
	return predicate.File(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.File {
	return predicate.File(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.File {
	return predicate.File(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.File {
	return predicate.File(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.File {
	return predicate.File(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.File {
	return predicate.File(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.File {
	return predicate.File(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.File {
	return predicate.File(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.File {
	return predicate.File(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldDeleteTime))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.File {
	return predicate.File(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.File {
	return predicate.File(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.File {
	return predicate.File(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.File {
	return predicate.File(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.File {
	return predicate.File(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.File {
	return predicate.File(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldCreateBy))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldRemark, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v uint32) predicate.File {
	return predicate.File(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v uint32) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...uint32) predicate.File {
	return predicate.File(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...uint32) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v uint32) predicate.File {
	return predicate.File(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v uint32) predicate.File {
	return predicate.File(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v uint32) predicate.File {
	return predicate.File(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v uint32) predicate.File {
	return predicate.File(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldTenantID))
}

// ProviderEQ applies the EQ predicate on the "provider" field.
func ProviderEQ(v Provider) predicate.File {
	return predicate.File(sql.FieldEQ(FieldProvider, v))
}

// ProviderNEQ applies the NEQ predicate on the "provider" field.
func ProviderNEQ(v Provider) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldProvider, v))
}

// ProviderIn applies the In predicate on the "provider" field.
func ProviderIn(vs ...Provider) predicate.File {
	return predicate.File(sql.FieldIn(FieldProvider, vs...))
}

// ProviderNotIn applies the NotIn predicate on the "provider" field.
func ProviderNotIn(vs ...Provider) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldProvider, vs...))
}

// ProviderIsNil applies the IsNil predicate on the "provider" field.
func ProviderIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldProvider))
}

// ProviderNotNil applies the NotNil predicate on the "provider" field.
func ProviderNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldProvider))
}

// BucketNameEQ applies the EQ predicate on the "bucket_name" field.
func BucketNameEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldBucketName, v))
}

// BucketNameNEQ applies the NEQ predicate on the "bucket_name" field.
func BucketNameNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldBucketName, v))
}

// BucketNameIn applies the In predicate on the "bucket_name" field.
func BucketNameIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldBucketName, vs...))
}

// BucketNameNotIn applies the NotIn predicate on the "bucket_name" field.
func BucketNameNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldBucketName, vs...))
}

// BucketNameGT applies the GT predicate on the "bucket_name" field.
func BucketNameGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldBucketName, v))
}

// BucketNameGTE applies the GTE predicate on the "bucket_name" field.
func BucketNameGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldBucketName, v))
}

// BucketNameLT applies the LT predicate on the "bucket_name" field.
func BucketNameLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldBucketName, v))
}

// BucketNameLTE applies the LTE predicate on the "bucket_name" field.
func BucketNameLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldBucketName, v))
}

// BucketNameContains applies the Contains predicate on the "bucket_name" field.
func BucketNameContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldBucketName, v))
}

// BucketNameHasPrefix applies the HasPrefix predicate on the "bucket_name" field.
func BucketNameHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldBucketName, v))
}

// BucketNameHasSuffix applies the HasSuffix predicate on the "bucket_name" field.
func BucketNameHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldBucketName, v))
}

// BucketNameIsNil applies the IsNil predicate on the "bucket_name" field.
func BucketNameIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldBucketName))
}

// BucketNameNotNil applies the NotNil predicate on the "bucket_name" field.
func BucketNameNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldBucketName))
}

// BucketNameEqualFold applies the EqualFold predicate on the "bucket_name" field.
func BucketNameEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldBucketName, v))
}

// BucketNameContainsFold applies the ContainsFold predicate on the "bucket_name" field.
func BucketNameContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldBucketName, v))
}

// FileDirectoryEQ applies the EQ predicate on the "file_directory" field.
func FileDirectoryEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldFileDirectory, v))
}

// FileDirectoryNEQ applies the NEQ predicate on the "file_directory" field.
func FileDirectoryNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldFileDirectory, v))
}

// FileDirectoryIn applies the In predicate on the "file_directory" field.
func FileDirectoryIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldFileDirectory, vs...))
}

// FileDirectoryNotIn applies the NotIn predicate on the "file_directory" field.
func FileDirectoryNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldFileDirectory, vs...))
}

// FileDirectoryGT applies the GT predicate on the "file_directory" field.
func FileDirectoryGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldFileDirectory, v))
}

// FileDirectoryGTE applies the GTE predicate on the "file_directory" field.
func FileDirectoryGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldFileDirectory, v))
}

// FileDirectoryLT applies the LT predicate on the "file_directory" field.
func FileDirectoryLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldFileDirectory, v))
}

// FileDirectoryLTE applies the LTE predicate on the "file_directory" field.
func FileDirectoryLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldFileDirectory, v))
}

// FileDirectoryContains applies the Contains predicate on the "file_directory" field.
func FileDirectoryContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldFileDirectory, v))
}

// FileDirectoryHasPrefix applies the HasPrefix predicate on the "file_directory" field.
func FileDirectoryHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldFileDirectory, v))
}

// FileDirectoryHasSuffix applies the HasSuffix predicate on the "file_directory" field.
func FileDirectoryHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldFileDirectory, v))
}

// FileDirectoryIsNil applies the IsNil predicate on the "file_directory" field.
func FileDirectoryIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldFileDirectory))
}

// FileDirectoryNotNil applies the NotNil predicate on the "file_directory" field.
func FileDirectoryNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldFileDirectory))
}

// FileDirectoryEqualFold applies the EqualFold predicate on the "file_directory" field.
func FileDirectoryEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldFileDirectory, v))
}

// FileDirectoryContainsFold applies the ContainsFold predicate on the "file_directory" field.
func FileDirectoryContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldFileDirectory, v))
}

// FileGUIDEQ applies the EQ predicate on the "file_guid" field.
func FileGUIDEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldFileGUID, v))
}

// FileGUIDNEQ applies the NEQ predicate on the "file_guid" field.
func FileGUIDNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldFileGUID, v))
}

// FileGUIDIn applies the In predicate on the "file_guid" field.
func FileGUIDIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldFileGUID, vs...))
}

// FileGUIDNotIn applies the NotIn predicate on the "file_guid" field.
func FileGUIDNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldFileGUID, vs...))
}

// FileGUIDGT applies the GT predicate on the "file_guid" field.
func FileGUIDGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldFileGUID, v))
}

// FileGUIDGTE applies the GTE predicate on the "file_guid" field.
func FileGUIDGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldFileGUID, v))
}

// FileGUIDLT applies the LT predicate on the "file_guid" field.
func FileGUIDLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldFileGUID, v))
}

// FileGUIDLTE applies the LTE predicate on the "file_guid" field.
func FileGUIDLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldFileGUID, v))
}

// FileGUIDContains applies the Contains predicate on the "file_guid" field.
func FileGUIDContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldFileGUID, v))
}

// FileGUIDHasPrefix applies the HasPrefix predicate on the "file_guid" field.
func FileGUIDHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldFileGUID, v))
}

// FileGUIDHasSuffix applies the HasSuffix predicate on the "file_guid" field.
func FileGUIDHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldFileGUID, v))
}

// FileGUIDIsNil applies the IsNil predicate on the "file_guid" field.
func FileGUIDIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldFileGUID))
}

// FileGUIDNotNil applies the NotNil predicate on the "file_guid" field.
func FileGUIDNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldFileGUID))
}

// FileGUIDEqualFold applies the EqualFold predicate on the "file_guid" field.
func FileGUIDEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldFileGUID, v))
}

// FileGUIDContainsFold applies the ContainsFold predicate on the "file_guid" field.
func FileGUIDContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldFileGUID, v))
}

// SaveFileNameEQ applies the EQ predicate on the "save_file_name" field.
func SaveFileNameEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldSaveFileName, v))
}

// SaveFileNameNEQ applies the NEQ predicate on the "save_file_name" field.
func SaveFileNameNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldSaveFileName, v))
}

// SaveFileNameIn applies the In predicate on the "save_file_name" field.
func SaveFileNameIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldSaveFileName, vs...))
}

// SaveFileNameNotIn applies the NotIn predicate on the "save_file_name" field.
func SaveFileNameNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldSaveFileName, vs...))
}

// SaveFileNameGT applies the GT predicate on the "save_file_name" field.
func SaveFileNameGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldSaveFileName, v))
}

// SaveFileNameGTE applies the GTE predicate on the "save_file_name" field.
func SaveFileNameGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldSaveFileName, v))
}

// SaveFileNameLT applies the LT predicate on the "save_file_name" field.
func SaveFileNameLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldSaveFileName, v))
}

// SaveFileNameLTE applies the LTE predicate on the "save_file_name" field.
func SaveFileNameLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldSaveFileName, v))
}

// SaveFileNameContains applies the Contains predicate on the "save_file_name" field.
func SaveFileNameContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldSaveFileName, v))
}

// SaveFileNameHasPrefix applies the HasPrefix predicate on the "save_file_name" field.
func SaveFileNameHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldSaveFileName, v))
}

// SaveFileNameHasSuffix applies the HasSuffix predicate on the "save_file_name" field.
func SaveFileNameHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldSaveFileName, v))
}

// SaveFileNameIsNil applies the IsNil predicate on the "save_file_name" field.
func SaveFileNameIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldSaveFileName))
}

// SaveFileNameNotNil applies the NotNil predicate on the "save_file_name" field.
func SaveFileNameNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldSaveFileName))
}

// SaveFileNameEqualFold applies the EqualFold predicate on the "save_file_name" field.
func SaveFileNameEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldSaveFileName, v))
}

// SaveFileNameContainsFold applies the ContainsFold predicate on the "save_file_name" field.
func SaveFileNameContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldSaveFileName, v))
}

// FileNameEQ applies the EQ predicate on the "file_name" field.
func FileNameEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldFileName, v))
}

// FileNameNEQ applies the NEQ predicate on the "file_name" field.
func FileNameNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldFileName, v))
}

// FileNameIn applies the In predicate on the "file_name" field.
func FileNameIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldFileName, vs...))
}

// FileNameNotIn applies the NotIn predicate on the "file_name" field.
func FileNameNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldFileName, vs...))
}

// FileNameGT applies the GT predicate on the "file_name" field.
func FileNameGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldFileName, v))
}

// FileNameGTE applies the GTE predicate on the "file_name" field.
func FileNameGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldFileName, v))
}

// FileNameLT applies the LT predicate on the "file_name" field.
func FileNameLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldFileName, v))
}

// FileNameLTE applies the LTE predicate on the "file_name" field.
func FileNameLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldFileName, v))
}

// FileNameContains applies the Contains predicate on the "file_name" field.
func FileNameContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldFileName, v))
}

// FileNameHasPrefix applies the HasPrefix predicate on the "file_name" field.
func FileNameHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldFileName, v))
}

// FileNameHasSuffix applies the HasSuffix predicate on the "file_name" field.
func FileNameHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldFileName, v))
}

// FileNameIsNil applies the IsNil predicate on the "file_name" field.
func FileNameIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldFileName))
}

// FileNameNotNil applies the NotNil predicate on the "file_name" field.
func FileNameNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldFileName))
}

// FileNameEqualFold applies the EqualFold predicate on the "file_name" field.
func FileNameEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldFileName, v))
}

// FileNameContainsFold applies the ContainsFold predicate on the "file_name" field.
func FileNameContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldFileName, v))
}

// ExtensionEQ applies the EQ predicate on the "extension" field.
func ExtensionEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldExtension, v))
}

// ExtensionNEQ applies the NEQ predicate on the "extension" field.
func ExtensionNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldExtension, v))
}

// ExtensionIn applies the In predicate on the "extension" field.
func ExtensionIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldExtension, vs...))
}

// ExtensionNotIn applies the NotIn predicate on the "extension" field.
func ExtensionNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldExtension, vs...))
}

// ExtensionGT applies the GT predicate on the "extension" field.
func ExtensionGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldExtension, v))
}

// ExtensionGTE applies the GTE predicate on the "extension" field.
func ExtensionGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldExtension, v))
}

// ExtensionLT applies the LT predicate on the "extension" field.
func ExtensionLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldExtension, v))
}

// ExtensionLTE applies the LTE predicate on the "extension" field.
func ExtensionLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldExtension, v))
}

// ExtensionContains applies the Contains predicate on the "extension" field.
func ExtensionContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldExtension, v))
}

// ExtensionHasPrefix applies the HasPrefix predicate on the "extension" field.
func ExtensionHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldExtension, v))
}

// ExtensionHasSuffix applies the HasSuffix predicate on the "extension" field.
func ExtensionHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldExtension, v))
}

// ExtensionIsNil applies the IsNil predicate on the "extension" field.
func ExtensionIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldExtension))
}

// ExtensionNotNil applies the NotNil predicate on the "extension" field.
func ExtensionNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldExtension))
}

// ExtensionEqualFold applies the EqualFold predicate on the "extension" field.
func ExtensionEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldExtension, v))
}

// ExtensionContainsFold applies the ContainsFold predicate on the "extension" field.
func ExtensionContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldExtension, v))
}

// SizeEQ applies the EQ predicate on the "size" field.
func SizeEQ(v uint64) predicate.File {
	return predicate.File(sql.FieldEQ(FieldSize, v))
}

// SizeNEQ applies the NEQ predicate on the "size" field.
func SizeNEQ(v uint64) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldSize, v))
}

// SizeIn applies the In predicate on the "size" field.
func SizeIn(vs ...uint64) predicate.File {
	return predicate.File(sql.FieldIn(FieldSize, vs...))
}

// SizeNotIn applies the NotIn predicate on the "size" field.
func SizeNotIn(vs ...uint64) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldSize, vs...))
}

// SizeGT applies the GT predicate on the "size" field.
func SizeGT(v uint64) predicate.File {
	return predicate.File(sql.FieldGT(FieldSize, v))
}

// SizeGTE applies the GTE predicate on the "size" field.
func SizeGTE(v uint64) predicate.File {
	return predicate.File(sql.FieldGTE(FieldSize, v))
}

// SizeLT applies the LT predicate on the "size" field.
func SizeLT(v uint64) predicate.File {
	return predicate.File(sql.FieldLT(FieldSize, v))
}

// SizeLTE applies the LTE predicate on the "size" field.
func SizeLTE(v uint64) predicate.File {
	return predicate.File(sql.FieldLTE(FieldSize, v))
}

// SizeIsNil applies the IsNil predicate on the "size" field.
func SizeIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldSize))
}

// SizeNotNil applies the NotNil predicate on the "size" field.
func SizeNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldSize))
}

// SizeFormatEQ applies the EQ predicate on the "size_format" field.
func SizeFormatEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldSizeFormat, v))
}

// SizeFormatNEQ applies the NEQ predicate on the "size_format" field.
func SizeFormatNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldSizeFormat, v))
}

// SizeFormatIn applies the In predicate on the "size_format" field.
func SizeFormatIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldSizeFormat, vs...))
}

// SizeFormatNotIn applies the NotIn predicate on the "size_format" field.
func SizeFormatNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldSizeFormat, vs...))
}

// SizeFormatGT applies the GT predicate on the "size_format" field.
func SizeFormatGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldSizeFormat, v))
}

// SizeFormatGTE applies the GTE predicate on the "size_format" field.
func SizeFormatGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldSizeFormat, v))
}

// SizeFormatLT applies the LT predicate on the "size_format" field.
func SizeFormatLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldSizeFormat, v))
}

// SizeFormatLTE applies the LTE predicate on the "size_format" field.
func SizeFormatLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldSizeFormat, v))
}

// SizeFormatContains applies the Contains predicate on the "size_format" field.
func SizeFormatContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldSizeFormat, v))
}

// SizeFormatHasPrefix applies the HasPrefix predicate on the "size_format" field.
func SizeFormatHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldSizeFormat, v))
}

// SizeFormatHasSuffix applies the HasSuffix predicate on the "size_format" field.
func SizeFormatHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldSizeFormat, v))
}

// SizeFormatIsNil applies the IsNil predicate on the "size_format" field.
func SizeFormatIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldSizeFormat))
}

// SizeFormatNotNil applies the NotNil predicate on the "size_format" field.
func SizeFormatNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldSizeFormat))
}

// SizeFormatEqualFold applies the EqualFold predicate on the "size_format" field.
func SizeFormatEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldSizeFormat, v))
}

// SizeFormatContainsFold applies the ContainsFold predicate on the "size_format" field.
func SizeFormatContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldSizeFormat, v))
}

// LinkURLEQ applies the EQ predicate on the "link_url" field.
func LinkURLEQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldLinkURL, v))
}

// LinkURLNEQ applies the NEQ predicate on the "link_url" field.
func LinkURLNEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldLinkURL, v))
}

// LinkURLIn applies the In predicate on the "link_url" field.
func LinkURLIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldLinkURL, vs...))
}

// LinkURLNotIn applies the NotIn predicate on the "link_url" field.
func LinkURLNotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldLinkURL, vs...))
}

// LinkURLGT applies the GT predicate on the "link_url" field.
func LinkURLGT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldLinkURL, v))
}

// LinkURLGTE applies the GTE predicate on the "link_url" field.
func LinkURLGTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldLinkURL, v))
}

// LinkURLLT applies the LT predicate on the "link_url" field.
func LinkURLLT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldLinkURL, v))
}

// LinkURLLTE applies the LTE predicate on the "link_url" field.
func LinkURLLTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldLinkURL, v))
}

// LinkURLContains applies the Contains predicate on the "link_url" field.
func LinkURLContains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldLinkURL, v))
}

// LinkURLHasPrefix applies the HasPrefix predicate on the "link_url" field.
func LinkURLHasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldLinkURL, v))
}

// LinkURLHasSuffix applies the HasSuffix predicate on the "link_url" field.
func LinkURLHasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldLinkURL, v))
}

// LinkURLIsNil applies the IsNil predicate on the "link_url" field.
func LinkURLIsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldLinkURL))
}

// LinkURLNotNil applies the NotNil predicate on the "link_url" field.
func LinkURLNotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldLinkURL))
}

// LinkURLEqualFold applies the EqualFold predicate on the "link_url" field.
func LinkURLEqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldLinkURL, v))
}

// LinkURLContainsFold applies the ContainsFold predicate on the "link_url" field.
func LinkURLContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldLinkURL, v))
}

// Md5EQ applies the EQ predicate on the "md5" field.
func Md5EQ(v string) predicate.File {
	return predicate.File(sql.FieldEQ(FieldMd5, v))
}

// Md5NEQ applies the NEQ predicate on the "md5" field.
func Md5NEQ(v string) predicate.File {
	return predicate.File(sql.FieldNEQ(FieldMd5, v))
}

// Md5In applies the In predicate on the "md5" field.
func Md5In(vs ...string) predicate.File {
	return predicate.File(sql.FieldIn(FieldMd5, vs...))
}

// Md5NotIn applies the NotIn predicate on the "md5" field.
func Md5NotIn(vs ...string) predicate.File {
	return predicate.File(sql.FieldNotIn(FieldMd5, vs...))
}

// Md5GT applies the GT predicate on the "md5" field.
func Md5GT(v string) predicate.File {
	return predicate.File(sql.FieldGT(FieldMd5, v))
}

// Md5GTE applies the GTE predicate on the "md5" field.
func Md5GTE(v string) predicate.File {
	return predicate.File(sql.FieldGTE(FieldMd5, v))
}

// Md5LT applies the LT predicate on the "md5" field.
func Md5LT(v string) predicate.File {
	return predicate.File(sql.FieldLT(FieldMd5, v))
}

// Md5LTE applies the LTE predicate on the "md5" field.
func Md5LTE(v string) predicate.File {
	return predicate.File(sql.FieldLTE(FieldMd5, v))
}

// Md5Contains applies the Contains predicate on the "md5" field.
func Md5Contains(v string) predicate.File {
	return predicate.File(sql.FieldContains(FieldMd5, v))
}

// Md5HasPrefix applies the HasPrefix predicate on the "md5" field.
func Md5HasPrefix(v string) predicate.File {
	return predicate.File(sql.FieldHasPrefix(FieldMd5, v))
}

// Md5HasSuffix applies the HasSuffix predicate on the "md5" field.
func Md5HasSuffix(v string) predicate.File {
	return predicate.File(sql.FieldHasSuffix(FieldMd5, v))
}

// Md5IsNil applies the IsNil predicate on the "md5" field.
func Md5IsNil() predicate.File {
	return predicate.File(sql.FieldIsNull(FieldMd5))
}

// Md5NotNil applies the NotNil predicate on the "md5" field.
func Md5NotNil() predicate.File {
	return predicate.File(sql.FieldNotNull(FieldMd5))
}

// Md5EqualFold applies the EqualFold predicate on the "md5" field.
func Md5EqualFold(v string) predicate.File {
	return predicate.File(sql.FieldEqualFold(FieldMd5, v))
}

// Md5ContainsFold applies the ContainsFold predicate on the "md5" field.
func Md5ContainsFold(v string) predicate.File {
	return predicate.File(sql.FieldContainsFold(FieldMd5, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.File) predicate.File {
	return predicate.File(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.File) predicate.File {
	return predicate.File(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.File) predicate.File {
	return predicate.File(sql.NotPredicates(p))
}
