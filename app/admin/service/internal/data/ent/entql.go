// Code generated by ent, DO NOT EDIT.

package ent

import (
	"kratos-admin/app/admin/service/internal/data/ent/adminloginlog"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"kratos-admin/app/admin/service/internal/data/ent/apiresource"
	"kratos-admin/app/admin/service/internal/data/ent/department"
	"kratos-admin/app/admin/service/internal/data/ent/dict"
	"kratos-admin/app/admin/service/internal/data/ent/file"
	"kratos-admin/app/admin/service/internal/data/ent/menu"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagecategory"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagerecipient"
	"kratos-admin/app/admin/service/internal/data/ent/organization"
	"kratos-admin/app/admin/service/internal/data/ent/position"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"kratos-admin/app/admin/service/internal/data/ent/privatemessage"
	"kratos-admin/app/admin/service/internal/data/ent/role"
	"kratos-admin/app/admin/service/internal/data/ent/task"
	"kratos-admin/app/admin/service/internal/data/ent/tenant"
	"kratos-admin/app/admin/service/internal/data/ent/user"
	"kratos-admin/app/admin/service/internal/data/ent/usercredential"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/entql"
	"entgo.io/ent/schema/field"
)

// schemaGraph holds a representation of ent/schema at runtime.
var schemaGraph = func() *sqlgraph.Schema {
	graph := &sqlgraph.Schema{Nodes: make([]*sqlgraph.Node, 19)}
	graph.Nodes[0] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   adminloginlog.Table,
			Columns: adminloginlog.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: adminloginlog.FieldID,
			},
		},
		Type: "AdminLoginLog",
		Fields: map[string]*sqlgraph.FieldSpec{
			adminloginlog.FieldCreateTime:     {Type: field.TypeTime, Column: adminloginlog.FieldCreateTime},
			adminloginlog.FieldLoginIP:        {Type: field.TypeString, Column: adminloginlog.FieldLoginIP},
			adminloginlog.FieldLoginMAC:       {Type: field.TypeString, Column: adminloginlog.FieldLoginMAC},
			adminloginlog.FieldLoginTime:      {Type: field.TypeTime, Column: adminloginlog.FieldLoginTime},
			adminloginlog.FieldUserAgent:      {Type: field.TypeString, Column: adminloginlog.FieldUserAgent},
			adminloginlog.FieldBrowserName:    {Type: field.TypeString, Column: adminloginlog.FieldBrowserName},
			adminloginlog.FieldBrowserVersion: {Type: field.TypeString, Column: adminloginlog.FieldBrowserVersion},
			adminloginlog.FieldClientID:       {Type: field.TypeString, Column: adminloginlog.FieldClientID},
			adminloginlog.FieldClientName:     {Type: field.TypeString, Column: adminloginlog.FieldClientName},
			adminloginlog.FieldOsName:         {Type: field.TypeString, Column: adminloginlog.FieldOsName},
			adminloginlog.FieldOsVersion:      {Type: field.TypeString, Column: adminloginlog.FieldOsVersion},
			adminloginlog.FieldUserID:         {Type: field.TypeUint32, Column: adminloginlog.FieldUserID},
			adminloginlog.FieldUsername:       {Type: field.TypeString, Column: adminloginlog.FieldUsername},
			adminloginlog.FieldStatusCode:     {Type: field.TypeInt32, Column: adminloginlog.FieldStatusCode},
			adminloginlog.FieldSuccess:        {Type: field.TypeBool, Column: adminloginlog.FieldSuccess},
			adminloginlog.FieldReason:         {Type: field.TypeString, Column: adminloginlog.FieldReason},
			adminloginlog.FieldLocation:       {Type: field.TypeString, Column: adminloginlog.FieldLocation},
		},
	}
	graph.Nodes[1] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   adminloginrestriction.Table,
			Columns: adminloginrestriction.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: adminloginrestriction.FieldID,
			},
		},
		Type: "AdminLoginRestriction",
		Fields: map[string]*sqlgraph.FieldSpec{
			adminloginrestriction.FieldCreateTime: {Type: field.TypeTime, Column: adminloginrestriction.FieldCreateTime},
			adminloginrestriction.FieldUpdateTime: {Type: field.TypeTime, Column: adminloginrestriction.FieldUpdateTime},
			adminloginrestriction.FieldDeleteTime: {Type: field.TypeTime, Column: adminloginrestriction.FieldDeleteTime},
			adminloginrestriction.FieldCreateBy:   {Type: field.TypeUint32, Column: adminloginrestriction.FieldCreateBy},
			adminloginrestriction.FieldUpdateBy:   {Type: field.TypeUint32, Column: adminloginrestriction.FieldUpdateBy},
			adminloginrestriction.FieldTargetID:   {Type: field.TypeUint32, Column: adminloginrestriction.FieldTargetID},
			adminloginrestriction.FieldValue:      {Type: field.TypeString, Column: adminloginrestriction.FieldValue},
			adminloginrestriction.FieldReason:     {Type: field.TypeString, Column: adminloginrestriction.FieldReason},
			adminloginrestriction.FieldType:       {Type: field.TypeEnum, Column: adminloginrestriction.FieldType},
			adminloginrestriction.FieldMethod:     {Type: field.TypeEnum, Column: adminloginrestriction.FieldMethod},
		},
	}
	graph.Nodes[2] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   adminoperationlog.Table,
			Columns: adminoperationlog.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: adminoperationlog.FieldID,
			},
		},
		Type: "AdminOperationLog",
		Fields: map[string]*sqlgraph.FieldSpec{
			adminoperationlog.FieldCreateTime:     {Type: field.TypeTime, Column: adminoperationlog.FieldCreateTime},
			adminoperationlog.FieldRequestID:      {Type: field.TypeString, Column: adminoperationlog.FieldRequestID},
			adminoperationlog.FieldMethod:         {Type: field.TypeString, Column: adminoperationlog.FieldMethod},
			adminoperationlog.FieldOperation:      {Type: field.TypeString, Column: adminoperationlog.FieldOperation},
			adminoperationlog.FieldPath:           {Type: field.TypeString, Column: adminoperationlog.FieldPath},
			adminoperationlog.FieldReferer:        {Type: field.TypeString, Column: adminoperationlog.FieldReferer},
			adminoperationlog.FieldRequestURI:     {Type: field.TypeString, Column: adminoperationlog.FieldRequestURI},
			adminoperationlog.FieldRequestBody:    {Type: field.TypeString, Column: adminoperationlog.FieldRequestBody},
			adminoperationlog.FieldRequestHeader:  {Type: field.TypeString, Column: adminoperationlog.FieldRequestHeader},
			adminoperationlog.FieldResponse:       {Type: field.TypeString, Column: adminoperationlog.FieldResponse},
			adminoperationlog.FieldCostTime:       {Type: field.TypeFloat64, Column: adminoperationlog.FieldCostTime},
			adminoperationlog.FieldUserID:         {Type: field.TypeUint32, Column: adminoperationlog.FieldUserID},
			adminoperationlog.FieldUsername:       {Type: field.TypeString, Column: adminoperationlog.FieldUsername},
			adminoperationlog.FieldClientIP:       {Type: field.TypeString, Column: adminoperationlog.FieldClientIP},
			adminoperationlog.FieldStatusCode:     {Type: field.TypeInt32, Column: adminoperationlog.FieldStatusCode},
			adminoperationlog.FieldReason:         {Type: field.TypeString, Column: adminoperationlog.FieldReason},
			adminoperationlog.FieldSuccess:        {Type: field.TypeBool, Column: adminoperationlog.FieldSuccess},
			adminoperationlog.FieldLocation:       {Type: field.TypeString, Column: adminoperationlog.FieldLocation},
			adminoperationlog.FieldUserAgent:      {Type: field.TypeString, Column: adminoperationlog.FieldUserAgent},
			adminoperationlog.FieldBrowserName:    {Type: field.TypeString, Column: adminoperationlog.FieldBrowserName},
			adminoperationlog.FieldBrowserVersion: {Type: field.TypeString, Column: adminoperationlog.FieldBrowserVersion},
			adminoperationlog.FieldClientID:       {Type: field.TypeString, Column: adminoperationlog.FieldClientID},
			adminoperationlog.FieldClientName:     {Type: field.TypeString, Column: adminoperationlog.FieldClientName},
			adminoperationlog.FieldOsName:         {Type: field.TypeString, Column: adminoperationlog.FieldOsName},
			adminoperationlog.FieldOsVersion:      {Type: field.TypeString, Column: adminoperationlog.FieldOsVersion},
		},
	}
	graph.Nodes[3] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   apiresource.Table,
			Columns: apiresource.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: apiresource.FieldID,
			},
		},
		Type: "ApiResource",
		Fields: map[string]*sqlgraph.FieldSpec{
			apiresource.FieldCreateTime:        {Type: field.TypeTime, Column: apiresource.FieldCreateTime},
			apiresource.FieldUpdateTime:        {Type: field.TypeTime, Column: apiresource.FieldUpdateTime},
			apiresource.FieldDeleteTime:        {Type: field.TypeTime, Column: apiresource.FieldDeleteTime},
			apiresource.FieldCreateBy:          {Type: field.TypeUint32, Column: apiresource.FieldCreateBy},
			apiresource.FieldUpdateBy:          {Type: field.TypeUint32, Column: apiresource.FieldUpdateBy},
			apiresource.FieldDescription:       {Type: field.TypeString, Column: apiresource.FieldDescription},
			apiresource.FieldModule:            {Type: field.TypeString, Column: apiresource.FieldModule},
			apiresource.FieldModuleDescription: {Type: field.TypeString, Column: apiresource.FieldModuleDescription},
			apiresource.FieldOperation:         {Type: field.TypeString, Column: apiresource.FieldOperation},
			apiresource.FieldPath:              {Type: field.TypeString, Column: apiresource.FieldPath},
			apiresource.FieldMethod:            {Type: field.TypeString, Column: apiresource.FieldMethod},
		},
	}
	graph.Nodes[4] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   department.Table,
			Columns: department.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: department.FieldID,
			},
		},
		Type: "Department",
		Fields: map[string]*sqlgraph.FieldSpec{
			department.FieldCreateTime:     {Type: field.TypeTime, Column: department.FieldCreateTime},
			department.FieldUpdateTime:     {Type: field.TypeTime, Column: department.FieldUpdateTime},
			department.FieldDeleteTime:     {Type: field.TypeTime, Column: department.FieldDeleteTime},
			department.FieldStatus:         {Type: field.TypeEnum, Column: department.FieldStatus},
			department.FieldCreateBy:       {Type: field.TypeUint32, Column: department.FieldCreateBy},
			department.FieldUpdateBy:       {Type: field.TypeUint32, Column: department.FieldUpdateBy},
			department.FieldRemark:         {Type: field.TypeString, Column: department.FieldRemark},
			department.FieldTenantID:       {Type: field.TypeUint32, Column: department.FieldTenantID},
			department.FieldName:           {Type: field.TypeString, Column: department.FieldName},
			department.FieldParentID:       {Type: field.TypeUint32, Column: department.FieldParentID},
			department.FieldOrganizationID: {Type: field.TypeUint32, Column: department.FieldOrganizationID},
			department.FieldSortID:         {Type: field.TypeInt32, Column: department.FieldSortID},
		},
	}
	graph.Nodes[5] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   dict.Table,
			Columns: dict.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: dict.FieldID,
			},
		},
		Type: "Dict",
		Fields: map[string]*sqlgraph.FieldSpec{
			dict.FieldCreateTime:    {Type: field.TypeTime, Column: dict.FieldCreateTime},
			dict.FieldUpdateTime:    {Type: field.TypeTime, Column: dict.FieldUpdateTime},
			dict.FieldDeleteTime:    {Type: field.TypeTime, Column: dict.FieldDeleteTime},
			dict.FieldStatus:        {Type: field.TypeEnum, Column: dict.FieldStatus},
			dict.FieldCreateBy:      {Type: field.TypeUint32, Column: dict.FieldCreateBy},
			dict.FieldUpdateBy:      {Type: field.TypeUint32, Column: dict.FieldUpdateBy},
			dict.FieldRemark:        {Type: field.TypeString, Column: dict.FieldRemark},
			dict.FieldTenantID:      {Type: field.TypeUint32, Column: dict.FieldTenantID},
			dict.FieldKey:           {Type: field.TypeString, Column: dict.FieldKey},
			dict.FieldCategory:      {Type: field.TypeString, Column: dict.FieldCategory},
			dict.FieldCategoryDesc:  {Type: field.TypeString, Column: dict.FieldCategoryDesc},
			dict.FieldValue:         {Type: field.TypeString, Column: dict.FieldValue},
			dict.FieldValueDesc:     {Type: field.TypeString, Column: dict.FieldValueDesc},
			dict.FieldValueDataType: {Type: field.TypeString, Column: dict.FieldValueDataType},
			dict.FieldSortID:        {Type: field.TypeInt32, Column: dict.FieldSortID},
		},
	}
	graph.Nodes[6] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   file.Table,
			Columns: file.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: file.FieldID,
			},
		},
		Type: "File",
		Fields: map[string]*sqlgraph.FieldSpec{
			file.FieldCreateTime:    {Type: field.TypeTime, Column: file.FieldCreateTime},
			file.FieldUpdateTime:    {Type: field.TypeTime, Column: file.FieldUpdateTime},
			file.FieldDeleteTime:    {Type: field.TypeTime, Column: file.FieldDeleteTime},
			file.FieldCreateBy:      {Type: field.TypeUint32, Column: file.FieldCreateBy},
			file.FieldRemark:        {Type: field.TypeString, Column: file.FieldRemark},
			file.FieldTenantID:      {Type: field.TypeUint32, Column: file.FieldTenantID},
			file.FieldProvider:      {Type: field.TypeEnum, Column: file.FieldProvider},
			file.FieldBucketName:    {Type: field.TypeString, Column: file.FieldBucketName},
			file.FieldFileDirectory: {Type: field.TypeString, Column: file.FieldFileDirectory},
			file.FieldFileGUID:      {Type: field.TypeString, Column: file.FieldFileGUID},
			file.FieldSaveFileName:  {Type: field.TypeString, Column: file.FieldSaveFileName},
			file.FieldFileName:      {Type: field.TypeString, Column: file.FieldFileName},
			file.FieldExtension:     {Type: field.TypeString, Column: file.FieldExtension},
			file.FieldSize:          {Type: field.TypeUint64, Column: file.FieldSize},
			file.FieldSizeFormat:    {Type: field.TypeString, Column: file.FieldSizeFormat},
			file.FieldLinkURL:       {Type: field.TypeString, Column: file.FieldLinkURL},
			file.FieldMd5:           {Type: field.TypeString, Column: file.FieldMd5},
		},
	}
	graph.Nodes[7] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   menu.Table,
			Columns: menu.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt32,
				Column: menu.FieldID,
			},
		},
		Type: "Menu",
		Fields: map[string]*sqlgraph.FieldSpec{
			menu.FieldStatus:     {Type: field.TypeEnum, Column: menu.FieldStatus},
			menu.FieldCreateTime: {Type: field.TypeTime, Column: menu.FieldCreateTime},
			menu.FieldUpdateTime: {Type: field.TypeTime, Column: menu.FieldUpdateTime},
			menu.FieldDeleteTime: {Type: field.TypeTime, Column: menu.FieldDeleteTime},
			menu.FieldCreateBy:   {Type: field.TypeUint32, Column: menu.FieldCreateBy},
			menu.FieldUpdateBy:   {Type: field.TypeUint32, Column: menu.FieldUpdateBy},
			menu.FieldRemark:     {Type: field.TypeString, Column: menu.FieldRemark},
			menu.FieldParentID:   {Type: field.TypeInt32, Column: menu.FieldParentID},
			menu.FieldType:       {Type: field.TypeEnum, Column: menu.FieldType},
			menu.FieldPath:       {Type: field.TypeString, Column: menu.FieldPath},
			menu.FieldRedirect:   {Type: field.TypeString, Column: menu.FieldRedirect},
			menu.FieldAlias:      {Type: field.TypeString, Column: menu.FieldAlias},
			menu.FieldName:       {Type: field.TypeString, Column: menu.FieldName},
			menu.FieldComponent:  {Type: field.TypeString, Column: menu.FieldComponent},
			menu.FieldMeta:       {Type: field.TypeJSON, Column: menu.FieldMeta},
		},
	}
	graph.Nodes[8] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   notificationmessage.Table,
			Columns: notificationmessage.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: notificationmessage.FieldID,
			},
		},
		Type: "NotificationMessage",
		Fields: map[string]*sqlgraph.FieldSpec{
			notificationmessage.FieldCreateTime: {Type: field.TypeTime, Column: notificationmessage.FieldCreateTime},
			notificationmessage.FieldUpdateTime: {Type: field.TypeTime, Column: notificationmessage.FieldUpdateTime},
			notificationmessage.FieldDeleteTime: {Type: field.TypeTime, Column: notificationmessage.FieldDeleteTime},
			notificationmessage.FieldCreateBy:   {Type: field.TypeUint32, Column: notificationmessage.FieldCreateBy},
			notificationmessage.FieldUpdateBy:   {Type: field.TypeUint32, Column: notificationmessage.FieldUpdateBy},
			notificationmessage.FieldTenantID:   {Type: field.TypeUint32, Column: notificationmessage.FieldTenantID},
			notificationmessage.FieldSubject:    {Type: field.TypeString, Column: notificationmessage.FieldSubject},
			notificationmessage.FieldContent:    {Type: field.TypeString, Column: notificationmessage.FieldContent},
			notificationmessage.FieldCategoryID: {Type: field.TypeUint32, Column: notificationmessage.FieldCategoryID},
			notificationmessage.FieldStatus:     {Type: field.TypeEnum, Column: notificationmessage.FieldStatus},
		},
	}
	graph.Nodes[9] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   notificationmessagecategory.Table,
			Columns: notificationmessagecategory.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: notificationmessagecategory.FieldID,
			},
		},
		Type: "NotificationMessageCategory",
		Fields: map[string]*sqlgraph.FieldSpec{
			notificationmessagecategory.FieldCreateTime: {Type: field.TypeTime, Column: notificationmessagecategory.FieldCreateTime},
			notificationmessagecategory.FieldUpdateTime: {Type: field.TypeTime, Column: notificationmessagecategory.FieldUpdateTime},
			notificationmessagecategory.FieldDeleteTime: {Type: field.TypeTime, Column: notificationmessagecategory.FieldDeleteTime},
			notificationmessagecategory.FieldCreateBy:   {Type: field.TypeUint32, Column: notificationmessagecategory.FieldCreateBy},
			notificationmessagecategory.FieldUpdateBy:   {Type: field.TypeUint32, Column: notificationmessagecategory.FieldUpdateBy},
			notificationmessagecategory.FieldRemark:     {Type: field.TypeString, Column: notificationmessagecategory.FieldRemark},
			notificationmessagecategory.FieldTenantID:   {Type: field.TypeUint32, Column: notificationmessagecategory.FieldTenantID},
			notificationmessagecategory.FieldName:       {Type: field.TypeString, Column: notificationmessagecategory.FieldName},
			notificationmessagecategory.FieldCode:       {Type: field.TypeString, Column: notificationmessagecategory.FieldCode},
			notificationmessagecategory.FieldSortID:     {Type: field.TypeInt32, Column: notificationmessagecategory.FieldSortID},
			notificationmessagecategory.FieldEnable:     {Type: field.TypeBool, Column: notificationmessagecategory.FieldEnable},
			notificationmessagecategory.FieldParentID:   {Type: field.TypeUint32, Column: notificationmessagecategory.FieldParentID},
		},
	}
	graph.Nodes[10] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   notificationmessagerecipient.Table,
			Columns: notificationmessagerecipient.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: notificationmessagerecipient.FieldID,
			},
		},
		Type: "NotificationMessageRecipient",
		Fields: map[string]*sqlgraph.FieldSpec{
			notificationmessagerecipient.FieldCreateTime:  {Type: field.TypeTime, Column: notificationmessagerecipient.FieldCreateTime},
			notificationmessagerecipient.FieldUpdateTime:  {Type: field.TypeTime, Column: notificationmessagerecipient.FieldUpdateTime},
			notificationmessagerecipient.FieldDeleteTime:  {Type: field.TypeTime, Column: notificationmessagerecipient.FieldDeleteTime},
			notificationmessagerecipient.FieldTenantID:    {Type: field.TypeUint32, Column: notificationmessagerecipient.FieldTenantID},
			notificationmessagerecipient.FieldMessageID:   {Type: field.TypeUint32, Column: notificationmessagerecipient.FieldMessageID},
			notificationmessagerecipient.FieldRecipientID: {Type: field.TypeUint32, Column: notificationmessagerecipient.FieldRecipientID},
			notificationmessagerecipient.FieldStatus:      {Type: field.TypeEnum, Column: notificationmessagerecipient.FieldStatus},
		},
	}
	graph.Nodes[11] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   organization.Table,
			Columns: organization.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: organization.FieldID,
			},
		},
		Type: "Organization",
		Fields: map[string]*sqlgraph.FieldSpec{
			organization.FieldCreateTime: {Type: field.TypeTime, Column: organization.FieldCreateTime},
			organization.FieldUpdateTime: {Type: field.TypeTime, Column: organization.FieldUpdateTime},
			organization.FieldDeleteTime: {Type: field.TypeTime, Column: organization.FieldDeleteTime},
			organization.FieldStatus:     {Type: field.TypeEnum, Column: organization.FieldStatus},
			organization.FieldCreateBy:   {Type: field.TypeUint32, Column: organization.FieldCreateBy},
			organization.FieldUpdateBy:   {Type: field.TypeUint32, Column: organization.FieldUpdateBy},
			organization.FieldRemark:     {Type: field.TypeString, Column: organization.FieldRemark},
			organization.FieldTenantID:   {Type: field.TypeUint32, Column: organization.FieldTenantID},
			organization.FieldName:       {Type: field.TypeString, Column: organization.FieldName},
			organization.FieldParentID:   {Type: field.TypeUint32, Column: organization.FieldParentID},
			organization.FieldSortID:     {Type: field.TypeInt32, Column: organization.FieldSortID},
		},
	}
	graph.Nodes[12] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   position.Table,
			Columns: position.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: position.FieldID,
			},
		},
		Type: "Position",
		Fields: map[string]*sqlgraph.FieldSpec{
			position.FieldCreateTime: {Type: field.TypeTime, Column: position.FieldCreateTime},
			position.FieldUpdateTime: {Type: field.TypeTime, Column: position.FieldUpdateTime},
			position.FieldDeleteTime: {Type: field.TypeTime, Column: position.FieldDeleteTime},
			position.FieldStatus:     {Type: field.TypeEnum, Column: position.FieldStatus},
			position.FieldCreateBy:   {Type: field.TypeUint32, Column: position.FieldCreateBy},
			position.FieldUpdateBy:   {Type: field.TypeUint32, Column: position.FieldUpdateBy},
			position.FieldRemark:     {Type: field.TypeString, Column: position.FieldRemark},
			position.FieldTenantID:   {Type: field.TypeUint32, Column: position.FieldTenantID},
			position.FieldName:       {Type: field.TypeString, Column: position.FieldName},
			position.FieldCode:       {Type: field.TypeString, Column: position.FieldCode},
			position.FieldParentID:   {Type: field.TypeUint32, Column: position.FieldParentID},
			position.FieldSortID:     {Type: field.TypeInt32, Column: position.FieldSortID},
		},
	}
	graph.Nodes[13] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   privatemessage.Table,
			Columns: privatemessage.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: privatemessage.FieldID,
			},
		},
		Type: "PrivateMessage",
		Fields: map[string]*sqlgraph.FieldSpec{
			privatemessage.FieldCreateTime: {Type: field.TypeTime, Column: privatemessage.FieldCreateTime},
			privatemessage.FieldUpdateTime: {Type: field.TypeTime, Column: privatemessage.FieldUpdateTime},
			privatemessage.FieldDeleteTime: {Type: field.TypeTime, Column: privatemessage.FieldDeleteTime},
			privatemessage.FieldTenantID:   {Type: field.TypeUint32, Column: privatemessage.FieldTenantID},
			privatemessage.FieldSubject:    {Type: field.TypeString, Column: privatemessage.FieldSubject},
			privatemessage.FieldContent:    {Type: field.TypeString, Column: privatemessage.FieldContent},
			privatemessage.FieldStatus:     {Type: field.TypeEnum, Column: privatemessage.FieldStatus},
			privatemessage.FieldSenderID:   {Type: field.TypeUint32, Column: privatemessage.FieldSenderID},
			privatemessage.FieldReceiverID: {Type: field.TypeUint32, Column: privatemessage.FieldReceiverID},
		},
	}
	graph.Nodes[14] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   role.Table,
			Columns: role.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: role.FieldID,
			},
		},
		Type: "Role",
		Fields: map[string]*sqlgraph.FieldSpec{
			role.FieldCreateTime: {Type: field.TypeTime, Column: role.FieldCreateTime},
			role.FieldUpdateTime: {Type: field.TypeTime, Column: role.FieldUpdateTime},
			role.FieldDeleteTime: {Type: field.TypeTime, Column: role.FieldDeleteTime},
			role.FieldStatus:     {Type: field.TypeEnum, Column: role.FieldStatus},
			role.FieldCreateBy:   {Type: field.TypeUint32, Column: role.FieldCreateBy},
			role.FieldUpdateBy:   {Type: field.TypeUint32, Column: role.FieldUpdateBy},
			role.FieldRemark:     {Type: field.TypeString, Column: role.FieldRemark},
			role.FieldTenantID:   {Type: field.TypeUint32, Column: role.FieldTenantID},
			role.FieldName:       {Type: field.TypeString, Column: role.FieldName},
			role.FieldCode:       {Type: field.TypeString, Column: role.FieldCode},
			role.FieldParentID:   {Type: field.TypeUint32, Column: role.FieldParentID},
			role.FieldSortID:     {Type: field.TypeInt32, Column: role.FieldSortID},
			role.FieldMenus:      {Type: field.TypeJSON, Column: role.FieldMenus},
			role.FieldApis:       {Type: field.TypeJSON, Column: role.FieldApis},
		},
	}
	graph.Nodes[15] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   task.Table,
			Columns: task.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: task.FieldID,
			},
		},
		Type: "Task",
		Fields: map[string]*sqlgraph.FieldSpec{
			task.FieldCreateTime:  {Type: field.TypeTime, Column: task.FieldCreateTime},
			task.FieldUpdateTime:  {Type: field.TypeTime, Column: task.FieldUpdateTime},
			task.FieldDeleteTime:  {Type: field.TypeTime, Column: task.FieldDeleteTime},
			task.FieldCreateBy:    {Type: field.TypeUint32, Column: task.FieldCreateBy},
			task.FieldUpdateBy:    {Type: field.TypeUint32, Column: task.FieldUpdateBy},
			task.FieldRemark:      {Type: field.TypeString, Column: task.FieldRemark},
			task.FieldTenantID:    {Type: field.TypeUint32, Column: task.FieldTenantID},
			task.FieldType:        {Type: field.TypeEnum, Column: task.FieldType},
			task.FieldTypeName:    {Type: field.TypeString, Column: task.FieldTypeName},
			task.FieldTaskPayload: {Type: field.TypeString, Column: task.FieldTaskPayload},
			task.FieldCronSpec:    {Type: field.TypeString, Column: task.FieldCronSpec},
			task.FieldTaskOptions: {Type: field.TypeJSON, Column: task.FieldTaskOptions},
			task.FieldEnable:      {Type: field.TypeBool, Column: task.FieldEnable},
		},
	}
	graph.Nodes[16] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   tenant.Table,
			Columns: tenant.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: tenant.FieldID,
			},
		},
		Type: "Tenant",
		Fields: map[string]*sqlgraph.FieldSpec{
			tenant.FieldCreateTime:     {Type: field.TypeTime, Column: tenant.FieldCreateTime},
			tenant.FieldUpdateTime:     {Type: field.TypeTime, Column: tenant.FieldUpdateTime},
			tenant.FieldDeleteTime:     {Type: field.TypeTime, Column: tenant.FieldDeleteTime},
			tenant.FieldStatus:         {Type: field.TypeEnum, Column: tenant.FieldStatus},
			tenant.FieldCreateBy:       {Type: field.TypeUint32, Column: tenant.FieldCreateBy},
			tenant.FieldUpdateBy:       {Type: field.TypeUint32, Column: tenant.FieldUpdateBy},
			tenant.FieldRemark:         {Type: field.TypeString, Column: tenant.FieldRemark},
			tenant.FieldName:           {Type: field.TypeString, Column: tenant.FieldName},
			tenant.FieldCode:           {Type: field.TypeString, Column: tenant.FieldCode},
			tenant.FieldMemberCount:    {Type: field.TypeInt32, Column: tenant.FieldMemberCount},
			tenant.FieldSubscriptionAt: {Type: field.TypeTime, Column: tenant.FieldSubscriptionAt},
			tenant.FieldUnsubscribeAt:  {Type: field.TypeTime, Column: tenant.FieldUnsubscribeAt},
		},
	}
	graph.Nodes[17] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   user.Table,
			Columns: user.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: user.FieldID,
			},
		},
		Type: "User",
		Fields: map[string]*sqlgraph.FieldSpec{
			user.FieldCreateBy:      {Type: field.TypeUint32, Column: user.FieldCreateBy},
			user.FieldUpdateBy:      {Type: field.TypeUint32, Column: user.FieldUpdateBy},
			user.FieldCreateTime:    {Type: field.TypeTime, Column: user.FieldCreateTime},
			user.FieldUpdateTime:    {Type: field.TypeTime, Column: user.FieldUpdateTime},
			user.FieldDeleteTime:    {Type: field.TypeTime, Column: user.FieldDeleteTime},
			user.FieldRemark:        {Type: field.TypeString, Column: user.FieldRemark},
			user.FieldStatus:        {Type: field.TypeEnum, Column: user.FieldStatus},
			user.FieldTenantID:      {Type: field.TypeUint32, Column: user.FieldTenantID},
			user.FieldUsername:      {Type: field.TypeString, Column: user.FieldUsername},
			user.FieldNickname:      {Type: field.TypeString, Column: user.FieldNickname},
			user.FieldRealname:      {Type: field.TypeString, Column: user.FieldRealname},
			user.FieldEmail:         {Type: field.TypeString, Column: user.FieldEmail},
			user.FieldMobile:        {Type: field.TypeString, Column: user.FieldMobile},
			user.FieldTelephone:     {Type: field.TypeString, Column: user.FieldTelephone},
			user.FieldAvatar:        {Type: field.TypeString, Column: user.FieldAvatar},
			user.FieldAddress:       {Type: field.TypeString, Column: user.FieldAddress},
			user.FieldRegion:        {Type: field.TypeString, Column: user.FieldRegion},
			user.FieldDescription:   {Type: field.TypeString, Column: user.FieldDescription},
			user.FieldGender:        {Type: field.TypeEnum, Column: user.FieldGender},
			user.FieldAuthority:     {Type: field.TypeEnum, Column: user.FieldAuthority},
			user.FieldLastLoginTime: {Type: field.TypeTime, Column: user.FieldLastLoginTime},
			user.FieldLastLoginIP:   {Type: field.TypeString, Column: user.FieldLastLoginIP},
			user.FieldOrgID:         {Type: field.TypeUint32, Column: user.FieldOrgID},
			user.FieldPositionID:    {Type: field.TypeUint32, Column: user.FieldPositionID},
			user.FieldWorkID:        {Type: field.TypeUint32, Column: user.FieldWorkID},
			user.FieldRoles:         {Type: field.TypeJSON, Column: user.FieldRoles},
		},
	}
	graph.Nodes[18] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   usercredential.Table,
			Columns: usercredential.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeUint32,
				Column: usercredential.FieldID,
			},
		},
		Type: "UserCredential",
		Fields: map[string]*sqlgraph.FieldSpec{
			usercredential.FieldCreateTime:     {Type: field.TypeTime, Column: usercredential.FieldCreateTime},
			usercredential.FieldUpdateTime:     {Type: field.TypeTime, Column: usercredential.FieldUpdateTime},
			usercredential.FieldDeleteTime:     {Type: field.TypeTime, Column: usercredential.FieldDeleteTime},
			usercredential.FieldTenantID:       {Type: field.TypeUint32, Column: usercredential.FieldTenantID},
			usercredential.FieldUserID:         {Type: field.TypeUint32, Column: usercredential.FieldUserID},
			usercredential.FieldIdentityType:   {Type: field.TypeEnum, Column: usercredential.FieldIdentityType},
			usercredential.FieldIdentifier:     {Type: field.TypeString, Column: usercredential.FieldIdentifier},
			usercredential.FieldCredentialType: {Type: field.TypeEnum, Column: usercredential.FieldCredentialType},
			usercredential.FieldCredential:     {Type: field.TypeString, Column: usercredential.FieldCredential},
			usercredential.FieldIsPrimary:      {Type: field.TypeBool, Column: usercredential.FieldIsPrimary},
			usercredential.FieldStatus:         {Type: field.TypeEnum, Column: usercredential.FieldStatus},
			usercredential.FieldExtraInfo:      {Type: field.TypeString, Column: usercredential.FieldExtraInfo},
			usercredential.FieldActivateToken:  {Type: field.TypeString, Column: usercredential.FieldActivateToken},
			usercredential.FieldResetToken:     {Type: field.TypeString, Column: usercredential.FieldResetToken},
		},
	}
	graph.MustAddE(
		"parent",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   department.ParentTable,
			Columns: []string{department.ParentColumn},
			Bidi:    false,
		},
		"Department",
		"Department",
	)
	graph.MustAddE(
		"children",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
		},
		"Department",
		"Department",
	)
	graph.MustAddE(
		"parent",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
		},
		"Menu",
		"Menu",
	)
	graph.MustAddE(
		"children",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
		},
		"Menu",
		"Menu",
	)
	graph.MustAddE(
		"parent",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   notificationmessagecategory.ParentTable,
			Columns: []string{notificationmessagecategory.ParentColumn},
			Bidi:    false,
		},
		"NotificationMessageCategory",
		"NotificationMessageCategory",
	)
	graph.MustAddE(
		"children",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   notificationmessagecategory.ChildrenTable,
			Columns: []string{notificationmessagecategory.ChildrenColumn},
			Bidi:    false,
		},
		"NotificationMessageCategory",
		"NotificationMessageCategory",
	)
	graph.MustAddE(
		"parent",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
		},
		"Organization",
		"Organization",
	)
	graph.MustAddE(
		"children",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
		},
		"Organization",
		"Organization",
	)
	graph.MustAddE(
		"parent",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   position.ParentTable,
			Columns: []string{position.ParentColumn},
			Bidi:    false,
		},
		"Position",
		"Position",
	)
	graph.MustAddE(
		"children",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   position.ChildrenTable,
			Columns: []string{position.ChildrenColumn},
			Bidi:    false,
		},
		"Position",
		"Position",
	)
	graph.MustAddE(
		"parent",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
		},
		"Role",
		"Role",
	)
	graph.MustAddE(
		"children",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
		},
		"Role",
		"Role",
	)
	return graph
}()

// predicateAdder wraps the addPredicate method.
// All update, update-one and query builders implement this interface.
type predicateAdder interface {
	addPredicate(func(s *sql.Selector))
}

// addPredicate implements the predicateAdder interface.
func (allq *AdminLoginLogQuery) addPredicate(pred func(s *sql.Selector)) {
	allq.predicates = append(allq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the AdminLoginLogQuery builder.
func (allq *AdminLoginLogQuery) Filter() *AdminLoginLogFilter {
	return &AdminLoginLogFilter{config: allq.config, predicateAdder: allq}
}

// addPredicate implements the predicateAdder interface.
func (m *AdminLoginLogMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the AdminLoginLogMutation builder.
func (m *AdminLoginLogMutation) Filter() *AdminLoginLogFilter {
	return &AdminLoginLogFilter{config: m.config, predicateAdder: m}
}

// AdminLoginLogFilter provides a generic filtering capability at runtime for AdminLoginLogQuery.
type AdminLoginLogFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *AdminLoginLogFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[0].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *AdminLoginLogFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(adminloginlog.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *AdminLoginLogFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(adminloginlog.FieldCreateTime))
}

// WhereLoginIP applies the entql string predicate on the login_ip field.
func (f *AdminLoginLogFilter) WhereLoginIP(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldLoginIP))
}

// WhereLoginMAC applies the entql string predicate on the login_mac field.
func (f *AdminLoginLogFilter) WhereLoginMAC(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldLoginMAC))
}

// WhereLoginTime applies the entql time.Time predicate on the login_time field.
func (f *AdminLoginLogFilter) WhereLoginTime(p entql.TimeP) {
	f.Where(p.Field(adminloginlog.FieldLoginTime))
}

// WhereUserAgent applies the entql string predicate on the user_agent field.
func (f *AdminLoginLogFilter) WhereUserAgent(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldUserAgent))
}

// WhereBrowserName applies the entql string predicate on the browser_name field.
func (f *AdminLoginLogFilter) WhereBrowserName(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldBrowserName))
}

// WhereBrowserVersion applies the entql string predicate on the browser_version field.
func (f *AdminLoginLogFilter) WhereBrowserVersion(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldBrowserVersion))
}

// WhereClientID applies the entql string predicate on the client_id field.
func (f *AdminLoginLogFilter) WhereClientID(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldClientID))
}

// WhereClientName applies the entql string predicate on the client_name field.
func (f *AdminLoginLogFilter) WhereClientName(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldClientName))
}

// WhereOsName applies the entql string predicate on the os_name field.
func (f *AdminLoginLogFilter) WhereOsName(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldOsName))
}

// WhereOsVersion applies the entql string predicate on the os_version field.
func (f *AdminLoginLogFilter) WhereOsVersion(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldOsVersion))
}

// WhereUserID applies the entql uint32 predicate on the user_id field.
func (f *AdminLoginLogFilter) WhereUserID(p entql.Uint32P) {
	f.Where(p.Field(adminloginlog.FieldUserID))
}

// WhereUsername applies the entql string predicate on the username field.
func (f *AdminLoginLogFilter) WhereUsername(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldUsername))
}

// WhereStatusCode applies the entql int32 predicate on the status_code field.
func (f *AdminLoginLogFilter) WhereStatusCode(p entql.Int32P) {
	f.Where(p.Field(adminloginlog.FieldStatusCode))
}

// WhereSuccess applies the entql bool predicate on the success field.
func (f *AdminLoginLogFilter) WhereSuccess(p entql.BoolP) {
	f.Where(p.Field(adminloginlog.FieldSuccess))
}

// WhereReason applies the entql string predicate on the reason field.
func (f *AdminLoginLogFilter) WhereReason(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldReason))
}

// WhereLocation applies the entql string predicate on the location field.
func (f *AdminLoginLogFilter) WhereLocation(p entql.StringP) {
	f.Where(p.Field(adminloginlog.FieldLocation))
}

// addPredicate implements the predicateAdder interface.
func (alrq *AdminLoginRestrictionQuery) addPredicate(pred func(s *sql.Selector)) {
	alrq.predicates = append(alrq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the AdminLoginRestrictionQuery builder.
func (alrq *AdminLoginRestrictionQuery) Filter() *AdminLoginRestrictionFilter {
	return &AdminLoginRestrictionFilter{config: alrq.config, predicateAdder: alrq}
}

// addPredicate implements the predicateAdder interface.
func (m *AdminLoginRestrictionMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the AdminLoginRestrictionMutation builder.
func (m *AdminLoginRestrictionMutation) Filter() *AdminLoginRestrictionFilter {
	return &AdminLoginRestrictionFilter{config: m.config, predicateAdder: m}
}

// AdminLoginRestrictionFilter provides a generic filtering capability at runtime for AdminLoginRestrictionQuery.
type AdminLoginRestrictionFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *AdminLoginRestrictionFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[1].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *AdminLoginRestrictionFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(adminloginrestriction.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *AdminLoginRestrictionFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(adminloginrestriction.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *AdminLoginRestrictionFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(adminloginrestriction.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *AdminLoginRestrictionFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(adminloginrestriction.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *AdminLoginRestrictionFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(adminloginrestriction.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *AdminLoginRestrictionFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(adminloginrestriction.FieldUpdateBy))
}

// WhereTargetID applies the entql uint32 predicate on the target_id field.
func (f *AdminLoginRestrictionFilter) WhereTargetID(p entql.Uint32P) {
	f.Where(p.Field(adminloginrestriction.FieldTargetID))
}

// WhereValue applies the entql string predicate on the value field.
func (f *AdminLoginRestrictionFilter) WhereValue(p entql.StringP) {
	f.Where(p.Field(adminloginrestriction.FieldValue))
}

// WhereReason applies the entql string predicate on the reason field.
func (f *AdminLoginRestrictionFilter) WhereReason(p entql.StringP) {
	f.Where(p.Field(adminloginrestriction.FieldReason))
}

// WhereType applies the entql string predicate on the type field.
func (f *AdminLoginRestrictionFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(adminloginrestriction.FieldType))
}

// WhereMethod applies the entql string predicate on the method field.
func (f *AdminLoginRestrictionFilter) WhereMethod(p entql.StringP) {
	f.Where(p.Field(adminloginrestriction.FieldMethod))
}

// addPredicate implements the predicateAdder interface.
func (aolq *AdminOperationLogQuery) addPredicate(pred func(s *sql.Selector)) {
	aolq.predicates = append(aolq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the AdminOperationLogQuery builder.
func (aolq *AdminOperationLogQuery) Filter() *AdminOperationLogFilter {
	return &AdminOperationLogFilter{config: aolq.config, predicateAdder: aolq}
}

// addPredicate implements the predicateAdder interface.
func (m *AdminOperationLogMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the AdminOperationLogMutation builder.
func (m *AdminOperationLogMutation) Filter() *AdminOperationLogFilter {
	return &AdminOperationLogFilter{config: m.config, predicateAdder: m}
}

// AdminOperationLogFilter provides a generic filtering capability at runtime for AdminOperationLogQuery.
type AdminOperationLogFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *AdminOperationLogFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[2].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *AdminOperationLogFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(adminoperationlog.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *AdminOperationLogFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(adminoperationlog.FieldCreateTime))
}

// WhereRequestID applies the entql string predicate on the request_id field.
func (f *AdminOperationLogFilter) WhereRequestID(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldRequestID))
}

// WhereMethod applies the entql string predicate on the method field.
func (f *AdminOperationLogFilter) WhereMethod(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldMethod))
}

// WhereOperation applies the entql string predicate on the operation field.
func (f *AdminOperationLogFilter) WhereOperation(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldOperation))
}

// WherePath applies the entql string predicate on the path field.
func (f *AdminOperationLogFilter) WherePath(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldPath))
}

// WhereReferer applies the entql string predicate on the referer field.
func (f *AdminOperationLogFilter) WhereReferer(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldReferer))
}

// WhereRequestURI applies the entql string predicate on the request_uri field.
func (f *AdminOperationLogFilter) WhereRequestURI(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldRequestURI))
}

// WhereRequestBody applies the entql string predicate on the request_body field.
func (f *AdminOperationLogFilter) WhereRequestBody(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldRequestBody))
}

// WhereRequestHeader applies the entql string predicate on the request_header field.
func (f *AdminOperationLogFilter) WhereRequestHeader(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldRequestHeader))
}

// WhereResponse applies the entql string predicate on the response field.
func (f *AdminOperationLogFilter) WhereResponse(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldResponse))
}

// WhereCostTime applies the entql float64 predicate on the cost_time field.
func (f *AdminOperationLogFilter) WhereCostTime(p entql.Float64P) {
	f.Where(p.Field(adminoperationlog.FieldCostTime))
}

// WhereUserID applies the entql uint32 predicate on the user_id field.
func (f *AdminOperationLogFilter) WhereUserID(p entql.Uint32P) {
	f.Where(p.Field(adminoperationlog.FieldUserID))
}

// WhereUsername applies the entql string predicate on the username field.
func (f *AdminOperationLogFilter) WhereUsername(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldUsername))
}

// WhereClientIP applies the entql string predicate on the client_ip field.
func (f *AdminOperationLogFilter) WhereClientIP(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldClientIP))
}

// WhereStatusCode applies the entql int32 predicate on the status_code field.
func (f *AdminOperationLogFilter) WhereStatusCode(p entql.Int32P) {
	f.Where(p.Field(adminoperationlog.FieldStatusCode))
}

// WhereReason applies the entql string predicate on the reason field.
func (f *AdminOperationLogFilter) WhereReason(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldReason))
}

// WhereSuccess applies the entql bool predicate on the success field.
func (f *AdminOperationLogFilter) WhereSuccess(p entql.BoolP) {
	f.Where(p.Field(adminoperationlog.FieldSuccess))
}

// WhereLocation applies the entql string predicate on the location field.
func (f *AdminOperationLogFilter) WhereLocation(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldLocation))
}

// WhereUserAgent applies the entql string predicate on the user_agent field.
func (f *AdminOperationLogFilter) WhereUserAgent(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldUserAgent))
}

// WhereBrowserName applies the entql string predicate on the browser_name field.
func (f *AdminOperationLogFilter) WhereBrowserName(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldBrowserName))
}

// WhereBrowserVersion applies the entql string predicate on the browser_version field.
func (f *AdminOperationLogFilter) WhereBrowserVersion(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldBrowserVersion))
}

// WhereClientID applies the entql string predicate on the client_id field.
func (f *AdminOperationLogFilter) WhereClientID(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldClientID))
}

// WhereClientName applies the entql string predicate on the client_name field.
func (f *AdminOperationLogFilter) WhereClientName(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldClientName))
}

// WhereOsName applies the entql string predicate on the os_name field.
func (f *AdminOperationLogFilter) WhereOsName(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldOsName))
}

// WhereOsVersion applies the entql string predicate on the os_version field.
func (f *AdminOperationLogFilter) WhereOsVersion(p entql.StringP) {
	f.Where(p.Field(adminoperationlog.FieldOsVersion))
}

// addPredicate implements the predicateAdder interface.
func (arq *ApiResourceQuery) addPredicate(pred func(s *sql.Selector)) {
	arq.predicates = append(arq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the ApiResourceQuery builder.
func (arq *ApiResourceQuery) Filter() *ApiResourceFilter {
	return &ApiResourceFilter{config: arq.config, predicateAdder: arq}
}

// addPredicate implements the predicateAdder interface.
func (m *ApiResourceMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the ApiResourceMutation builder.
func (m *ApiResourceMutation) Filter() *ApiResourceFilter {
	return &ApiResourceFilter{config: m.config, predicateAdder: m}
}

// ApiResourceFilter provides a generic filtering capability at runtime for ApiResourceQuery.
type ApiResourceFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *ApiResourceFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[3].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *ApiResourceFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(apiresource.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *ApiResourceFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(apiresource.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *ApiResourceFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(apiresource.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *ApiResourceFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(apiresource.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *ApiResourceFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(apiresource.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *ApiResourceFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(apiresource.FieldUpdateBy))
}

// WhereDescription applies the entql string predicate on the description field.
func (f *ApiResourceFilter) WhereDescription(p entql.StringP) {
	f.Where(p.Field(apiresource.FieldDescription))
}

// WhereModule applies the entql string predicate on the module field.
func (f *ApiResourceFilter) WhereModule(p entql.StringP) {
	f.Where(p.Field(apiresource.FieldModule))
}

// WhereModuleDescription applies the entql string predicate on the module_description field.
func (f *ApiResourceFilter) WhereModuleDescription(p entql.StringP) {
	f.Where(p.Field(apiresource.FieldModuleDescription))
}

// WhereOperation applies the entql string predicate on the operation field.
func (f *ApiResourceFilter) WhereOperation(p entql.StringP) {
	f.Where(p.Field(apiresource.FieldOperation))
}

// WherePath applies the entql string predicate on the path field.
func (f *ApiResourceFilter) WherePath(p entql.StringP) {
	f.Where(p.Field(apiresource.FieldPath))
}

// WhereMethod applies the entql string predicate on the method field.
func (f *ApiResourceFilter) WhereMethod(p entql.StringP) {
	f.Where(p.Field(apiresource.FieldMethod))
}

// addPredicate implements the predicateAdder interface.
func (dq *DepartmentQuery) addPredicate(pred func(s *sql.Selector)) {
	dq.predicates = append(dq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the DepartmentQuery builder.
func (dq *DepartmentQuery) Filter() *DepartmentFilter {
	return &DepartmentFilter{config: dq.config, predicateAdder: dq}
}

// addPredicate implements the predicateAdder interface.
func (m *DepartmentMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the DepartmentMutation builder.
func (m *DepartmentMutation) Filter() *DepartmentFilter {
	return &DepartmentFilter{config: m.config, predicateAdder: m}
}

// DepartmentFilter provides a generic filtering capability at runtime for DepartmentQuery.
type DepartmentFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *DepartmentFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[4].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *DepartmentFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(department.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *DepartmentFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(department.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *DepartmentFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(department.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *DepartmentFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(department.FieldDeleteTime))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *DepartmentFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(department.FieldStatus))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *DepartmentFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(department.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *DepartmentFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(department.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *DepartmentFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(department.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *DepartmentFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(department.FieldTenantID))
}

// WhereName applies the entql string predicate on the name field.
func (f *DepartmentFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(department.FieldName))
}

// WhereParentID applies the entql uint32 predicate on the parent_id field.
func (f *DepartmentFilter) WhereParentID(p entql.Uint32P) {
	f.Where(p.Field(department.FieldParentID))
}

// WhereOrganizationID applies the entql uint32 predicate on the organization_id field.
func (f *DepartmentFilter) WhereOrganizationID(p entql.Uint32P) {
	f.Where(p.Field(department.FieldOrganizationID))
}

// WhereSortID applies the entql int32 predicate on the sort_id field.
func (f *DepartmentFilter) WhereSortID(p entql.Int32P) {
	f.Where(p.Field(department.FieldSortID))
}

// WhereHasParent applies a predicate to check if query has an edge parent.
func (f *DepartmentFilter) WhereHasParent() {
	f.Where(entql.HasEdge("parent"))
}

// WhereHasParentWith applies a predicate to check if query has an edge parent with a given conditions (other predicates).
func (f *DepartmentFilter) WhereHasParentWith(preds ...predicate.Department) {
	f.Where(entql.HasEdgeWith("parent", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasChildren applies a predicate to check if query has an edge children.
func (f *DepartmentFilter) WhereHasChildren() {
	f.Where(entql.HasEdge("children"))
}

// WhereHasChildrenWith applies a predicate to check if query has an edge children with a given conditions (other predicates).
func (f *DepartmentFilter) WhereHasChildrenWith(preds ...predicate.Department) {
	f.Where(entql.HasEdgeWith("children", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (dq *DictQuery) addPredicate(pred func(s *sql.Selector)) {
	dq.predicates = append(dq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the DictQuery builder.
func (dq *DictQuery) Filter() *DictFilter {
	return &DictFilter{config: dq.config, predicateAdder: dq}
}

// addPredicate implements the predicateAdder interface.
func (m *DictMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the DictMutation builder.
func (m *DictMutation) Filter() *DictFilter {
	return &DictFilter{config: m.config, predicateAdder: m}
}

// DictFilter provides a generic filtering capability at runtime for DictQuery.
type DictFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *DictFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[5].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *DictFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(dict.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *DictFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(dict.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *DictFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(dict.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *DictFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(dict.FieldDeleteTime))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *DictFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(dict.FieldStatus))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *DictFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(dict.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *DictFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(dict.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *DictFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(dict.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *DictFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(dict.FieldTenantID))
}

// WhereKey applies the entql string predicate on the key field.
func (f *DictFilter) WhereKey(p entql.StringP) {
	f.Where(p.Field(dict.FieldKey))
}

// WhereCategory applies the entql string predicate on the category field.
func (f *DictFilter) WhereCategory(p entql.StringP) {
	f.Where(p.Field(dict.FieldCategory))
}

// WhereCategoryDesc applies the entql string predicate on the category_desc field.
func (f *DictFilter) WhereCategoryDesc(p entql.StringP) {
	f.Where(p.Field(dict.FieldCategoryDesc))
}

// WhereValue applies the entql string predicate on the value field.
func (f *DictFilter) WhereValue(p entql.StringP) {
	f.Where(p.Field(dict.FieldValue))
}

// WhereValueDesc applies the entql string predicate on the value_desc field.
func (f *DictFilter) WhereValueDesc(p entql.StringP) {
	f.Where(p.Field(dict.FieldValueDesc))
}

// WhereValueDataType applies the entql string predicate on the value_data_type field.
func (f *DictFilter) WhereValueDataType(p entql.StringP) {
	f.Where(p.Field(dict.FieldValueDataType))
}

// WhereSortID applies the entql int32 predicate on the sort_id field.
func (f *DictFilter) WhereSortID(p entql.Int32P) {
	f.Where(p.Field(dict.FieldSortID))
}

// addPredicate implements the predicateAdder interface.
func (fq *FileQuery) addPredicate(pred func(s *sql.Selector)) {
	fq.predicates = append(fq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the FileQuery builder.
func (fq *FileQuery) Filter() *FileFilter {
	return &FileFilter{config: fq.config, predicateAdder: fq}
}

// addPredicate implements the predicateAdder interface.
func (m *FileMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the FileMutation builder.
func (m *FileMutation) Filter() *FileFilter {
	return &FileFilter{config: m.config, predicateAdder: m}
}

// FileFilter provides a generic filtering capability at runtime for FileQuery.
type FileFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *FileFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[6].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *FileFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(file.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *FileFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(file.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *FileFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(file.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *FileFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(file.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *FileFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(file.FieldCreateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *FileFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(file.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *FileFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(file.FieldTenantID))
}

// WhereProvider applies the entql string predicate on the provider field.
func (f *FileFilter) WhereProvider(p entql.StringP) {
	f.Where(p.Field(file.FieldProvider))
}

// WhereBucketName applies the entql string predicate on the bucket_name field.
func (f *FileFilter) WhereBucketName(p entql.StringP) {
	f.Where(p.Field(file.FieldBucketName))
}

// WhereFileDirectory applies the entql string predicate on the file_directory field.
func (f *FileFilter) WhereFileDirectory(p entql.StringP) {
	f.Where(p.Field(file.FieldFileDirectory))
}

// WhereFileGUID applies the entql string predicate on the file_guid field.
func (f *FileFilter) WhereFileGUID(p entql.StringP) {
	f.Where(p.Field(file.FieldFileGUID))
}

// WhereSaveFileName applies the entql string predicate on the save_file_name field.
func (f *FileFilter) WhereSaveFileName(p entql.StringP) {
	f.Where(p.Field(file.FieldSaveFileName))
}

// WhereFileName applies the entql string predicate on the file_name field.
func (f *FileFilter) WhereFileName(p entql.StringP) {
	f.Where(p.Field(file.FieldFileName))
}

// WhereExtension applies the entql string predicate on the extension field.
func (f *FileFilter) WhereExtension(p entql.StringP) {
	f.Where(p.Field(file.FieldExtension))
}

// WhereSize applies the entql uint64 predicate on the size field.
func (f *FileFilter) WhereSize(p entql.Uint64P) {
	f.Where(p.Field(file.FieldSize))
}

// WhereSizeFormat applies the entql string predicate on the size_format field.
func (f *FileFilter) WhereSizeFormat(p entql.StringP) {
	f.Where(p.Field(file.FieldSizeFormat))
}

// WhereLinkURL applies the entql string predicate on the link_url field.
func (f *FileFilter) WhereLinkURL(p entql.StringP) {
	f.Where(p.Field(file.FieldLinkURL))
}

// WhereMd5 applies the entql string predicate on the md5 field.
func (f *FileFilter) WhereMd5(p entql.StringP) {
	f.Where(p.Field(file.FieldMd5))
}

// addPredicate implements the predicateAdder interface.
func (mq *MenuQuery) addPredicate(pred func(s *sql.Selector)) {
	mq.predicates = append(mq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the MenuQuery builder.
func (mq *MenuQuery) Filter() *MenuFilter {
	return &MenuFilter{config: mq.config, predicateAdder: mq}
}

// addPredicate implements the predicateAdder interface.
func (m *MenuMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the MenuMutation builder.
func (m *MenuMutation) Filter() *MenuFilter {
	return &MenuFilter{config: m.config, predicateAdder: m}
}

// MenuFilter provides a generic filtering capability at runtime for MenuQuery.
type MenuFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *MenuFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[7].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int32 predicate on the id field.
func (f *MenuFilter) WhereID(p entql.Int32P) {
	f.Where(p.Field(menu.FieldID))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *MenuFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(menu.FieldStatus))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *MenuFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(menu.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *MenuFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(menu.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *MenuFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(menu.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *MenuFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(menu.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *MenuFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(menu.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *MenuFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(menu.FieldRemark))
}

// WhereParentID applies the entql int32 predicate on the parent_id field.
func (f *MenuFilter) WhereParentID(p entql.Int32P) {
	f.Where(p.Field(menu.FieldParentID))
}

// WhereType applies the entql string predicate on the type field.
func (f *MenuFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(menu.FieldType))
}

// WherePath applies the entql string predicate on the path field.
func (f *MenuFilter) WherePath(p entql.StringP) {
	f.Where(p.Field(menu.FieldPath))
}

// WhereRedirect applies the entql string predicate on the redirect field.
func (f *MenuFilter) WhereRedirect(p entql.StringP) {
	f.Where(p.Field(menu.FieldRedirect))
}

// WhereAlias applies the entql string predicate on the alias field.
func (f *MenuFilter) WhereAlias(p entql.StringP) {
	f.Where(p.Field(menu.FieldAlias))
}

// WhereName applies the entql string predicate on the name field.
func (f *MenuFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(menu.FieldName))
}

// WhereComponent applies the entql string predicate on the component field.
func (f *MenuFilter) WhereComponent(p entql.StringP) {
	f.Where(p.Field(menu.FieldComponent))
}

// WhereMeta applies the entql json.RawMessage predicate on the meta field.
func (f *MenuFilter) WhereMeta(p entql.BytesP) {
	f.Where(p.Field(menu.FieldMeta))
}

// WhereHasParent applies a predicate to check if query has an edge parent.
func (f *MenuFilter) WhereHasParent() {
	f.Where(entql.HasEdge("parent"))
}

// WhereHasParentWith applies a predicate to check if query has an edge parent with a given conditions (other predicates).
func (f *MenuFilter) WhereHasParentWith(preds ...predicate.Menu) {
	f.Where(entql.HasEdgeWith("parent", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasChildren applies a predicate to check if query has an edge children.
func (f *MenuFilter) WhereHasChildren() {
	f.Where(entql.HasEdge("children"))
}

// WhereHasChildrenWith applies a predicate to check if query has an edge children with a given conditions (other predicates).
func (f *MenuFilter) WhereHasChildrenWith(preds ...predicate.Menu) {
	f.Where(entql.HasEdgeWith("children", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (nmq *NotificationMessageQuery) addPredicate(pred func(s *sql.Selector)) {
	nmq.predicates = append(nmq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the NotificationMessageQuery builder.
func (nmq *NotificationMessageQuery) Filter() *NotificationMessageFilter {
	return &NotificationMessageFilter{config: nmq.config, predicateAdder: nmq}
}

// addPredicate implements the predicateAdder interface.
func (m *NotificationMessageMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the NotificationMessageMutation builder.
func (m *NotificationMessageMutation) Filter() *NotificationMessageFilter {
	return &NotificationMessageFilter{config: m.config, predicateAdder: m}
}

// NotificationMessageFilter provides a generic filtering capability at runtime for NotificationMessageQuery.
type NotificationMessageFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *NotificationMessageFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[8].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *NotificationMessageFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessage.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *NotificationMessageFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessage.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *NotificationMessageFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessage.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *NotificationMessageFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessage.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *NotificationMessageFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(notificationmessage.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *NotificationMessageFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(notificationmessage.FieldUpdateBy))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *NotificationMessageFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessage.FieldTenantID))
}

// WhereSubject applies the entql string predicate on the subject field.
func (f *NotificationMessageFilter) WhereSubject(p entql.StringP) {
	f.Where(p.Field(notificationmessage.FieldSubject))
}

// WhereContent applies the entql string predicate on the content field.
func (f *NotificationMessageFilter) WhereContent(p entql.StringP) {
	f.Where(p.Field(notificationmessage.FieldContent))
}

// WhereCategoryID applies the entql uint32 predicate on the category_id field.
func (f *NotificationMessageFilter) WhereCategoryID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessage.FieldCategoryID))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *NotificationMessageFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(notificationmessage.FieldStatus))
}

// addPredicate implements the predicateAdder interface.
func (nmcq *NotificationMessageCategoryQuery) addPredicate(pred func(s *sql.Selector)) {
	nmcq.predicates = append(nmcq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the NotificationMessageCategoryQuery builder.
func (nmcq *NotificationMessageCategoryQuery) Filter() *NotificationMessageCategoryFilter {
	return &NotificationMessageCategoryFilter{config: nmcq.config, predicateAdder: nmcq}
}

// addPredicate implements the predicateAdder interface.
func (m *NotificationMessageCategoryMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the NotificationMessageCategoryMutation builder.
func (m *NotificationMessageCategoryMutation) Filter() *NotificationMessageCategoryFilter {
	return &NotificationMessageCategoryFilter{config: m.config, predicateAdder: m}
}

// NotificationMessageCategoryFilter provides a generic filtering capability at runtime for NotificationMessageCategoryQuery.
type NotificationMessageCategoryFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *NotificationMessageCategoryFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[9].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *NotificationMessageCategoryFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagecategory.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *NotificationMessageCategoryFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessagecategory.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *NotificationMessageCategoryFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessagecategory.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *NotificationMessageCategoryFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessagecategory.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *NotificationMessageCategoryFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagecategory.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *NotificationMessageCategoryFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagecategory.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *NotificationMessageCategoryFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(notificationmessagecategory.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *NotificationMessageCategoryFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagecategory.FieldTenantID))
}

// WhereName applies the entql string predicate on the name field.
func (f *NotificationMessageCategoryFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(notificationmessagecategory.FieldName))
}

// WhereCode applies the entql string predicate on the code field.
func (f *NotificationMessageCategoryFilter) WhereCode(p entql.StringP) {
	f.Where(p.Field(notificationmessagecategory.FieldCode))
}

// WhereSortID applies the entql int32 predicate on the sort_id field.
func (f *NotificationMessageCategoryFilter) WhereSortID(p entql.Int32P) {
	f.Where(p.Field(notificationmessagecategory.FieldSortID))
}

// WhereEnable applies the entql bool predicate on the enable field.
func (f *NotificationMessageCategoryFilter) WhereEnable(p entql.BoolP) {
	f.Where(p.Field(notificationmessagecategory.FieldEnable))
}

// WhereParentID applies the entql uint32 predicate on the parent_id field.
func (f *NotificationMessageCategoryFilter) WhereParentID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagecategory.FieldParentID))
}

// WhereHasParent applies a predicate to check if query has an edge parent.
func (f *NotificationMessageCategoryFilter) WhereHasParent() {
	f.Where(entql.HasEdge("parent"))
}

// WhereHasParentWith applies a predicate to check if query has an edge parent with a given conditions (other predicates).
func (f *NotificationMessageCategoryFilter) WhereHasParentWith(preds ...predicate.NotificationMessageCategory) {
	f.Where(entql.HasEdgeWith("parent", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasChildren applies a predicate to check if query has an edge children.
func (f *NotificationMessageCategoryFilter) WhereHasChildren() {
	f.Where(entql.HasEdge("children"))
}

// WhereHasChildrenWith applies a predicate to check if query has an edge children with a given conditions (other predicates).
func (f *NotificationMessageCategoryFilter) WhereHasChildrenWith(preds ...predicate.NotificationMessageCategory) {
	f.Where(entql.HasEdgeWith("children", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (nmrq *NotificationMessageRecipientQuery) addPredicate(pred func(s *sql.Selector)) {
	nmrq.predicates = append(nmrq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the NotificationMessageRecipientQuery builder.
func (nmrq *NotificationMessageRecipientQuery) Filter() *NotificationMessageRecipientFilter {
	return &NotificationMessageRecipientFilter{config: nmrq.config, predicateAdder: nmrq}
}

// addPredicate implements the predicateAdder interface.
func (m *NotificationMessageRecipientMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the NotificationMessageRecipientMutation builder.
func (m *NotificationMessageRecipientMutation) Filter() *NotificationMessageRecipientFilter {
	return &NotificationMessageRecipientFilter{config: m.config, predicateAdder: m}
}

// NotificationMessageRecipientFilter provides a generic filtering capability at runtime for NotificationMessageRecipientQuery.
type NotificationMessageRecipientFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *NotificationMessageRecipientFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[10].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *NotificationMessageRecipientFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagerecipient.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *NotificationMessageRecipientFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessagerecipient.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *NotificationMessageRecipientFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessagerecipient.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *NotificationMessageRecipientFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(notificationmessagerecipient.FieldDeleteTime))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *NotificationMessageRecipientFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagerecipient.FieldTenantID))
}

// WhereMessageID applies the entql uint32 predicate on the message_id field.
func (f *NotificationMessageRecipientFilter) WhereMessageID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagerecipient.FieldMessageID))
}

// WhereRecipientID applies the entql uint32 predicate on the recipient_id field.
func (f *NotificationMessageRecipientFilter) WhereRecipientID(p entql.Uint32P) {
	f.Where(p.Field(notificationmessagerecipient.FieldRecipientID))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *NotificationMessageRecipientFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(notificationmessagerecipient.FieldStatus))
}

// addPredicate implements the predicateAdder interface.
func (oq *OrganizationQuery) addPredicate(pred func(s *sql.Selector)) {
	oq.predicates = append(oq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the OrganizationQuery builder.
func (oq *OrganizationQuery) Filter() *OrganizationFilter {
	return &OrganizationFilter{config: oq.config, predicateAdder: oq}
}

// addPredicate implements the predicateAdder interface.
func (m *OrganizationMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the OrganizationMutation builder.
func (m *OrganizationMutation) Filter() *OrganizationFilter {
	return &OrganizationFilter{config: m.config, predicateAdder: m}
}

// OrganizationFilter provides a generic filtering capability at runtime for OrganizationQuery.
type OrganizationFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *OrganizationFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[11].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *OrganizationFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(organization.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *OrganizationFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(organization.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *OrganizationFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(organization.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *OrganizationFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(organization.FieldDeleteTime))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *OrganizationFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(organization.FieldStatus))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *OrganizationFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(organization.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *OrganizationFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(organization.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *OrganizationFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(organization.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *OrganizationFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(organization.FieldTenantID))
}

// WhereName applies the entql string predicate on the name field.
func (f *OrganizationFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(organization.FieldName))
}

// WhereParentID applies the entql uint32 predicate on the parent_id field.
func (f *OrganizationFilter) WhereParentID(p entql.Uint32P) {
	f.Where(p.Field(organization.FieldParentID))
}

// WhereSortID applies the entql int32 predicate on the sort_id field.
func (f *OrganizationFilter) WhereSortID(p entql.Int32P) {
	f.Where(p.Field(organization.FieldSortID))
}

// WhereHasParent applies a predicate to check if query has an edge parent.
func (f *OrganizationFilter) WhereHasParent() {
	f.Where(entql.HasEdge("parent"))
}

// WhereHasParentWith applies a predicate to check if query has an edge parent with a given conditions (other predicates).
func (f *OrganizationFilter) WhereHasParentWith(preds ...predicate.Organization) {
	f.Where(entql.HasEdgeWith("parent", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasChildren applies a predicate to check if query has an edge children.
func (f *OrganizationFilter) WhereHasChildren() {
	f.Where(entql.HasEdge("children"))
}

// WhereHasChildrenWith applies a predicate to check if query has an edge children with a given conditions (other predicates).
func (f *OrganizationFilter) WhereHasChildrenWith(preds ...predicate.Organization) {
	f.Where(entql.HasEdgeWith("children", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (pq *PositionQuery) addPredicate(pred func(s *sql.Selector)) {
	pq.predicates = append(pq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the PositionQuery builder.
func (pq *PositionQuery) Filter() *PositionFilter {
	return &PositionFilter{config: pq.config, predicateAdder: pq}
}

// addPredicate implements the predicateAdder interface.
func (m *PositionMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the PositionMutation builder.
func (m *PositionMutation) Filter() *PositionFilter {
	return &PositionFilter{config: m.config, predicateAdder: m}
}

// PositionFilter provides a generic filtering capability at runtime for PositionQuery.
type PositionFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *PositionFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[12].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *PositionFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(position.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *PositionFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(position.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *PositionFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(position.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *PositionFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(position.FieldDeleteTime))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *PositionFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(position.FieldStatus))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *PositionFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(position.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *PositionFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(position.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *PositionFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(position.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *PositionFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(position.FieldTenantID))
}

// WhereName applies the entql string predicate on the name field.
func (f *PositionFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(position.FieldName))
}

// WhereCode applies the entql string predicate on the code field.
func (f *PositionFilter) WhereCode(p entql.StringP) {
	f.Where(p.Field(position.FieldCode))
}

// WhereParentID applies the entql uint32 predicate on the parent_id field.
func (f *PositionFilter) WhereParentID(p entql.Uint32P) {
	f.Where(p.Field(position.FieldParentID))
}

// WhereSortID applies the entql int32 predicate on the sort_id field.
func (f *PositionFilter) WhereSortID(p entql.Int32P) {
	f.Where(p.Field(position.FieldSortID))
}

// WhereHasParent applies a predicate to check if query has an edge parent.
func (f *PositionFilter) WhereHasParent() {
	f.Where(entql.HasEdge("parent"))
}

// WhereHasParentWith applies a predicate to check if query has an edge parent with a given conditions (other predicates).
func (f *PositionFilter) WhereHasParentWith(preds ...predicate.Position) {
	f.Where(entql.HasEdgeWith("parent", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasChildren applies a predicate to check if query has an edge children.
func (f *PositionFilter) WhereHasChildren() {
	f.Where(entql.HasEdge("children"))
}

// WhereHasChildrenWith applies a predicate to check if query has an edge children with a given conditions (other predicates).
func (f *PositionFilter) WhereHasChildrenWith(preds ...predicate.Position) {
	f.Where(entql.HasEdgeWith("children", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (pmq *PrivateMessageQuery) addPredicate(pred func(s *sql.Selector)) {
	pmq.predicates = append(pmq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the PrivateMessageQuery builder.
func (pmq *PrivateMessageQuery) Filter() *PrivateMessageFilter {
	return &PrivateMessageFilter{config: pmq.config, predicateAdder: pmq}
}

// addPredicate implements the predicateAdder interface.
func (m *PrivateMessageMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the PrivateMessageMutation builder.
func (m *PrivateMessageMutation) Filter() *PrivateMessageFilter {
	return &PrivateMessageFilter{config: m.config, predicateAdder: m}
}

// PrivateMessageFilter provides a generic filtering capability at runtime for PrivateMessageQuery.
type PrivateMessageFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *PrivateMessageFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[13].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *PrivateMessageFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(privatemessage.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *PrivateMessageFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(privatemessage.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *PrivateMessageFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(privatemessage.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *PrivateMessageFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(privatemessage.FieldDeleteTime))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *PrivateMessageFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(privatemessage.FieldTenantID))
}

// WhereSubject applies the entql string predicate on the subject field.
func (f *PrivateMessageFilter) WhereSubject(p entql.StringP) {
	f.Where(p.Field(privatemessage.FieldSubject))
}

// WhereContent applies the entql string predicate on the content field.
func (f *PrivateMessageFilter) WhereContent(p entql.StringP) {
	f.Where(p.Field(privatemessage.FieldContent))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *PrivateMessageFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(privatemessage.FieldStatus))
}

// WhereSenderID applies the entql uint32 predicate on the sender_id field.
func (f *PrivateMessageFilter) WhereSenderID(p entql.Uint32P) {
	f.Where(p.Field(privatemessage.FieldSenderID))
}

// WhereReceiverID applies the entql uint32 predicate on the receiver_id field.
func (f *PrivateMessageFilter) WhereReceiverID(p entql.Uint32P) {
	f.Where(p.Field(privatemessage.FieldReceiverID))
}

// addPredicate implements the predicateAdder interface.
func (rq *RoleQuery) addPredicate(pred func(s *sql.Selector)) {
	rq.predicates = append(rq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the RoleQuery builder.
func (rq *RoleQuery) Filter() *RoleFilter {
	return &RoleFilter{config: rq.config, predicateAdder: rq}
}

// addPredicate implements the predicateAdder interface.
func (m *RoleMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the RoleMutation builder.
func (m *RoleMutation) Filter() *RoleFilter {
	return &RoleFilter{config: m.config, predicateAdder: m}
}

// RoleFilter provides a generic filtering capability at runtime for RoleQuery.
type RoleFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *RoleFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[14].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *RoleFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(role.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *RoleFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(role.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *RoleFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(role.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *RoleFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(role.FieldDeleteTime))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *RoleFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(role.FieldStatus))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *RoleFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(role.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *RoleFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(role.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *RoleFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(role.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *RoleFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(role.FieldTenantID))
}

// WhereName applies the entql string predicate on the name field.
func (f *RoleFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(role.FieldName))
}

// WhereCode applies the entql string predicate on the code field.
func (f *RoleFilter) WhereCode(p entql.StringP) {
	f.Where(p.Field(role.FieldCode))
}

// WhereParentID applies the entql uint32 predicate on the parent_id field.
func (f *RoleFilter) WhereParentID(p entql.Uint32P) {
	f.Where(p.Field(role.FieldParentID))
}

// WhereSortID applies the entql int32 predicate on the sort_id field.
func (f *RoleFilter) WhereSortID(p entql.Int32P) {
	f.Where(p.Field(role.FieldSortID))
}

// WhereMenus applies the entql json.RawMessage predicate on the menus field.
func (f *RoleFilter) WhereMenus(p entql.BytesP) {
	f.Where(p.Field(role.FieldMenus))
}

// WhereApis applies the entql json.RawMessage predicate on the apis field.
func (f *RoleFilter) WhereApis(p entql.BytesP) {
	f.Where(p.Field(role.FieldApis))
}

// WhereHasParent applies a predicate to check if query has an edge parent.
func (f *RoleFilter) WhereHasParent() {
	f.Where(entql.HasEdge("parent"))
}

// WhereHasParentWith applies a predicate to check if query has an edge parent with a given conditions (other predicates).
func (f *RoleFilter) WhereHasParentWith(preds ...predicate.Role) {
	f.Where(entql.HasEdgeWith("parent", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasChildren applies a predicate to check if query has an edge children.
func (f *RoleFilter) WhereHasChildren() {
	f.Where(entql.HasEdge("children"))
}

// WhereHasChildrenWith applies a predicate to check if query has an edge children with a given conditions (other predicates).
func (f *RoleFilter) WhereHasChildrenWith(preds ...predicate.Role) {
	f.Where(entql.HasEdgeWith("children", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (tq *TaskQuery) addPredicate(pred func(s *sql.Selector)) {
	tq.predicates = append(tq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the TaskQuery builder.
func (tq *TaskQuery) Filter() *TaskFilter {
	return &TaskFilter{config: tq.config, predicateAdder: tq}
}

// addPredicate implements the predicateAdder interface.
func (m *TaskMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the TaskMutation builder.
func (m *TaskMutation) Filter() *TaskFilter {
	return &TaskFilter{config: m.config, predicateAdder: m}
}

// TaskFilter provides a generic filtering capability at runtime for TaskQuery.
type TaskFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *TaskFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[15].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *TaskFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(task.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *TaskFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(task.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *TaskFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(task.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *TaskFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(task.FieldDeleteTime))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *TaskFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(task.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *TaskFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(task.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *TaskFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(task.FieldRemark))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *TaskFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(task.FieldTenantID))
}

// WhereType applies the entql string predicate on the type field.
func (f *TaskFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(task.FieldType))
}

// WhereTypeName applies the entql string predicate on the type_name field.
func (f *TaskFilter) WhereTypeName(p entql.StringP) {
	f.Where(p.Field(task.FieldTypeName))
}

// WhereTaskPayload applies the entql string predicate on the task_payload field.
func (f *TaskFilter) WhereTaskPayload(p entql.StringP) {
	f.Where(p.Field(task.FieldTaskPayload))
}

// WhereCronSpec applies the entql string predicate on the cron_spec field.
func (f *TaskFilter) WhereCronSpec(p entql.StringP) {
	f.Where(p.Field(task.FieldCronSpec))
}

// WhereTaskOptions applies the entql json.RawMessage predicate on the task_options field.
func (f *TaskFilter) WhereTaskOptions(p entql.BytesP) {
	f.Where(p.Field(task.FieldTaskOptions))
}

// WhereEnable applies the entql bool predicate on the enable field.
func (f *TaskFilter) WhereEnable(p entql.BoolP) {
	f.Where(p.Field(task.FieldEnable))
}

// addPredicate implements the predicateAdder interface.
func (tq *TenantQuery) addPredicate(pred func(s *sql.Selector)) {
	tq.predicates = append(tq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the TenantQuery builder.
func (tq *TenantQuery) Filter() *TenantFilter {
	return &TenantFilter{config: tq.config, predicateAdder: tq}
}

// addPredicate implements the predicateAdder interface.
func (m *TenantMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the TenantMutation builder.
func (m *TenantMutation) Filter() *TenantFilter {
	return &TenantFilter{config: m.config, predicateAdder: m}
}

// TenantFilter provides a generic filtering capability at runtime for TenantQuery.
type TenantFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *TenantFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[16].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *TenantFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(tenant.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *TenantFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(tenant.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *TenantFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(tenant.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *TenantFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(tenant.FieldDeleteTime))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *TenantFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(tenant.FieldStatus))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *TenantFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(tenant.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *TenantFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(tenant.FieldUpdateBy))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *TenantFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(tenant.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *TenantFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(tenant.FieldName))
}

// WhereCode applies the entql string predicate on the code field.
func (f *TenantFilter) WhereCode(p entql.StringP) {
	f.Where(p.Field(tenant.FieldCode))
}

// WhereMemberCount applies the entql int32 predicate on the member_count field.
func (f *TenantFilter) WhereMemberCount(p entql.Int32P) {
	f.Where(p.Field(tenant.FieldMemberCount))
}

// WhereSubscriptionAt applies the entql time.Time predicate on the subscription_at field.
func (f *TenantFilter) WhereSubscriptionAt(p entql.TimeP) {
	f.Where(p.Field(tenant.FieldSubscriptionAt))
}

// WhereUnsubscribeAt applies the entql time.Time predicate on the unsubscribe_at field.
func (f *TenantFilter) WhereUnsubscribeAt(p entql.TimeP) {
	f.Where(p.Field(tenant.FieldUnsubscribeAt))
}

// addPredicate implements the predicateAdder interface.
func (uq *UserQuery) addPredicate(pred func(s *sql.Selector)) {
	uq.predicates = append(uq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the UserQuery builder.
func (uq *UserQuery) Filter() *UserFilter {
	return &UserFilter{config: uq.config, predicateAdder: uq}
}

// addPredicate implements the predicateAdder interface.
func (m *UserMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the UserMutation builder.
func (m *UserMutation) Filter() *UserFilter {
	return &UserFilter{config: m.config, predicateAdder: m}
}

// UserFilter provides a generic filtering capability at runtime for UserQuery.
type UserFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *UserFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[17].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *UserFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(user.FieldID))
}

// WhereCreateBy applies the entql uint32 predicate on the create_by field.
func (f *UserFilter) WhereCreateBy(p entql.Uint32P) {
	f.Where(p.Field(user.FieldCreateBy))
}

// WhereUpdateBy applies the entql uint32 predicate on the update_by field.
func (f *UserFilter) WhereUpdateBy(p entql.Uint32P) {
	f.Where(p.Field(user.FieldUpdateBy))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *UserFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(user.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *UserFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(user.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *UserFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(user.FieldDeleteTime))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *UserFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(user.FieldRemark))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *UserFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(user.FieldStatus))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *UserFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(user.FieldTenantID))
}

// WhereUsername applies the entql string predicate on the username field.
func (f *UserFilter) WhereUsername(p entql.StringP) {
	f.Where(p.Field(user.FieldUsername))
}

// WhereNickname applies the entql string predicate on the nickname field.
func (f *UserFilter) WhereNickname(p entql.StringP) {
	f.Where(p.Field(user.FieldNickname))
}

// WhereRealname applies the entql string predicate on the realname field.
func (f *UserFilter) WhereRealname(p entql.StringP) {
	f.Where(p.Field(user.FieldRealname))
}

// WhereEmail applies the entql string predicate on the email field.
func (f *UserFilter) WhereEmail(p entql.StringP) {
	f.Where(p.Field(user.FieldEmail))
}

// WhereMobile applies the entql string predicate on the mobile field.
func (f *UserFilter) WhereMobile(p entql.StringP) {
	f.Where(p.Field(user.FieldMobile))
}

// WhereTelephone applies the entql string predicate on the telephone field.
func (f *UserFilter) WhereTelephone(p entql.StringP) {
	f.Where(p.Field(user.FieldTelephone))
}

// WhereAvatar applies the entql string predicate on the avatar field.
func (f *UserFilter) WhereAvatar(p entql.StringP) {
	f.Where(p.Field(user.FieldAvatar))
}

// WhereAddress applies the entql string predicate on the address field.
func (f *UserFilter) WhereAddress(p entql.StringP) {
	f.Where(p.Field(user.FieldAddress))
}

// WhereRegion applies the entql string predicate on the region field.
func (f *UserFilter) WhereRegion(p entql.StringP) {
	f.Where(p.Field(user.FieldRegion))
}

// WhereDescription applies the entql string predicate on the description field.
func (f *UserFilter) WhereDescription(p entql.StringP) {
	f.Where(p.Field(user.FieldDescription))
}

// WhereGender applies the entql string predicate on the gender field.
func (f *UserFilter) WhereGender(p entql.StringP) {
	f.Where(p.Field(user.FieldGender))
}

// WhereAuthority applies the entql string predicate on the authority field.
func (f *UserFilter) WhereAuthority(p entql.StringP) {
	f.Where(p.Field(user.FieldAuthority))
}

// WhereLastLoginTime applies the entql time.Time predicate on the last_login_time field.
func (f *UserFilter) WhereLastLoginTime(p entql.TimeP) {
	f.Where(p.Field(user.FieldLastLoginTime))
}

// WhereLastLoginIP applies the entql string predicate on the last_login_ip field.
func (f *UserFilter) WhereLastLoginIP(p entql.StringP) {
	f.Where(p.Field(user.FieldLastLoginIP))
}

// WhereOrgID applies the entql uint32 predicate on the org_id field.
func (f *UserFilter) WhereOrgID(p entql.Uint32P) {
	f.Where(p.Field(user.FieldOrgID))
}

// WherePositionID applies the entql uint32 predicate on the position_id field.
func (f *UserFilter) WherePositionID(p entql.Uint32P) {
	f.Where(p.Field(user.FieldPositionID))
}

// WhereWorkID applies the entql uint32 predicate on the work_id field.
func (f *UserFilter) WhereWorkID(p entql.Uint32P) {
	f.Where(p.Field(user.FieldWorkID))
}

// WhereRoles applies the entql json.RawMessage predicate on the roles field.
func (f *UserFilter) WhereRoles(p entql.BytesP) {
	f.Where(p.Field(user.FieldRoles))
}

// addPredicate implements the predicateAdder interface.
func (ucq *UserCredentialQuery) addPredicate(pred func(s *sql.Selector)) {
	ucq.predicates = append(ucq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the UserCredentialQuery builder.
func (ucq *UserCredentialQuery) Filter() *UserCredentialFilter {
	return &UserCredentialFilter{config: ucq.config, predicateAdder: ucq}
}

// addPredicate implements the predicateAdder interface.
func (m *UserCredentialMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the UserCredentialMutation builder.
func (m *UserCredentialMutation) Filter() *UserCredentialFilter {
	return &UserCredentialFilter{config: m.config, predicateAdder: m}
}

// UserCredentialFilter provides a generic filtering capability at runtime for UserCredentialQuery.
type UserCredentialFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *UserCredentialFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[18].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql uint32 predicate on the id field.
func (f *UserCredentialFilter) WhereID(p entql.Uint32P) {
	f.Where(p.Field(usercredential.FieldID))
}

// WhereCreateTime applies the entql time.Time predicate on the create_time field.
func (f *UserCredentialFilter) WhereCreateTime(p entql.TimeP) {
	f.Where(p.Field(usercredential.FieldCreateTime))
}

// WhereUpdateTime applies the entql time.Time predicate on the update_time field.
func (f *UserCredentialFilter) WhereUpdateTime(p entql.TimeP) {
	f.Where(p.Field(usercredential.FieldUpdateTime))
}

// WhereDeleteTime applies the entql time.Time predicate on the delete_time field.
func (f *UserCredentialFilter) WhereDeleteTime(p entql.TimeP) {
	f.Where(p.Field(usercredential.FieldDeleteTime))
}

// WhereTenantID applies the entql uint32 predicate on the tenant_id field.
func (f *UserCredentialFilter) WhereTenantID(p entql.Uint32P) {
	f.Where(p.Field(usercredential.FieldTenantID))
}

// WhereUserID applies the entql uint32 predicate on the user_id field.
func (f *UserCredentialFilter) WhereUserID(p entql.Uint32P) {
	f.Where(p.Field(usercredential.FieldUserID))
}

// WhereIdentityType applies the entql string predicate on the identity_type field.
func (f *UserCredentialFilter) WhereIdentityType(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldIdentityType))
}

// WhereIdentifier applies the entql string predicate on the identifier field.
func (f *UserCredentialFilter) WhereIdentifier(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldIdentifier))
}

// WhereCredentialType applies the entql string predicate on the credential_type field.
func (f *UserCredentialFilter) WhereCredentialType(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldCredentialType))
}

// WhereCredential applies the entql string predicate on the credential field.
func (f *UserCredentialFilter) WhereCredential(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldCredential))
}

// WhereIsPrimary applies the entql bool predicate on the is_primary field.
func (f *UserCredentialFilter) WhereIsPrimary(p entql.BoolP) {
	f.Where(p.Field(usercredential.FieldIsPrimary))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *UserCredentialFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldStatus))
}

// WhereExtraInfo applies the entql string predicate on the extra_info field.
func (f *UserCredentialFilter) WhereExtraInfo(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldExtraInfo))
}

// WhereActivateToken applies the entql string predicate on the activate_token field.
func (f *UserCredentialFilter) WhereActivateToken(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldActivateToken))
}

// WhereResetToken applies the entql string predicate on the reset_token field.
func (f *UserCredentialFilter) WhereResetToken(p entql.StringP) {
	f.Where(p.Field(usercredential.FieldResetToken))
}
