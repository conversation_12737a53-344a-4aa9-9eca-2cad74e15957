// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"kratos-admin/app/admin/service/internal/data/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// UserUpdate is the builder for updating User entities.
type UserUpdate struct {
	config
	hooks     []Hook
	mutation  *UserMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the UserUpdate builder.
func (uu *UserUpdate) Where(ps ...predicate.User) *UserUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetCreateBy sets the "create_by" field.
func (uu *UserUpdate) SetCreateBy(u uint32) *UserUpdate {
	uu.mutation.ResetCreateBy()
	uu.mutation.SetCreateBy(u)
	return uu
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (uu *UserUpdate) SetNillableCreateBy(u *uint32) *UserUpdate {
	if u != nil {
		uu.SetCreateBy(*u)
	}
	return uu
}

// AddCreateBy adds u to the "create_by" field.
func (uu *UserUpdate) AddCreateBy(u int32) *UserUpdate {
	uu.mutation.AddCreateBy(u)
	return uu
}

// ClearCreateBy clears the value of the "create_by" field.
func (uu *UserUpdate) ClearCreateBy() *UserUpdate {
	uu.mutation.ClearCreateBy()
	return uu
}

// SetUpdateBy sets the "update_by" field.
func (uu *UserUpdate) SetUpdateBy(u uint32) *UserUpdate {
	uu.mutation.ResetUpdateBy()
	uu.mutation.SetUpdateBy(u)
	return uu
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (uu *UserUpdate) SetNillableUpdateBy(u *uint32) *UserUpdate {
	if u != nil {
		uu.SetUpdateBy(*u)
	}
	return uu
}

// AddUpdateBy adds u to the "update_by" field.
func (uu *UserUpdate) AddUpdateBy(u int32) *UserUpdate {
	uu.mutation.AddUpdateBy(u)
	return uu
}

// ClearUpdateBy clears the value of the "update_by" field.
func (uu *UserUpdate) ClearUpdateBy() *UserUpdate {
	uu.mutation.ClearUpdateBy()
	return uu
}

// SetUpdateTime sets the "update_time" field.
func (uu *UserUpdate) SetUpdateTime(t time.Time) *UserUpdate {
	uu.mutation.SetUpdateTime(t)
	return uu
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (uu *UserUpdate) SetNillableUpdateTime(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetUpdateTime(*t)
	}
	return uu
}

// ClearUpdateTime clears the value of the "update_time" field.
func (uu *UserUpdate) ClearUpdateTime() *UserUpdate {
	uu.mutation.ClearUpdateTime()
	return uu
}

// SetDeleteTime sets the "delete_time" field.
func (uu *UserUpdate) SetDeleteTime(t time.Time) *UserUpdate {
	uu.mutation.SetDeleteTime(t)
	return uu
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDeleteTime(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetDeleteTime(*t)
	}
	return uu
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (uu *UserUpdate) ClearDeleteTime() *UserUpdate {
	uu.mutation.ClearDeleteTime()
	return uu
}

// SetRemark sets the "remark" field.
func (uu *UserUpdate) SetRemark(s string) *UserUpdate {
	uu.mutation.SetRemark(s)
	return uu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (uu *UserUpdate) SetNillableRemark(s *string) *UserUpdate {
	if s != nil {
		uu.SetRemark(*s)
	}
	return uu
}

// ClearRemark clears the value of the "remark" field.
func (uu *UserUpdate) ClearRemark() *UserUpdate {
	uu.mutation.ClearRemark()
	return uu
}

// SetStatus sets the "status" field.
func (uu *UserUpdate) SetStatus(u user.Status) *UserUpdate {
	uu.mutation.SetStatus(u)
	return uu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uu *UserUpdate) SetNillableStatus(u *user.Status) *UserUpdate {
	if u != nil {
		uu.SetStatus(*u)
	}
	return uu
}

// ClearStatus clears the value of the "status" field.
func (uu *UserUpdate) ClearStatus() *UserUpdate {
	uu.mutation.ClearStatus()
	return uu
}

// SetNickname sets the "nickname" field.
func (uu *UserUpdate) SetNickname(s string) *UserUpdate {
	uu.mutation.SetNickname(s)
	return uu
}

// SetNillableNickname sets the "nickname" field if the given value is not nil.
func (uu *UserUpdate) SetNillableNickname(s *string) *UserUpdate {
	if s != nil {
		uu.SetNickname(*s)
	}
	return uu
}

// ClearNickname clears the value of the "nickname" field.
func (uu *UserUpdate) ClearNickname() *UserUpdate {
	uu.mutation.ClearNickname()
	return uu
}

// SetRealname sets the "realname" field.
func (uu *UserUpdate) SetRealname(s string) *UserUpdate {
	uu.mutation.SetRealname(s)
	return uu
}

// SetNillableRealname sets the "realname" field if the given value is not nil.
func (uu *UserUpdate) SetNillableRealname(s *string) *UserUpdate {
	if s != nil {
		uu.SetRealname(*s)
	}
	return uu
}

// ClearRealname clears the value of the "realname" field.
func (uu *UserUpdate) ClearRealname() *UserUpdate {
	uu.mutation.ClearRealname()
	return uu
}

// SetEmail sets the "email" field.
func (uu *UserUpdate) SetEmail(s string) *UserUpdate {
	uu.mutation.SetEmail(s)
	return uu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uu *UserUpdate) SetNillableEmail(s *string) *UserUpdate {
	if s != nil {
		uu.SetEmail(*s)
	}
	return uu
}

// ClearEmail clears the value of the "email" field.
func (uu *UserUpdate) ClearEmail() *UserUpdate {
	uu.mutation.ClearEmail()
	return uu
}

// SetMobile sets the "mobile" field.
func (uu *UserUpdate) SetMobile(s string) *UserUpdate {
	uu.mutation.SetMobile(s)
	return uu
}

// SetNillableMobile sets the "mobile" field if the given value is not nil.
func (uu *UserUpdate) SetNillableMobile(s *string) *UserUpdate {
	if s != nil {
		uu.SetMobile(*s)
	}
	return uu
}

// ClearMobile clears the value of the "mobile" field.
func (uu *UserUpdate) ClearMobile() *UserUpdate {
	uu.mutation.ClearMobile()
	return uu
}

// SetTelephone sets the "telephone" field.
func (uu *UserUpdate) SetTelephone(s string) *UserUpdate {
	uu.mutation.SetTelephone(s)
	return uu
}

// SetNillableTelephone sets the "telephone" field if the given value is not nil.
func (uu *UserUpdate) SetNillableTelephone(s *string) *UserUpdate {
	if s != nil {
		uu.SetTelephone(*s)
	}
	return uu
}

// ClearTelephone clears the value of the "telephone" field.
func (uu *UserUpdate) ClearTelephone() *UserUpdate {
	uu.mutation.ClearTelephone()
	return uu
}

// SetAvatar sets the "avatar" field.
func (uu *UserUpdate) SetAvatar(s string) *UserUpdate {
	uu.mutation.SetAvatar(s)
	return uu
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAvatar(s *string) *UserUpdate {
	if s != nil {
		uu.SetAvatar(*s)
	}
	return uu
}

// ClearAvatar clears the value of the "avatar" field.
func (uu *UserUpdate) ClearAvatar() *UserUpdate {
	uu.mutation.ClearAvatar()
	return uu
}

// SetAddress sets the "address" field.
func (uu *UserUpdate) SetAddress(s string) *UserUpdate {
	uu.mutation.SetAddress(s)
	return uu
}

// SetNillableAddress sets the "address" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAddress(s *string) *UserUpdate {
	if s != nil {
		uu.SetAddress(*s)
	}
	return uu
}

// ClearAddress clears the value of the "address" field.
func (uu *UserUpdate) ClearAddress() *UserUpdate {
	uu.mutation.ClearAddress()
	return uu
}

// SetRegion sets the "region" field.
func (uu *UserUpdate) SetRegion(s string) *UserUpdate {
	uu.mutation.SetRegion(s)
	return uu
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (uu *UserUpdate) SetNillableRegion(s *string) *UserUpdate {
	if s != nil {
		uu.SetRegion(*s)
	}
	return uu
}

// ClearRegion clears the value of the "region" field.
func (uu *UserUpdate) ClearRegion() *UserUpdate {
	uu.mutation.ClearRegion()
	return uu
}

// SetDescription sets the "description" field.
func (uu *UserUpdate) SetDescription(s string) *UserUpdate {
	uu.mutation.SetDescription(s)
	return uu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDescription(s *string) *UserUpdate {
	if s != nil {
		uu.SetDescription(*s)
	}
	return uu
}

// ClearDescription clears the value of the "description" field.
func (uu *UserUpdate) ClearDescription() *UserUpdate {
	uu.mutation.ClearDescription()
	return uu
}

// SetGender sets the "gender" field.
func (uu *UserUpdate) SetGender(u user.Gender) *UserUpdate {
	uu.mutation.SetGender(u)
	return uu
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uu *UserUpdate) SetNillableGender(u *user.Gender) *UserUpdate {
	if u != nil {
		uu.SetGender(*u)
	}
	return uu
}

// ClearGender clears the value of the "gender" field.
func (uu *UserUpdate) ClearGender() *UserUpdate {
	uu.mutation.ClearGender()
	return uu
}

// SetAuthority sets the "authority" field.
func (uu *UserUpdate) SetAuthority(u user.Authority) *UserUpdate {
	uu.mutation.SetAuthority(u)
	return uu
}

// SetNillableAuthority sets the "authority" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAuthority(u *user.Authority) *UserUpdate {
	if u != nil {
		uu.SetAuthority(*u)
	}
	return uu
}

// ClearAuthority clears the value of the "authority" field.
func (uu *UserUpdate) ClearAuthority() *UserUpdate {
	uu.mutation.ClearAuthority()
	return uu
}

// SetLastLoginTime sets the "last_login_time" field.
func (uu *UserUpdate) SetLastLoginTime(t time.Time) *UserUpdate {
	uu.mutation.SetLastLoginTime(t)
	return uu
}

// SetNillableLastLoginTime sets the "last_login_time" field if the given value is not nil.
func (uu *UserUpdate) SetNillableLastLoginTime(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetLastLoginTime(*t)
	}
	return uu
}

// ClearLastLoginTime clears the value of the "last_login_time" field.
func (uu *UserUpdate) ClearLastLoginTime() *UserUpdate {
	uu.mutation.ClearLastLoginTime()
	return uu
}

// SetLastLoginIP sets the "last_login_ip" field.
func (uu *UserUpdate) SetLastLoginIP(s string) *UserUpdate {
	uu.mutation.SetLastLoginIP(s)
	return uu
}

// SetNillableLastLoginIP sets the "last_login_ip" field if the given value is not nil.
func (uu *UserUpdate) SetNillableLastLoginIP(s *string) *UserUpdate {
	if s != nil {
		uu.SetLastLoginIP(*s)
	}
	return uu
}

// ClearLastLoginIP clears the value of the "last_login_ip" field.
func (uu *UserUpdate) ClearLastLoginIP() *UserUpdate {
	uu.mutation.ClearLastLoginIP()
	return uu
}

// SetOrgID sets the "org_id" field.
func (uu *UserUpdate) SetOrgID(u uint32) *UserUpdate {
	uu.mutation.ResetOrgID()
	uu.mutation.SetOrgID(u)
	return uu
}

// SetNillableOrgID sets the "org_id" field if the given value is not nil.
func (uu *UserUpdate) SetNillableOrgID(u *uint32) *UserUpdate {
	if u != nil {
		uu.SetOrgID(*u)
	}
	return uu
}

// AddOrgID adds u to the "org_id" field.
func (uu *UserUpdate) AddOrgID(u int32) *UserUpdate {
	uu.mutation.AddOrgID(u)
	return uu
}

// ClearOrgID clears the value of the "org_id" field.
func (uu *UserUpdate) ClearOrgID() *UserUpdate {
	uu.mutation.ClearOrgID()
	return uu
}

// SetPositionID sets the "position_id" field.
func (uu *UserUpdate) SetPositionID(u uint32) *UserUpdate {
	uu.mutation.ResetPositionID()
	uu.mutation.SetPositionID(u)
	return uu
}

// SetNillablePositionID sets the "position_id" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePositionID(u *uint32) *UserUpdate {
	if u != nil {
		uu.SetPositionID(*u)
	}
	return uu
}

// AddPositionID adds u to the "position_id" field.
func (uu *UserUpdate) AddPositionID(u int32) *UserUpdate {
	uu.mutation.AddPositionID(u)
	return uu
}

// ClearPositionID clears the value of the "position_id" field.
func (uu *UserUpdate) ClearPositionID() *UserUpdate {
	uu.mutation.ClearPositionID()
	return uu
}

// SetWorkID sets the "work_id" field.
func (uu *UserUpdate) SetWorkID(u uint32) *UserUpdate {
	uu.mutation.ResetWorkID()
	uu.mutation.SetWorkID(u)
	return uu
}

// SetNillableWorkID sets the "work_id" field if the given value is not nil.
func (uu *UserUpdate) SetNillableWorkID(u *uint32) *UserUpdate {
	if u != nil {
		uu.SetWorkID(*u)
	}
	return uu
}

// AddWorkID adds u to the "work_id" field.
func (uu *UserUpdate) AddWorkID(u int32) *UserUpdate {
	uu.mutation.AddWorkID(u)
	return uu
}

// ClearWorkID clears the value of the "work_id" field.
func (uu *UserUpdate) ClearWorkID() *UserUpdate {
	uu.mutation.ClearWorkID()
	return uu
}

// SetRoles sets the "roles" field.
func (uu *UserUpdate) SetRoles(s []string) *UserUpdate {
	uu.mutation.SetRoles(s)
	return uu
}

// AppendRoles appends s to the "roles" field.
func (uu *UserUpdate) AppendRoles(s []string) *UserUpdate {
	uu.mutation.AppendRoles(s)
	return uu
}

// ClearRoles clears the value of the "roles" field.
func (uu *UserUpdate) ClearRoles() *UserUpdate {
	uu.mutation.ClearRoles()
	return uu
}

// Mutation returns the UserMutation object of the builder.
func (uu *UserUpdate) Mutation() *UserMutation {
	return uu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UserUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UserUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UserUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UserUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uu *UserUpdate) check() error {
	if v, ok := uu.mutation.Status(); ok {
		if err := user.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "User.status": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Nickname(); ok {
		if err := user.NicknameValidator(v); err != nil {
			return &ValidationError{Name: "nickname", err: fmt.Errorf(`ent: validator failed for field "User.nickname": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Realname(); ok {
		if err := user.RealnameValidator(v); err != nil {
			return &ValidationError{Name: "realname", err: fmt.Errorf(`ent: validator failed for field "User.realname": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Mobile(); ok {
		if err := user.MobileValidator(v); err != nil {
			return &ValidationError{Name: "mobile", err: fmt.Errorf(`ent: validator failed for field "User.mobile": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Telephone(); ok {
		if err := user.TelephoneValidator(v); err != nil {
			return &ValidationError{Name: "telephone", err: fmt.Errorf(`ent: validator failed for field "User.telephone": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Avatar(); ok {
		if err := user.AvatarValidator(v); err != nil {
			return &ValidationError{Name: "avatar", err: fmt.Errorf(`ent: validator failed for field "User.avatar": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Address(); ok {
		if err := user.AddressValidator(v); err != nil {
			return &ValidationError{Name: "address", err: fmt.Errorf(`ent: validator failed for field "User.address": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Region(); ok {
		if err := user.RegionValidator(v); err != nil {
			return &ValidationError{Name: "region", err: fmt.Errorf(`ent: validator failed for field "User.region": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Description(); ok {
		if err := user.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "User.description": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Gender(); ok {
		if err := user.GenderValidator(v); err != nil {
			return &ValidationError{Name: "gender", err: fmt.Errorf(`ent: validator failed for field "User.gender": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Authority(); ok {
		if err := user.AuthorityValidator(v); err != nil {
			return &ValidationError{Name: "authority", err: fmt.Errorf(`ent: validator failed for field "User.authority": %w`, err)}
		}
	}
	if v, ok := uu.mutation.LastLoginIP(); ok {
		if err := user.LastLoginIPValidator(v); err != nil {
			return &ValidationError{Name: "last_login_ip", err: fmt.Errorf(`ent: validator failed for field "User.last_login_ip": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (uu *UserUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UserUpdate {
	uu.modifiers = append(uu.modifiers, modifiers...)
	return uu
}

func (uu *UserUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeUint32))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.CreateBy(); ok {
		_spec.SetField(user.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := uu.mutation.AddedCreateBy(); ok {
		_spec.AddField(user.FieldCreateBy, field.TypeUint32, value)
	}
	if uu.mutation.CreateByCleared() {
		_spec.ClearField(user.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := uu.mutation.UpdateBy(); ok {
		_spec.SetField(user.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := uu.mutation.AddedUpdateBy(); ok {
		_spec.AddField(user.FieldUpdateBy, field.TypeUint32, value)
	}
	if uu.mutation.UpdateByCleared() {
		_spec.ClearField(user.FieldUpdateBy, field.TypeUint32)
	}
	if uu.mutation.CreateTimeCleared() {
		_spec.ClearField(user.FieldCreateTime, field.TypeTime)
	}
	if value, ok := uu.mutation.UpdateTime(); ok {
		_spec.SetField(user.FieldUpdateTime, field.TypeTime, value)
	}
	if uu.mutation.UpdateTimeCleared() {
		_spec.ClearField(user.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := uu.mutation.DeleteTime(); ok {
		_spec.SetField(user.FieldDeleteTime, field.TypeTime, value)
	}
	if uu.mutation.DeleteTimeCleared() {
		_spec.ClearField(user.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := uu.mutation.Remark(); ok {
		_spec.SetField(user.FieldRemark, field.TypeString, value)
	}
	if uu.mutation.RemarkCleared() {
		_spec.ClearField(user.FieldRemark, field.TypeString)
	}
	if value, ok := uu.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeEnum, value)
	}
	if uu.mutation.StatusCleared() {
		_spec.ClearField(user.FieldStatus, field.TypeEnum)
	}
	if uu.mutation.TenantIDCleared() {
		_spec.ClearField(user.FieldTenantID, field.TypeUint32)
	}
	if uu.mutation.UsernameCleared() {
		_spec.ClearField(user.FieldUsername, field.TypeString)
	}
	if value, ok := uu.mutation.Nickname(); ok {
		_spec.SetField(user.FieldNickname, field.TypeString, value)
	}
	if uu.mutation.NicknameCleared() {
		_spec.ClearField(user.FieldNickname, field.TypeString)
	}
	if value, ok := uu.mutation.Realname(); ok {
		_spec.SetField(user.FieldRealname, field.TypeString, value)
	}
	if uu.mutation.RealnameCleared() {
		_spec.ClearField(user.FieldRealname, field.TypeString)
	}
	if value, ok := uu.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if uu.mutation.EmailCleared() {
		_spec.ClearField(user.FieldEmail, field.TypeString)
	}
	if value, ok := uu.mutation.Mobile(); ok {
		_spec.SetField(user.FieldMobile, field.TypeString, value)
	}
	if uu.mutation.MobileCleared() {
		_spec.ClearField(user.FieldMobile, field.TypeString)
	}
	if value, ok := uu.mutation.Telephone(); ok {
		_spec.SetField(user.FieldTelephone, field.TypeString, value)
	}
	if uu.mutation.TelephoneCleared() {
		_spec.ClearField(user.FieldTelephone, field.TypeString)
	}
	if value, ok := uu.mutation.Avatar(); ok {
		_spec.SetField(user.FieldAvatar, field.TypeString, value)
	}
	if uu.mutation.AvatarCleared() {
		_spec.ClearField(user.FieldAvatar, field.TypeString)
	}
	if value, ok := uu.mutation.Address(); ok {
		_spec.SetField(user.FieldAddress, field.TypeString, value)
	}
	if uu.mutation.AddressCleared() {
		_spec.ClearField(user.FieldAddress, field.TypeString)
	}
	if value, ok := uu.mutation.Region(); ok {
		_spec.SetField(user.FieldRegion, field.TypeString, value)
	}
	if uu.mutation.RegionCleared() {
		_spec.ClearField(user.FieldRegion, field.TypeString)
	}
	if value, ok := uu.mutation.Description(); ok {
		_spec.SetField(user.FieldDescription, field.TypeString, value)
	}
	if uu.mutation.DescriptionCleared() {
		_spec.ClearField(user.FieldDescription, field.TypeString)
	}
	if value, ok := uu.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeEnum, value)
	}
	if uu.mutation.GenderCleared() {
		_spec.ClearField(user.FieldGender, field.TypeEnum)
	}
	if value, ok := uu.mutation.Authority(); ok {
		_spec.SetField(user.FieldAuthority, field.TypeEnum, value)
	}
	if uu.mutation.AuthorityCleared() {
		_spec.ClearField(user.FieldAuthority, field.TypeEnum)
	}
	if value, ok := uu.mutation.LastLoginTime(); ok {
		_spec.SetField(user.FieldLastLoginTime, field.TypeTime, value)
	}
	if uu.mutation.LastLoginTimeCleared() {
		_spec.ClearField(user.FieldLastLoginTime, field.TypeTime)
	}
	if value, ok := uu.mutation.LastLoginIP(); ok {
		_spec.SetField(user.FieldLastLoginIP, field.TypeString, value)
	}
	if uu.mutation.LastLoginIPCleared() {
		_spec.ClearField(user.FieldLastLoginIP, field.TypeString)
	}
	if value, ok := uu.mutation.OrgID(); ok {
		_spec.SetField(user.FieldOrgID, field.TypeUint32, value)
	}
	if value, ok := uu.mutation.AddedOrgID(); ok {
		_spec.AddField(user.FieldOrgID, field.TypeUint32, value)
	}
	if uu.mutation.OrgIDCleared() {
		_spec.ClearField(user.FieldOrgID, field.TypeUint32)
	}
	if value, ok := uu.mutation.PositionID(); ok {
		_spec.SetField(user.FieldPositionID, field.TypeUint32, value)
	}
	if value, ok := uu.mutation.AddedPositionID(); ok {
		_spec.AddField(user.FieldPositionID, field.TypeUint32, value)
	}
	if uu.mutation.PositionIDCleared() {
		_spec.ClearField(user.FieldPositionID, field.TypeUint32)
	}
	if value, ok := uu.mutation.WorkID(); ok {
		_spec.SetField(user.FieldWorkID, field.TypeUint32, value)
	}
	if value, ok := uu.mutation.AddedWorkID(); ok {
		_spec.AddField(user.FieldWorkID, field.TypeUint32, value)
	}
	if uu.mutation.WorkIDCleared() {
		_spec.ClearField(user.FieldWorkID, field.TypeUint32)
	}
	if value, ok := uu.mutation.Roles(); ok {
		_spec.SetField(user.FieldRoles, field.TypeJSON, value)
	}
	if value, ok := uu.mutation.AppendedRoles(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, user.FieldRoles, value)
		})
	}
	if uu.mutation.RolesCleared() {
		_spec.ClearField(user.FieldRoles, field.TypeJSON)
	}
	_spec.AddModifiers(uu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UserUpdateOne is the builder for updating a single User entity.
type UserUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *UserMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetCreateBy sets the "create_by" field.
func (uuo *UserUpdateOne) SetCreateBy(u uint32) *UserUpdateOne {
	uuo.mutation.ResetCreateBy()
	uuo.mutation.SetCreateBy(u)
	return uuo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableCreateBy(u *uint32) *UserUpdateOne {
	if u != nil {
		uuo.SetCreateBy(*u)
	}
	return uuo
}

// AddCreateBy adds u to the "create_by" field.
func (uuo *UserUpdateOne) AddCreateBy(u int32) *UserUpdateOne {
	uuo.mutation.AddCreateBy(u)
	return uuo
}

// ClearCreateBy clears the value of the "create_by" field.
func (uuo *UserUpdateOne) ClearCreateBy() *UserUpdateOne {
	uuo.mutation.ClearCreateBy()
	return uuo
}

// SetUpdateBy sets the "update_by" field.
func (uuo *UserUpdateOne) SetUpdateBy(u uint32) *UserUpdateOne {
	uuo.mutation.ResetUpdateBy()
	uuo.mutation.SetUpdateBy(u)
	return uuo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableUpdateBy(u *uint32) *UserUpdateOne {
	if u != nil {
		uuo.SetUpdateBy(*u)
	}
	return uuo
}

// AddUpdateBy adds u to the "update_by" field.
func (uuo *UserUpdateOne) AddUpdateBy(u int32) *UserUpdateOne {
	uuo.mutation.AddUpdateBy(u)
	return uuo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (uuo *UserUpdateOne) ClearUpdateBy() *UserUpdateOne {
	uuo.mutation.ClearUpdateBy()
	return uuo
}

// SetUpdateTime sets the "update_time" field.
func (uuo *UserUpdateOne) SetUpdateTime(t time.Time) *UserUpdateOne {
	uuo.mutation.SetUpdateTime(t)
	return uuo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableUpdateTime(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetUpdateTime(*t)
	}
	return uuo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (uuo *UserUpdateOne) ClearUpdateTime() *UserUpdateOne {
	uuo.mutation.ClearUpdateTime()
	return uuo
}

// SetDeleteTime sets the "delete_time" field.
func (uuo *UserUpdateOne) SetDeleteTime(t time.Time) *UserUpdateOne {
	uuo.mutation.SetDeleteTime(t)
	return uuo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDeleteTime(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetDeleteTime(*t)
	}
	return uuo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (uuo *UserUpdateOne) ClearDeleteTime() *UserUpdateOne {
	uuo.mutation.ClearDeleteTime()
	return uuo
}

// SetRemark sets the "remark" field.
func (uuo *UserUpdateOne) SetRemark(s string) *UserUpdateOne {
	uuo.mutation.SetRemark(s)
	return uuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableRemark(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetRemark(*s)
	}
	return uuo
}

// ClearRemark clears the value of the "remark" field.
func (uuo *UserUpdateOne) ClearRemark() *UserUpdateOne {
	uuo.mutation.ClearRemark()
	return uuo
}

// SetStatus sets the "status" field.
func (uuo *UserUpdateOne) SetStatus(u user.Status) *UserUpdateOne {
	uuo.mutation.SetStatus(u)
	return uuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableStatus(u *user.Status) *UserUpdateOne {
	if u != nil {
		uuo.SetStatus(*u)
	}
	return uuo
}

// ClearStatus clears the value of the "status" field.
func (uuo *UserUpdateOne) ClearStatus() *UserUpdateOne {
	uuo.mutation.ClearStatus()
	return uuo
}

// SetNickname sets the "nickname" field.
func (uuo *UserUpdateOne) SetNickname(s string) *UserUpdateOne {
	uuo.mutation.SetNickname(s)
	return uuo
}

// SetNillableNickname sets the "nickname" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableNickname(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetNickname(*s)
	}
	return uuo
}

// ClearNickname clears the value of the "nickname" field.
func (uuo *UserUpdateOne) ClearNickname() *UserUpdateOne {
	uuo.mutation.ClearNickname()
	return uuo
}

// SetRealname sets the "realname" field.
func (uuo *UserUpdateOne) SetRealname(s string) *UserUpdateOne {
	uuo.mutation.SetRealname(s)
	return uuo
}

// SetNillableRealname sets the "realname" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableRealname(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetRealname(*s)
	}
	return uuo
}

// ClearRealname clears the value of the "realname" field.
func (uuo *UserUpdateOne) ClearRealname() *UserUpdateOne {
	uuo.mutation.ClearRealname()
	return uuo
}

// SetEmail sets the "email" field.
func (uuo *UserUpdateOne) SetEmail(s string) *UserUpdateOne {
	uuo.mutation.SetEmail(s)
	return uuo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableEmail(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetEmail(*s)
	}
	return uuo
}

// ClearEmail clears the value of the "email" field.
func (uuo *UserUpdateOne) ClearEmail() *UserUpdateOne {
	uuo.mutation.ClearEmail()
	return uuo
}

// SetMobile sets the "mobile" field.
func (uuo *UserUpdateOne) SetMobile(s string) *UserUpdateOne {
	uuo.mutation.SetMobile(s)
	return uuo
}

// SetNillableMobile sets the "mobile" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableMobile(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetMobile(*s)
	}
	return uuo
}

// ClearMobile clears the value of the "mobile" field.
func (uuo *UserUpdateOne) ClearMobile() *UserUpdateOne {
	uuo.mutation.ClearMobile()
	return uuo
}

// SetTelephone sets the "telephone" field.
func (uuo *UserUpdateOne) SetTelephone(s string) *UserUpdateOne {
	uuo.mutation.SetTelephone(s)
	return uuo
}

// SetNillableTelephone sets the "telephone" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableTelephone(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetTelephone(*s)
	}
	return uuo
}

// ClearTelephone clears the value of the "telephone" field.
func (uuo *UserUpdateOne) ClearTelephone() *UserUpdateOne {
	uuo.mutation.ClearTelephone()
	return uuo
}

// SetAvatar sets the "avatar" field.
func (uuo *UserUpdateOne) SetAvatar(s string) *UserUpdateOne {
	uuo.mutation.SetAvatar(s)
	return uuo
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAvatar(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetAvatar(*s)
	}
	return uuo
}

// ClearAvatar clears the value of the "avatar" field.
func (uuo *UserUpdateOne) ClearAvatar() *UserUpdateOne {
	uuo.mutation.ClearAvatar()
	return uuo
}

// SetAddress sets the "address" field.
func (uuo *UserUpdateOne) SetAddress(s string) *UserUpdateOne {
	uuo.mutation.SetAddress(s)
	return uuo
}

// SetNillableAddress sets the "address" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAddress(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetAddress(*s)
	}
	return uuo
}

// ClearAddress clears the value of the "address" field.
func (uuo *UserUpdateOne) ClearAddress() *UserUpdateOne {
	uuo.mutation.ClearAddress()
	return uuo
}

// SetRegion sets the "region" field.
func (uuo *UserUpdateOne) SetRegion(s string) *UserUpdateOne {
	uuo.mutation.SetRegion(s)
	return uuo
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableRegion(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetRegion(*s)
	}
	return uuo
}

// ClearRegion clears the value of the "region" field.
func (uuo *UserUpdateOne) ClearRegion() *UserUpdateOne {
	uuo.mutation.ClearRegion()
	return uuo
}

// SetDescription sets the "description" field.
func (uuo *UserUpdateOne) SetDescription(s string) *UserUpdateOne {
	uuo.mutation.SetDescription(s)
	return uuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDescription(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetDescription(*s)
	}
	return uuo
}

// ClearDescription clears the value of the "description" field.
func (uuo *UserUpdateOne) ClearDescription() *UserUpdateOne {
	uuo.mutation.ClearDescription()
	return uuo
}

// SetGender sets the "gender" field.
func (uuo *UserUpdateOne) SetGender(u user.Gender) *UserUpdateOne {
	uuo.mutation.SetGender(u)
	return uuo
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableGender(u *user.Gender) *UserUpdateOne {
	if u != nil {
		uuo.SetGender(*u)
	}
	return uuo
}

// ClearGender clears the value of the "gender" field.
func (uuo *UserUpdateOne) ClearGender() *UserUpdateOne {
	uuo.mutation.ClearGender()
	return uuo
}

// SetAuthority sets the "authority" field.
func (uuo *UserUpdateOne) SetAuthority(u user.Authority) *UserUpdateOne {
	uuo.mutation.SetAuthority(u)
	return uuo
}

// SetNillableAuthority sets the "authority" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAuthority(u *user.Authority) *UserUpdateOne {
	if u != nil {
		uuo.SetAuthority(*u)
	}
	return uuo
}

// ClearAuthority clears the value of the "authority" field.
func (uuo *UserUpdateOne) ClearAuthority() *UserUpdateOne {
	uuo.mutation.ClearAuthority()
	return uuo
}

// SetLastLoginTime sets the "last_login_time" field.
func (uuo *UserUpdateOne) SetLastLoginTime(t time.Time) *UserUpdateOne {
	uuo.mutation.SetLastLoginTime(t)
	return uuo
}

// SetNillableLastLoginTime sets the "last_login_time" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableLastLoginTime(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetLastLoginTime(*t)
	}
	return uuo
}

// ClearLastLoginTime clears the value of the "last_login_time" field.
func (uuo *UserUpdateOne) ClearLastLoginTime() *UserUpdateOne {
	uuo.mutation.ClearLastLoginTime()
	return uuo
}

// SetLastLoginIP sets the "last_login_ip" field.
func (uuo *UserUpdateOne) SetLastLoginIP(s string) *UserUpdateOne {
	uuo.mutation.SetLastLoginIP(s)
	return uuo
}

// SetNillableLastLoginIP sets the "last_login_ip" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableLastLoginIP(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetLastLoginIP(*s)
	}
	return uuo
}

// ClearLastLoginIP clears the value of the "last_login_ip" field.
func (uuo *UserUpdateOne) ClearLastLoginIP() *UserUpdateOne {
	uuo.mutation.ClearLastLoginIP()
	return uuo
}

// SetOrgID sets the "org_id" field.
func (uuo *UserUpdateOne) SetOrgID(u uint32) *UserUpdateOne {
	uuo.mutation.ResetOrgID()
	uuo.mutation.SetOrgID(u)
	return uuo
}

// SetNillableOrgID sets the "org_id" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableOrgID(u *uint32) *UserUpdateOne {
	if u != nil {
		uuo.SetOrgID(*u)
	}
	return uuo
}

// AddOrgID adds u to the "org_id" field.
func (uuo *UserUpdateOne) AddOrgID(u int32) *UserUpdateOne {
	uuo.mutation.AddOrgID(u)
	return uuo
}

// ClearOrgID clears the value of the "org_id" field.
func (uuo *UserUpdateOne) ClearOrgID() *UserUpdateOne {
	uuo.mutation.ClearOrgID()
	return uuo
}

// SetPositionID sets the "position_id" field.
func (uuo *UserUpdateOne) SetPositionID(u uint32) *UserUpdateOne {
	uuo.mutation.ResetPositionID()
	uuo.mutation.SetPositionID(u)
	return uuo
}

// SetNillablePositionID sets the "position_id" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePositionID(u *uint32) *UserUpdateOne {
	if u != nil {
		uuo.SetPositionID(*u)
	}
	return uuo
}

// AddPositionID adds u to the "position_id" field.
func (uuo *UserUpdateOne) AddPositionID(u int32) *UserUpdateOne {
	uuo.mutation.AddPositionID(u)
	return uuo
}

// ClearPositionID clears the value of the "position_id" field.
func (uuo *UserUpdateOne) ClearPositionID() *UserUpdateOne {
	uuo.mutation.ClearPositionID()
	return uuo
}

// SetWorkID sets the "work_id" field.
func (uuo *UserUpdateOne) SetWorkID(u uint32) *UserUpdateOne {
	uuo.mutation.ResetWorkID()
	uuo.mutation.SetWorkID(u)
	return uuo
}

// SetNillableWorkID sets the "work_id" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableWorkID(u *uint32) *UserUpdateOne {
	if u != nil {
		uuo.SetWorkID(*u)
	}
	return uuo
}

// AddWorkID adds u to the "work_id" field.
func (uuo *UserUpdateOne) AddWorkID(u int32) *UserUpdateOne {
	uuo.mutation.AddWorkID(u)
	return uuo
}

// ClearWorkID clears the value of the "work_id" field.
func (uuo *UserUpdateOne) ClearWorkID() *UserUpdateOne {
	uuo.mutation.ClearWorkID()
	return uuo
}

// SetRoles sets the "roles" field.
func (uuo *UserUpdateOne) SetRoles(s []string) *UserUpdateOne {
	uuo.mutation.SetRoles(s)
	return uuo
}

// AppendRoles appends s to the "roles" field.
func (uuo *UserUpdateOne) AppendRoles(s []string) *UserUpdateOne {
	uuo.mutation.AppendRoles(s)
	return uuo
}

// ClearRoles clears the value of the "roles" field.
func (uuo *UserUpdateOne) ClearRoles() *UserUpdateOne {
	uuo.mutation.ClearRoles()
	return uuo
}

// Mutation returns the UserMutation object of the builder.
func (uuo *UserUpdateOne) Mutation() *UserMutation {
	return uuo.mutation
}

// Where appends a list predicates to the UserUpdate builder.
func (uuo *UserUpdateOne) Where(ps ...predicate.User) *UserUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UserUpdateOne) Select(field string, fields ...string) *UserUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated User entity.
func (uuo *UserUpdateOne) Save(ctx context.Context) (*User, error) {
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UserUpdateOne) SaveX(ctx context.Context) *User {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UserUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UserUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UserUpdateOne) check() error {
	if v, ok := uuo.mutation.Status(); ok {
		if err := user.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "User.status": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Nickname(); ok {
		if err := user.NicknameValidator(v); err != nil {
			return &ValidationError{Name: "nickname", err: fmt.Errorf(`ent: validator failed for field "User.nickname": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Realname(); ok {
		if err := user.RealnameValidator(v); err != nil {
			return &ValidationError{Name: "realname", err: fmt.Errorf(`ent: validator failed for field "User.realname": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Mobile(); ok {
		if err := user.MobileValidator(v); err != nil {
			return &ValidationError{Name: "mobile", err: fmt.Errorf(`ent: validator failed for field "User.mobile": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Telephone(); ok {
		if err := user.TelephoneValidator(v); err != nil {
			return &ValidationError{Name: "telephone", err: fmt.Errorf(`ent: validator failed for field "User.telephone": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Avatar(); ok {
		if err := user.AvatarValidator(v); err != nil {
			return &ValidationError{Name: "avatar", err: fmt.Errorf(`ent: validator failed for field "User.avatar": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Address(); ok {
		if err := user.AddressValidator(v); err != nil {
			return &ValidationError{Name: "address", err: fmt.Errorf(`ent: validator failed for field "User.address": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Region(); ok {
		if err := user.RegionValidator(v); err != nil {
			return &ValidationError{Name: "region", err: fmt.Errorf(`ent: validator failed for field "User.region": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Description(); ok {
		if err := user.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "User.description": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Gender(); ok {
		if err := user.GenderValidator(v); err != nil {
			return &ValidationError{Name: "gender", err: fmt.Errorf(`ent: validator failed for field "User.gender": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Authority(); ok {
		if err := user.AuthorityValidator(v); err != nil {
			return &ValidationError{Name: "authority", err: fmt.Errorf(`ent: validator failed for field "User.authority": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.LastLoginIP(); ok {
		if err := user.LastLoginIPValidator(v); err != nil {
			return &ValidationError{Name: "last_login_ip", err: fmt.Errorf(`ent: validator failed for field "User.last_login_ip": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (uuo *UserUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UserUpdateOne {
	uuo.modifiers = append(uuo.modifiers, modifiers...)
	return uuo
}

func (uuo *UserUpdateOne) sqlSave(ctx context.Context) (_node *User, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeUint32))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "User.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for _, f := range fields {
			if !user.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.CreateBy(); ok {
		_spec.SetField(user.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := uuo.mutation.AddedCreateBy(); ok {
		_spec.AddField(user.FieldCreateBy, field.TypeUint32, value)
	}
	if uuo.mutation.CreateByCleared() {
		_spec.ClearField(user.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := uuo.mutation.UpdateBy(); ok {
		_spec.SetField(user.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := uuo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(user.FieldUpdateBy, field.TypeUint32, value)
	}
	if uuo.mutation.UpdateByCleared() {
		_spec.ClearField(user.FieldUpdateBy, field.TypeUint32)
	}
	if uuo.mutation.CreateTimeCleared() {
		_spec.ClearField(user.FieldCreateTime, field.TypeTime)
	}
	if value, ok := uuo.mutation.UpdateTime(); ok {
		_spec.SetField(user.FieldUpdateTime, field.TypeTime, value)
	}
	if uuo.mutation.UpdateTimeCleared() {
		_spec.ClearField(user.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := uuo.mutation.DeleteTime(); ok {
		_spec.SetField(user.FieldDeleteTime, field.TypeTime, value)
	}
	if uuo.mutation.DeleteTimeCleared() {
		_spec.ClearField(user.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := uuo.mutation.Remark(); ok {
		_spec.SetField(user.FieldRemark, field.TypeString, value)
	}
	if uuo.mutation.RemarkCleared() {
		_spec.ClearField(user.FieldRemark, field.TypeString)
	}
	if value, ok := uuo.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeEnum, value)
	}
	if uuo.mutation.StatusCleared() {
		_spec.ClearField(user.FieldStatus, field.TypeEnum)
	}
	if uuo.mutation.TenantIDCleared() {
		_spec.ClearField(user.FieldTenantID, field.TypeUint32)
	}
	if uuo.mutation.UsernameCleared() {
		_spec.ClearField(user.FieldUsername, field.TypeString)
	}
	if value, ok := uuo.mutation.Nickname(); ok {
		_spec.SetField(user.FieldNickname, field.TypeString, value)
	}
	if uuo.mutation.NicknameCleared() {
		_spec.ClearField(user.FieldNickname, field.TypeString)
	}
	if value, ok := uuo.mutation.Realname(); ok {
		_spec.SetField(user.FieldRealname, field.TypeString, value)
	}
	if uuo.mutation.RealnameCleared() {
		_spec.ClearField(user.FieldRealname, field.TypeString)
	}
	if value, ok := uuo.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if uuo.mutation.EmailCleared() {
		_spec.ClearField(user.FieldEmail, field.TypeString)
	}
	if value, ok := uuo.mutation.Mobile(); ok {
		_spec.SetField(user.FieldMobile, field.TypeString, value)
	}
	if uuo.mutation.MobileCleared() {
		_spec.ClearField(user.FieldMobile, field.TypeString)
	}
	if value, ok := uuo.mutation.Telephone(); ok {
		_spec.SetField(user.FieldTelephone, field.TypeString, value)
	}
	if uuo.mutation.TelephoneCleared() {
		_spec.ClearField(user.FieldTelephone, field.TypeString)
	}
	if value, ok := uuo.mutation.Avatar(); ok {
		_spec.SetField(user.FieldAvatar, field.TypeString, value)
	}
	if uuo.mutation.AvatarCleared() {
		_spec.ClearField(user.FieldAvatar, field.TypeString)
	}
	if value, ok := uuo.mutation.Address(); ok {
		_spec.SetField(user.FieldAddress, field.TypeString, value)
	}
	if uuo.mutation.AddressCleared() {
		_spec.ClearField(user.FieldAddress, field.TypeString)
	}
	if value, ok := uuo.mutation.Region(); ok {
		_spec.SetField(user.FieldRegion, field.TypeString, value)
	}
	if uuo.mutation.RegionCleared() {
		_spec.ClearField(user.FieldRegion, field.TypeString)
	}
	if value, ok := uuo.mutation.Description(); ok {
		_spec.SetField(user.FieldDescription, field.TypeString, value)
	}
	if uuo.mutation.DescriptionCleared() {
		_spec.ClearField(user.FieldDescription, field.TypeString)
	}
	if value, ok := uuo.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeEnum, value)
	}
	if uuo.mutation.GenderCleared() {
		_spec.ClearField(user.FieldGender, field.TypeEnum)
	}
	if value, ok := uuo.mutation.Authority(); ok {
		_spec.SetField(user.FieldAuthority, field.TypeEnum, value)
	}
	if uuo.mutation.AuthorityCleared() {
		_spec.ClearField(user.FieldAuthority, field.TypeEnum)
	}
	if value, ok := uuo.mutation.LastLoginTime(); ok {
		_spec.SetField(user.FieldLastLoginTime, field.TypeTime, value)
	}
	if uuo.mutation.LastLoginTimeCleared() {
		_spec.ClearField(user.FieldLastLoginTime, field.TypeTime)
	}
	if value, ok := uuo.mutation.LastLoginIP(); ok {
		_spec.SetField(user.FieldLastLoginIP, field.TypeString, value)
	}
	if uuo.mutation.LastLoginIPCleared() {
		_spec.ClearField(user.FieldLastLoginIP, field.TypeString)
	}
	if value, ok := uuo.mutation.OrgID(); ok {
		_spec.SetField(user.FieldOrgID, field.TypeUint32, value)
	}
	if value, ok := uuo.mutation.AddedOrgID(); ok {
		_spec.AddField(user.FieldOrgID, field.TypeUint32, value)
	}
	if uuo.mutation.OrgIDCleared() {
		_spec.ClearField(user.FieldOrgID, field.TypeUint32)
	}
	if value, ok := uuo.mutation.PositionID(); ok {
		_spec.SetField(user.FieldPositionID, field.TypeUint32, value)
	}
	if value, ok := uuo.mutation.AddedPositionID(); ok {
		_spec.AddField(user.FieldPositionID, field.TypeUint32, value)
	}
	if uuo.mutation.PositionIDCleared() {
		_spec.ClearField(user.FieldPositionID, field.TypeUint32)
	}
	if value, ok := uuo.mutation.WorkID(); ok {
		_spec.SetField(user.FieldWorkID, field.TypeUint32, value)
	}
	if value, ok := uuo.mutation.AddedWorkID(); ok {
		_spec.AddField(user.FieldWorkID, field.TypeUint32, value)
	}
	if uuo.mutation.WorkIDCleared() {
		_spec.ClearField(user.FieldWorkID, field.TypeUint32)
	}
	if value, ok := uuo.mutation.Roles(); ok {
		_spec.SetField(user.FieldRoles, field.TypeJSON, value)
	}
	if value, ok := uuo.mutation.AppendedRoles(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, user.FieldRoles, value)
		})
	}
	if uuo.mutation.RolesCleared() {
		_spec.ClearField(user.FieldRoles, field.TypeJSON)
	}
	_spec.AddModifiers(uuo.modifiers...)
	_node = &User{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}
