// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminOperationLogQuery is the builder for querying AdminOperationLog entities.
type AdminOperationLogQuery struct {
	config
	ctx        *QueryContext
	order      []adminoperationlog.OrderOption
	inters     []Interceptor
	predicates []predicate.AdminOperationLog
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AdminOperationLogQuery builder.
func (aolq *AdminOperationLogQuery) Where(ps ...predicate.AdminOperationLog) *AdminOperationLogQuery {
	aolq.predicates = append(aolq.predicates, ps...)
	return aolq
}

// Limit the number of records to be returned by this query.
func (aolq *AdminOperationLogQuery) Limit(limit int) *AdminOperationLogQuery {
	aolq.ctx.Limit = &limit
	return aolq
}

// Offset to start from.
func (aolq *AdminOperationLogQuery) Offset(offset int) *AdminOperationLogQuery {
	aolq.ctx.Offset = &offset
	return aolq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (aolq *AdminOperationLogQuery) Unique(unique bool) *AdminOperationLogQuery {
	aolq.ctx.Unique = &unique
	return aolq
}

// Order specifies how the records should be ordered.
func (aolq *AdminOperationLogQuery) Order(o ...adminoperationlog.OrderOption) *AdminOperationLogQuery {
	aolq.order = append(aolq.order, o...)
	return aolq
}

// First returns the first AdminOperationLog entity from the query.
// Returns a *NotFoundError when no AdminOperationLog was found.
func (aolq *AdminOperationLogQuery) First(ctx context.Context) (*AdminOperationLog, error) {
	nodes, err := aolq.Limit(1).All(setContextOp(ctx, aolq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{adminoperationlog.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) FirstX(ctx context.Context) *AdminOperationLog {
	node, err := aolq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AdminOperationLog ID from the query.
// Returns a *NotFoundError when no AdminOperationLog ID was found.
func (aolq *AdminOperationLogQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = aolq.Limit(1).IDs(setContextOp(ctx, aolq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{adminoperationlog.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := aolq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AdminOperationLog entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AdminOperationLog entity is found.
// Returns a *NotFoundError when no AdminOperationLog entities are found.
func (aolq *AdminOperationLogQuery) Only(ctx context.Context) (*AdminOperationLog, error) {
	nodes, err := aolq.Limit(2).All(setContextOp(ctx, aolq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{adminoperationlog.Label}
	default:
		return nil, &NotSingularError{adminoperationlog.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) OnlyX(ctx context.Context) *AdminOperationLog {
	node, err := aolq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AdminOperationLog ID in the query.
// Returns a *NotSingularError when more than one AdminOperationLog ID is found.
// Returns a *NotFoundError when no entities are found.
func (aolq *AdminOperationLogQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = aolq.Limit(2).IDs(setContextOp(ctx, aolq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{adminoperationlog.Label}
	default:
		err = &NotSingularError{adminoperationlog.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := aolq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AdminOperationLogs.
func (aolq *AdminOperationLogQuery) All(ctx context.Context) ([]*AdminOperationLog, error) {
	ctx = setContextOp(ctx, aolq.ctx, ent.OpQueryAll)
	if err := aolq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AdminOperationLog, *AdminOperationLogQuery]()
	return withInterceptors[[]*AdminOperationLog](ctx, aolq, qr, aolq.inters)
}

// AllX is like All, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) AllX(ctx context.Context) []*AdminOperationLog {
	nodes, err := aolq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AdminOperationLog IDs.
func (aolq *AdminOperationLogQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if aolq.ctx.Unique == nil && aolq.path != nil {
		aolq.Unique(true)
	}
	ctx = setContextOp(ctx, aolq.ctx, ent.OpQueryIDs)
	if err = aolq.Select(adminoperationlog.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := aolq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (aolq *AdminOperationLogQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, aolq.ctx, ent.OpQueryCount)
	if err := aolq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, aolq, querierCount[*AdminOperationLogQuery](), aolq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) CountX(ctx context.Context) int {
	count, err := aolq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (aolq *AdminOperationLogQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, aolq.ctx, ent.OpQueryExist)
	switch _, err := aolq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (aolq *AdminOperationLogQuery) ExistX(ctx context.Context) bool {
	exist, err := aolq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AdminOperationLogQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (aolq *AdminOperationLogQuery) Clone() *AdminOperationLogQuery {
	if aolq == nil {
		return nil
	}
	return &AdminOperationLogQuery{
		config:     aolq.config,
		ctx:        aolq.ctx.Clone(),
		order:      append([]adminoperationlog.OrderOption{}, aolq.order...),
		inters:     append([]Interceptor{}, aolq.inters...),
		predicates: append([]predicate.AdminOperationLog{}, aolq.predicates...),
		// clone intermediate query.
		sql:       aolq.sql.Clone(),
		path:      aolq.path,
		modifiers: append([]func(*sql.Selector){}, aolq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AdminOperationLog.Query().
//		GroupBy(adminoperationlog.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (aolq *AdminOperationLogQuery) GroupBy(field string, fields ...string) *AdminOperationLogGroupBy {
	aolq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AdminOperationLogGroupBy{build: aolq}
	grbuild.flds = &aolq.ctx.Fields
	grbuild.label = adminoperationlog.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.AdminOperationLog.Query().
//		Select(adminoperationlog.FieldCreateTime).
//		Scan(ctx, &v)
func (aolq *AdminOperationLogQuery) Select(fields ...string) *AdminOperationLogSelect {
	aolq.ctx.Fields = append(aolq.ctx.Fields, fields...)
	sbuild := &AdminOperationLogSelect{AdminOperationLogQuery: aolq}
	sbuild.label = adminoperationlog.Label
	sbuild.flds, sbuild.scan = &aolq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AdminOperationLogSelect configured with the given aggregations.
func (aolq *AdminOperationLogQuery) Aggregate(fns ...AggregateFunc) *AdminOperationLogSelect {
	return aolq.Select().Aggregate(fns...)
}

func (aolq *AdminOperationLogQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range aolq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, aolq); err != nil {
				return err
			}
		}
	}
	for _, f := range aolq.ctx.Fields {
		if !adminoperationlog.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if aolq.path != nil {
		prev, err := aolq.path(ctx)
		if err != nil {
			return err
		}
		aolq.sql = prev
	}
	return nil
}

func (aolq *AdminOperationLogQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AdminOperationLog, error) {
	var (
		nodes = []*AdminOperationLog{}
		_spec = aolq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AdminOperationLog).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AdminOperationLog{config: aolq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(aolq.modifiers) > 0 {
		_spec.Modifiers = aolq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, aolq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (aolq *AdminOperationLogQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := aolq.querySpec()
	if len(aolq.modifiers) > 0 {
		_spec.Modifiers = aolq.modifiers
	}
	_spec.Node.Columns = aolq.ctx.Fields
	if len(aolq.ctx.Fields) > 0 {
		_spec.Unique = aolq.ctx.Unique != nil && *aolq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, aolq.driver, _spec)
}

func (aolq *AdminOperationLogQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(adminoperationlog.Table, adminoperationlog.Columns, sqlgraph.NewFieldSpec(adminoperationlog.FieldID, field.TypeUint32))
	_spec.From = aolq.sql
	if unique := aolq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if aolq.path != nil {
		_spec.Unique = true
	}
	if fields := aolq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, adminoperationlog.FieldID)
		for i := range fields {
			if fields[i] != adminoperationlog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := aolq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := aolq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := aolq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := aolq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (aolq *AdminOperationLogQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(aolq.driver.Dialect())
	t1 := builder.Table(adminoperationlog.Table)
	columns := aolq.ctx.Fields
	if len(columns) == 0 {
		columns = adminoperationlog.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if aolq.sql != nil {
		selector = aolq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if aolq.ctx.Unique != nil && *aolq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range aolq.modifiers {
		m(selector)
	}
	for _, p := range aolq.predicates {
		p(selector)
	}
	for _, p := range aolq.order {
		p(selector)
	}
	if offset := aolq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := aolq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (aolq *AdminOperationLogQuery) ForUpdate(opts ...sql.LockOption) *AdminOperationLogQuery {
	if aolq.driver.Dialect() == dialect.Postgres {
		aolq.Unique(false)
	}
	aolq.modifiers = append(aolq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return aolq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (aolq *AdminOperationLogQuery) ForShare(opts ...sql.LockOption) *AdminOperationLogQuery {
	if aolq.driver.Dialect() == dialect.Postgres {
		aolq.Unique(false)
	}
	aolq.modifiers = append(aolq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return aolq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aolq *AdminOperationLogQuery) Modify(modifiers ...func(s *sql.Selector)) *AdminOperationLogSelect {
	aolq.modifiers = append(aolq.modifiers, modifiers...)
	return aolq.Select()
}

// AdminOperationLogGroupBy is the group-by builder for AdminOperationLog entities.
type AdminOperationLogGroupBy struct {
	selector
	build *AdminOperationLogQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (aolgb *AdminOperationLogGroupBy) Aggregate(fns ...AggregateFunc) *AdminOperationLogGroupBy {
	aolgb.fns = append(aolgb.fns, fns...)
	return aolgb
}

// Scan applies the selector query and scans the result into the given value.
func (aolgb *AdminOperationLogGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aolgb.build.ctx, ent.OpQueryGroupBy)
	if err := aolgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AdminOperationLogQuery, *AdminOperationLogGroupBy](ctx, aolgb.build, aolgb, aolgb.build.inters, v)
}

func (aolgb *AdminOperationLogGroupBy) sqlScan(ctx context.Context, root *AdminOperationLogQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(aolgb.fns))
	for _, fn := range aolgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*aolgb.flds)+len(aolgb.fns))
		for _, f := range *aolgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*aolgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aolgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AdminOperationLogSelect is the builder for selecting fields of AdminOperationLog entities.
type AdminOperationLogSelect struct {
	*AdminOperationLogQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (aols *AdminOperationLogSelect) Aggregate(fns ...AggregateFunc) *AdminOperationLogSelect {
	aols.fns = append(aols.fns, fns...)
	return aols
}

// Scan applies the selector query and scans the result into the given value.
func (aols *AdminOperationLogSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aols.ctx, ent.OpQuerySelect)
	if err := aols.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AdminOperationLogQuery, *AdminOperationLogSelect](ctx, aols.AdminOperationLogQuery, aols, aols.inters, v)
}

func (aols *AdminOperationLogSelect) sqlScan(ctx context.Context, root *AdminOperationLogQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(aols.fns))
	for _, fn := range aols.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*aols.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aols.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aols *AdminOperationLogSelect) Modify(modifiers ...func(s *sql.Selector)) *AdminOperationLogSelect {
	aols.modifiers = append(aols.modifiers, modifiers...)
	return aols
}
