// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/privatemessage"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PrivateMessageCreate is the builder for creating a PrivateMessage entity.
type PrivateMessageCreate struct {
	config
	mutation *PrivateMessageMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (pmc *PrivateMessageCreate) SetCreateTime(t time.Time) *PrivateMessageCreate {
	pmc.mutation.SetCreateTime(t)
	return pmc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableCreateTime(t *time.Time) *PrivateMessageCreate {
	if t != nil {
		pmc.SetCreateTime(*t)
	}
	return pmc
}

// SetUpdateTime sets the "update_time" field.
func (pmc *PrivateMessageCreate) SetUpdateTime(t time.Time) *PrivateMessageCreate {
	pmc.mutation.SetUpdateTime(t)
	return pmc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableUpdateTime(t *time.Time) *PrivateMessageCreate {
	if t != nil {
		pmc.SetUpdateTime(*t)
	}
	return pmc
}

// SetDeleteTime sets the "delete_time" field.
func (pmc *PrivateMessageCreate) SetDeleteTime(t time.Time) *PrivateMessageCreate {
	pmc.mutation.SetDeleteTime(t)
	return pmc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableDeleteTime(t *time.Time) *PrivateMessageCreate {
	if t != nil {
		pmc.SetDeleteTime(*t)
	}
	return pmc
}

// SetTenantID sets the "tenant_id" field.
func (pmc *PrivateMessageCreate) SetTenantID(u uint32) *PrivateMessageCreate {
	pmc.mutation.SetTenantID(u)
	return pmc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableTenantID(u *uint32) *PrivateMessageCreate {
	if u != nil {
		pmc.SetTenantID(*u)
	}
	return pmc
}

// SetSubject sets the "subject" field.
func (pmc *PrivateMessageCreate) SetSubject(s string) *PrivateMessageCreate {
	pmc.mutation.SetSubject(s)
	return pmc
}

// SetNillableSubject sets the "subject" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableSubject(s *string) *PrivateMessageCreate {
	if s != nil {
		pmc.SetSubject(*s)
	}
	return pmc
}

// SetContent sets the "content" field.
func (pmc *PrivateMessageCreate) SetContent(s string) *PrivateMessageCreate {
	pmc.mutation.SetContent(s)
	return pmc
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableContent(s *string) *PrivateMessageCreate {
	if s != nil {
		pmc.SetContent(*s)
	}
	return pmc
}

// SetStatus sets the "status" field.
func (pmc *PrivateMessageCreate) SetStatus(pr privatemessage.Status) *PrivateMessageCreate {
	pmc.mutation.SetStatus(pr)
	return pmc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableStatus(pr *privatemessage.Status) *PrivateMessageCreate {
	if pr != nil {
		pmc.SetStatus(*pr)
	}
	return pmc
}

// SetSenderID sets the "sender_id" field.
func (pmc *PrivateMessageCreate) SetSenderID(u uint32) *PrivateMessageCreate {
	pmc.mutation.SetSenderID(u)
	return pmc
}

// SetNillableSenderID sets the "sender_id" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableSenderID(u *uint32) *PrivateMessageCreate {
	if u != nil {
		pmc.SetSenderID(*u)
	}
	return pmc
}

// SetReceiverID sets the "receiver_id" field.
func (pmc *PrivateMessageCreate) SetReceiverID(u uint32) *PrivateMessageCreate {
	pmc.mutation.SetReceiverID(u)
	return pmc
}

// SetNillableReceiverID sets the "receiver_id" field if the given value is not nil.
func (pmc *PrivateMessageCreate) SetNillableReceiverID(u *uint32) *PrivateMessageCreate {
	if u != nil {
		pmc.SetReceiverID(*u)
	}
	return pmc
}

// SetID sets the "id" field.
func (pmc *PrivateMessageCreate) SetID(u uint32) *PrivateMessageCreate {
	pmc.mutation.SetID(u)
	return pmc
}

// Mutation returns the PrivateMessageMutation object of the builder.
func (pmc *PrivateMessageCreate) Mutation() *PrivateMessageMutation {
	return pmc.mutation
}

// Save creates the PrivateMessage in the database.
func (pmc *PrivateMessageCreate) Save(ctx context.Context) (*PrivateMessage, error) {
	return withHooks(ctx, pmc.sqlSave, pmc.mutation, pmc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pmc *PrivateMessageCreate) SaveX(ctx context.Context) *PrivateMessage {
	v, err := pmc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pmc *PrivateMessageCreate) Exec(ctx context.Context) error {
	_, err := pmc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pmc *PrivateMessageCreate) ExecX(ctx context.Context) {
	if err := pmc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pmc *PrivateMessageCreate) check() error {
	if v, ok := pmc.mutation.TenantID(); ok {
		if err := privatemessage.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "PrivateMessage.tenant_id": %w`, err)}
		}
	}
	if v, ok := pmc.mutation.Status(); ok {
		if err := privatemessage.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PrivateMessage.status": %w`, err)}
		}
	}
	if v, ok := pmc.mutation.ID(); ok {
		if err := privatemessage.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "PrivateMessage.id": %w`, err)}
		}
	}
	return nil
}

func (pmc *PrivateMessageCreate) sqlSave(ctx context.Context) (*PrivateMessage, error) {
	if err := pmc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pmc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pmc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	pmc.mutation.id = &_node.ID
	pmc.mutation.done = true
	return _node, nil
}

func (pmc *PrivateMessageCreate) createSpec() (*PrivateMessage, *sqlgraph.CreateSpec) {
	var (
		_node = &PrivateMessage{config: pmc.config}
		_spec = sqlgraph.NewCreateSpec(privatemessage.Table, sqlgraph.NewFieldSpec(privatemessage.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = pmc.conflict
	if id, ok := pmc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := pmc.mutation.CreateTime(); ok {
		_spec.SetField(privatemessage.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := pmc.mutation.UpdateTime(); ok {
		_spec.SetField(privatemessage.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := pmc.mutation.DeleteTime(); ok {
		_spec.SetField(privatemessage.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := pmc.mutation.TenantID(); ok {
		_spec.SetField(privatemessage.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := pmc.mutation.Subject(); ok {
		_spec.SetField(privatemessage.FieldSubject, field.TypeString, value)
		_node.Subject = &value
	}
	if value, ok := pmc.mutation.Content(); ok {
		_spec.SetField(privatemessage.FieldContent, field.TypeString, value)
		_node.Content = &value
	}
	if value, ok := pmc.mutation.Status(); ok {
		_spec.SetField(privatemessage.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := pmc.mutation.SenderID(); ok {
		_spec.SetField(privatemessage.FieldSenderID, field.TypeUint32, value)
		_node.SenderID = &value
	}
	if value, ok := pmc.mutation.ReceiverID(); ok {
		_spec.SetField(privatemessage.FieldReceiverID, field.TypeUint32, value)
		_node.ReceiverID = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PrivateMessage.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PrivateMessageUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (pmc *PrivateMessageCreate) OnConflict(opts ...sql.ConflictOption) *PrivateMessageUpsertOne {
	pmc.conflict = opts
	return &PrivateMessageUpsertOne{
		create: pmc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PrivateMessage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (pmc *PrivateMessageCreate) OnConflictColumns(columns ...string) *PrivateMessageUpsertOne {
	pmc.conflict = append(pmc.conflict, sql.ConflictColumns(columns...))
	return &PrivateMessageUpsertOne{
		create: pmc,
	}
}

type (
	// PrivateMessageUpsertOne is the builder for "upsert"-ing
	//  one PrivateMessage node.
	PrivateMessageUpsertOne struct {
		create *PrivateMessageCreate
	}

	// PrivateMessageUpsert is the "OnConflict" setter.
	PrivateMessageUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *PrivateMessageUpsert) SetUpdateTime(v time.Time) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateUpdateTime() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *PrivateMessageUpsert) ClearUpdateTime() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *PrivateMessageUpsert) SetDeleteTime(v time.Time) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateDeleteTime() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *PrivateMessageUpsert) ClearDeleteTime() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldDeleteTime)
	return u
}

// SetSubject sets the "subject" field.
func (u *PrivateMessageUpsert) SetSubject(v string) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldSubject, v)
	return u
}

// UpdateSubject sets the "subject" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateSubject() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldSubject)
	return u
}

// ClearSubject clears the value of the "subject" field.
func (u *PrivateMessageUpsert) ClearSubject() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldSubject)
	return u
}

// SetContent sets the "content" field.
func (u *PrivateMessageUpsert) SetContent(v string) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldContent, v)
	return u
}

// UpdateContent sets the "content" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateContent() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldContent)
	return u
}

// ClearContent clears the value of the "content" field.
func (u *PrivateMessageUpsert) ClearContent() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldContent)
	return u
}

// SetStatus sets the "status" field.
func (u *PrivateMessageUpsert) SetStatus(v privatemessage.Status) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateStatus() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *PrivateMessageUpsert) ClearStatus() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldStatus)
	return u
}

// SetSenderID sets the "sender_id" field.
func (u *PrivateMessageUpsert) SetSenderID(v uint32) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldSenderID, v)
	return u
}

// UpdateSenderID sets the "sender_id" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateSenderID() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldSenderID)
	return u
}

// AddSenderID adds v to the "sender_id" field.
func (u *PrivateMessageUpsert) AddSenderID(v uint32) *PrivateMessageUpsert {
	u.Add(privatemessage.FieldSenderID, v)
	return u
}

// ClearSenderID clears the value of the "sender_id" field.
func (u *PrivateMessageUpsert) ClearSenderID() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldSenderID)
	return u
}

// SetReceiverID sets the "receiver_id" field.
func (u *PrivateMessageUpsert) SetReceiverID(v uint32) *PrivateMessageUpsert {
	u.Set(privatemessage.FieldReceiverID, v)
	return u
}

// UpdateReceiverID sets the "receiver_id" field to the value that was provided on create.
func (u *PrivateMessageUpsert) UpdateReceiverID() *PrivateMessageUpsert {
	u.SetExcluded(privatemessage.FieldReceiverID)
	return u
}

// AddReceiverID adds v to the "receiver_id" field.
func (u *PrivateMessageUpsert) AddReceiverID(v uint32) *PrivateMessageUpsert {
	u.Add(privatemessage.FieldReceiverID, v)
	return u
}

// ClearReceiverID clears the value of the "receiver_id" field.
func (u *PrivateMessageUpsert) ClearReceiverID() *PrivateMessageUpsert {
	u.SetNull(privatemessage.FieldReceiverID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.PrivateMessage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(privatemessage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PrivateMessageUpsertOne) UpdateNewValues() *PrivateMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(privatemessage.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(privatemessage.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(privatemessage.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PrivateMessage.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PrivateMessageUpsertOne) Ignore() *PrivateMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PrivateMessageUpsertOne) DoNothing() *PrivateMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PrivateMessageCreate.OnConflict
// documentation for more info.
func (u *PrivateMessageUpsertOne) Update(set func(*PrivateMessageUpsert)) *PrivateMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PrivateMessageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *PrivateMessageUpsertOne) SetUpdateTime(v time.Time) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateUpdateTime() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *PrivateMessageUpsertOne) ClearUpdateTime() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *PrivateMessageUpsertOne) SetDeleteTime(v time.Time) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateDeleteTime() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *PrivateMessageUpsertOne) ClearDeleteTime() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearDeleteTime()
	})
}

// SetSubject sets the "subject" field.
func (u *PrivateMessageUpsertOne) SetSubject(v string) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetSubject(v)
	})
}

// UpdateSubject sets the "subject" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateSubject() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateSubject()
	})
}

// ClearSubject clears the value of the "subject" field.
func (u *PrivateMessageUpsertOne) ClearSubject() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearSubject()
	})
}

// SetContent sets the "content" field.
func (u *PrivateMessageUpsertOne) SetContent(v string) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetContent(v)
	})
}

// UpdateContent sets the "content" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateContent() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateContent()
	})
}

// ClearContent clears the value of the "content" field.
func (u *PrivateMessageUpsertOne) ClearContent() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearContent()
	})
}

// SetStatus sets the "status" field.
func (u *PrivateMessageUpsertOne) SetStatus(v privatemessage.Status) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateStatus() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *PrivateMessageUpsertOne) ClearStatus() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearStatus()
	})
}

// SetSenderID sets the "sender_id" field.
func (u *PrivateMessageUpsertOne) SetSenderID(v uint32) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetSenderID(v)
	})
}

// AddSenderID adds v to the "sender_id" field.
func (u *PrivateMessageUpsertOne) AddSenderID(v uint32) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.AddSenderID(v)
	})
}

// UpdateSenderID sets the "sender_id" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateSenderID() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateSenderID()
	})
}

// ClearSenderID clears the value of the "sender_id" field.
func (u *PrivateMessageUpsertOne) ClearSenderID() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearSenderID()
	})
}

// SetReceiverID sets the "receiver_id" field.
func (u *PrivateMessageUpsertOne) SetReceiverID(v uint32) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetReceiverID(v)
	})
}

// AddReceiverID adds v to the "receiver_id" field.
func (u *PrivateMessageUpsertOne) AddReceiverID(v uint32) *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.AddReceiverID(v)
	})
}

// UpdateReceiverID sets the "receiver_id" field to the value that was provided on create.
func (u *PrivateMessageUpsertOne) UpdateReceiverID() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateReceiverID()
	})
}

// ClearReceiverID clears the value of the "receiver_id" field.
func (u *PrivateMessageUpsertOne) ClearReceiverID() *PrivateMessageUpsertOne {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearReceiverID()
	})
}

// Exec executes the query.
func (u *PrivateMessageUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for PrivateMessageCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PrivateMessageUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PrivateMessageUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PrivateMessageUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PrivateMessageCreateBulk is the builder for creating many PrivateMessage entities in bulk.
type PrivateMessageCreateBulk struct {
	config
	err      error
	builders []*PrivateMessageCreate
	conflict []sql.ConflictOption
}

// Save creates the PrivateMessage entities in the database.
func (pmcb *PrivateMessageCreateBulk) Save(ctx context.Context) ([]*PrivateMessage, error) {
	if pmcb.err != nil {
		return nil, pmcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pmcb.builders))
	nodes := make([]*PrivateMessage, len(pmcb.builders))
	mutators := make([]Mutator, len(pmcb.builders))
	for i := range pmcb.builders {
		func(i int, root context.Context) {
			builder := pmcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PrivateMessageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pmcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = pmcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pmcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pmcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pmcb *PrivateMessageCreateBulk) SaveX(ctx context.Context) []*PrivateMessage {
	v, err := pmcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pmcb *PrivateMessageCreateBulk) Exec(ctx context.Context) error {
	_, err := pmcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pmcb *PrivateMessageCreateBulk) ExecX(ctx context.Context) {
	if err := pmcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PrivateMessage.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PrivateMessageUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (pmcb *PrivateMessageCreateBulk) OnConflict(opts ...sql.ConflictOption) *PrivateMessageUpsertBulk {
	pmcb.conflict = opts
	return &PrivateMessageUpsertBulk{
		create: pmcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PrivateMessage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (pmcb *PrivateMessageCreateBulk) OnConflictColumns(columns ...string) *PrivateMessageUpsertBulk {
	pmcb.conflict = append(pmcb.conflict, sql.ConflictColumns(columns...))
	return &PrivateMessageUpsertBulk{
		create: pmcb,
	}
}

// PrivateMessageUpsertBulk is the builder for "upsert"-ing
// a bulk of PrivateMessage nodes.
type PrivateMessageUpsertBulk struct {
	create *PrivateMessageCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.PrivateMessage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(privatemessage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PrivateMessageUpsertBulk) UpdateNewValues() *PrivateMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(privatemessage.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(privatemessage.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(privatemessage.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PrivateMessage.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PrivateMessageUpsertBulk) Ignore() *PrivateMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PrivateMessageUpsertBulk) DoNothing() *PrivateMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PrivateMessageCreateBulk.OnConflict
// documentation for more info.
func (u *PrivateMessageUpsertBulk) Update(set func(*PrivateMessageUpsert)) *PrivateMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PrivateMessageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *PrivateMessageUpsertBulk) SetUpdateTime(v time.Time) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateUpdateTime() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *PrivateMessageUpsertBulk) ClearUpdateTime() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *PrivateMessageUpsertBulk) SetDeleteTime(v time.Time) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateDeleteTime() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *PrivateMessageUpsertBulk) ClearDeleteTime() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearDeleteTime()
	})
}

// SetSubject sets the "subject" field.
func (u *PrivateMessageUpsertBulk) SetSubject(v string) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetSubject(v)
	})
}

// UpdateSubject sets the "subject" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateSubject() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateSubject()
	})
}

// ClearSubject clears the value of the "subject" field.
func (u *PrivateMessageUpsertBulk) ClearSubject() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearSubject()
	})
}

// SetContent sets the "content" field.
func (u *PrivateMessageUpsertBulk) SetContent(v string) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetContent(v)
	})
}

// UpdateContent sets the "content" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateContent() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateContent()
	})
}

// ClearContent clears the value of the "content" field.
func (u *PrivateMessageUpsertBulk) ClearContent() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearContent()
	})
}

// SetStatus sets the "status" field.
func (u *PrivateMessageUpsertBulk) SetStatus(v privatemessage.Status) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateStatus() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *PrivateMessageUpsertBulk) ClearStatus() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearStatus()
	})
}

// SetSenderID sets the "sender_id" field.
func (u *PrivateMessageUpsertBulk) SetSenderID(v uint32) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetSenderID(v)
	})
}

// AddSenderID adds v to the "sender_id" field.
func (u *PrivateMessageUpsertBulk) AddSenderID(v uint32) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.AddSenderID(v)
	})
}

// UpdateSenderID sets the "sender_id" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateSenderID() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateSenderID()
	})
}

// ClearSenderID clears the value of the "sender_id" field.
func (u *PrivateMessageUpsertBulk) ClearSenderID() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearSenderID()
	})
}

// SetReceiverID sets the "receiver_id" field.
func (u *PrivateMessageUpsertBulk) SetReceiverID(v uint32) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.SetReceiverID(v)
	})
}

// AddReceiverID adds v to the "receiver_id" field.
func (u *PrivateMessageUpsertBulk) AddReceiverID(v uint32) *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.AddReceiverID(v)
	})
}

// UpdateReceiverID sets the "receiver_id" field to the value that was provided on create.
func (u *PrivateMessageUpsertBulk) UpdateReceiverID() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.UpdateReceiverID()
	})
}

// ClearReceiverID clears the value of the "receiver_id" field.
func (u *PrivateMessageUpsertBulk) ClearReceiverID() *PrivateMessageUpsertBulk {
	return u.Update(func(s *PrivateMessageUpsert) {
		s.ClearReceiverID()
	})
}

// Exec executes the query.
func (u *PrivateMessageUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the PrivateMessageCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for PrivateMessageCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PrivateMessageUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
