// Code generated by ent, DO NOT EDIT.

package tenant

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldDeleteTime, v))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCreateBy, v))
}

// UpdateBy applies equality check predicate on the "update_by" field. It's identical to UpdateByEQ.
func UpdateBy(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUpdateBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCode, v))
}

// MemberCount applies equality check predicate on the "member_count" field. It's identical to MemberCountEQ.
func MemberCount(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMemberCount, v))
}

// SubscriptionAt applies equality check predicate on the "subscription_at" field. It's identical to SubscriptionAtEQ.
func SubscriptionAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSubscriptionAt, v))
}

// UnsubscribeAt applies equality check predicate on the "unsubscribe_at" field. It's identical to UnsubscribeAtEQ.
func UnsubscribeAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUnsubscribeAt, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldDeleteTime))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldStatus))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldCreateBy))
}

// UpdateByEQ applies the EQ predicate on the "update_by" field.
func UpdateByEQ(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUpdateBy, v))
}

// UpdateByNEQ applies the NEQ predicate on the "update_by" field.
func UpdateByNEQ(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldUpdateBy, v))
}

// UpdateByIn applies the In predicate on the "update_by" field.
func UpdateByIn(vs ...uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldUpdateBy, vs...))
}

// UpdateByNotIn applies the NotIn predicate on the "update_by" field.
func UpdateByNotIn(vs ...uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldUpdateBy, vs...))
}

// UpdateByGT applies the GT predicate on the "update_by" field.
func UpdateByGT(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldUpdateBy, v))
}

// UpdateByGTE applies the GTE predicate on the "update_by" field.
func UpdateByGTE(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldUpdateBy, v))
}

// UpdateByLT applies the LT predicate on the "update_by" field.
func UpdateByLT(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldUpdateBy, v))
}

// UpdateByLTE applies the LTE predicate on the "update_by" field.
func UpdateByLTE(v uint32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldUpdateBy, v))
}

// UpdateByIsNil applies the IsNil predicate on the "update_by" field.
func UpdateByIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldUpdateBy))
}

// UpdateByNotNil applies the NotNil predicate on the "update_by" field.
func UpdateByNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldUpdateBy))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldCode, v))
}

// CodeIsNil applies the IsNil predicate on the "code" field.
func CodeIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldCode))
}

// CodeNotNil applies the NotNil predicate on the "code" field.
func CodeNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldCode))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldCode, v))
}

// MemberCountEQ applies the EQ predicate on the "member_count" field.
func MemberCountEQ(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMemberCount, v))
}

// MemberCountNEQ applies the NEQ predicate on the "member_count" field.
func MemberCountNEQ(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldMemberCount, v))
}

// MemberCountIn applies the In predicate on the "member_count" field.
func MemberCountIn(vs ...int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldMemberCount, vs...))
}

// MemberCountNotIn applies the NotIn predicate on the "member_count" field.
func MemberCountNotIn(vs ...int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldMemberCount, vs...))
}

// MemberCountGT applies the GT predicate on the "member_count" field.
func MemberCountGT(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldMemberCount, v))
}

// MemberCountGTE applies the GTE predicate on the "member_count" field.
func MemberCountGTE(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldMemberCount, v))
}

// MemberCountLT applies the LT predicate on the "member_count" field.
func MemberCountLT(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldMemberCount, v))
}

// MemberCountLTE applies the LTE predicate on the "member_count" field.
func MemberCountLTE(v int32) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldMemberCount, v))
}

// MemberCountIsNil applies the IsNil predicate on the "member_count" field.
func MemberCountIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldMemberCount))
}

// MemberCountNotNil applies the NotNil predicate on the "member_count" field.
func MemberCountNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldMemberCount))
}

// SubscriptionAtEQ applies the EQ predicate on the "subscription_at" field.
func SubscriptionAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSubscriptionAt, v))
}

// SubscriptionAtNEQ applies the NEQ predicate on the "subscription_at" field.
func SubscriptionAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldSubscriptionAt, v))
}

// SubscriptionAtIn applies the In predicate on the "subscription_at" field.
func SubscriptionAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldSubscriptionAt, vs...))
}

// SubscriptionAtNotIn applies the NotIn predicate on the "subscription_at" field.
func SubscriptionAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldSubscriptionAt, vs...))
}

// SubscriptionAtGT applies the GT predicate on the "subscription_at" field.
func SubscriptionAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldSubscriptionAt, v))
}

// SubscriptionAtGTE applies the GTE predicate on the "subscription_at" field.
func SubscriptionAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldSubscriptionAt, v))
}

// SubscriptionAtLT applies the LT predicate on the "subscription_at" field.
func SubscriptionAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldSubscriptionAt, v))
}

// SubscriptionAtLTE applies the LTE predicate on the "subscription_at" field.
func SubscriptionAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldSubscriptionAt, v))
}

// SubscriptionAtIsNil applies the IsNil predicate on the "subscription_at" field.
func SubscriptionAtIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldSubscriptionAt))
}

// SubscriptionAtNotNil applies the NotNil predicate on the "subscription_at" field.
func SubscriptionAtNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldSubscriptionAt))
}

// UnsubscribeAtEQ applies the EQ predicate on the "unsubscribe_at" field.
func UnsubscribeAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUnsubscribeAt, v))
}

// UnsubscribeAtNEQ applies the NEQ predicate on the "unsubscribe_at" field.
func UnsubscribeAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldUnsubscribeAt, v))
}

// UnsubscribeAtIn applies the In predicate on the "unsubscribe_at" field.
func UnsubscribeAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldUnsubscribeAt, vs...))
}

// UnsubscribeAtNotIn applies the NotIn predicate on the "unsubscribe_at" field.
func UnsubscribeAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldUnsubscribeAt, vs...))
}

// UnsubscribeAtGT applies the GT predicate on the "unsubscribe_at" field.
func UnsubscribeAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldUnsubscribeAt, v))
}

// UnsubscribeAtGTE applies the GTE predicate on the "unsubscribe_at" field.
func UnsubscribeAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldUnsubscribeAt, v))
}

// UnsubscribeAtLT applies the LT predicate on the "unsubscribe_at" field.
func UnsubscribeAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldUnsubscribeAt, v))
}

// UnsubscribeAtLTE applies the LTE predicate on the "unsubscribe_at" field.
func UnsubscribeAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldUnsubscribeAt, v))
}

// UnsubscribeAtIsNil applies the IsNil predicate on the "unsubscribe_at" field.
func UnsubscribeAtIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldUnsubscribeAt))
}

// UnsubscribeAtNotNil applies the NotNil predicate on the "unsubscribe_at" field.
func UnsubscribeAtNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldUnsubscribeAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Tenant) predicate.Tenant {
	return predicate.Tenant(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Tenant) predicate.Tenant {
	return predicate.Tenant(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Tenant) predicate.Tenant {
	return predicate.Tenant(sql.NotPredicates(p))
}
