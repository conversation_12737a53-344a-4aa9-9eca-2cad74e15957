// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"kratos-admin/app/admin/service/internal/data/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TenantUpdate is the builder for updating Tenant entities.
type TenantUpdate struct {
	config
	hooks     []Hook
	mutation  *TenantMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TenantUpdate builder.
func (tu *TenantUpdate) Where(ps ...predicate.Tenant) *TenantUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetUpdateTime sets the "update_time" field.
func (tu *TenantUpdate) SetUpdateTime(t time.Time) *TenantUpdate {
	tu.mutation.SetUpdateTime(t)
	return tu
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableUpdateTime(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetUpdateTime(*t)
	}
	return tu
}

// ClearUpdateTime clears the value of the "update_time" field.
func (tu *TenantUpdate) ClearUpdateTime() *TenantUpdate {
	tu.mutation.ClearUpdateTime()
	return tu
}

// SetDeleteTime sets the "delete_time" field.
func (tu *TenantUpdate) SetDeleteTime(t time.Time) *TenantUpdate {
	tu.mutation.SetDeleteTime(t)
	return tu
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableDeleteTime(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetDeleteTime(*t)
	}
	return tu
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (tu *TenantUpdate) ClearDeleteTime() *TenantUpdate {
	tu.mutation.ClearDeleteTime()
	return tu
}

// SetStatus sets the "status" field.
func (tu *TenantUpdate) SetStatus(t tenant.Status) *TenantUpdate {
	tu.mutation.SetStatus(t)
	return tu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableStatus(t *tenant.Status) *TenantUpdate {
	if t != nil {
		tu.SetStatus(*t)
	}
	return tu
}

// ClearStatus clears the value of the "status" field.
func (tu *TenantUpdate) ClearStatus() *TenantUpdate {
	tu.mutation.ClearStatus()
	return tu
}

// SetCreateBy sets the "create_by" field.
func (tu *TenantUpdate) SetCreateBy(u uint32) *TenantUpdate {
	tu.mutation.ResetCreateBy()
	tu.mutation.SetCreateBy(u)
	return tu
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableCreateBy(u *uint32) *TenantUpdate {
	if u != nil {
		tu.SetCreateBy(*u)
	}
	return tu
}

// AddCreateBy adds u to the "create_by" field.
func (tu *TenantUpdate) AddCreateBy(u int32) *TenantUpdate {
	tu.mutation.AddCreateBy(u)
	return tu
}

// ClearCreateBy clears the value of the "create_by" field.
func (tu *TenantUpdate) ClearCreateBy() *TenantUpdate {
	tu.mutation.ClearCreateBy()
	return tu
}

// SetUpdateBy sets the "update_by" field.
func (tu *TenantUpdate) SetUpdateBy(u uint32) *TenantUpdate {
	tu.mutation.ResetUpdateBy()
	tu.mutation.SetUpdateBy(u)
	return tu
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableUpdateBy(u *uint32) *TenantUpdate {
	if u != nil {
		tu.SetUpdateBy(*u)
	}
	return tu
}

// AddUpdateBy adds u to the "update_by" field.
func (tu *TenantUpdate) AddUpdateBy(u int32) *TenantUpdate {
	tu.mutation.AddUpdateBy(u)
	return tu
}

// ClearUpdateBy clears the value of the "update_by" field.
func (tu *TenantUpdate) ClearUpdateBy() *TenantUpdate {
	tu.mutation.ClearUpdateBy()
	return tu
}

// SetRemark sets the "remark" field.
func (tu *TenantUpdate) SetRemark(s string) *TenantUpdate {
	tu.mutation.SetRemark(s)
	return tu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableRemark(s *string) *TenantUpdate {
	if s != nil {
		tu.SetRemark(*s)
	}
	return tu
}

// ClearRemark clears the value of the "remark" field.
func (tu *TenantUpdate) ClearRemark() *TenantUpdate {
	tu.mutation.ClearRemark()
	return tu
}

// SetName sets the "name" field.
func (tu *TenantUpdate) SetName(s string) *TenantUpdate {
	tu.mutation.SetName(s)
	return tu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableName(s *string) *TenantUpdate {
	if s != nil {
		tu.SetName(*s)
	}
	return tu
}

// ClearName clears the value of the "name" field.
func (tu *TenantUpdate) ClearName() *TenantUpdate {
	tu.mutation.ClearName()
	return tu
}

// SetCode sets the "code" field.
func (tu *TenantUpdate) SetCode(s string) *TenantUpdate {
	tu.mutation.SetCode(s)
	return tu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableCode(s *string) *TenantUpdate {
	if s != nil {
		tu.SetCode(*s)
	}
	return tu
}

// ClearCode clears the value of the "code" field.
func (tu *TenantUpdate) ClearCode() *TenantUpdate {
	tu.mutation.ClearCode()
	return tu
}

// SetMemberCount sets the "member_count" field.
func (tu *TenantUpdate) SetMemberCount(i int32) *TenantUpdate {
	tu.mutation.ResetMemberCount()
	tu.mutation.SetMemberCount(i)
	return tu
}

// SetNillableMemberCount sets the "member_count" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableMemberCount(i *int32) *TenantUpdate {
	if i != nil {
		tu.SetMemberCount(*i)
	}
	return tu
}

// AddMemberCount adds i to the "member_count" field.
func (tu *TenantUpdate) AddMemberCount(i int32) *TenantUpdate {
	tu.mutation.AddMemberCount(i)
	return tu
}

// ClearMemberCount clears the value of the "member_count" field.
func (tu *TenantUpdate) ClearMemberCount() *TenantUpdate {
	tu.mutation.ClearMemberCount()
	return tu
}

// SetSubscriptionAt sets the "subscription_at" field.
func (tu *TenantUpdate) SetSubscriptionAt(t time.Time) *TenantUpdate {
	tu.mutation.SetSubscriptionAt(t)
	return tu
}

// SetNillableSubscriptionAt sets the "subscription_at" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableSubscriptionAt(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetSubscriptionAt(*t)
	}
	return tu
}

// ClearSubscriptionAt clears the value of the "subscription_at" field.
func (tu *TenantUpdate) ClearSubscriptionAt() *TenantUpdate {
	tu.mutation.ClearSubscriptionAt()
	return tu
}

// SetUnsubscribeAt sets the "unsubscribe_at" field.
func (tu *TenantUpdate) SetUnsubscribeAt(t time.Time) *TenantUpdate {
	tu.mutation.SetUnsubscribeAt(t)
	return tu
}

// SetNillableUnsubscribeAt sets the "unsubscribe_at" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableUnsubscribeAt(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetUnsubscribeAt(*t)
	}
	return tu
}

// ClearUnsubscribeAt clears the value of the "unsubscribe_at" field.
func (tu *TenantUpdate) ClearUnsubscribeAt() *TenantUpdate {
	tu.mutation.ClearUnsubscribeAt()
	return tu
}

// Mutation returns the TenantMutation object of the builder.
func (tu *TenantUpdate) Mutation() *TenantMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TenantUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TenantUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TenantUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TenantUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *TenantUpdate) check() error {
	if v, ok := tu.mutation.Status(); ok {
		if err := tenant.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Tenant.status": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Name(); ok {
		if err := tenant.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Tenant.name": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Code(); ok {
		if err := tenant.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Tenant.code": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tu *TenantUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TenantUpdate {
	tu.modifiers = append(tu.modifiers, modifiers...)
	return tu
}

func (tu *TenantUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(tenant.Table, tenant.Columns, sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeUint32))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if tu.mutation.CreateTimeCleared() {
		_spec.ClearField(tenant.FieldCreateTime, field.TypeTime)
	}
	if value, ok := tu.mutation.UpdateTime(); ok {
		_spec.SetField(tenant.FieldUpdateTime, field.TypeTime, value)
	}
	if tu.mutation.UpdateTimeCleared() {
		_spec.ClearField(tenant.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := tu.mutation.DeleteTime(); ok {
		_spec.SetField(tenant.FieldDeleteTime, field.TypeTime, value)
	}
	if tu.mutation.DeleteTimeCleared() {
		_spec.ClearField(tenant.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := tu.mutation.Status(); ok {
		_spec.SetField(tenant.FieldStatus, field.TypeEnum, value)
	}
	if tu.mutation.StatusCleared() {
		_spec.ClearField(tenant.FieldStatus, field.TypeEnum)
	}
	if value, ok := tu.mutation.CreateBy(); ok {
		_spec.SetField(tenant.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := tu.mutation.AddedCreateBy(); ok {
		_spec.AddField(tenant.FieldCreateBy, field.TypeUint32, value)
	}
	if tu.mutation.CreateByCleared() {
		_spec.ClearField(tenant.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := tu.mutation.UpdateBy(); ok {
		_spec.SetField(tenant.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := tu.mutation.AddedUpdateBy(); ok {
		_spec.AddField(tenant.FieldUpdateBy, field.TypeUint32, value)
	}
	if tu.mutation.UpdateByCleared() {
		_spec.ClearField(tenant.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := tu.mutation.Remark(); ok {
		_spec.SetField(tenant.FieldRemark, field.TypeString, value)
	}
	if tu.mutation.RemarkCleared() {
		_spec.ClearField(tenant.FieldRemark, field.TypeString)
	}
	if value, ok := tu.mutation.Name(); ok {
		_spec.SetField(tenant.FieldName, field.TypeString, value)
	}
	if tu.mutation.NameCleared() {
		_spec.ClearField(tenant.FieldName, field.TypeString)
	}
	if value, ok := tu.mutation.Code(); ok {
		_spec.SetField(tenant.FieldCode, field.TypeString, value)
	}
	if tu.mutation.CodeCleared() {
		_spec.ClearField(tenant.FieldCode, field.TypeString)
	}
	if value, ok := tu.mutation.MemberCount(); ok {
		_spec.SetField(tenant.FieldMemberCount, field.TypeInt32, value)
	}
	if value, ok := tu.mutation.AddedMemberCount(); ok {
		_spec.AddField(tenant.FieldMemberCount, field.TypeInt32, value)
	}
	if tu.mutation.MemberCountCleared() {
		_spec.ClearField(tenant.FieldMemberCount, field.TypeInt32)
	}
	if value, ok := tu.mutation.SubscriptionAt(); ok {
		_spec.SetField(tenant.FieldSubscriptionAt, field.TypeTime, value)
	}
	if tu.mutation.SubscriptionAtCleared() {
		_spec.ClearField(tenant.FieldSubscriptionAt, field.TypeTime)
	}
	if value, ok := tu.mutation.UnsubscribeAt(); ok {
		_spec.SetField(tenant.FieldUnsubscribeAt, field.TypeTime, value)
	}
	if tu.mutation.UnsubscribeAtCleared() {
		_spec.ClearField(tenant.FieldUnsubscribeAt, field.TypeTime)
	}
	_spec.AddModifiers(tu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tenant.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TenantUpdateOne is the builder for updating a single Tenant entity.
type TenantUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TenantMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (tuo *TenantUpdateOne) SetUpdateTime(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetUpdateTime(t)
	return tuo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableUpdateTime(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetUpdateTime(*t)
	}
	return tuo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (tuo *TenantUpdateOne) ClearUpdateTime() *TenantUpdateOne {
	tuo.mutation.ClearUpdateTime()
	return tuo
}

// SetDeleteTime sets the "delete_time" field.
func (tuo *TenantUpdateOne) SetDeleteTime(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetDeleteTime(t)
	return tuo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableDeleteTime(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetDeleteTime(*t)
	}
	return tuo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (tuo *TenantUpdateOne) ClearDeleteTime() *TenantUpdateOne {
	tuo.mutation.ClearDeleteTime()
	return tuo
}

// SetStatus sets the "status" field.
func (tuo *TenantUpdateOne) SetStatus(t tenant.Status) *TenantUpdateOne {
	tuo.mutation.SetStatus(t)
	return tuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableStatus(t *tenant.Status) *TenantUpdateOne {
	if t != nil {
		tuo.SetStatus(*t)
	}
	return tuo
}

// ClearStatus clears the value of the "status" field.
func (tuo *TenantUpdateOne) ClearStatus() *TenantUpdateOne {
	tuo.mutation.ClearStatus()
	return tuo
}

// SetCreateBy sets the "create_by" field.
func (tuo *TenantUpdateOne) SetCreateBy(u uint32) *TenantUpdateOne {
	tuo.mutation.ResetCreateBy()
	tuo.mutation.SetCreateBy(u)
	return tuo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableCreateBy(u *uint32) *TenantUpdateOne {
	if u != nil {
		tuo.SetCreateBy(*u)
	}
	return tuo
}

// AddCreateBy adds u to the "create_by" field.
func (tuo *TenantUpdateOne) AddCreateBy(u int32) *TenantUpdateOne {
	tuo.mutation.AddCreateBy(u)
	return tuo
}

// ClearCreateBy clears the value of the "create_by" field.
func (tuo *TenantUpdateOne) ClearCreateBy() *TenantUpdateOne {
	tuo.mutation.ClearCreateBy()
	return tuo
}

// SetUpdateBy sets the "update_by" field.
func (tuo *TenantUpdateOne) SetUpdateBy(u uint32) *TenantUpdateOne {
	tuo.mutation.ResetUpdateBy()
	tuo.mutation.SetUpdateBy(u)
	return tuo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableUpdateBy(u *uint32) *TenantUpdateOne {
	if u != nil {
		tuo.SetUpdateBy(*u)
	}
	return tuo
}

// AddUpdateBy adds u to the "update_by" field.
func (tuo *TenantUpdateOne) AddUpdateBy(u int32) *TenantUpdateOne {
	tuo.mutation.AddUpdateBy(u)
	return tuo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (tuo *TenantUpdateOne) ClearUpdateBy() *TenantUpdateOne {
	tuo.mutation.ClearUpdateBy()
	return tuo
}

// SetRemark sets the "remark" field.
func (tuo *TenantUpdateOne) SetRemark(s string) *TenantUpdateOne {
	tuo.mutation.SetRemark(s)
	return tuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableRemark(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetRemark(*s)
	}
	return tuo
}

// ClearRemark clears the value of the "remark" field.
func (tuo *TenantUpdateOne) ClearRemark() *TenantUpdateOne {
	tuo.mutation.ClearRemark()
	return tuo
}

// SetName sets the "name" field.
func (tuo *TenantUpdateOne) SetName(s string) *TenantUpdateOne {
	tuo.mutation.SetName(s)
	return tuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableName(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetName(*s)
	}
	return tuo
}

// ClearName clears the value of the "name" field.
func (tuo *TenantUpdateOne) ClearName() *TenantUpdateOne {
	tuo.mutation.ClearName()
	return tuo
}

// SetCode sets the "code" field.
func (tuo *TenantUpdateOne) SetCode(s string) *TenantUpdateOne {
	tuo.mutation.SetCode(s)
	return tuo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableCode(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetCode(*s)
	}
	return tuo
}

// ClearCode clears the value of the "code" field.
func (tuo *TenantUpdateOne) ClearCode() *TenantUpdateOne {
	tuo.mutation.ClearCode()
	return tuo
}

// SetMemberCount sets the "member_count" field.
func (tuo *TenantUpdateOne) SetMemberCount(i int32) *TenantUpdateOne {
	tuo.mutation.ResetMemberCount()
	tuo.mutation.SetMemberCount(i)
	return tuo
}

// SetNillableMemberCount sets the "member_count" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableMemberCount(i *int32) *TenantUpdateOne {
	if i != nil {
		tuo.SetMemberCount(*i)
	}
	return tuo
}

// AddMemberCount adds i to the "member_count" field.
func (tuo *TenantUpdateOne) AddMemberCount(i int32) *TenantUpdateOne {
	tuo.mutation.AddMemberCount(i)
	return tuo
}

// ClearMemberCount clears the value of the "member_count" field.
func (tuo *TenantUpdateOne) ClearMemberCount() *TenantUpdateOne {
	tuo.mutation.ClearMemberCount()
	return tuo
}

// SetSubscriptionAt sets the "subscription_at" field.
func (tuo *TenantUpdateOne) SetSubscriptionAt(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetSubscriptionAt(t)
	return tuo
}

// SetNillableSubscriptionAt sets the "subscription_at" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableSubscriptionAt(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetSubscriptionAt(*t)
	}
	return tuo
}

// ClearSubscriptionAt clears the value of the "subscription_at" field.
func (tuo *TenantUpdateOne) ClearSubscriptionAt() *TenantUpdateOne {
	tuo.mutation.ClearSubscriptionAt()
	return tuo
}

// SetUnsubscribeAt sets the "unsubscribe_at" field.
func (tuo *TenantUpdateOne) SetUnsubscribeAt(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetUnsubscribeAt(t)
	return tuo
}

// SetNillableUnsubscribeAt sets the "unsubscribe_at" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableUnsubscribeAt(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetUnsubscribeAt(*t)
	}
	return tuo
}

// ClearUnsubscribeAt clears the value of the "unsubscribe_at" field.
func (tuo *TenantUpdateOne) ClearUnsubscribeAt() *TenantUpdateOne {
	tuo.mutation.ClearUnsubscribeAt()
	return tuo
}

// Mutation returns the TenantMutation object of the builder.
func (tuo *TenantUpdateOne) Mutation() *TenantMutation {
	return tuo.mutation
}

// Where appends a list predicates to the TenantUpdate builder.
func (tuo *TenantUpdateOne) Where(ps ...predicate.Tenant) *TenantUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TenantUpdateOne) Select(field string, fields ...string) *TenantUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Tenant entity.
func (tuo *TenantUpdateOne) Save(ctx context.Context) (*Tenant, error) {
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TenantUpdateOne) SaveX(ctx context.Context) *Tenant {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TenantUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TenantUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TenantUpdateOne) check() error {
	if v, ok := tuo.mutation.Status(); ok {
		if err := tenant.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Tenant.status": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Name(); ok {
		if err := tenant.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Tenant.name": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Code(); ok {
		if err := tenant.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Tenant.code": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tuo *TenantUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TenantUpdateOne {
	tuo.modifiers = append(tuo.modifiers, modifiers...)
	return tuo
}

func (tuo *TenantUpdateOne) sqlSave(ctx context.Context) (_node *Tenant, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(tenant.Table, tenant.Columns, sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeUint32))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Tenant.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tenant.FieldID)
		for _, f := range fields {
			if !tenant.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != tenant.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if tuo.mutation.CreateTimeCleared() {
		_spec.ClearField(tenant.FieldCreateTime, field.TypeTime)
	}
	if value, ok := tuo.mutation.UpdateTime(); ok {
		_spec.SetField(tenant.FieldUpdateTime, field.TypeTime, value)
	}
	if tuo.mutation.UpdateTimeCleared() {
		_spec.ClearField(tenant.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := tuo.mutation.DeleteTime(); ok {
		_spec.SetField(tenant.FieldDeleteTime, field.TypeTime, value)
	}
	if tuo.mutation.DeleteTimeCleared() {
		_spec.ClearField(tenant.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := tuo.mutation.Status(); ok {
		_spec.SetField(tenant.FieldStatus, field.TypeEnum, value)
	}
	if tuo.mutation.StatusCleared() {
		_spec.ClearField(tenant.FieldStatus, field.TypeEnum)
	}
	if value, ok := tuo.mutation.CreateBy(); ok {
		_spec.SetField(tenant.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := tuo.mutation.AddedCreateBy(); ok {
		_spec.AddField(tenant.FieldCreateBy, field.TypeUint32, value)
	}
	if tuo.mutation.CreateByCleared() {
		_spec.ClearField(tenant.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := tuo.mutation.UpdateBy(); ok {
		_spec.SetField(tenant.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := tuo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(tenant.FieldUpdateBy, field.TypeUint32, value)
	}
	if tuo.mutation.UpdateByCleared() {
		_spec.ClearField(tenant.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := tuo.mutation.Remark(); ok {
		_spec.SetField(tenant.FieldRemark, field.TypeString, value)
	}
	if tuo.mutation.RemarkCleared() {
		_spec.ClearField(tenant.FieldRemark, field.TypeString)
	}
	if value, ok := tuo.mutation.Name(); ok {
		_spec.SetField(tenant.FieldName, field.TypeString, value)
	}
	if tuo.mutation.NameCleared() {
		_spec.ClearField(tenant.FieldName, field.TypeString)
	}
	if value, ok := tuo.mutation.Code(); ok {
		_spec.SetField(tenant.FieldCode, field.TypeString, value)
	}
	if tuo.mutation.CodeCleared() {
		_spec.ClearField(tenant.FieldCode, field.TypeString)
	}
	if value, ok := tuo.mutation.MemberCount(); ok {
		_spec.SetField(tenant.FieldMemberCount, field.TypeInt32, value)
	}
	if value, ok := tuo.mutation.AddedMemberCount(); ok {
		_spec.AddField(tenant.FieldMemberCount, field.TypeInt32, value)
	}
	if tuo.mutation.MemberCountCleared() {
		_spec.ClearField(tenant.FieldMemberCount, field.TypeInt32)
	}
	if value, ok := tuo.mutation.SubscriptionAt(); ok {
		_spec.SetField(tenant.FieldSubscriptionAt, field.TypeTime, value)
	}
	if tuo.mutation.SubscriptionAtCleared() {
		_spec.ClearField(tenant.FieldSubscriptionAt, field.TypeTime)
	}
	if value, ok := tuo.mutation.UnsubscribeAt(); ok {
		_spec.SetField(tenant.FieldUnsubscribeAt, field.TypeTime, value)
	}
	if tuo.mutation.UnsubscribeAtCleared() {
		_spec.ClearField(tenant.FieldUnsubscribeAt, field.TypeTime)
	}
	_spec.AddModifiers(tuo.modifiers...)
	_node = &Tenant{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tenant.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
