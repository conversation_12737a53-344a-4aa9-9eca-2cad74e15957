// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AdminLoginLog is the predicate function for adminloginlog builders.
type AdminLoginLog func(*sql.Selector)

// AdminLoginRestriction is the predicate function for adminloginrestriction builders.
type AdminLoginRestriction func(*sql.Selector)

// AdminOperationLog is the predicate function for adminoperationlog builders.
type AdminOperationLog func(*sql.Selector)

// ApiResource is the predicate function for apiresource builders.
type ApiResource func(*sql.Selector)

// Department is the predicate function for department builders.
type Department func(*sql.Selector)

// Dict is the predicate function for dict builders.
type Dict func(*sql.Selector)

// File is the predicate function for file builders.
type File func(*sql.Selector)

// Menu is the predicate function for menu builders.
type Menu func(*sql.Selector)

// NotificationMessage is the predicate function for notificationmessage builders.
type NotificationMessage func(*sql.Selector)

// NotificationMessageCategory is the predicate function for notificationmessagecategory builders.
type NotificationMessageCategory func(*sql.Selector)

// NotificationMessageRecipient is the predicate function for notificationmessagerecipient builders.
type NotificationMessageRecipient func(*sql.Selector)

// Organization is the predicate function for organization builders.
type Organization func(*sql.Selector)

// Position is the predicate function for position builders.
type Position func(*sql.Selector)

// PrivateMessage is the predicate function for privatemessage builders.
type PrivateMessage func(*sql.Selector)

// Role is the predicate function for role builders.
type Role func(*sql.Selector)

// Task is the predicate function for task builders.
type Task func(*sql.Selector)

// Tenant is the predicate function for tenant builders.
type Tenant func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)

// UserCredential is the predicate function for usercredential builders.
type UserCredential func(*sql.Selector)
