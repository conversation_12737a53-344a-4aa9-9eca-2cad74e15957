// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"kratos-admin/app/admin/service/internal/data/ent/usercredential"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserCredentialQuery is the builder for querying UserCredential entities.
type UserCredentialQuery struct {
	config
	ctx        *QueryContext
	order      []usercredential.OrderOption
	inters     []Interceptor
	predicates []predicate.UserCredential
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UserCredentialQuery builder.
func (ucq *UserCredentialQuery) Where(ps ...predicate.UserCredential) *UserCredentialQuery {
	ucq.predicates = append(ucq.predicates, ps...)
	return ucq
}

// Limit the number of records to be returned by this query.
func (ucq *UserCredentialQuery) Limit(limit int) *UserCredentialQuery {
	ucq.ctx.Limit = &limit
	return ucq
}

// Offset to start from.
func (ucq *UserCredentialQuery) Offset(offset int) *UserCredentialQuery {
	ucq.ctx.Offset = &offset
	return ucq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ucq *UserCredentialQuery) Unique(unique bool) *UserCredentialQuery {
	ucq.ctx.Unique = &unique
	return ucq
}

// Order specifies how the records should be ordered.
func (ucq *UserCredentialQuery) Order(o ...usercredential.OrderOption) *UserCredentialQuery {
	ucq.order = append(ucq.order, o...)
	return ucq
}

// First returns the first UserCredential entity from the query.
// Returns a *NotFoundError when no UserCredential was found.
func (ucq *UserCredentialQuery) First(ctx context.Context) (*UserCredential, error) {
	nodes, err := ucq.Limit(1).All(setContextOp(ctx, ucq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{usercredential.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ucq *UserCredentialQuery) FirstX(ctx context.Context) *UserCredential {
	node, err := ucq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first UserCredential ID from the query.
// Returns a *NotFoundError when no UserCredential ID was found.
func (ucq *UserCredentialQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = ucq.Limit(1).IDs(setContextOp(ctx, ucq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{usercredential.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ucq *UserCredentialQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := ucq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single UserCredential entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one UserCredential entity is found.
// Returns a *NotFoundError when no UserCredential entities are found.
func (ucq *UserCredentialQuery) Only(ctx context.Context) (*UserCredential, error) {
	nodes, err := ucq.Limit(2).All(setContextOp(ctx, ucq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{usercredential.Label}
	default:
		return nil, &NotSingularError{usercredential.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ucq *UserCredentialQuery) OnlyX(ctx context.Context) *UserCredential {
	node, err := ucq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only UserCredential ID in the query.
// Returns a *NotSingularError when more than one UserCredential ID is found.
// Returns a *NotFoundError when no entities are found.
func (ucq *UserCredentialQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = ucq.Limit(2).IDs(setContextOp(ctx, ucq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{usercredential.Label}
	default:
		err = &NotSingularError{usercredential.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ucq *UserCredentialQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := ucq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of UserCredentials.
func (ucq *UserCredentialQuery) All(ctx context.Context) ([]*UserCredential, error) {
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryAll)
	if err := ucq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*UserCredential, *UserCredentialQuery]()
	return withInterceptors[[]*UserCredential](ctx, ucq, qr, ucq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ucq *UserCredentialQuery) AllX(ctx context.Context) []*UserCredential {
	nodes, err := ucq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of UserCredential IDs.
func (ucq *UserCredentialQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if ucq.ctx.Unique == nil && ucq.path != nil {
		ucq.Unique(true)
	}
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryIDs)
	if err = ucq.Select(usercredential.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ucq *UserCredentialQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := ucq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ucq *UserCredentialQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryCount)
	if err := ucq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ucq, querierCount[*UserCredentialQuery](), ucq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ucq *UserCredentialQuery) CountX(ctx context.Context) int {
	count, err := ucq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ucq *UserCredentialQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryExist)
	switch _, err := ucq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ucq *UserCredentialQuery) ExistX(ctx context.Context) bool {
	exist, err := ucq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UserCredentialQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ucq *UserCredentialQuery) Clone() *UserCredentialQuery {
	if ucq == nil {
		return nil
	}
	return &UserCredentialQuery{
		config:     ucq.config,
		ctx:        ucq.ctx.Clone(),
		order:      append([]usercredential.OrderOption{}, ucq.order...),
		inters:     append([]Interceptor{}, ucq.inters...),
		predicates: append([]predicate.UserCredential{}, ucq.predicates...),
		// clone intermediate query.
		sql:       ucq.sql.Clone(),
		path:      ucq.path,
		modifiers: append([]func(*sql.Selector){}, ucq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.UserCredential.Query().
//		GroupBy(usercredential.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ucq *UserCredentialQuery) GroupBy(field string, fields ...string) *UserCredentialGroupBy {
	ucq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UserCredentialGroupBy{build: ucq}
	grbuild.flds = &ucq.ctx.Fields
	grbuild.label = usercredential.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.UserCredential.Query().
//		Select(usercredential.FieldCreateTime).
//		Scan(ctx, &v)
func (ucq *UserCredentialQuery) Select(fields ...string) *UserCredentialSelect {
	ucq.ctx.Fields = append(ucq.ctx.Fields, fields...)
	sbuild := &UserCredentialSelect{UserCredentialQuery: ucq}
	sbuild.label = usercredential.Label
	sbuild.flds, sbuild.scan = &ucq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UserCredentialSelect configured with the given aggregations.
func (ucq *UserCredentialQuery) Aggregate(fns ...AggregateFunc) *UserCredentialSelect {
	return ucq.Select().Aggregate(fns...)
}

func (ucq *UserCredentialQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ucq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ucq); err != nil {
				return err
			}
		}
	}
	for _, f := range ucq.ctx.Fields {
		if !usercredential.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ucq.path != nil {
		prev, err := ucq.path(ctx)
		if err != nil {
			return err
		}
		ucq.sql = prev
	}
	return nil
}

func (ucq *UserCredentialQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*UserCredential, error) {
	var (
		nodes = []*UserCredential{}
		_spec = ucq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*UserCredential).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &UserCredential{config: ucq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(ucq.modifiers) > 0 {
		_spec.Modifiers = ucq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ucq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ucq *UserCredentialQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ucq.querySpec()
	if len(ucq.modifiers) > 0 {
		_spec.Modifiers = ucq.modifiers
	}
	_spec.Node.Columns = ucq.ctx.Fields
	if len(ucq.ctx.Fields) > 0 {
		_spec.Unique = ucq.ctx.Unique != nil && *ucq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ucq.driver, _spec)
}

func (ucq *UserCredentialQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(usercredential.Table, usercredential.Columns, sqlgraph.NewFieldSpec(usercredential.FieldID, field.TypeUint32))
	_spec.From = ucq.sql
	if unique := ucq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ucq.path != nil {
		_spec.Unique = true
	}
	if fields := ucq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, usercredential.FieldID)
		for i := range fields {
			if fields[i] != usercredential.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ucq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ucq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ucq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ucq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ucq *UserCredentialQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ucq.driver.Dialect())
	t1 := builder.Table(usercredential.Table)
	columns := ucq.ctx.Fields
	if len(columns) == 0 {
		columns = usercredential.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ucq.sql != nil {
		selector = ucq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ucq.ctx.Unique != nil && *ucq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range ucq.modifiers {
		m(selector)
	}
	for _, p := range ucq.predicates {
		p(selector)
	}
	for _, p := range ucq.order {
		p(selector)
	}
	if offset := ucq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ucq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (ucq *UserCredentialQuery) ForUpdate(opts ...sql.LockOption) *UserCredentialQuery {
	if ucq.driver.Dialect() == dialect.Postgres {
		ucq.Unique(false)
	}
	ucq.modifiers = append(ucq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return ucq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (ucq *UserCredentialQuery) ForShare(opts ...sql.LockOption) *UserCredentialQuery {
	if ucq.driver.Dialect() == dialect.Postgres {
		ucq.Unique(false)
	}
	ucq.modifiers = append(ucq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return ucq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ucq *UserCredentialQuery) Modify(modifiers ...func(s *sql.Selector)) *UserCredentialSelect {
	ucq.modifiers = append(ucq.modifiers, modifiers...)
	return ucq.Select()
}

// UserCredentialGroupBy is the group-by builder for UserCredential entities.
type UserCredentialGroupBy struct {
	selector
	build *UserCredentialQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ucgb *UserCredentialGroupBy) Aggregate(fns ...AggregateFunc) *UserCredentialGroupBy {
	ucgb.fns = append(ucgb.fns, fns...)
	return ucgb
}

// Scan applies the selector query and scans the result into the given value.
func (ucgb *UserCredentialGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ucgb.build.ctx, ent.OpQueryGroupBy)
	if err := ucgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserCredentialQuery, *UserCredentialGroupBy](ctx, ucgb.build, ucgb, ucgb.build.inters, v)
}

func (ucgb *UserCredentialGroupBy) sqlScan(ctx context.Context, root *UserCredentialQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ucgb.fns))
	for _, fn := range ucgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ucgb.flds)+len(ucgb.fns))
		for _, f := range *ucgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ucgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ucgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UserCredentialSelect is the builder for selecting fields of UserCredential entities.
type UserCredentialSelect struct {
	*UserCredentialQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ucs *UserCredentialSelect) Aggregate(fns ...AggregateFunc) *UserCredentialSelect {
	ucs.fns = append(ucs.fns, fns...)
	return ucs
}

// Scan applies the selector query and scans the result into the given value.
func (ucs *UserCredentialSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ucs.ctx, ent.OpQuerySelect)
	if err := ucs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserCredentialQuery, *UserCredentialSelect](ctx, ucs.UserCredentialQuery, ucs, ucs.inters, v)
}

func (ucs *UserCredentialSelect) sqlScan(ctx context.Context, root *UserCredentialQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ucs.fns))
	for _, fn := range ucs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ucs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ucs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ucs *UserCredentialSelect) Modify(modifiers ...func(s *sql.Selector)) *UserCredentialSelect {
	ucs.modifiers = append(ucs.modifiers, modifiers...)
	return ucs
}
