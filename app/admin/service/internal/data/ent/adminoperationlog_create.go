// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminOperationLogCreate is the builder for creating a AdminOperationLog entity.
type AdminOperationLogCreate struct {
	config
	mutation *AdminOperationLogMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (aolc *AdminOperationLogCreate) SetCreateTime(t time.Time) *AdminOperationLogCreate {
	aolc.mutation.SetCreateTime(t)
	return aolc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableCreateTime(t *time.Time) *AdminOperationLogCreate {
	if t != nil {
		aolc.SetCreateTime(*t)
	}
	return aolc
}

// SetRequestID sets the "request_id" field.
func (aolc *AdminOperationLogCreate) SetRequestID(s string) *AdminOperationLogCreate {
	aolc.mutation.SetRequestID(s)
	return aolc
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableRequestID(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetRequestID(*s)
	}
	return aolc
}

// SetMethod sets the "method" field.
func (aolc *AdminOperationLogCreate) SetMethod(s string) *AdminOperationLogCreate {
	aolc.mutation.SetMethod(s)
	return aolc
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableMethod(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetMethod(*s)
	}
	return aolc
}

// SetOperation sets the "operation" field.
func (aolc *AdminOperationLogCreate) SetOperation(s string) *AdminOperationLogCreate {
	aolc.mutation.SetOperation(s)
	return aolc
}

// SetNillableOperation sets the "operation" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableOperation(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetOperation(*s)
	}
	return aolc
}

// SetPath sets the "path" field.
func (aolc *AdminOperationLogCreate) SetPath(s string) *AdminOperationLogCreate {
	aolc.mutation.SetPath(s)
	return aolc
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillablePath(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetPath(*s)
	}
	return aolc
}

// SetReferer sets the "referer" field.
func (aolc *AdminOperationLogCreate) SetReferer(s string) *AdminOperationLogCreate {
	aolc.mutation.SetReferer(s)
	return aolc
}

// SetNillableReferer sets the "referer" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableReferer(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetReferer(*s)
	}
	return aolc
}

// SetRequestURI sets the "request_uri" field.
func (aolc *AdminOperationLogCreate) SetRequestURI(s string) *AdminOperationLogCreate {
	aolc.mutation.SetRequestURI(s)
	return aolc
}

// SetNillableRequestURI sets the "request_uri" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableRequestURI(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetRequestURI(*s)
	}
	return aolc
}

// SetRequestBody sets the "request_body" field.
func (aolc *AdminOperationLogCreate) SetRequestBody(s string) *AdminOperationLogCreate {
	aolc.mutation.SetRequestBody(s)
	return aolc
}

// SetNillableRequestBody sets the "request_body" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableRequestBody(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetRequestBody(*s)
	}
	return aolc
}

// SetRequestHeader sets the "request_header" field.
func (aolc *AdminOperationLogCreate) SetRequestHeader(s string) *AdminOperationLogCreate {
	aolc.mutation.SetRequestHeader(s)
	return aolc
}

// SetNillableRequestHeader sets the "request_header" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableRequestHeader(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetRequestHeader(*s)
	}
	return aolc
}

// SetResponse sets the "response" field.
func (aolc *AdminOperationLogCreate) SetResponse(s string) *AdminOperationLogCreate {
	aolc.mutation.SetResponse(s)
	return aolc
}

// SetNillableResponse sets the "response" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableResponse(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetResponse(*s)
	}
	return aolc
}

// SetCostTime sets the "cost_time" field.
func (aolc *AdminOperationLogCreate) SetCostTime(f float64) *AdminOperationLogCreate {
	aolc.mutation.SetCostTime(f)
	return aolc
}

// SetNillableCostTime sets the "cost_time" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableCostTime(f *float64) *AdminOperationLogCreate {
	if f != nil {
		aolc.SetCostTime(*f)
	}
	return aolc
}

// SetUserID sets the "user_id" field.
func (aolc *AdminOperationLogCreate) SetUserID(u uint32) *AdminOperationLogCreate {
	aolc.mutation.SetUserID(u)
	return aolc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableUserID(u *uint32) *AdminOperationLogCreate {
	if u != nil {
		aolc.SetUserID(*u)
	}
	return aolc
}

// SetUsername sets the "username" field.
func (aolc *AdminOperationLogCreate) SetUsername(s string) *AdminOperationLogCreate {
	aolc.mutation.SetUsername(s)
	return aolc
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableUsername(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetUsername(*s)
	}
	return aolc
}

// SetClientIP sets the "client_ip" field.
func (aolc *AdminOperationLogCreate) SetClientIP(s string) *AdminOperationLogCreate {
	aolc.mutation.SetClientIP(s)
	return aolc
}

// SetNillableClientIP sets the "client_ip" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableClientIP(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetClientIP(*s)
	}
	return aolc
}

// SetStatusCode sets the "status_code" field.
func (aolc *AdminOperationLogCreate) SetStatusCode(i int32) *AdminOperationLogCreate {
	aolc.mutation.SetStatusCode(i)
	return aolc
}

// SetNillableStatusCode sets the "status_code" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableStatusCode(i *int32) *AdminOperationLogCreate {
	if i != nil {
		aolc.SetStatusCode(*i)
	}
	return aolc
}

// SetReason sets the "reason" field.
func (aolc *AdminOperationLogCreate) SetReason(s string) *AdminOperationLogCreate {
	aolc.mutation.SetReason(s)
	return aolc
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableReason(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetReason(*s)
	}
	return aolc
}

// SetSuccess sets the "success" field.
func (aolc *AdminOperationLogCreate) SetSuccess(b bool) *AdminOperationLogCreate {
	aolc.mutation.SetSuccess(b)
	return aolc
}

// SetNillableSuccess sets the "success" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableSuccess(b *bool) *AdminOperationLogCreate {
	if b != nil {
		aolc.SetSuccess(*b)
	}
	return aolc
}

// SetLocation sets the "location" field.
func (aolc *AdminOperationLogCreate) SetLocation(s string) *AdminOperationLogCreate {
	aolc.mutation.SetLocation(s)
	return aolc
}

// SetNillableLocation sets the "location" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableLocation(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetLocation(*s)
	}
	return aolc
}

// SetUserAgent sets the "user_agent" field.
func (aolc *AdminOperationLogCreate) SetUserAgent(s string) *AdminOperationLogCreate {
	aolc.mutation.SetUserAgent(s)
	return aolc
}

// SetNillableUserAgent sets the "user_agent" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableUserAgent(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetUserAgent(*s)
	}
	return aolc
}

// SetBrowserName sets the "browser_name" field.
func (aolc *AdminOperationLogCreate) SetBrowserName(s string) *AdminOperationLogCreate {
	aolc.mutation.SetBrowserName(s)
	return aolc
}

// SetNillableBrowserName sets the "browser_name" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableBrowserName(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetBrowserName(*s)
	}
	return aolc
}

// SetBrowserVersion sets the "browser_version" field.
func (aolc *AdminOperationLogCreate) SetBrowserVersion(s string) *AdminOperationLogCreate {
	aolc.mutation.SetBrowserVersion(s)
	return aolc
}

// SetNillableBrowserVersion sets the "browser_version" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableBrowserVersion(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetBrowserVersion(*s)
	}
	return aolc
}

// SetClientID sets the "client_id" field.
func (aolc *AdminOperationLogCreate) SetClientID(s string) *AdminOperationLogCreate {
	aolc.mutation.SetClientID(s)
	return aolc
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableClientID(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetClientID(*s)
	}
	return aolc
}

// SetClientName sets the "client_name" field.
func (aolc *AdminOperationLogCreate) SetClientName(s string) *AdminOperationLogCreate {
	aolc.mutation.SetClientName(s)
	return aolc
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableClientName(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetClientName(*s)
	}
	return aolc
}

// SetOsName sets the "os_name" field.
func (aolc *AdminOperationLogCreate) SetOsName(s string) *AdminOperationLogCreate {
	aolc.mutation.SetOsName(s)
	return aolc
}

// SetNillableOsName sets the "os_name" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableOsName(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetOsName(*s)
	}
	return aolc
}

// SetOsVersion sets the "os_version" field.
func (aolc *AdminOperationLogCreate) SetOsVersion(s string) *AdminOperationLogCreate {
	aolc.mutation.SetOsVersion(s)
	return aolc
}

// SetNillableOsVersion sets the "os_version" field if the given value is not nil.
func (aolc *AdminOperationLogCreate) SetNillableOsVersion(s *string) *AdminOperationLogCreate {
	if s != nil {
		aolc.SetOsVersion(*s)
	}
	return aolc
}

// SetID sets the "id" field.
func (aolc *AdminOperationLogCreate) SetID(u uint32) *AdminOperationLogCreate {
	aolc.mutation.SetID(u)
	return aolc
}

// Mutation returns the AdminOperationLogMutation object of the builder.
func (aolc *AdminOperationLogCreate) Mutation() *AdminOperationLogMutation {
	return aolc.mutation
}

// Save creates the AdminOperationLog in the database.
func (aolc *AdminOperationLogCreate) Save(ctx context.Context) (*AdminOperationLog, error) {
	return withHooks(ctx, aolc.sqlSave, aolc.mutation, aolc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (aolc *AdminOperationLogCreate) SaveX(ctx context.Context) *AdminOperationLog {
	v, err := aolc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aolc *AdminOperationLogCreate) Exec(ctx context.Context) error {
	_, err := aolc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aolc *AdminOperationLogCreate) ExecX(ctx context.Context) {
	if err := aolc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (aolc *AdminOperationLogCreate) check() error {
	if v, ok := aolc.mutation.ID(); ok {
		if err := adminoperationlog.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "AdminOperationLog.id": %w`, err)}
		}
	}
	return nil
}

func (aolc *AdminOperationLogCreate) sqlSave(ctx context.Context) (*AdminOperationLog, error) {
	if err := aolc.check(); err != nil {
		return nil, err
	}
	_node, _spec := aolc.createSpec()
	if err := sqlgraph.CreateNode(ctx, aolc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	aolc.mutation.id = &_node.ID
	aolc.mutation.done = true
	return _node, nil
}

func (aolc *AdminOperationLogCreate) createSpec() (*AdminOperationLog, *sqlgraph.CreateSpec) {
	var (
		_node = &AdminOperationLog{config: aolc.config}
		_spec = sqlgraph.NewCreateSpec(adminoperationlog.Table, sqlgraph.NewFieldSpec(adminoperationlog.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = aolc.conflict
	if id, ok := aolc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := aolc.mutation.CreateTime(); ok {
		_spec.SetField(adminoperationlog.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := aolc.mutation.RequestID(); ok {
		_spec.SetField(adminoperationlog.FieldRequestID, field.TypeString, value)
		_node.RequestID = &value
	}
	if value, ok := aolc.mutation.Method(); ok {
		_spec.SetField(adminoperationlog.FieldMethod, field.TypeString, value)
		_node.Method = &value
	}
	if value, ok := aolc.mutation.Operation(); ok {
		_spec.SetField(adminoperationlog.FieldOperation, field.TypeString, value)
		_node.Operation = &value
	}
	if value, ok := aolc.mutation.Path(); ok {
		_spec.SetField(adminoperationlog.FieldPath, field.TypeString, value)
		_node.Path = &value
	}
	if value, ok := aolc.mutation.Referer(); ok {
		_spec.SetField(adminoperationlog.FieldReferer, field.TypeString, value)
		_node.Referer = &value
	}
	if value, ok := aolc.mutation.RequestURI(); ok {
		_spec.SetField(adminoperationlog.FieldRequestURI, field.TypeString, value)
		_node.RequestURI = &value
	}
	if value, ok := aolc.mutation.RequestBody(); ok {
		_spec.SetField(adminoperationlog.FieldRequestBody, field.TypeString, value)
		_node.RequestBody = &value
	}
	if value, ok := aolc.mutation.RequestHeader(); ok {
		_spec.SetField(adminoperationlog.FieldRequestHeader, field.TypeString, value)
		_node.RequestHeader = &value
	}
	if value, ok := aolc.mutation.Response(); ok {
		_spec.SetField(adminoperationlog.FieldResponse, field.TypeString, value)
		_node.Response = &value
	}
	if value, ok := aolc.mutation.CostTime(); ok {
		_spec.SetField(adminoperationlog.FieldCostTime, field.TypeFloat64, value)
		_node.CostTime = &value
	}
	if value, ok := aolc.mutation.UserID(); ok {
		_spec.SetField(adminoperationlog.FieldUserID, field.TypeUint32, value)
		_node.UserID = &value
	}
	if value, ok := aolc.mutation.Username(); ok {
		_spec.SetField(adminoperationlog.FieldUsername, field.TypeString, value)
		_node.Username = &value
	}
	if value, ok := aolc.mutation.ClientIP(); ok {
		_spec.SetField(adminoperationlog.FieldClientIP, field.TypeString, value)
		_node.ClientIP = &value
	}
	if value, ok := aolc.mutation.StatusCode(); ok {
		_spec.SetField(adminoperationlog.FieldStatusCode, field.TypeInt32, value)
		_node.StatusCode = &value
	}
	if value, ok := aolc.mutation.Reason(); ok {
		_spec.SetField(adminoperationlog.FieldReason, field.TypeString, value)
		_node.Reason = &value
	}
	if value, ok := aolc.mutation.Success(); ok {
		_spec.SetField(adminoperationlog.FieldSuccess, field.TypeBool, value)
		_node.Success = &value
	}
	if value, ok := aolc.mutation.Location(); ok {
		_spec.SetField(adminoperationlog.FieldLocation, field.TypeString, value)
		_node.Location = &value
	}
	if value, ok := aolc.mutation.UserAgent(); ok {
		_spec.SetField(adminoperationlog.FieldUserAgent, field.TypeString, value)
		_node.UserAgent = &value
	}
	if value, ok := aolc.mutation.BrowserName(); ok {
		_spec.SetField(adminoperationlog.FieldBrowserName, field.TypeString, value)
		_node.BrowserName = &value
	}
	if value, ok := aolc.mutation.BrowserVersion(); ok {
		_spec.SetField(adminoperationlog.FieldBrowserVersion, field.TypeString, value)
		_node.BrowserVersion = &value
	}
	if value, ok := aolc.mutation.ClientID(); ok {
		_spec.SetField(adminoperationlog.FieldClientID, field.TypeString, value)
		_node.ClientID = &value
	}
	if value, ok := aolc.mutation.ClientName(); ok {
		_spec.SetField(adminoperationlog.FieldClientName, field.TypeString, value)
		_node.ClientName = &value
	}
	if value, ok := aolc.mutation.OsName(); ok {
		_spec.SetField(adminoperationlog.FieldOsName, field.TypeString, value)
		_node.OsName = &value
	}
	if value, ok := aolc.mutation.OsVersion(); ok {
		_spec.SetField(adminoperationlog.FieldOsVersion, field.TypeString, value)
		_node.OsVersion = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AdminOperationLog.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AdminOperationLogUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (aolc *AdminOperationLogCreate) OnConflict(opts ...sql.ConflictOption) *AdminOperationLogUpsertOne {
	aolc.conflict = opts
	return &AdminOperationLogUpsertOne{
		create: aolc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AdminOperationLog.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aolc *AdminOperationLogCreate) OnConflictColumns(columns ...string) *AdminOperationLogUpsertOne {
	aolc.conflict = append(aolc.conflict, sql.ConflictColumns(columns...))
	return &AdminOperationLogUpsertOne{
		create: aolc,
	}
}

type (
	// AdminOperationLogUpsertOne is the builder for "upsert"-ing
	//  one AdminOperationLog node.
	AdminOperationLogUpsertOne struct {
		create *AdminOperationLogCreate
	}

	// AdminOperationLogUpsert is the "OnConflict" setter.
	AdminOperationLogUpsert struct {
		*sql.UpdateSet
	}
)

// SetRequestID sets the "request_id" field.
func (u *AdminOperationLogUpsert) SetRequestID(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldRequestID, v)
	return u
}

// UpdateRequestID sets the "request_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateRequestID() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldRequestID)
	return u
}

// ClearRequestID clears the value of the "request_id" field.
func (u *AdminOperationLogUpsert) ClearRequestID() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldRequestID)
	return u
}

// SetMethod sets the "method" field.
func (u *AdminOperationLogUpsert) SetMethod(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldMethod, v)
	return u
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateMethod() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldMethod)
	return u
}

// ClearMethod clears the value of the "method" field.
func (u *AdminOperationLogUpsert) ClearMethod() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldMethod)
	return u
}

// SetOperation sets the "operation" field.
func (u *AdminOperationLogUpsert) SetOperation(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldOperation, v)
	return u
}

// UpdateOperation sets the "operation" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateOperation() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldOperation)
	return u
}

// ClearOperation clears the value of the "operation" field.
func (u *AdminOperationLogUpsert) ClearOperation() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldOperation)
	return u
}

// SetPath sets the "path" field.
func (u *AdminOperationLogUpsert) SetPath(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdatePath() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldPath)
	return u
}

// ClearPath clears the value of the "path" field.
func (u *AdminOperationLogUpsert) ClearPath() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldPath)
	return u
}

// SetReferer sets the "referer" field.
func (u *AdminOperationLogUpsert) SetReferer(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldReferer, v)
	return u
}

// UpdateReferer sets the "referer" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateReferer() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldReferer)
	return u
}

// ClearReferer clears the value of the "referer" field.
func (u *AdminOperationLogUpsert) ClearReferer() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldReferer)
	return u
}

// SetRequestURI sets the "request_uri" field.
func (u *AdminOperationLogUpsert) SetRequestURI(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldRequestURI, v)
	return u
}

// UpdateRequestURI sets the "request_uri" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateRequestURI() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldRequestURI)
	return u
}

// ClearRequestURI clears the value of the "request_uri" field.
func (u *AdminOperationLogUpsert) ClearRequestURI() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldRequestURI)
	return u
}

// SetRequestBody sets the "request_body" field.
func (u *AdminOperationLogUpsert) SetRequestBody(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldRequestBody, v)
	return u
}

// UpdateRequestBody sets the "request_body" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateRequestBody() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldRequestBody)
	return u
}

// ClearRequestBody clears the value of the "request_body" field.
func (u *AdminOperationLogUpsert) ClearRequestBody() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldRequestBody)
	return u
}

// SetRequestHeader sets the "request_header" field.
func (u *AdminOperationLogUpsert) SetRequestHeader(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldRequestHeader, v)
	return u
}

// UpdateRequestHeader sets the "request_header" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateRequestHeader() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldRequestHeader)
	return u
}

// ClearRequestHeader clears the value of the "request_header" field.
func (u *AdminOperationLogUpsert) ClearRequestHeader() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldRequestHeader)
	return u
}

// SetResponse sets the "response" field.
func (u *AdminOperationLogUpsert) SetResponse(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldResponse, v)
	return u
}

// UpdateResponse sets the "response" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateResponse() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldResponse)
	return u
}

// ClearResponse clears the value of the "response" field.
func (u *AdminOperationLogUpsert) ClearResponse() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldResponse)
	return u
}

// SetCostTime sets the "cost_time" field.
func (u *AdminOperationLogUpsert) SetCostTime(v float64) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldCostTime, v)
	return u
}

// UpdateCostTime sets the "cost_time" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateCostTime() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldCostTime)
	return u
}

// AddCostTime adds v to the "cost_time" field.
func (u *AdminOperationLogUpsert) AddCostTime(v float64) *AdminOperationLogUpsert {
	u.Add(adminoperationlog.FieldCostTime, v)
	return u
}

// ClearCostTime clears the value of the "cost_time" field.
func (u *AdminOperationLogUpsert) ClearCostTime() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldCostTime)
	return u
}

// SetUserID sets the "user_id" field.
func (u *AdminOperationLogUpsert) SetUserID(v uint32) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateUserID() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *AdminOperationLogUpsert) AddUserID(v uint32) *AdminOperationLogUpsert {
	u.Add(adminoperationlog.FieldUserID, v)
	return u
}

// ClearUserID clears the value of the "user_id" field.
func (u *AdminOperationLogUpsert) ClearUserID() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldUserID)
	return u
}

// SetUsername sets the "username" field.
func (u *AdminOperationLogUpsert) SetUsername(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldUsername, v)
	return u
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateUsername() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldUsername)
	return u
}

// ClearUsername clears the value of the "username" field.
func (u *AdminOperationLogUpsert) ClearUsername() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldUsername)
	return u
}

// SetClientIP sets the "client_ip" field.
func (u *AdminOperationLogUpsert) SetClientIP(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldClientIP, v)
	return u
}

// UpdateClientIP sets the "client_ip" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateClientIP() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldClientIP)
	return u
}

// ClearClientIP clears the value of the "client_ip" field.
func (u *AdminOperationLogUpsert) ClearClientIP() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldClientIP)
	return u
}

// SetStatusCode sets the "status_code" field.
func (u *AdminOperationLogUpsert) SetStatusCode(v int32) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldStatusCode, v)
	return u
}

// UpdateStatusCode sets the "status_code" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateStatusCode() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldStatusCode)
	return u
}

// AddStatusCode adds v to the "status_code" field.
func (u *AdminOperationLogUpsert) AddStatusCode(v int32) *AdminOperationLogUpsert {
	u.Add(adminoperationlog.FieldStatusCode, v)
	return u
}

// ClearStatusCode clears the value of the "status_code" field.
func (u *AdminOperationLogUpsert) ClearStatusCode() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldStatusCode)
	return u
}

// SetReason sets the "reason" field.
func (u *AdminOperationLogUpsert) SetReason(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldReason, v)
	return u
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateReason() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldReason)
	return u
}

// ClearReason clears the value of the "reason" field.
func (u *AdminOperationLogUpsert) ClearReason() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldReason)
	return u
}

// SetSuccess sets the "success" field.
func (u *AdminOperationLogUpsert) SetSuccess(v bool) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldSuccess, v)
	return u
}

// UpdateSuccess sets the "success" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateSuccess() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldSuccess)
	return u
}

// ClearSuccess clears the value of the "success" field.
func (u *AdminOperationLogUpsert) ClearSuccess() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldSuccess)
	return u
}

// SetLocation sets the "location" field.
func (u *AdminOperationLogUpsert) SetLocation(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldLocation, v)
	return u
}

// UpdateLocation sets the "location" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateLocation() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldLocation)
	return u
}

// ClearLocation clears the value of the "location" field.
func (u *AdminOperationLogUpsert) ClearLocation() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldLocation)
	return u
}

// SetUserAgent sets the "user_agent" field.
func (u *AdminOperationLogUpsert) SetUserAgent(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldUserAgent, v)
	return u
}

// UpdateUserAgent sets the "user_agent" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateUserAgent() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldUserAgent)
	return u
}

// ClearUserAgent clears the value of the "user_agent" field.
func (u *AdminOperationLogUpsert) ClearUserAgent() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldUserAgent)
	return u
}

// SetBrowserName sets the "browser_name" field.
func (u *AdminOperationLogUpsert) SetBrowserName(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldBrowserName, v)
	return u
}

// UpdateBrowserName sets the "browser_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateBrowserName() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldBrowserName)
	return u
}

// ClearBrowserName clears the value of the "browser_name" field.
func (u *AdminOperationLogUpsert) ClearBrowserName() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldBrowserName)
	return u
}

// SetBrowserVersion sets the "browser_version" field.
func (u *AdminOperationLogUpsert) SetBrowserVersion(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldBrowserVersion, v)
	return u
}

// UpdateBrowserVersion sets the "browser_version" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateBrowserVersion() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldBrowserVersion)
	return u
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (u *AdminOperationLogUpsert) ClearBrowserVersion() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldBrowserVersion)
	return u
}

// SetClientID sets the "client_id" field.
func (u *AdminOperationLogUpsert) SetClientID(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldClientID, v)
	return u
}

// UpdateClientID sets the "client_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateClientID() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldClientID)
	return u
}

// ClearClientID clears the value of the "client_id" field.
func (u *AdminOperationLogUpsert) ClearClientID() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldClientID)
	return u
}

// SetClientName sets the "client_name" field.
func (u *AdminOperationLogUpsert) SetClientName(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldClientName, v)
	return u
}

// UpdateClientName sets the "client_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateClientName() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldClientName)
	return u
}

// ClearClientName clears the value of the "client_name" field.
func (u *AdminOperationLogUpsert) ClearClientName() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldClientName)
	return u
}

// SetOsName sets the "os_name" field.
func (u *AdminOperationLogUpsert) SetOsName(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldOsName, v)
	return u
}

// UpdateOsName sets the "os_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateOsName() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldOsName)
	return u
}

// ClearOsName clears the value of the "os_name" field.
func (u *AdminOperationLogUpsert) ClearOsName() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldOsName)
	return u
}

// SetOsVersion sets the "os_version" field.
func (u *AdminOperationLogUpsert) SetOsVersion(v string) *AdminOperationLogUpsert {
	u.Set(adminoperationlog.FieldOsVersion, v)
	return u
}

// UpdateOsVersion sets the "os_version" field to the value that was provided on create.
func (u *AdminOperationLogUpsert) UpdateOsVersion() *AdminOperationLogUpsert {
	u.SetExcluded(adminoperationlog.FieldOsVersion)
	return u
}

// ClearOsVersion clears the value of the "os_version" field.
func (u *AdminOperationLogUpsert) ClearOsVersion() *AdminOperationLogUpsert {
	u.SetNull(adminoperationlog.FieldOsVersion)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AdminOperationLog.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(adminoperationlog.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AdminOperationLogUpsertOne) UpdateNewValues() *AdminOperationLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(adminoperationlog.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(adminoperationlog.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AdminOperationLog.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AdminOperationLogUpsertOne) Ignore() *AdminOperationLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AdminOperationLogUpsertOne) DoNothing() *AdminOperationLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AdminOperationLogCreate.OnConflict
// documentation for more info.
func (u *AdminOperationLogUpsertOne) Update(set func(*AdminOperationLogUpsert)) *AdminOperationLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AdminOperationLogUpsert{UpdateSet: update})
	}))
	return u
}

// SetRequestID sets the "request_id" field.
func (u *AdminOperationLogUpsertOne) SetRequestID(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestID(v)
	})
}

// UpdateRequestID sets the "request_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateRequestID() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestID()
	})
}

// ClearRequestID clears the value of the "request_id" field.
func (u *AdminOperationLogUpsertOne) ClearRequestID() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestID()
	})
}

// SetMethod sets the "method" field.
func (u *AdminOperationLogUpsertOne) SetMethod(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateMethod() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateMethod()
	})
}

// ClearMethod clears the value of the "method" field.
func (u *AdminOperationLogUpsertOne) ClearMethod() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearMethod()
	})
}

// SetOperation sets the "operation" field.
func (u *AdminOperationLogUpsertOne) SetOperation(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetOperation(v)
	})
}

// UpdateOperation sets the "operation" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateOperation() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateOperation()
	})
}

// ClearOperation clears the value of the "operation" field.
func (u *AdminOperationLogUpsertOne) ClearOperation() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearOperation()
	})
}

// SetPath sets the "path" field.
func (u *AdminOperationLogUpsertOne) SetPath(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdatePath() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdatePath()
	})
}

// ClearPath clears the value of the "path" field.
func (u *AdminOperationLogUpsertOne) ClearPath() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearPath()
	})
}

// SetReferer sets the "referer" field.
func (u *AdminOperationLogUpsertOne) SetReferer(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetReferer(v)
	})
}

// UpdateReferer sets the "referer" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateReferer() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateReferer()
	})
}

// ClearReferer clears the value of the "referer" field.
func (u *AdminOperationLogUpsertOne) ClearReferer() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearReferer()
	})
}

// SetRequestURI sets the "request_uri" field.
func (u *AdminOperationLogUpsertOne) SetRequestURI(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestURI(v)
	})
}

// UpdateRequestURI sets the "request_uri" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateRequestURI() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestURI()
	})
}

// ClearRequestURI clears the value of the "request_uri" field.
func (u *AdminOperationLogUpsertOne) ClearRequestURI() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestURI()
	})
}

// SetRequestBody sets the "request_body" field.
func (u *AdminOperationLogUpsertOne) SetRequestBody(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestBody(v)
	})
}

// UpdateRequestBody sets the "request_body" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateRequestBody() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestBody()
	})
}

// ClearRequestBody clears the value of the "request_body" field.
func (u *AdminOperationLogUpsertOne) ClearRequestBody() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestBody()
	})
}

// SetRequestHeader sets the "request_header" field.
func (u *AdminOperationLogUpsertOne) SetRequestHeader(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestHeader(v)
	})
}

// UpdateRequestHeader sets the "request_header" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateRequestHeader() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestHeader()
	})
}

// ClearRequestHeader clears the value of the "request_header" field.
func (u *AdminOperationLogUpsertOne) ClearRequestHeader() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestHeader()
	})
}

// SetResponse sets the "response" field.
func (u *AdminOperationLogUpsertOne) SetResponse(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetResponse(v)
	})
}

// UpdateResponse sets the "response" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateResponse() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateResponse()
	})
}

// ClearResponse clears the value of the "response" field.
func (u *AdminOperationLogUpsertOne) ClearResponse() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearResponse()
	})
}

// SetCostTime sets the "cost_time" field.
func (u *AdminOperationLogUpsertOne) SetCostTime(v float64) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetCostTime(v)
	})
}

// AddCostTime adds v to the "cost_time" field.
func (u *AdminOperationLogUpsertOne) AddCostTime(v float64) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.AddCostTime(v)
	})
}

// UpdateCostTime sets the "cost_time" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateCostTime() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateCostTime()
	})
}

// ClearCostTime clears the value of the "cost_time" field.
func (u *AdminOperationLogUpsertOne) ClearCostTime() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearCostTime()
	})
}

// SetUserID sets the "user_id" field.
func (u *AdminOperationLogUpsertOne) SetUserID(v uint32) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AdminOperationLogUpsertOne) AddUserID(v uint32) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateUserID() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *AdminOperationLogUpsertOne) ClearUserID() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearUserID()
	})
}

// SetUsername sets the "username" field.
func (u *AdminOperationLogUpsertOne) SetUsername(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetUsername(v)
	})
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateUsername() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateUsername()
	})
}

// ClearUsername clears the value of the "username" field.
func (u *AdminOperationLogUpsertOne) ClearUsername() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearUsername()
	})
}

// SetClientIP sets the "client_ip" field.
func (u *AdminOperationLogUpsertOne) SetClientIP(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetClientIP(v)
	})
}

// UpdateClientIP sets the "client_ip" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateClientIP() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateClientIP()
	})
}

// ClearClientIP clears the value of the "client_ip" field.
func (u *AdminOperationLogUpsertOne) ClearClientIP() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearClientIP()
	})
}

// SetStatusCode sets the "status_code" field.
func (u *AdminOperationLogUpsertOne) SetStatusCode(v int32) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetStatusCode(v)
	})
}

// AddStatusCode adds v to the "status_code" field.
func (u *AdminOperationLogUpsertOne) AddStatusCode(v int32) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.AddStatusCode(v)
	})
}

// UpdateStatusCode sets the "status_code" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateStatusCode() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateStatusCode()
	})
}

// ClearStatusCode clears the value of the "status_code" field.
func (u *AdminOperationLogUpsertOne) ClearStatusCode() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearStatusCode()
	})
}

// SetReason sets the "reason" field.
func (u *AdminOperationLogUpsertOne) SetReason(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateReason() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateReason()
	})
}

// ClearReason clears the value of the "reason" field.
func (u *AdminOperationLogUpsertOne) ClearReason() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearReason()
	})
}

// SetSuccess sets the "success" field.
func (u *AdminOperationLogUpsertOne) SetSuccess(v bool) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetSuccess(v)
	})
}

// UpdateSuccess sets the "success" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateSuccess() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateSuccess()
	})
}

// ClearSuccess clears the value of the "success" field.
func (u *AdminOperationLogUpsertOne) ClearSuccess() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearSuccess()
	})
}

// SetLocation sets the "location" field.
func (u *AdminOperationLogUpsertOne) SetLocation(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetLocation(v)
	})
}

// UpdateLocation sets the "location" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateLocation() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateLocation()
	})
}

// ClearLocation clears the value of the "location" field.
func (u *AdminOperationLogUpsertOne) ClearLocation() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearLocation()
	})
}

// SetUserAgent sets the "user_agent" field.
func (u *AdminOperationLogUpsertOne) SetUserAgent(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetUserAgent(v)
	})
}

// UpdateUserAgent sets the "user_agent" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateUserAgent() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateUserAgent()
	})
}

// ClearUserAgent clears the value of the "user_agent" field.
func (u *AdminOperationLogUpsertOne) ClearUserAgent() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearUserAgent()
	})
}

// SetBrowserName sets the "browser_name" field.
func (u *AdminOperationLogUpsertOne) SetBrowserName(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetBrowserName(v)
	})
}

// UpdateBrowserName sets the "browser_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateBrowserName() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateBrowserName()
	})
}

// ClearBrowserName clears the value of the "browser_name" field.
func (u *AdminOperationLogUpsertOne) ClearBrowserName() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearBrowserName()
	})
}

// SetBrowserVersion sets the "browser_version" field.
func (u *AdminOperationLogUpsertOne) SetBrowserVersion(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetBrowserVersion(v)
	})
}

// UpdateBrowserVersion sets the "browser_version" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateBrowserVersion() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateBrowserVersion()
	})
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (u *AdminOperationLogUpsertOne) ClearBrowserVersion() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearBrowserVersion()
	})
}

// SetClientID sets the "client_id" field.
func (u *AdminOperationLogUpsertOne) SetClientID(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetClientID(v)
	})
}

// UpdateClientID sets the "client_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateClientID() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateClientID()
	})
}

// ClearClientID clears the value of the "client_id" field.
func (u *AdminOperationLogUpsertOne) ClearClientID() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearClientID()
	})
}

// SetClientName sets the "client_name" field.
func (u *AdminOperationLogUpsertOne) SetClientName(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetClientName(v)
	})
}

// UpdateClientName sets the "client_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateClientName() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateClientName()
	})
}

// ClearClientName clears the value of the "client_name" field.
func (u *AdminOperationLogUpsertOne) ClearClientName() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearClientName()
	})
}

// SetOsName sets the "os_name" field.
func (u *AdminOperationLogUpsertOne) SetOsName(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetOsName(v)
	})
}

// UpdateOsName sets the "os_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateOsName() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateOsName()
	})
}

// ClearOsName clears the value of the "os_name" field.
func (u *AdminOperationLogUpsertOne) ClearOsName() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearOsName()
	})
}

// SetOsVersion sets the "os_version" field.
func (u *AdminOperationLogUpsertOne) SetOsVersion(v string) *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetOsVersion(v)
	})
}

// UpdateOsVersion sets the "os_version" field to the value that was provided on create.
func (u *AdminOperationLogUpsertOne) UpdateOsVersion() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateOsVersion()
	})
}

// ClearOsVersion clears the value of the "os_version" field.
func (u *AdminOperationLogUpsertOne) ClearOsVersion() *AdminOperationLogUpsertOne {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearOsVersion()
	})
}

// Exec executes the query.
func (u *AdminOperationLogUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AdminOperationLogCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AdminOperationLogUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AdminOperationLogUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AdminOperationLogUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AdminOperationLogCreateBulk is the builder for creating many AdminOperationLog entities in bulk.
type AdminOperationLogCreateBulk struct {
	config
	err      error
	builders []*AdminOperationLogCreate
	conflict []sql.ConflictOption
}

// Save creates the AdminOperationLog entities in the database.
func (aolcb *AdminOperationLogCreateBulk) Save(ctx context.Context) ([]*AdminOperationLog, error) {
	if aolcb.err != nil {
		return nil, aolcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(aolcb.builders))
	nodes := make([]*AdminOperationLog, len(aolcb.builders))
	mutators := make([]Mutator, len(aolcb.builders))
	for i := range aolcb.builders {
		func(i int, root context.Context) {
			builder := aolcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AdminOperationLogMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, aolcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = aolcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, aolcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, aolcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (aolcb *AdminOperationLogCreateBulk) SaveX(ctx context.Context) []*AdminOperationLog {
	v, err := aolcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aolcb *AdminOperationLogCreateBulk) Exec(ctx context.Context) error {
	_, err := aolcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aolcb *AdminOperationLogCreateBulk) ExecX(ctx context.Context) {
	if err := aolcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AdminOperationLog.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AdminOperationLogUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (aolcb *AdminOperationLogCreateBulk) OnConflict(opts ...sql.ConflictOption) *AdminOperationLogUpsertBulk {
	aolcb.conflict = opts
	return &AdminOperationLogUpsertBulk{
		create: aolcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AdminOperationLog.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aolcb *AdminOperationLogCreateBulk) OnConflictColumns(columns ...string) *AdminOperationLogUpsertBulk {
	aolcb.conflict = append(aolcb.conflict, sql.ConflictColumns(columns...))
	return &AdminOperationLogUpsertBulk{
		create: aolcb,
	}
}

// AdminOperationLogUpsertBulk is the builder for "upsert"-ing
// a bulk of AdminOperationLog nodes.
type AdminOperationLogUpsertBulk struct {
	create *AdminOperationLogCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AdminOperationLog.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(adminoperationlog.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AdminOperationLogUpsertBulk) UpdateNewValues() *AdminOperationLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(adminoperationlog.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(adminoperationlog.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AdminOperationLog.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AdminOperationLogUpsertBulk) Ignore() *AdminOperationLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AdminOperationLogUpsertBulk) DoNothing() *AdminOperationLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AdminOperationLogCreateBulk.OnConflict
// documentation for more info.
func (u *AdminOperationLogUpsertBulk) Update(set func(*AdminOperationLogUpsert)) *AdminOperationLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AdminOperationLogUpsert{UpdateSet: update})
	}))
	return u
}

// SetRequestID sets the "request_id" field.
func (u *AdminOperationLogUpsertBulk) SetRequestID(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestID(v)
	})
}

// UpdateRequestID sets the "request_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateRequestID() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestID()
	})
}

// ClearRequestID clears the value of the "request_id" field.
func (u *AdminOperationLogUpsertBulk) ClearRequestID() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestID()
	})
}

// SetMethod sets the "method" field.
func (u *AdminOperationLogUpsertBulk) SetMethod(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateMethod() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateMethod()
	})
}

// ClearMethod clears the value of the "method" field.
func (u *AdminOperationLogUpsertBulk) ClearMethod() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearMethod()
	})
}

// SetOperation sets the "operation" field.
func (u *AdminOperationLogUpsertBulk) SetOperation(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetOperation(v)
	})
}

// UpdateOperation sets the "operation" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateOperation() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateOperation()
	})
}

// ClearOperation clears the value of the "operation" field.
func (u *AdminOperationLogUpsertBulk) ClearOperation() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearOperation()
	})
}

// SetPath sets the "path" field.
func (u *AdminOperationLogUpsertBulk) SetPath(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdatePath() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdatePath()
	})
}

// ClearPath clears the value of the "path" field.
func (u *AdminOperationLogUpsertBulk) ClearPath() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearPath()
	})
}

// SetReferer sets the "referer" field.
func (u *AdminOperationLogUpsertBulk) SetReferer(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetReferer(v)
	})
}

// UpdateReferer sets the "referer" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateReferer() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateReferer()
	})
}

// ClearReferer clears the value of the "referer" field.
func (u *AdminOperationLogUpsertBulk) ClearReferer() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearReferer()
	})
}

// SetRequestURI sets the "request_uri" field.
func (u *AdminOperationLogUpsertBulk) SetRequestURI(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestURI(v)
	})
}

// UpdateRequestURI sets the "request_uri" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateRequestURI() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestURI()
	})
}

// ClearRequestURI clears the value of the "request_uri" field.
func (u *AdminOperationLogUpsertBulk) ClearRequestURI() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestURI()
	})
}

// SetRequestBody sets the "request_body" field.
func (u *AdminOperationLogUpsertBulk) SetRequestBody(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestBody(v)
	})
}

// UpdateRequestBody sets the "request_body" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateRequestBody() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestBody()
	})
}

// ClearRequestBody clears the value of the "request_body" field.
func (u *AdminOperationLogUpsertBulk) ClearRequestBody() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestBody()
	})
}

// SetRequestHeader sets the "request_header" field.
func (u *AdminOperationLogUpsertBulk) SetRequestHeader(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetRequestHeader(v)
	})
}

// UpdateRequestHeader sets the "request_header" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateRequestHeader() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateRequestHeader()
	})
}

// ClearRequestHeader clears the value of the "request_header" field.
func (u *AdminOperationLogUpsertBulk) ClearRequestHeader() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearRequestHeader()
	})
}

// SetResponse sets the "response" field.
func (u *AdminOperationLogUpsertBulk) SetResponse(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetResponse(v)
	})
}

// UpdateResponse sets the "response" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateResponse() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateResponse()
	})
}

// ClearResponse clears the value of the "response" field.
func (u *AdminOperationLogUpsertBulk) ClearResponse() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearResponse()
	})
}

// SetCostTime sets the "cost_time" field.
func (u *AdminOperationLogUpsertBulk) SetCostTime(v float64) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetCostTime(v)
	})
}

// AddCostTime adds v to the "cost_time" field.
func (u *AdminOperationLogUpsertBulk) AddCostTime(v float64) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.AddCostTime(v)
	})
}

// UpdateCostTime sets the "cost_time" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateCostTime() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateCostTime()
	})
}

// ClearCostTime clears the value of the "cost_time" field.
func (u *AdminOperationLogUpsertBulk) ClearCostTime() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearCostTime()
	})
}

// SetUserID sets the "user_id" field.
func (u *AdminOperationLogUpsertBulk) SetUserID(v uint32) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AdminOperationLogUpsertBulk) AddUserID(v uint32) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateUserID() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *AdminOperationLogUpsertBulk) ClearUserID() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearUserID()
	})
}

// SetUsername sets the "username" field.
func (u *AdminOperationLogUpsertBulk) SetUsername(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetUsername(v)
	})
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateUsername() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateUsername()
	})
}

// ClearUsername clears the value of the "username" field.
func (u *AdminOperationLogUpsertBulk) ClearUsername() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearUsername()
	})
}

// SetClientIP sets the "client_ip" field.
func (u *AdminOperationLogUpsertBulk) SetClientIP(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetClientIP(v)
	})
}

// UpdateClientIP sets the "client_ip" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateClientIP() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateClientIP()
	})
}

// ClearClientIP clears the value of the "client_ip" field.
func (u *AdminOperationLogUpsertBulk) ClearClientIP() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearClientIP()
	})
}

// SetStatusCode sets the "status_code" field.
func (u *AdminOperationLogUpsertBulk) SetStatusCode(v int32) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetStatusCode(v)
	})
}

// AddStatusCode adds v to the "status_code" field.
func (u *AdminOperationLogUpsertBulk) AddStatusCode(v int32) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.AddStatusCode(v)
	})
}

// UpdateStatusCode sets the "status_code" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateStatusCode() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateStatusCode()
	})
}

// ClearStatusCode clears the value of the "status_code" field.
func (u *AdminOperationLogUpsertBulk) ClearStatusCode() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearStatusCode()
	})
}

// SetReason sets the "reason" field.
func (u *AdminOperationLogUpsertBulk) SetReason(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateReason() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateReason()
	})
}

// ClearReason clears the value of the "reason" field.
func (u *AdminOperationLogUpsertBulk) ClearReason() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearReason()
	})
}

// SetSuccess sets the "success" field.
func (u *AdminOperationLogUpsertBulk) SetSuccess(v bool) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetSuccess(v)
	})
}

// UpdateSuccess sets the "success" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateSuccess() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateSuccess()
	})
}

// ClearSuccess clears the value of the "success" field.
func (u *AdminOperationLogUpsertBulk) ClearSuccess() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearSuccess()
	})
}

// SetLocation sets the "location" field.
func (u *AdminOperationLogUpsertBulk) SetLocation(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetLocation(v)
	})
}

// UpdateLocation sets the "location" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateLocation() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateLocation()
	})
}

// ClearLocation clears the value of the "location" field.
func (u *AdminOperationLogUpsertBulk) ClearLocation() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearLocation()
	})
}

// SetUserAgent sets the "user_agent" field.
func (u *AdminOperationLogUpsertBulk) SetUserAgent(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetUserAgent(v)
	})
}

// UpdateUserAgent sets the "user_agent" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateUserAgent() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateUserAgent()
	})
}

// ClearUserAgent clears the value of the "user_agent" field.
func (u *AdminOperationLogUpsertBulk) ClearUserAgent() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearUserAgent()
	})
}

// SetBrowserName sets the "browser_name" field.
func (u *AdminOperationLogUpsertBulk) SetBrowserName(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetBrowserName(v)
	})
}

// UpdateBrowserName sets the "browser_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateBrowserName() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateBrowserName()
	})
}

// ClearBrowserName clears the value of the "browser_name" field.
func (u *AdminOperationLogUpsertBulk) ClearBrowserName() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearBrowserName()
	})
}

// SetBrowserVersion sets the "browser_version" field.
func (u *AdminOperationLogUpsertBulk) SetBrowserVersion(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetBrowserVersion(v)
	})
}

// UpdateBrowserVersion sets the "browser_version" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateBrowserVersion() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateBrowserVersion()
	})
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (u *AdminOperationLogUpsertBulk) ClearBrowserVersion() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearBrowserVersion()
	})
}

// SetClientID sets the "client_id" field.
func (u *AdminOperationLogUpsertBulk) SetClientID(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetClientID(v)
	})
}

// UpdateClientID sets the "client_id" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateClientID() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateClientID()
	})
}

// ClearClientID clears the value of the "client_id" field.
func (u *AdminOperationLogUpsertBulk) ClearClientID() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearClientID()
	})
}

// SetClientName sets the "client_name" field.
func (u *AdminOperationLogUpsertBulk) SetClientName(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetClientName(v)
	})
}

// UpdateClientName sets the "client_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateClientName() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateClientName()
	})
}

// ClearClientName clears the value of the "client_name" field.
func (u *AdminOperationLogUpsertBulk) ClearClientName() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearClientName()
	})
}

// SetOsName sets the "os_name" field.
func (u *AdminOperationLogUpsertBulk) SetOsName(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetOsName(v)
	})
}

// UpdateOsName sets the "os_name" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateOsName() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateOsName()
	})
}

// ClearOsName clears the value of the "os_name" field.
func (u *AdminOperationLogUpsertBulk) ClearOsName() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearOsName()
	})
}

// SetOsVersion sets the "os_version" field.
func (u *AdminOperationLogUpsertBulk) SetOsVersion(v string) *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.SetOsVersion(v)
	})
}

// UpdateOsVersion sets the "os_version" field to the value that was provided on create.
func (u *AdminOperationLogUpsertBulk) UpdateOsVersion() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.UpdateOsVersion()
	})
}

// ClearOsVersion clears the value of the "os_version" field.
func (u *AdminOperationLogUpsertBulk) ClearOsVersion() *AdminOperationLogUpsertBulk {
	return u.Update(func(s *AdminOperationLogUpsert) {
		s.ClearOsVersion()
	})
}

// Exec executes the query.
func (u *AdminOperationLogUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AdminOperationLogCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AdminOperationLogCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AdminOperationLogUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
