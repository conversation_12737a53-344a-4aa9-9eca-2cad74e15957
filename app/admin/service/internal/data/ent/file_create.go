// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/file"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FileCreate is the builder for creating a File entity.
type FileCreate struct {
	config
	mutation *FileMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (fc *FileCreate) SetCreateTime(t time.Time) *FileCreate {
	fc.mutation.SetCreateTime(t)
	return fc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (fc *FileCreate) SetNillableCreateTime(t *time.Time) *FileCreate {
	if t != nil {
		fc.SetCreateTime(*t)
	}
	return fc
}

// SetUpdateTime sets the "update_time" field.
func (fc *FileCreate) SetUpdateTime(t time.Time) *FileCreate {
	fc.mutation.SetUpdateTime(t)
	return fc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (fc *FileCreate) SetNillableUpdateTime(t *time.Time) *FileCreate {
	if t != nil {
		fc.SetUpdateTime(*t)
	}
	return fc
}

// SetDeleteTime sets the "delete_time" field.
func (fc *FileCreate) SetDeleteTime(t time.Time) *FileCreate {
	fc.mutation.SetDeleteTime(t)
	return fc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (fc *FileCreate) SetNillableDeleteTime(t *time.Time) *FileCreate {
	if t != nil {
		fc.SetDeleteTime(*t)
	}
	return fc
}

// SetCreateBy sets the "create_by" field.
func (fc *FileCreate) SetCreateBy(u uint32) *FileCreate {
	fc.mutation.SetCreateBy(u)
	return fc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (fc *FileCreate) SetNillableCreateBy(u *uint32) *FileCreate {
	if u != nil {
		fc.SetCreateBy(*u)
	}
	return fc
}

// SetRemark sets the "remark" field.
func (fc *FileCreate) SetRemark(s string) *FileCreate {
	fc.mutation.SetRemark(s)
	return fc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (fc *FileCreate) SetNillableRemark(s *string) *FileCreate {
	if s != nil {
		fc.SetRemark(*s)
	}
	return fc
}

// SetTenantID sets the "tenant_id" field.
func (fc *FileCreate) SetTenantID(u uint32) *FileCreate {
	fc.mutation.SetTenantID(u)
	return fc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (fc *FileCreate) SetNillableTenantID(u *uint32) *FileCreate {
	if u != nil {
		fc.SetTenantID(*u)
	}
	return fc
}

// SetProvider sets the "provider" field.
func (fc *FileCreate) SetProvider(f file.Provider) *FileCreate {
	fc.mutation.SetProvider(f)
	return fc
}

// SetNillableProvider sets the "provider" field if the given value is not nil.
func (fc *FileCreate) SetNillableProvider(f *file.Provider) *FileCreate {
	if f != nil {
		fc.SetProvider(*f)
	}
	return fc
}

// SetBucketName sets the "bucket_name" field.
func (fc *FileCreate) SetBucketName(s string) *FileCreate {
	fc.mutation.SetBucketName(s)
	return fc
}

// SetNillableBucketName sets the "bucket_name" field if the given value is not nil.
func (fc *FileCreate) SetNillableBucketName(s *string) *FileCreate {
	if s != nil {
		fc.SetBucketName(*s)
	}
	return fc
}

// SetFileDirectory sets the "file_directory" field.
func (fc *FileCreate) SetFileDirectory(s string) *FileCreate {
	fc.mutation.SetFileDirectory(s)
	return fc
}

// SetNillableFileDirectory sets the "file_directory" field if the given value is not nil.
func (fc *FileCreate) SetNillableFileDirectory(s *string) *FileCreate {
	if s != nil {
		fc.SetFileDirectory(*s)
	}
	return fc
}

// SetFileGUID sets the "file_guid" field.
func (fc *FileCreate) SetFileGUID(s string) *FileCreate {
	fc.mutation.SetFileGUID(s)
	return fc
}

// SetNillableFileGUID sets the "file_guid" field if the given value is not nil.
func (fc *FileCreate) SetNillableFileGUID(s *string) *FileCreate {
	if s != nil {
		fc.SetFileGUID(*s)
	}
	return fc
}

// SetSaveFileName sets the "save_file_name" field.
func (fc *FileCreate) SetSaveFileName(s string) *FileCreate {
	fc.mutation.SetSaveFileName(s)
	return fc
}

// SetNillableSaveFileName sets the "save_file_name" field if the given value is not nil.
func (fc *FileCreate) SetNillableSaveFileName(s *string) *FileCreate {
	if s != nil {
		fc.SetSaveFileName(*s)
	}
	return fc
}

// SetFileName sets the "file_name" field.
func (fc *FileCreate) SetFileName(s string) *FileCreate {
	fc.mutation.SetFileName(s)
	return fc
}

// SetNillableFileName sets the "file_name" field if the given value is not nil.
func (fc *FileCreate) SetNillableFileName(s *string) *FileCreate {
	if s != nil {
		fc.SetFileName(*s)
	}
	return fc
}

// SetExtension sets the "extension" field.
func (fc *FileCreate) SetExtension(s string) *FileCreate {
	fc.mutation.SetExtension(s)
	return fc
}

// SetNillableExtension sets the "extension" field if the given value is not nil.
func (fc *FileCreate) SetNillableExtension(s *string) *FileCreate {
	if s != nil {
		fc.SetExtension(*s)
	}
	return fc
}

// SetSize sets the "size" field.
func (fc *FileCreate) SetSize(u uint64) *FileCreate {
	fc.mutation.SetSize(u)
	return fc
}

// SetNillableSize sets the "size" field if the given value is not nil.
func (fc *FileCreate) SetNillableSize(u *uint64) *FileCreate {
	if u != nil {
		fc.SetSize(*u)
	}
	return fc
}

// SetSizeFormat sets the "size_format" field.
func (fc *FileCreate) SetSizeFormat(s string) *FileCreate {
	fc.mutation.SetSizeFormat(s)
	return fc
}

// SetNillableSizeFormat sets the "size_format" field if the given value is not nil.
func (fc *FileCreate) SetNillableSizeFormat(s *string) *FileCreate {
	if s != nil {
		fc.SetSizeFormat(*s)
	}
	return fc
}

// SetLinkURL sets the "link_url" field.
func (fc *FileCreate) SetLinkURL(s string) *FileCreate {
	fc.mutation.SetLinkURL(s)
	return fc
}

// SetNillableLinkURL sets the "link_url" field if the given value is not nil.
func (fc *FileCreate) SetNillableLinkURL(s *string) *FileCreate {
	if s != nil {
		fc.SetLinkURL(*s)
	}
	return fc
}

// SetMd5 sets the "md5" field.
func (fc *FileCreate) SetMd5(s string) *FileCreate {
	fc.mutation.SetMd5(s)
	return fc
}

// SetNillableMd5 sets the "md5" field if the given value is not nil.
func (fc *FileCreate) SetNillableMd5(s *string) *FileCreate {
	if s != nil {
		fc.SetMd5(*s)
	}
	return fc
}

// SetID sets the "id" field.
func (fc *FileCreate) SetID(u uint32) *FileCreate {
	fc.mutation.SetID(u)
	return fc
}

// Mutation returns the FileMutation object of the builder.
func (fc *FileCreate) Mutation() *FileMutation {
	return fc.mutation
}

// Save creates the File in the database.
func (fc *FileCreate) Save(ctx context.Context) (*File, error) {
	fc.defaults()
	return withHooks(ctx, fc.sqlSave, fc.mutation, fc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (fc *FileCreate) SaveX(ctx context.Context) *File {
	v, err := fc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fc *FileCreate) Exec(ctx context.Context) error {
	_, err := fc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fc *FileCreate) ExecX(ctx context.Context) {
	if err := fc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fc *FileCreate) defaults() {
	if _, ok := fc.mutation.Remark(); !ok {
		v := file.DefaultRemark
		fc.mutation.SetRemark(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fc *FileCreate) check() error {
	if v, ok := fc.mutation.TenantID(); ok {
		if err := file.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "File.tenant_id": %w`, err)}
		}
	}
	if v, ok := fc.mutation.Provider(); ok {
		if err := file.ProviderValidator(v); err != nil {
			return &ValidationError{Name: "provider", err: fmt.Errorf(`ent: validator failed for field "File.provider": %w`, err)}
		}
	}
	if v, ok := fc.mutation.ID(); ok {
		if err := file.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "File.id": %w`, err)}
		}
	}
	return nil
}

func (fc *FileCreate) sqlSave(ctx context.Context) (*File, error) {
	if err := fc.check(); err != nil {
		return nil, err
	}
	_node, _spec := fc.createSpec()
	if err := sqlgraph.CreateNode(ctx, fc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	fc.mutation.id = &_node.ID
	fc.mutation.done = true
	return _node, nil
}

func (fc *FileCreate) createSpec() (*File, *sqlgraph.CreateSpec) {
	var (
		_node = &File{config: fc.config}
		_spec = sqlgraph.NewCreateSpec(file.Table, sqlgraph.NewFieldSpec(file.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = fc.conflict
	if id, ok := fc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := fc.mutation.CreateTime(); ok {
		_spec.SetField(file.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := fc.mutation.UpdateTime(); ok {
		_spec.SetField(file.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := fc.mutation.DeleteTime(); ok {
		_spec.SetField(file.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := fc.mutation.CreateBy(); ok {
		_spec.SetField(file.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := fc.mutation.Remark(); ok {
		_spec.SetField(file.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := fc.mutation.TenantID(); ok {
		_spec.SetField(file.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := fc.mutation.Provider(); ok {
		_spec.SetField(file.FieldProvider, field.TypeEnum, value)
		_node.Provider = &value
	}
	if value, ok := fc.mutation.BucketName(); ok {
		_spec.SetField(file.FieldBucketName, field.TypeString, value)
		_node.BucketName = &value
	}
	if value, ok := fc.mutation.FileDirectory(); ok {
		_spec.SetField(file.FieldFileDirectory, field.TypeString, value)
		_node.FileDirectory = &value
	}
	if value, ok := fc.mutation.FileGUID(); ok {
		_spec.SetField(file.FieldFileGUID, field.TypeString, value)
		_node.FileGUID = &value
	}
	if value, ok := fc.mutation.SaveFileName(); ok {
		_spec.SetField(file.FieldSaveFileName, field.TypeString, value)
		_node.SaveFileName = &value
	}
	if value, ok := fc.mutation.FileName(); ok {
		_spec.SetField(file.FieldFileName, field.TypeString, value)
		_node.FileName = &value
	}
	if value, ok := fc.mutation.Extension(); ok {
		_spec.SetField(file.FieldExtension, field.TypeString, value)
		_node.Extension = &value
	}
	if value, ok := fc.mutation.Size(); ok {
		_spec.SetField(file.FieldSize, field.TypeUint64, value)
		_node.Size = &value
	}
	if value, ok := fc.mutation.SizeFormat(); ok {
		_spec.SetField(file.FieldSizeFormat, field.TypeString, value)
		_node.SizeFormat = &value
	}
	if value, ok := fc.mutation.LinkURL(); ok {
		_spec.SetField(file.FieldLinkURL, field.TypeString, value)
		_node.LinkURL = &value
	}
	if value, ok := fc.mutation.Md5(); ok {
		_spec.SetField(file.FieldMd5, field.TypeString, value)
		_node.Md5 = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.File.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.FileUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (fc *FileCreate) OnConflict(opts ...sql.ConflictOption) *FileUpsertOne {
	fc.conflict = opts
	return &FileUpsertOne{
		create: fc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (fc *FileCreate) OnConflictColumns(columns ...string) *FileUpsertOne {
	fc.conflict = append(fc.conflict, sql.ConflictColumns(columns...))
	return &FileUpsertOne{
		create: fc,
	}
}

type (
	// FileUpsertOne is the builder for "upsert"-ing
	//  one File node.
	FileUpsertOne struct {
		create *FileCreate
	}

	// FileUpsert is the "OnConflict" setter.
	FileUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *FileUpsert) SetUpdateTime(v time.Time) *FileUpsert {
	u.Set(file.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *FileUpsert) UpdateUpdateTime() *FileUpsert {
	u.SetExcluded(file.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *FileUpsert) ClearUpdateTime() *FileUpsert {
	u.SetNull(file.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *FileUpsert) SetDeleteTime(v time.Time) *FileUpsert {
	u.Set(file.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *FileUpsert) UpdateDeleteTime() *FileUpsert {
	u.SetExcluded(file.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *FileUpsert) ClearDeleteTime() *FileUpsert {
	u.SetNull(file.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *FileUpsert) SetCreateBy(v uint32) *FileUpsert {
	u.Set(file.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *FileUpsert) UpdateCreateBy() *FileUpsert {
	u.SetExcluded(file.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *FileUpsert) AddCreateBy(v uint32) *FileUpsert {
	u.Add(file.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *FileUpsert) ClearCreateBy() *FileUpsert {
	u.SetNull(file.FieldCreateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *FileUpsert) SetRemark(v string) *FileUpsert {
	u.Set(file.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *FileUpsert) UpdateRemark() *FileUpsert {
	u.SetExcluded(file.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *FileUpsert) ClearRemark() *FileUpsert {
	u.SetNull(file.FieldRemark)
	return u
}

// SetProvider sets the "provider" field.
func (u *FileUpsert) SetProvider(v file.Provider) *FileUpsert {
	u.Set(file.FieldProvider, v)
	return u
}

// UpdateProvider sets the "provider" field to the value that was provided on create.
func (u *FileUpsert) UpdateProvider() *FileUpsert {
	u.SetExcluded(file.FieldProvider)
	return u
}

// ClearProvider clears the value of the "provider" field.
func (u *FileUpsert) ClearProvider() *FileUpsert {
	u.SetNull(file.FieldProvider)
	return u
}

// SetBucketName sets the "bucket_name" field.
func (u *FileUpsert) SetBucketName(v string) *FileUpsert {
	u.Set(file.FieldBucketName, v)
	return u
}

// UpdateBucketName sets the "bucket_name" field to the value that was provided on create.
func (u *FileUpsert) UpdateBucketName() *FileUpsert {
	u.SetExcluded(file.FieldBucketName)
	return u
}

// ClearBucketName clears the value of the "bucket_name" field.
func (u *FileUpsert) ClearBucketName() *FileUpsert {
	u.SetNull(file.FieldBucketName)
	return u
}

// SetFileDirectory sets the "file_directory" field.
func (u *FileUpsert) SetFileDirectory(v string) *FileUpsert {
	u.Set(file.FieldFileDirectory, v)
	return u
}

// UpdateFileDirectory sets the "file_directory" field to the value that was provided on create.
func (u *FileUpsert) UpdateFileDirectory() *FileUpsert {
	u.SetExcluded(file.FieldFileDirectory)
	return u
}

// ClearFileDirectory clears the value of the "file_directory" field.
func (u *FileUpsert) ClearFileDirectory() *FileUpsert {
	u.SetNull(file.FieldFileDirectory)
	return u
}

// SetFileGUID sets the "file_guid" field.
func (u *FileUpsert) SetFileGUID(v string) *FileUpsert {
	u.Set(file.FieldFileGUID, v)
	return u
}

// UpdateFileGUID sets the "file_guid" field to the value that was provided on create.
func (u *FileUpsert) UpdateFileGUID() *FileUpsert {
	u.SetExcluded(file.FieldFileGUID)
	return u
}

// ClearFileGUID clears the value of the "file_guid" field.
func (u *FileUpsert) ClearFileGUID() *FileUpsert {
	u.SetNull(file.FieldFileGUID)
	return u
}

// SetSaveFileName sets the "save_file_name" field.
func (u *FileUpsert) SetSaveFileName(v string) *FileUpsert {
	u.Set(file.FieldSaveFileName, v)
	return u
}

// UpdateSaveFileName sets the "save_file_name" field to the value that was provided on create.
func (u *FileUpsert) UpdateSaveFileName() *FileUpsert {
	u.SetExcluded(file.FieldSaveFileName)
	return u
}

// ClearSaveFileName clears the value of the "save_file_name" field.
func (u *FileUpsert) ClearSaveFileName() *FileUpsert {
	u.SetNull(file.FieldSaveFileName)
	return u
}

// SetFileName sets the "file_name" field.
func (u *FileUpsert) SetFileName(v string) *FileUpsert {
	u.Set(file.FieldFileName, v)
	return u
}

// UpdateFileName sets the "file_name" field to the value that was provided on create.
func (u *FileUpsert) UpdateFileName() *FileUpsert {
	u.SetExcluded(file.FieldFileName)
	return u
}

// ClearFileName clears the value of the "file_name" field.
func (u *FileUpsert) ClearFileName() *FileUpsert {
	u.SetNull(file.FieldFileName)
	return u
}

// SetExtension sets the "extension" field.
func (u *FileUpsert) SetExtension(v string) *FileUpsert {
	u.Set(file.FieldExtension, v)
	return u
}

// UpdateExtension sets the "extension" field to the value that was provided on create.
func (u *FileUpsert) UpdateExtension() *FileUpsert {
	u.SetExcluded(file.FieldExtension)
	return u
}

// ClearExtension clears the value of the "extension" field.
func (u *FileUpsert) ClearExtension() *FileUpsert {
	u.SetNull(file.FieldExtension)
	return u
}

// SetSize sets the "size" field.
func (u *FileUpsert) SetSize(v uint64) *FileUpsert {
	u.Set(file.FieldSize, v)
	return u
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *FileUpsert) UpdateSize() *FileUpsert {
	u.SetExcluded(file.FieldSize)
	return u
}

// AddSize adds v to the "size" field.
func (u *FileUpsert) AddSize(v uint64) *FileUpsert {
	u.Add(file.FieldSize, v)
	return u
}

// ClearSize clears the value of the "size" field.
func (u *FileUpsert) ClearSize() *FileUpsert {
	u.SetNull(file.FieldSize)
	return u
}

// SetSizeFormat sets the "size_format" field.
func (u *FileUpsert) SetSizeFormat(v string) *FileUpsert {
	u.Set(file.FieldSizeFormat, v)
	return u
}

// UpdateSizeFormat sets the "size_format" field to the value that was provided on create.
func (u *FileUpsert) UpdateSizeFormat() *FileUpsert {
	u.SetExcluded(file.FieldSizeFormat)
	return u
}

// ClearSizeFormat clears the value of the "size_format" field.
func (u *FileUpsert) ClearSizeFormat() *FileUpsert {
	u.SetNull(file.FieldSizeFormat)
	return u
}

// SetLinkURL sets the "link_url" field.
func (u *FileUpsert) SetLinkURL(v string) *FileUpsert {
	u.Set(file.FieldLinkURL, v)
	return u
}

// UpdateLinkURL sets the "link_url" field to the value that was provided on create.
func (u *FileUpsert) UpdateLinkURL() *FileUpsert {
	u.SetExcluded(file.FieldLinkURL)
	return u
}

// ClearLinkURL clears the value of the "link_url" field.
func (u *FileUpsert) ClearLinkURL() *FileUpsert {
	u.SetNull(file.FieldLinkURL)
	return u
}

// SetMd5 sets the "md5" field.
func (u *FileUpsert) SetMd5(v string) *FileUpsert {
	u.Set(file.FieldMd5, v)
	return u
}

// UpdateMd5 sets the "md5" field to the value that was provided on create.
func (u *FileUpsert) UpdateMd5() *FileUpsert {
	u.SetExcluded(file.FieldMd5)
	return u
}

// ClearMd5 clears the value of the "md5" field.
func (u *FileUpsert) ClearMd5() *FileUpsert {
	u.SetNull(file.FieldMd5)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(file.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *FileUpsertOne) UpdateNewValues() *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(file.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(file.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(file.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.File.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *FileUpsertOne) Ignore() *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *FileUpsertOne) DoNothing() *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the FileCreate.OnConflict
// documentation for more info.
func (u *FileUpsertOne) Update(set func(*FileUpsert)) *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&FileUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *FileUpsertOne) SetUpdateTime(v time.Time) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateUpdateTime() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *FileUpsertOne) ClearUpdateTime() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *FileUpsertOne) SetDeleteTime(v time.Time) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateDeleteTime() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *FileUpsertOne) ClearDeleteTime() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *FileUpsertOne) SetCreateBy(v uint32) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *FileUpsertOne) AddCreateBy(v uint32) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateCreateBy() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *FileUpsertOne) ClearCreateBy() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearCreateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *FileUpsertOne) SetRemark(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateRemark() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *FileUpsertOne) ClearRemark() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearRemark()
	})
}

// SetProvider sets the "provider" field.
func (u *FileUpsertOne) SetProvider(v file.Provider) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetProvider(v)
	})
}

// UpdateProvider sets the "provider" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateProvider() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateProvider()
	})
}

// ClearProvider clears the value of the "provider" field.
func (u *FileUpsertOne) ClearProvider() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearProvider()
	})
}

// SetBucketName sets the "bucket_name" field.
func (u *FileUpsertOne) SetBucketName(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetBucketName(v)
	})
}

// UpdateBucketName sets the "bucket_name" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateBucketName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateBucketName()
	})
}

// ClearBucketName clears the value of the "bucket_name" field.
func (u *FileUpsertOne) ClearBucketName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearBucketName()
	})
}

// SetFileDirectory sets the "file_directory" field.
func (u *FileUpsertOne) SetFileDirectory(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetFileDirectory(v)
	})
}

// UpdateFileDirectory sets the "file_directory" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateFileDirectory() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileDirectory()
	})
}

// ClearFileDirectory clears the value of the "file_directory" field.
func (u *FileUpsertOne) ClearFileDirectory() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearFileDirectory()
	})
}

// SetFileGUID sets the "file_guid" field.
func (u *FileUpsertOne) SetFileGUID(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetFileGUID(v)
	})
}

// UpdateFileGUID sets the "file_guid" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateFileGUID() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileGUID()
	})
}

// ClearFileGUID clears the value of the "file_guid" field.
func (u *FileUpsertOne) ClearFileGUID() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearFileGUID()
	})
}

// SetSaveFileName sets the "save_file_name" field.
func (u *FileUpsertOne) SetSaveFileName(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetSaveFileName(v)
	})
}

// UpdateSaveFileName sets the "save_file_name" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateSaveFileName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSaveFileName()
	})
}

// ClearSaveFileName clears the value of the "save_file_name" field.
func (u *FileUpsertOne) ClearSaveFileName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearSaveFileName()
	})
}

// SetFileName sets the "file_name" field.
func (u *FileUpsertOne) SetFileName(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetFileName(v)
	})
}

// UpdateFileName sets the "file_name" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateFileName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileName()
	})
}

// ClearFileName clears the value of the "file_name" field.
func (u *FileUpsertOne) ClearFileName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearFileName()
	})
}

// SetExtension sets the "extension" field.
func (u *FileUpsertOne) SetExtension(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetExtension(v)
	})
}

// UpdateExtension sets the "extension" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateExtension() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateExtension()
	})
}

// ClearExtension clears the value of the "extension" field.
func (u *FileUpsertOne) ClearExtension() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearExtension()
	})
}

// SetSize sets the "size" field.
func (u *FileUpsertOne) SetSize(v uint64) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetSize(v)
	})
}

// AddSize adds v to the "size" field.
func (u *FileUpsertOne) AddSize(v uint64) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.AddSize(v)
	})
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateSize() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSize()
	})
}

// ClearSize clears the value of the "size" field.
func (u *FileUpsertOne) ClearSize() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearSize()
	})
}

// SetSizeFormat sets the "size_format" field.
func (u *FileUpsertOne) SetSizeFormat(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetSizeFormat(v)
	})
}

// UpdateSizeFormat sets the "size_format" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateSizeFormat() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSizeFormat()
	})
}

// ClearSizeFormat clears the value of the "size_format" field.
func (u *FileUpsertOne) ClearSizeFormat() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearSizeFormat()
	})
}

// SetLinkURL sets the "link_url" field.
func (u *FileUpsertOne) SetLinkURL(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetLinkURL(v)
	})
}

// UpdateLinkURL sets the "link_url" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateLinkURL() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateLinkURL()
	})
}

// ClearLinkURL clears the value of the "link_url" field.
func (u *FileUpsertOne) ClearLinkURL() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearLinkURL()
	})
}

// SetMd5 sets the "md5" field.
func (u *FileUpsertOne) SetMd5(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetMd5(v)
	})
}

// UpdateMd5 sets the "md5" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateMd5() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateMd5()
	})
}

// ClearMd5 clears the value of the "md5" field.
func (u *FileUpsertOne) ClearMd5() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearMd5()
	})
}

// Exec executes the query.
func (u *FileUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for FileCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *FileUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *FileUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *FileUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// FileCreateBulk is the builder for creating many File entities in bulk.
type FileCreateBulk struct {
	config
	err      error
	builders []*FileCreate
	conflict []sql.ConflictOption
}

// Save creates the File entities in the database.
func (fcb *FileCreateBulk) Save(ctx context.Context) ([]*File, error) {
	if fcb.err != nil {
		return nil, fcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(fcb.builders))
	nodes := make([]*File, len(fcb.builders))
	mutators := make([]Mutator, len(fcb.builders))
	for i := range fcb.builders {
		func(i int, root context.Context) {
			builder := fcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FileMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, fcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = fcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, fcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, fcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (fcb *FileCreateBulk) SaveX(ctx context.Context) []*File {
	v, err := fcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fcb *FileCreateBulk) Exec(ctx context.Context) error {
	_, err := fcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fcb *FileCreateBulk) ExecX(ctx context.Context) {
	if err := fcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.File.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.FileUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (fcb *FileCreateBulk) OnConflict(opts ...sql.ConflictOption) *FileUpsertBulk {
	fcb.conflict = opts
	return &FileUpsertBulk{
		create: fcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (fcb *FileCreateBulk) OnConflictColumns(columns ...string) *FileUpsertBulk {
	fcb.conflict = append(fcb.conflict, sql.ConflictColumns(columns...))
	return &FileUpsertBulk{
		create: fcb,
	}
}

// FileUpsertBulk is the builder for "upsert"-ing
// a bulk of File nodes.
type FileUpsertBulk struct {
	create *FileCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(file.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *FileUpsertBulk) UpdateNewValues() *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(file.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(file.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(file.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *FileUpsertBulk) Ignore() *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *FileUpsertBulk) DoNothing() *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the FileCreateBulk.OnConflict
// documentation for more info.
func (u *FileUpsertBulk) Update(set func(*FileUpsert)) *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&FileUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *FileUpsertBulk) SetUpdateTime(v time.Time) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateUpdateTime() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *FileUpsertBulk) ClearUpdateTime() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *FileUpsertBulk) SetDeleteTime(v time.Time) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateDeleteTime() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *FileUpsertBulk) ClearDeleteTime() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *FileUpsertBulk) SetCreateBy(v uint32) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *FileUpsertBulk) AddCreateBy(v uint32) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateCreateBy() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *FileUpsertBulk) ClearCreateBy() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearCreateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *FileUpsertBulk) SetRemark(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateRemark() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *FileUpsertBulk) ClearRemark() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearRemark()
	})
}

// SetProvider sets the "provider" field.
func (u *FileUpsertBulk) SetProvider(v file.Provider) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetProvider(v)
	})
}

// UpdateProvider sets the "provider" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateProvider() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateProvider()
	})
}

// ClearProvider clears the value of the "provider" field.
func (u *FileUpsertBulk) ClearProvider() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearProvider()
	})
}

// SetBucketName sets the "bucket_name" field.
func (u *FileUpsertBulk) SetBucketName(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetBucketName(v)
	})
}

// UpdateBucketName sets the "bucket_name" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateBucketName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateBucketName()
	})
}

// ClearBucketName clears the value of the "bucket_name" field.
func (u *FileUpsertBulk) ClearBucketName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearBucketName()
	})
}

// SetFileDirectory sets the "file_directory" field.
func (u *FileUpsertBulk) SetFileDirectory(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetFileDirectory(v)
	})
}

// UpdateFileDirectory sets the "file_directory" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateFileDirectory() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileDirectory()
	})
}

// ClearFileDirectory clears the value of the "file_directory" field.
func (u *FileUpsertBulk) ClearFileDirectory() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearFileDirectory()
	})
}

// SetFileGUID sets the "file_guid" field.
func (u *FileUpsertBulk) SetFileGUID(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetFileGUID(v)
	})
}

// UpdateFileGUID sets the "file_guid" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateFileGUID() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileGUID()
	})
}

// ClearFileGUID clears the value of the "file_guid" field.
func (u *FileUpsertBulk) ClearFileGUID() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearFileGUID()
	})
}

// SetSaveFileName sets the "save_file_name" field.
func (u *FileUpsertBulk) SetSaveFileName(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetSaveFileName(v)
	})
}

// UpdateSaveFileName sets the "save_file_name" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateSaveFileName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSaveFileName()
	})
}

// ClearSaveFileName clears the value of the "save_file_name" field.
func (u *FileUpsertBulk) ClearSaveFileName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearSaveFileName()
	})
}

// SetFileName sets the "file_name" field.
func (u *FileUpsertBulk) SetFileName(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetFileName(v)
	})
}

// UpdateFileName sets the "file_name" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateFileName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileName()
	})
}

// ClearFileName clears the value of the "file_name" field.
func (u *FileUpsertBulk) ClearFileName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearFileName()
	})
}

// SetExtension sets the "extension" field.
func (u *FileUpsertBulk) SetExtension(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetExtension(v)
	})
}

// UpdateExtension sets the "extension" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateExtension() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateExtension()
	})
}

// ClearExtension clears the value of the "extension" field.
func (u *FileUpsertBulk) ClearExtension() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearExtension()
	})
}

// SetSize sets the "size" field.
func (u *FileUpsertBulk) SetSize(v uint64) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetSize(v)
	})
}

// AddSize adds v to the "size" field.
func (u *FileUpsertBulk) AddSize(v uint64) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.AddSize(v)
	})
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateSize() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSize()
	})
}

// ClearSize clears the value of the "size" field.
func (u *FileUpsertBulk) ClearSize() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearSize()
	})
}

// SetSizeFormat sets the "size_format" field.
func (u *FileUpsertBulk) SetSizeFormat(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetSizeFormat(v)
	})
}

// UpdateSizeFormat sets the "size_format" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateSizeFormat() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSizeFormat()
	})
}

// ClearSizeFormat clears the value of the "size_format" field.
func (u *FileUpsertBulk) ClearSizeFormat() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearSizeFormat()
	})
}

// SetLinkURL sets the "link_url" field.
func (u *FileUpsertBulk) SetLinkURL(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetLinkURL(v)
	})
}

// UpdateLinkURL sets the "link_url" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateLinkURL() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateLinkURL()
	})
}

// ClearLinkURL clears the value of the "link_url" field.
func (u *FileUpsertBulk) ClearLinkURL() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearLinkURL()
	})
}

// SetMd5 sets the "md5" field.
func (u *FileUpsertBulk) SetMd5(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetMd5(v)
	})
}

// UpdateMd5 sets the "md5" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateMd5() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateMd5()
	})
}

// ClearMd5 clears the value of the "md5" field.
func (u *FileUpsertBulk) ClearMd5() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearMd5()
	})
}

// Exec executes the query.
func (u *FileUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the FileCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for FileCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *FileUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
