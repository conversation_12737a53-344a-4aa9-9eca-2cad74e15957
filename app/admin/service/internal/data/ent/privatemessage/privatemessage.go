// Code generated by ent, DO NOT EDIT.

package privatemessage

import (
	"fmt"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the privatemessage type in the database.
	Label = "private_message"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldDeleteTime holds the string denoting the delete_time field in the database.
	FieldDeleteTime = "delete_time"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldSubject holds the string denoting the subject field in the database.
	FieldSubject = "subject"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldSenderID holds the string denoting the sender_id field in the database.
	FieldSenderID = "sender_id"
	// FieldReceiverID holds the string denoting the receiver_id field in the database.
	FieldReceiverID = "receiver_id"
	// Table holds the table name of the privatemessage in the database.
	Table = "private_messages"
)

// Columns holds all SQL columns for privatemessage fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldDeleteTime,
	FieldTenantID,
	FieldSubject,
	FieldContent,
	FieldStatus,
	FieldSenderID,
	FieldReceiverID,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// TenantIDValidator is a validator for the "tenant_id" field. It is called by the builders before save.
	TenantIDValidator func(uint32) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(uint32) error
)

// Status defines the type for the "status" enum field.
type Status string

// Status values.
const (
	StatusUnknown  Status = "UNKNOWN"
	StatusDraft    Status = "DRAFT"
	StatusSent     Status = "SENT"
	StatusReceived Status = "RECEIVED"
	StatusRead     Status = "READ"
	StatusArchived Status = "ARCHIVED"
	StatusDeleted  Status = "DELETED"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusUnknown, StatusDraft, StatusSent, StatusReceived, StatusRead, StatusArchived, StatusDeleted:
		return nil
	default:
		return fmt.Errorf("privatemessage: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the PrivateMessage queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByDeleteTime orders the results by the delete_time field.
func ByDeleteTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeleteTime, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// BySubject orders the results by the subject field.
func BySubject(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSubject, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// BySenderID orders the results by the sender_id field.
func BySenderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSenderID, opts...).ToFunc()
}

// ByReceiverID orders the results by the receiver_id field.
func ByReceiverID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReceiverID, opts...).ToFunc()
}
