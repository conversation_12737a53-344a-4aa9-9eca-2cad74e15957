// Code generated by ent, DO NOT EDIT.

package privatemessage

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldDeleteTime, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldTenantID, v))
}

// Subject applies equality check predicate on the "subject" field. It's identical to SubjectEQ.
func Subject(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldSubject, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldContent, v))
}

// SenderID applies equality check predicate on the "sender_id" field. It's identical to SenderIDEQ.
func SenderID(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldSenderID, v))
}

// ReceiverID applies equality check predicate on the "receiver_id" field. It's identical to ReceiverIDEQ.
func ReceiverID(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldReceiverID, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldDeleteTime))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldTenantID))
}

// SubjectEQ applies the EQ predicate on the "subject" field.
func SubjectEQ(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldSubject, v))
}

// SubjectNEQ applies the NEQ predicate on the "subject" field.
func SubjectNEQ(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldSubject, v))
}

// SubjectIn applies the In predicate on the "subject" field.
func SubjectIn(vs ...string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldSubject, vs...))
}

// SubjectNotIn applies the NotIn predicate on the "subject" field.
func SubjectNotIn(vs ...string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldSubject, vs...))
}

// SubjectGT applies the GT predicate on the "subject" field.
func SubjectGT(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldSubject, v))
}

// SubjectGTE applies the GTE predicate on the "subject" field.
func SubjectGTE(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldSubject, v))
}

// SubjectLT applies the LT predicate on the "subject" field.
func SubjectLT(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldSubject, v))
}

// SubjectLTE applies the LTE predicate on the "subject" field.
func SubjectLTE(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldSubject, v))
}

// SubjectContains applies the Contains predicate on the "subject" field.
func SubjectContains(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldContains(FieldSubject, v))
}

// SubjectHasPrefix applies the HasPrefix predicate on the "subject" field.
func SubjectHasPrefix(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldHasPrefix(FieldSubject, v))
}

// SubjectHasSuffix applies the HasSuffix predicate on the "subject" field.
func SubjectHasSuffix(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldHasSuffix(FieldSubject, v))
}

// SubjectIsNil applies the IsNil predicate on the "subject" field.
func SubjectIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldSubject))
}

// SubjectNotNil applies the NotNil predicate on the "subject" field.
func SubjectNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldSubject))
}

// SubjectEqualFold applies the EqualFold predicate on the "subject" field.
func SubjectEqualFold(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEqualFold(FieldSubject, v))
}

// SubjectContainsFold applies the ContainsFold predicate on the "subject" field.
func SubjectContainsFold(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldContainsFold(FieldSubject, v))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldHasSuffix(FieldContent, v))
}

// ContentIsNil applies the IsNil predicate on the "content" field.
func ContentIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldContent))
}

// ContentNotNil applies the NotNil predicate on the "content" field.
func ContentNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldContent))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldContainsFold(FieldContent, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldStatus))
}

// SenderIDEQ applies the EQ predicate on the "sender_id" field.
func SenderIDEQ(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldSenderID, v))
}

// SenderIDNEQ applies the NEQ predicate on the "sender_id" field.
func SenderIDNEQ(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldSenderID, v))
}

// SenderIDIn applies the In predicate on the "sender_id" field.
func SenderIDIn(vs ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldSenderID, vs...))
}

// SenderIDNotIn applies the NotIn predicate on the "sender_id" field.
func SenderIDNotIn(vs ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldSenderID, vs...))
}

// SenderIDGT applies the GT predicate on the "sender_id" field.
func SenderIDGT(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldSenderID, v))
}

// SenderIDGTE applies the GTE predicate on the "sender_id" field.
func SenderIDGTE(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldSenderID, v))
}

// SenderIDLT applies the LT predicate on the "sender_id" field.
func SenderIDLT(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldSenderID, v))
}

// SenderIDLTE applies the LTE predicate on the "sender_id" field.
func SenderIDLTE(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldSenderID, v))
}

// SenderIDIsNil applies the IsNil predicate on the "sender_id" field.
func SenderIDIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldSenderID))
}

// SenderIDNotNil applies the NotNil predicate on the "sender_id" field.
func SenderIDNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldSenderID))
}

// ReceiverIDEQ applies the EQ predicate on the "receiver_id" field.
func ReceiverIDEQ(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldEQ(FieldReceiverID, v))
}

// ReceiverIDNEQ applies the NEQ predicate on the "receiver_id" field.
func ReceiverIDNEQ(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNEQ(FieldReceiverID, v))
}

// ReceiverIDIn applies the In predicate on the "receiver_id" field.
func ReceiverIDIn(vs ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIn(FieldReceiverID, vs...))
}

// ReceiverIDNotIn applies the NotIn predicate on the "receiver_id" field.
func ReceiverIDNotIn(vs ...uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotIn(FieldReceiverID, vs...))
}

// ReceiverIDGT applies the GT predicate on the "receiver_id" field.
func ReceiverIDGT(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGT(FieldReceiverID, v))
}

// ReceiverIDGTE applies the GTE predicate on the "receiver_id" field.
func ReceiverIDGTE(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldGTE(FieldReceiverID, v))
}

// ReceiverIDLT applies the LT predicate on the "receiver_id" field.
func ReceiverIDLT(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLT(FieldReceiverID, v))
}

// ReceiverIDLTE applies the LTE predicate on the "receiver_id" field.
func ReceiverIDLTE(v uint32) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldLTE(FieldReceiverID, v))
}

// ReceiverIDIsNil applies the IsNil predicate on the "receiver_id" field.
func ReceiverIDIsNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldIsNull(FieldReceiverID))
}

// ReceiverIDNotNil applies the NotNil predicate on the "receiver_id" field.
func ReceiverIDNotNil() predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.FieldNotNull(FieldReceiverID))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PrivateMessage) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PrivateMessage) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PrivateMessage) predicate.PrivateMessage {
	return predicate.PrivateMessage(sql.NotPredicates(p))
}
