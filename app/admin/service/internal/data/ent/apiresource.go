// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/apiresource"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// API资源表
type ApiResource struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 创建者ID
	CreateBy *uint32 `json:"create_by,omitempty"`
	// 更新者ID
	UpdateBy *uint32 `json:"update_by,omitempty"`
	// 描述
	Description *string `json:"description,omitempty"`
	// 所属业务模块
	Module *string `json:"module,omitempty"`
	// 业务模块描述
	ModuleDescription *string `json:"module_description,omitempty"`
	// 接口操作名
	Operation *string `json:"operation,omitempty"`
	// 接口路径
	Path *string `json:"path,omitempty"`
	// 请求方法
	Method       *string `json:"method,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ApiResource) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case apiresource.FieldID, apiresource.FieldCreateBy, apiresource.FieldUpdateBy:
			values[i] = new(sql.NullInt64)
		case apiresource.FieldDescription, apiresource.FieldModule, apiresource.FieldModuleDescription, apiresource.FieldOperation, apiresource.FieldPath, apiresource.FieldMethod:
			values[i] = new(sql.NullString)
		case apiresource.FieldCreateTime, apiresource.FieldUpdateTime, apiresource.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ApiResource fields.
func (ar *ApiResource) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case apiresource.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ar.ID = uint32(value.Int64)
		case apiresource.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				ar.CreateTime = new(time.Time)
				*ar.CreateTime = value.Time
			}
		case apiresource.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				ar.UpdateTime = new(time.Time)
				*ar.UpdateTime = value.Time
			}
		case apiresource.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				ar.DeleteTime = new(time.Time)
				*ar.DeleteTime = value.Time
			}
		case apiresource.FieldCreateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_by", values[i])
			} else if value.Valid {
				ar.CreateBy = new(uint32)
				*ar.CreateBy = uint32(value.Int64)
			}
		case apiresource.FieldUpdateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field update_by", values[i])
			} else if value.Valid {
				ar.UpdateBy = new(uint32)
				*ar.UpdateBy = uint32(value.Int64)
			}
		case apiresource.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				ar.Description = new(string)
				*ar.Description = value.String
			}
		case apiresource.FieldModule:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field module", values[i])
			} else if value.Valid {
				ar.Module = new(string)
				*ar.Module = value.String
			}
		case apiresource.FieldModuleDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field module_description", values[i])
			} else if value.Valid {
				ar.ModuleDescription = new(string)
				*ar.ModuleDescription = value.String
			}
		case apiresource.FieldOperation:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field operation", values[i])
			} else if value.Valid {
				ar.Operation = new(string)
				*ar.Operation = value.String
			}
		case apiresource.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				ar.Path = new(string)
				*ar.Path = value.String
			}
		case apiresource.FieldMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field method", values[i])
			} else if value.Valid {
				ar.Method = new(string)
				*ar.Method = value.String
			}
		default:
			ar.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ApiResource.
// This includes values selected through modifiers, order, etc.
func (ar *ApiResource) Value(name string) (ent.Value, error) {
	return ar.selectValues.Get(name)
}

// Update returns a builder for updating this ApiResource.
// Note that you need to call ApiResource.Unwrap() before calling this method if this ApiResource
// was returned from a transaction, and the transaction was committed or rolled back.
func (ar *ApiResource) Update() *ApiResourceUpdateOne {
	return NewApiResourceClient(ar.config).UpdateOne(ar)
}

// Unwrap unwraps the ApiResource entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ar *ApiResource) Unwrap() *ApiResource {
	_tx, ok := ar.config.driver.(*txDriver)
	if !ok {
		panic("ent: ApiResource is not a transactional entity")
	}
	ar.config.driver = _tx.drv
	return ar
}

// String implements the fmt.Stringer.
func (ar *ApiResource) String() string {
	var builder strings.Builder
	builder.WriteString("ApiResource(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ar.ID))
	if v := ar.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ar.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ar.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ar.CreateBy; v != nil {
		builder.WriteString("create_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := ar.UpdateBy; v != nil {
		builder.WriteString("update_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := ar.Description; v != nil {
		builder.WriteString("description=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ar.Module; v != nil {
		builder.WriteString("module=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ar.ModuleDescription; v != nil {
		builder.WriteString("module_description=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ar.Operation; v != nil {
		builder.WriteString("operation=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ar.Path; v != nil {
		builder.WriteString("path=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ar.Method; v != nil {
		builder.WriteString("method=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// ApiResources is a parsable slice of ApiResource.
type ApiResources []*ApiResource
