// Code generated by ent, DO NOT EDIT.

package adminloginlog

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldCreateTime, v))
}

// LoginIP applies equality check predicate on the "login_ip" field. It's identical to LoginIPEQ.
func LoginIP(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLoginIP, v))
}

// LoginMAC applies equality check predicate on the "login_mac" field. It's identical to LoginMACEQ.
func LoginMAC(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLoginMAC, v))
}

// LoginTime applies equality check predicate on the "login_time" field. It's identical to LoginTimeEQ.
func LoginTime(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLoginTime, v))
}

// UserAgent applies equality check predicate on the "user_agent" field. It's identical to UserAgentEQ.
func UserAgent(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldUserAgent, v))
}

// BrowserName applies equality check predicate on the "browser_name" field. It's identical to BrowserNameEQ.
func BrowserName(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldBrowserName, v))
}

// BrowserVersion applies equality check predicate on the "browser_version" field. It's identical to BrowserVersionEQ.
func BrowserVersion(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldBrowserVersion, v))
}

// ClientID applies equality check predicate on the "client_id" field. It's identical to ClientIDEQ.
func ClientID(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldClientID, v))
}

// ClientName applies equality check predicate on the "client_name" field. It's identical to ClientNameEQ.
func ClientName(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldClientName, v))
}

// OsName applies equality check predicate on the "os_name" field. It's identical to OsNameEQ.
func OsName(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldOsName, v))
}

// OsVersion applies equality check predicate on the "os_version" field. It's identical to OsVersionEQ.
func OsVersion(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldOsVersion, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldUserID, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldUsername, v))
}

// StatusCode applies equality check predicate on the "status_code" field. It's identical to StatusCodeEQ.
func StatusCode(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldStatusCode, v))
}

// Success applies equality check predicate on the "success" field. It's identical to SuccessEQ.
func Success(v bool) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldSuccess, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldReason, v))
}

// Location applies equality check predicate on the "location" field. It's identical to LocationEQ.
func Location(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLocation, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldCreateTime))
}

// LoginIPEQ applies the EQ predicate on the "login_ip" field.
func LoginIPEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLoginIP, v))
}

// LoginIPNEQ applies the NEQ predicate on the "login_ip" field.
func LoginIPNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldLoginIP, v))
}

// LoginIPIn applies the In predicate on the "login_ip" field.
func LoginIPIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldLoginIP, vs...))
}

// LoginIPNotIn applies the NotIn predicate on the "login_ip" field.
func LoginIPNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldLoginIP, vs...))
}

// LoginIPGT applies the GT predicate on the "login_ip" field.
func LoginIPGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldLoginIP, v))
}

// LoginIPGTE applies the GTE predicate on the "login_ip" field.
func LoginIPGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldLoginIP, v))
}

// LoginIPLT applies the LT predicate on the "login_ip" field.
func LoginIPLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldLoginIP, v))
}

// LoginIPLTE applies the LTE predicate on the "login_ip" field.
func LoginIPLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldLoginIP, v))
}

// LoginIPContains applies the Contains predicate on the "login_ip" field.
func LoginIPContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldLoginIP, v))
}

// LoginIPHasPrefix applies the HasPrefix predicate on the "login_ip" field.
func LoginIPHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldLoginIP, v))
}

// LoginIPHasSuffix applies the HasSuffix predicate on the "login_ip" field.
func LoginIPHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldLoginIP, v))
}

// LoginIPIsNil applies the IsNil predicate on the "login_ip" field.
func LoginIPIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldLoginIP))
}

// LoginIPNotNil applies the NotNil predicate on the "login_ip" field.
func LoginIPNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldLoginIP))
}

// LoginIPEqualFold applies the EqualFold predicate on the "login_ip" field.
func LoginIPEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldLoginIP, v))
}

// LoginIPContainsFold applies the ContainsFold predicate on the "login_ip" field.
func LoginIPContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldLoginIP, v))
}

// LoginMACEQ applies the EQ predicate on the "login_mac" field.
func LoginMACEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLoginMAC, v))
}

// LoginMACNEQ applies the NEQ predicate on the "login_mac" field.
func LoginMACNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldLoginMAC, v))
}

// LoginMACIn applies the In predicate on the "login_mac" field.
func LoginMACIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldLoginMAC, vs...))
}

// LoginMACNotIn applies the NotIn predicate on the "login_mac" field.
func LoginMACNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldLoginMAC, vs...))
}

// LoginMACGT applies the GT predicate on the "login_mac" field.
func LoginMACGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldLoginMAC, v))
}

// LoginMACGTE applies the GTE predicate on the "login_mac" field.
func LoginMACGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldLoginMAC, v))
}

// LoginMACLT applies the LT predicate on the "login_mac" field.
func LoginMACLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldLoginMAC, v))
}

// LoginMACLTE applies the LTE predicate on the "login_mac" field.
func LoginMACLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldLoginMAC, v))
}

// LoginMACContains applies the Contains predicate on the "login_mac" field.
func LoginMACContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldLoginMAC, v))
}

// LoginMACHasPrefix applies the HasPrefix predicate on the "login_mac" field.
func LoginMACHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldLoginMAC, v))
}

// LoginMACHasSuffix applies the HasSuffix predicate on the "login_mac" field.
func LoginMACHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldLoginMAC, v))
}

// LoginMACIsNil applies the IsNil predicate on the "login_mac" field.
func LoginMACIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldLoginMAC))
}

// LoginMACNotNil applies the NotNil predicate on the "login_mac" field.
func LoginMACNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldLoginMAC))
}

// LoginMACEqualFold applies the EqualFold predicate on the "login_mac" field.
func LoginMACEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldLoginMAC, v))
}

// LoginMACContainsFold applies the ContainsFold predicate on the "login_mac" field.
func LoginMACContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldLoginMAC, v))
}

// LoginTimeEQ applies the EQ predicate on the "login_time" field.
func LoginTimeEQ(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLoginTime, v))
}

// LoginTimeNEQ applies the NEQ predicate on the "login_time" field.
func LoginTimeNEQ(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldLoginTime, v))
}

// LoginTimeIn applies the In predicate on the "login_time" field.
func LoginTimeIn(vs ...time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldLoginTime, vs...))
}

// LoginTimeNotIn applies the NotIn predicate on the "login_time" field.
func LoginTimeNotIn(vs ...time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldLoginTime, vs...))
}

// LoginTimeGT applies the GT predicate on the "login_time" field.
func LoginTimeGT(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldLoginTime, v))
}

// LoginTimeGTE applies the GTE predicate on the "login_time" field.
func LoginTimeGTE(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldLoginTime, v))
}

// LoginTimeLT applies the LT predicate on the "login_time" field.
func LoginTimeLT(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldLoginTime, v))
}

// LoginTimeLTE applies the LTE predicate on the "login_time" field.
func LoginTimeLTE(v time.Time) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldLoginTime, v))
}

// LoginTimeIsNil applies the IsNil predicate on the "login_time" field.
func LoginTimeIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldLoginTime))
}

// LoginTimeNotNil applies the NotNil predicate on the "login_time" field.
func LoginTimeNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldLoginTime))
}

// UserAgentEQ applies the EQ predicate on the "user_agent" field.
func UserAgentEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldUserAgent, v))
}

// UserAgentNEQ applies the NEQ predicate on the "user_agent" field.
func UserAgentNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldUserAgent, v))
}

// UserAgentIn applies the In predicate on the "user_agent" field.
func UserAgentIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldUserAgent, vs...))
}

// UserAgentNotIn applies the NotIn predicate on the "user_agent" field.
func UserAgentNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldUserAgent, vs...))
}

// UserAgentGT applies the GT predicate on the "user_agent" field.
func UserAgentGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldUserAgent, v))
}

// UserAgentGTE applies the GTE predicate on the "user_agent" field.
func UserAgentGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldUserAgent, v))
}

// UserAgentLT applies the LT predicate on the "user_agent" field.
func UserAgentLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldUserAgent, v))
}

// UserAgentLTE applies the LTE predicate on the "user_agent" field.
func UserAgentLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldUserAgent, v))
}

// UserAgentContains applies the Contains predicate on the "user_agent" field.
func UserAgentContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldUserAgent, v))
}

// UserAgentHasPrefix applies the HasPrefix predicate on the "user_agent" field.
func UserAgentHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldUserAgent, v))
}

// UserAgentHasSuffix applies the HasSuffix predicate on the "user_agent" field.
func UserAgentHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldUserAgent, v))
}

// UserAgentIsNil applies the IsNil predicate on the "user_agent" field.
func UserAgentIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldUserAgent))
}

// UserAgentNotNil applies the NotNil predicate on the "user_agent" field.
func UserAgentNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldUserAgent))
}

// UserAgentEqualFold applies the EqualFold predicate on the "user_agent" field.
func UserAgentEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldUserAgent, v))
}

// UserAgentContainsFold applies the ContainsFold predicate on the "user_agent" field.
func UserAgentContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldUserAgent, v))
}

// BrowserNameEQ applies the EQ predicate on the "browser_name" field.
func BrowserNameEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldBrowserName, v))
}

// BrowserNameNEQ applies the NEQ predicate on the "browser_name" field.
func BrowserNameNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldBrowserName, v))
}

// BrowserNameIn applies the In predicate on the "browser_name" field.
func BrowserNameIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldBrowserName, vs...))
}

// BrowserNameNotIn applies the NotIn predicate on the "browser_name" field.
func BrowserNameNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldBrowserName, vs...))
}

// BrowserNameGT applies the GT predicate on the "browser_name" field.
func BrowserNameGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldBrowserName, v))
}

// BrowserNameGTE applies the GTE predicate on the "browser_name" field.
func BrowserNameGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldBrowserName, v))
}

// BrowserNameLT applies the LT predicate on the "browser_name" field.
func BrowserNameLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldBrowserName, v))
}

// BrowserNameLTE applies the LTE predicate on the "browser_name" field.
func BrowserNameLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldBrowserName, v))
}

// BrowserNameContains applies the Contains predicate on the "browser_name" field.
func BrowserNameContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldBrowserName, v))
}

// BrowserNameHasPrefix applies the HasPrefix predicate on the "browser_name" field.
func BrowserNameHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldBrowserName, v))
}

// BrowserNameHasSuffix applies the HasSuffix predicate on the "browser_name" field.
func BrowserNameHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldBrowserName, v))
}

// BrowserNameIsNil applies the IsNil predicate on the "browser_name" field.
func BrowserNameIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldBrowserName))
}

// BrowserNameNotNil applies the NotNil predicate on the "browser_name" field.
func BrowserNameNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldBrowserName))
}

// BrowserNameEqualFold applies the EqualFold predicate on the "browser_name" field.
func BrowserNameEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldBrowserName, v))
}

// BrowserNameContainsFold applies the ContainsFold predicate on the "browser_name" field.
func BrowserNameContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldBrowserName, v))
}

// BrowserVersionEQ applies the EQ predicate on the "browser_version" field.
func BrowserVersionEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldBrowserVersion, v))
}

// BrowserVersionNEQ applies the NEQ predicate on the "browser_version" field.
func BrowserVersionNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldBrowserVersion, v))
}

// BrowserVersionIn applies the In predicate on the "browser_version" field.
func BrowserVersionIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldBrowserVersion, vs...))
}

// BrowserVersionNotIn applies the NotIn predicate on the "browser_version" field.
func BrowserVersionNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldBrowserVersion, vs...))
}

// BrowserVersionGT applies the GT predicate on the "browser_version" field.
func BrowserVersionGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldBrowserVersion, v))
}

// BrowserVersionGTE applies the GTE predicate on the "browser_version" field.
func BrowserVersionGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldBrowserVersion, v))
}

// BrowserVersionLT applies the LT predicate on the "browser_version" field.
func BrowserVersionLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldBrowserVersion, v))
}

// BrowserVersionLTE applies the LTE predicate on the "browser_version" field.
func BrowserVersionLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldBrowserVersion, v))
}

// BrowserVersionContains applies the Contains predicate on the "browser_version" field.
func BrowserVersionContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldBrowserVersion, v))
}

// BrowserVersionHasPrefix applies the HasPrefix predicate on the "browser_version" field.
func BrowserVersionHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldBrowserVersion, v))
}

// BrowserVersionHasSuffix applies the HasSuffix predicate on the "browser_version" field.
func BrowserVersionHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldBrowserVersion, v))
}

// BrowserVersionIsNil applies the IsNil predicate on the "browser_version" field.
func BrowserVersionIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldBrowserVersion))
}

// BrowserVersionNotNil applies the NotNil predicate on the "browser_version" field.
func BrowserVersionNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldBrowserVersion))
}

// BrowserVersionEqualFold applies the EqualFold predicate on the "browser_version" field.
func BrowserVersionEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldBrowserVersion, v))
}

// BrowserVersionContainsFold applies the ContainsFold predicate on the "browser_version" field.
func BrowserVersionContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldBrowserVersion, v))
}

// ClientIDEQ applies the EQ predicate on the "client_id" field.
func ClientIDEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldClientID, v))
}

// ClientIDNEQ applies the NEQ predicate on the "client_id" field.
func ClientIDNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldClientID, v))
}

// ClientIDIn applies the In predicate on the "client_id" field.
func ClientIDIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldClientID, vs...))
}

// ClientIDNotIn applies the NotIn predicate on the "client_id" field.
func ClientIDNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldClientID, vs...))
}

// ClientIDGT applies the GT predicate on the "client_id" field.
func ClientIDGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldClientID, v))
}

// ClientIDGTE applies the GTE predicate on the "client_id" field.
func ClientIDGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldClientID, v))
}

// ClientIDLT applies the LT predicate on the "client_id" field.
func ClientIDLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldClientID, v))
}

// ClientIDLTE applies the LTE predicate on the "client_id" field.
func ClientIDLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldClientID, v))
}

// ClientIDContains applies the Contains predicate on the "client_id" field.
func ClientIDContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldClientID, v))
}

// ClientIDHasPrefix applies the HasPrefix predicate on the "client_id" field.
func ClientIDHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldClientID, v))
}

// ClientIDHasSuffix applies the HasSuffix predicate on the "client_id" field.
func ClientIDHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldClientID, v))
}

// ClientIDIsNil applies the IsNil predicate on the "client_id" field.
func ClientIDIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldClientID))
}

// ClientIDNotNil applies the NotNil predicate on the "client_id" field.
func ClientIDNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldClientID))
}

// ClientIDEqualFold applies the EqualFold predicate on the "client_id" field.
func ClientIDEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldClientID, v))
}

// ClientIDContainsFold applies the ContainsFold predicate on the "client_id" field.
func ClientIDContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldClientID, v))
}

// ClientNameEQ applies the EQ predicate on the "client_name" field.
func ClientNameEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldClientName, v))
}

// ClientNameNEQ applies the NEQ predicate on the "client_name" field.
func ClientNameNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldClientName, v))
}

// ClientNameIn applies the In predicate on the "client_name" field.
func ClientNameIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldClientName, vs...))
}

// ClientNameNotIn applies the NotIn predicate on the "client_name" field.
func ClientNameNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldClientName, vs...))
}

// ClientNameGT applies the GT predicate on the "client_name" field.
func ClientNameGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldClientName, v))
}

// ClientNameGTE applies the GTE predicate on the "client_name" field.
func ClientNameGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldClientName, v))
}

// ClientNameLT applies the LT predicate on the "client_name" field.
func ClientNameLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldClientName, v))
}

// ClientNameLTE applies the LTE predicate on the "client_name" field.
func ClientNameLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldClientName, v))
}

// ClientNameContains applies the Contains predicate on the "client_name" field.
func ClientNameContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldClientName, v))
}

// ClientNameHasPrefix applies the HasPrefix predicate on the "client_name" field.
func ClientNameHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldClientName, v))
}

// ClientNameHasSuffix applies the HasSuffix predicate on the "client_name" field.
func ClientNameHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldClientName, v))
}

// ClientNameIsNil applies the IsNil predicate on the "client_name" field.
func ClientNameIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldClientName))
}

// ClientNameNotNil applies the NotNil predicate on the "client_name" field.
func ClientNameNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldClientName))
}

// ClientNameEqualFold applies the EqualFold predicate on the "client_name" field.
func ClientNameEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldClientName, v))
}

// ClientNameContainsFold applies the ContainsFold predicate on the "client_name" field.
func ClientNameContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldClientName, v))
}

// OsNameEQ applies the EQ predicate on the "os_name" field.
func OsNameEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldOsName, v))
}

// OsNameNEQ applies the NEQ predicate on the "os_name" field.
func OsNameNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldOsName, v))
}

// OsNameIn applies the In predicate on the "os_name" field.
func OsNameIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldOsName, vs...))
}

// OsNameNotIn applies the NotIn predicate on the "os_name" field.
func OsNameNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldOsName, vs...))
}

// OsNameGT applies the GT predicate on the "os_name" field.
func OsNameGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldOsName, v))
}

// OsNameGTE applies the GTE predicate on the "os_name" field.
func OsNameGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldOsName, v))
}

// OsNameLT applies the LT predicate on the "os_name" field.
func OsNameLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldOsName, v))
}

// OsNameLTE applies the LTE predicate on the "os_name" field.
func OsNameLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldOsName, v))
}

// OsNameContains applies the Contains predicate on the "os_name" field.
func OsNameContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldOsName, v))
}

// OsNameHasPrefix applies the HasPrefix predicate on the "os_name" field.
func OsNameHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldOsName, v))
}

// OsNameHasSuffix applies the HasSuffix predicate on the "os_name" field.
func OsNameHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldOsName, v))
}

// OsNameIsNil applies the IsNil predicate on the "os_name" field.
func OsNameIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldOsName))
}

// OsNameNotNil applies the NotNil predicate on the "os_name" field.
func OsNameNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldOsName))
}

// OsNameEqualFold applies the EqualFold predicate on the "os_name" field.
func OsNameEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldOsName, v))
}

// OsNameContainsFold applies the ContainsFold predicate on the "os_name" field.
func OsNameContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldOsName, v))
}

// OsVersionEQ applies the EQ predicate on the "os_version" field.
func OsVersionEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldOsVersion, v))
}

// OsVersionNEQ applies the NEQ predicate on the "os_version" field.
func OsVersionNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldOsVersion, v))
}

// OsVersionIn applies the In predicate on the "os_version" field.
func OsVersionIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldOsVersion, vs...))
}

// OsVersionNotIn applies the NotIn predicate on the "os_version" field.
func OsVersionNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldOsVersion, vs...))
}

// OsVersionGT applies the GT predicate on the "os_version" field.
func OsVersionGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldOsVersion, v))
}

// OsVersionGTE applies the GTE predicate on the "os_version" field.
func OsVersionGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldOsVersion, v))
}

// OsVersionLT applies the LT predicate on the "os_version" field.
func OsVersionLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldOsVersion, v))
}

// OsVersionLTE applies the LTE predicate on the "os_version" field.
func OsVersionLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldOsVersion, v))
}

// OsVersionContains applies the Contains predicate on the "os_version" field.
func OsVersionContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldOsVersion, v))
}

// OsVersionHasPrefix applies the HasPrefix predicate on the "os_version" field.
func OsVersionHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldOsVersion, v))
}

// OsVersionHasSuffix applies the HasSuffix predicate on the "os_version" field.
func OsVersionHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldOsVersion, v))
}

// OsVersionIsNil applies the IsNil predicate on the "os_version" field.
func OsVersionIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldOsVersion))
}

// OsVersionNotNil applies the NotNil predicate on the "os_version" field.
func OsVersionNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldOsVersion))
}

// OsVersionEqualFold applies the EqualFold predicate on the "os_version" field.
func OsVersionEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldOsVersion, v))
}

// OsVersionContainsFold applies the ContainsFold predicate on the "os_version" field.
func OsVersionContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldOsVersion, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uint32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldUserID))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameIsNil applies the IsNil predicate on the "username" field.
func UsernameIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldUsername))
}

// UsernameNotNil applies the NotNil predicate on the "username" field.
func UsernameNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldUsername))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldUsername, v))
}

// StatusCodeEQ applies the EQ predicate on the "status_code" field.
func StatusCodeEQ(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldStatusCode, v))
}

// StatusCodeNEQ applies the NEQ predicate on the "status_code" field.
func StatusCodeNEQ(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldStatusCode, v))
}

// StatusCodeIn applies the In predicate on the "status_code" field.
func StatusCodeIn(vs ...int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldStatusCode, vs...))
}

// StatusCodeNotIn applies the NotIn predicate on the "status_code" field.
func StatusCodeNotIn(vs ...int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldStatusCode, vs...))
}

// StatusCodeGT applies the GT predicate on the "status_code" field.
func StatusCodeGT(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldStatusCode, v))
}

// StatusCodeGTE applies the GTE predicate on the "status_code" field.
func StatusCodeGTE(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldStatusCode, v))
}

// StatusCodeLT applies the LT predicate on the "status_code" field.
func StatusCodeLT(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldStatusCode, v))
}

// StatusCodeLTE applies the LTE predicate on the "status_code" field.
func StatusCodeLTE(v int32) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldStatusCode, v))
}

// StatusCodeIsNil applies the IsNil predicate on the "status_code" field.
func StatusCodeIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldStatusCode))
}

// StatusCodeNotNil applies the NotNil predicate on the "status_code" field.
func StatusCodeNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldStatusCode))
}

// SuccessEQ applies the EQ predicate on the "success" field.
func SuccessEQ(v bool) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldSuccess, v))
}

// SuccessNEQ applies the NEQ predicate on the "success" field.
func SuccessNEQ(v bool) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldSuccess, v))
}

// SuccessIsNil applies the IsNil predicate on the "success" field.
func SuccessIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldSuccess))
}

// SuccessNotNil applies the NotNil predicate on the "success" field.
func SuccessNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldSuccess))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldReason, v))
}

// LocationEQ applies the EQ predicate on the "location" field.
func LocationEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEQ(FieldLocation, v))
}

// LocationNEQ applies the NEQ predicate on the "location" field.
func LocationNEQ(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNEQ(FieldLocation, v))
}

// LocationIn applies the In predicate on the "location" field.
func LocationIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIn(FieldLocation, vs...))
}

// LocationNotIn applies the NotIn predicate on the "location" field.
func LocationNotIn(vs ...string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotIn(FieldLocation, vs...))
}

// LocationGT applies the GT predicate on the "location" field.
func LocationGT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGT(FieldLocation, v))
}

// LocationGTE applies the GTE predicate on the "location" field.
func LocationGTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldGTE(FieldLocation, v))
}

// LocationLT applies the LT predicate on the "location" field.
func LocationLT(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLT(FieldLocation, v))
}

// LocationLTE applies the LTE predicate on the "location" field.
func LocationLTE(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldLTE(FieldLocation, v))
}

// LocationContains applies the Contains predicate on the "location" field.
func LocationContains(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContains(FieldLocation, v))
}

// LocationHasPrefix applies the HasPrefix predicate on the "location" field.
func LocationHasPrefix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasPrefix(FieldLocation, v))
}

// LocationHasSuffix applies the HasSuffix predicate on the "location" field.
func LocationHasSuffix(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldHasSuffix(FieldLocation, v))
}

// LocationIsNil applies the IsNil predicate on the "location" field.
func LocationIsNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldIsNull(FieldLocation))
}

// LocationNotNil applies the NotNil predicate on the "location" field.
func LocationNotNil() predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldNotNull(FieldLocation))
}

// LocationEqualFold applies the EqualFold predicate on the "location" field.
func LocationEqualFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldEqualFold(FieldLocation, v))
}

// LocationContainsFold applies the ContainsFold predicate on the "location" field.
func LocationContainsFold(v string) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.FieldContainsFold(FieldLocation, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AdminLoginLog) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AdminLoginLog) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AdminLoginLog) predicate.AdminLoginLog {
	return predicate.AdminLoginLog(sql.NotPredicates(p))
}
