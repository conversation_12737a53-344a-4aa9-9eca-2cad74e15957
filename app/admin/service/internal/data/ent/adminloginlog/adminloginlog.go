// Code generated by ent, DO NOT EDIT.

package adminloginlog

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the adminloginlog type in the database.
	Label = "admin_login_log"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldLoginIP holds the string denoting the login_ip field in the database.
	FieldLoginIP = "login_ip"
	// FieldLoginMAC holds the string denoting the login_mac field in the database.
	FieldLoginMAC = "login_mac"
	// FieldLoginTime holds the string denoting the login_time field in the database.
	FieldLoginTime = "login_time"
	// FieldUserAgent holds the string denoting the user_agent field in the database.
	FieldUserAgent = "user_agent"
	// FieldBrowserName holds the string denoting the browser_name field in the database.
	FieldBrowserName = "browser_name"
	// FieldBrowserVersion holds the string denoting the browser_version field in the database.
	FieldBrowserVersion = "browser_version"
	// FieldClientID holds the string denoting the client_id field in the database.
	FieldClientID = "client_id"
	// FieldClientName holds the string denoting the client_name field in the database.
	FieldClientName = "client_name"
	// FieldOsName holds the string denoting the os_name field in the database.
	FieldOsName = "os_name"
	// FieldOsVersion holds the string denoting the os_version field in the database.
	FieldOsVersion = "os_version"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldStatusCode holds the string denoting the status_code field in the database.
	FieldStatusCode = "status_code"
	// FieldSuccess holds the string denoting the success field in the database.
	FieldSuccess = "success"
	// FieldReason holds the string denoting the reason field in the database.
	FieldReason = "reason"
	// FieldLocation holds the string denoting the location field in the database.
	FieldLocation = "location"
	// Table holds the table name of the adminloginlog in the database.
	Table = "admin_login_logs"
)

// Columns holds all SQL columns for adminloginlog fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldLoginIP,
	FieldLoginMAC,
	FieldLoginTime,
	FieldUserAgent,
	FieldBrowserName,
	FieldBrowserVersion,
	FieldClientID,
	FieldClientName,
	FieldOsName,
	FieldOsVersion,
	FieldUserID,
	FieldUsername,
	FieldStatusCode,
	FieldSuccess,
	FieldReason,
	FieldLocation,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(uint32) error
)

// OrderOption defines the ordering options for the AdminLoginLog queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByLoginIP orders the results by the login_ip field.
func ByLoginIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLoginIP, opts...).ToFunc()
}

// ByLoginMAC orders the results by the login_mac field.
func ByLoginMAC(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLoginMAC, opts...).ToFunc()
}

// ByLoginTime orders the results by the login_time field.
func ByLoginTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLoginTime, opts...).ToFunc()
}

// ByUserAgent orders the results by the user_agent field.
func ByUserAgent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserAgent, opts...).ToFunc()
}

// ByBrowserName orders the results by the browser_name field.
func ByBrowserName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBrowserName, opts...).ToFunc()
}

// ByBrowserVersion orders the results by the browser_version field.
func ByBrowserVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBrowserVersion, opts...).ToFunc()
}

// ByClientID orders the results by the client_id field.
func ByClientID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientID, opts...).ToFunc()
}

// ByClientName orders the results by the client_name field.
func ByClientName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientName, opts...).ToFunc()
}

// ByOsName orders the results by the os_name field.
func ByOsName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOsName, opts...).ToFunc()
}

// ByOsVersion orders the results by the os_version field.
func ByOsVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOsVersion, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByStatusCode orders the results by the status_code field.
func ByStatusCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatusCode, opts...).ToFunc()
}

// BySuccess orders the results by the success field.
func BySuccess(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSuccess, opts...).ToFunc()
}

// ByReason orders the results by the reason field.
func ByReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReason, opts...).ToFunc()
}

// ByLocation orders the results by the location field.
func ByLocation(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLocation, opts...).ToFunc()
}
