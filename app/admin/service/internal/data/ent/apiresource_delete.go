// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-admin/app/admin/service/internal/data/ent/apiresource"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ApiResourceDelete is the builder for deleting a ApiResource entity.
type ApiResourceDelete struct {
	config
	hooks    []Hook
	mutation *ApiResourceMutation
}

// Where appends a list predicates to the ApiResourceDelete builder.
func (ard *ApiResourceDelete) Where(ps ...predicate.ApiResource) *ApiResourceDelete {
	ard.mutation.Where(ps...)
	return ard
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ard *ApiResourceDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ard.sqlExec, ard.mutation, ard.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ard *ApiResourceDelete) ExecX(ctx context.Context) int {
	n, err := ard.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ard *ApiResourceDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(apiresource.Table, sqlgraph.NewFieldSpec(apiresource.FieldID, field.TypeUint32))
	if ps := ard.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ard.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ard.mutation.done = true
	return affected, err
}

// ApiResourceDeleteOne is the builder for deleting a single ApiResource entity.
type ApiResourceDeleteOne struct {
	ard *ApiResourceDelete
}

// Where appends a list predicates to the ApiResourceDelete builder.
func (ardo *ApiResourceDeleteOne) Where(ps ...predicate.ApiResource) *ApiResourceDeleteOne {
	ardo.ard.mutation.Where(ps...)
	return ardo
}

// Exec executes the deletion query.
func (ardo *ApiResourceDeleteOne) Exec(ctx context.Context) error {
	n, err := ardo.ard.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{apiresource.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ardo *ApiResourceDeleteOne) ExecX(ctx context.Context) {
	if err := ardo.Exec(ctx); err != nil {
		panic(err)
	}
}
