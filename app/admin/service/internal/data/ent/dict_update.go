// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/dict"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DictUpdate is the builder for updating Dict entities.
type DictUpdate struct {
	config
	hooks     []Hook
	mutation  *DictMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DictUpdate builder.
func (du *DictUpdate) Where(ps ...predicate.Dict) *DictUpdate {
	du.mutation.Where(ps...)
	return du
}

// SetUpdateTime sets the "update_time" field.
func (du *DictUpdate) SetUpdateTime(t time.Time) *DictUpdate {
	du.mutation.SetUpdateTime(t)
	return du
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (du *DictUpdate) SetNillableUpdateTime(t *time.Time) *DictUpdate {
	if t != nil {
		du.SetUpdateTime(*t)
	}
	return du
}

// ClearUpdateTime clears the value of the "update_time" field.
func (du *DictUpdate) ClearUpdateTime() *DictUpdate {
	du.mutation.ClearUpdateTime()
	return du
}

// SetDeleteTime sets the "delete_time" field.
func (du *DictUpdate) SetDeleteTime(t time.Time) *DictUpdate {
	du.mutation.SetDeleteTime(t)
	return du
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (du *DictUpdate) SetNillableDeleteTime(t *time.Time) *DictUpdate {
	if t != nil {
		du.SetDeleteTime(*t)
	}
	return du
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (du *DictUpdate) ClearDeleteTime() *DictUpdate {
	du.mutation.ClearDeleteTime()
	return du
}

// SetStatus sets the "status" field.
func (du *DictUpdate) SetStatus(d dict.Status) *DictUpdate {
	du.mutation.SetStatus(d)
	return du
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (du *DictUpdate) SetNillableStatus(d *dict.Status) *DictUpdate {
	if d != nil {
		du.SetStatus(*d)
	}
	return du
}

// ClearStatus clears the value of the "status" field.
func (du *DictUpdate) ClearStatus() *DictUpdate {
	du.mutation.ClearStatus()
	return du
}

// SetCreateBy sets the "create_by" field.
func (du *DictUpdate) SetCreateBy(u uint32) *DictUpdate {
	du.mutation.ResetCreateBy()
	du.mutation.SetCreateBy(u)
	return du
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (du *DictUpdate) SetNillableCreateBy(u *uint32) *DictUpdate {
	if u != nil {
		du.SetCreateBy(*u)
	}
	return du
}

// AddCreateBy adds u to the "create_by" field.
func (du *DictUpdate) AddCreateBy(u int32) *DictUpdate {
	du.mutation.AddCreateBy(u)
	return du
}

// ClearCreateBy clears the value of the "create_by" field.
func (du *DictUpdate) ClearCreateBy() *DictUpdate {
	du.mutation.ClearCreateBy()
	return du
}

// SetUpdateBy sets the "update_by" field.
func (du *DictUpdate) SetUpdateBy(u uint32) *DictUpdate {
	du.mutation.ResetUpdateBy()
	du.mutation.SetUpdateBy(u)
	return du
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (du *DictUpdate) SetNillableUpdateBy(u *uint32) *DictUpdate {
	if u != nil {
		du.SetUpdateBy(*u)
	}
	return du
}

// AddUpdateBy adds u to the "update_by" field.
func (du *DictUpdate) AddUpdateBy(u int32) *DictUpdate {
	du.mutation.AddUpdateBy(u)
	return du
}

// ClearUpdateBy clears the value of the "update_by" field.
func (du *DictUpdate) ClearUpdateBy() *DictUpdate {
	du.mutation.ClearUpdateBy()
	return du
}

// SetRemark sets the "remark" field.
func (du *DictUpdate) SetRemark(s string) *DictUpdate {
	du.mutation.SetRemark(s)
	return du
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (du *DictUpdate) SetNillableRemark(s *string) *DictUpdate {
	if s != nil {
		du.SetRemark(*s)
	}
	return du
}

// ClearRemark clears the value of the "remark" field.
func (du *DictUpdate) ClearRemark() *DictUpdate {
	du.mutation.ClearRemark()
	return du
}

// SetKey sets the "key" field.
func (du *DictUpdate) SetKey(s string) *DictUpdate {
	du.mutation.SetKey(s)
	return du
}

// SetNillableKey sets the "key" field if the given value is not nil.
func (du *DictUpdate) SetNillableKey(s *string) *DictUpdate {
	if s != nil {
		du.SetKey(*s)
	}
	return du
}

// ClearKey clears the value of the "key" field.
func (du *DictUpdate) ClearKey() *DictUpdate {
	du.mutation.ClearKey()
	return du
}

// SetCategory sets the "category" field.
func (du *DictUpdate) SetCategory(s string) *DictUpdate {
	du.mutation.SetCategory(s)
	return du
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (du *DictUpdate) SetNillableCategory(s *string) *DictUpdate {
	if s != nil {
		du.SetCategory(*s)
	}
	return du
}

// ClearCategory clears the value of the "category" field.
func (du *DictUpdate) ClearCategory() *DictUpdate {
	du.mutation.ClearCategory()
	return du
}

// SetCategoryDesc sets the "category_desc" field.
func (du *DictUpdate) SetCategoryDesc(s string) *DictUpdate {
	du.mutation.SetCategoryDesc(s)
	return du
}

// SetNillableCategoryDesc sets the "category_desc" field if the given value is not nil.
func (du *DictUpdate) SetNillableCategoryDesc(s *string) *DictUpdate {
	if s != nil {
		du.SetCategoryDesc(*s)
	}
	return du
}

// ClearCategoryDesc clears the value of the "category_desc" field.
func (du *DictUpdate) ClearCategoryDesc() *DictUpdate {
	du.mutation.ClearCategoryDesc()
	return du
}

// SetValue sets the "value" field.
func (du *DictUpdate) SetValue(s string) *DictUpdate {
	du.mutation.SetValue(s)
	return du
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (du *DictUpdate) SetNillableValue(s *string) *DictUpdate {
	if s != nil {
		du.SetValue(*s)
	}
	return du
}

// ClearValue clears the value of the "value" field.
func (du *DictUpdate) ClearValue() *DictUpdate {
	du.mutation.ClearValue()
	return du
}

// SetValueDesc sets the "value_desc" field.
func (du *DictUpdate) SetValueDesc(s string) *DictUpdate {
	du.mutation.SetValueDesc(s)
	return du
}

// SetNillableValueDesc sets the "value_desc" field if the given value is not nil.
func (du *DictUpdate) SetNillableValueDesc(s *string) *DictUpdate {
	if s != nil {
		du.SetValueDesc(*s)
	}
	return du
}

// ClearValueDesc clears the value of the "value_desc" field.
func (du *DictUpdate) ClearValueDesc() *DictUpdate {
	du.mutation.ClearValueDesc()
	return du
}

// SetValueDataType sets the "value_data_type" field.
func (du *DictUpdate) SetValueDataType(s string) *DictUpdate {
	du.mutation.SetValueDataType(s)
	return du
}

// SetNillableValueDataType sets the "value_data_type" field if the given value is not nil.
func (du *DictUpdate) SetNillableValueDataType(s *string) *DictUpdate {
	if s != nil {
		du.SetValueDataType(*s)
	}
	return du
}

// ClearValueDataType clears the value of the "value_data_type" field.
func (du *DictUpdate) ClearValueDataType() *DictUpdate {
	du.mutation.ClearValueDataType()
	return du
}

// SetSortID sets the "sort_id" field.
func (du *DictUpdate) SetSortID(i int32) *DictUpdate {
	du.mutation.ResetSortID()
	du.mutation.SetSortID(i)
	return du
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (du *DictUpdate) SetNillableSortID(i *int32) *DictUpdate {
	if i != nil {
		du.SetSortID(*i)
	}
	return du
}

// AddSortID adds i to the "sort_id" field.
func (du *DictUpdate) AddSortID(i int32) *DictUpdate {
	du.mutation.AddSortID(i)
	return du
}

// ClearSortID clears the value of the "sort_id" field.
func (du *DictUpdate) ClearSortID() *DictUpdate {
	du.mutation.ClearSortID()
	return du
}

// Mutation returns the DictMutation object of the builder.
func (du *DictUpdate) Mutation() *DictMutation {
	return du.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (du *DictUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, du.sqlSave, du.mutation, du.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (du *DictUpdate) SaveX(ctx context.Context) int {
	affected, err := du.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (du *DictUpdate) Exec(ctx context.Context) error {
	_, err := du.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (du *DictUpdate) ExecX(ctx context.Context) {
	if err := du.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (du *DictUpdate) check() error {
	if v, ok := du.mutation.Status(); ok {
		if err := dict.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Dict.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (du *DictUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DictUpdate {
	du.modifiers = append(du.modifiers, modifiers...)
	return du
}

func (du *DictUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := du.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(dict.Table, dict.Columns, sqlgraph.NewFieldSpec(dict.FieldID, field.TypeUint32))
	if ps := du.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if du.mutation.CreateTimeCleared() {
		_spec.ClearField(dict.FieldCreateTime, field.TypeTime)
	}
	if value, ok := du.mutation.UpdateTime(); ok {
		_spec.SetField(dict.FieldUpdateTime, field.TypeTime, value)
	}
	if du.mutation.UpdateTimeCleared() {
		_spec.ClearField(dict.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := du.mutation.DeleteTime(); ok {
		_spec.SetField(dict.FieldDeleteTime, field.TypeTime, value)
	}
	if du.mutation.DeleteTimeCleared() {
		_spec.ClearField(dict.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := du.mutation.Status(); ok {
		_spec.SetField(dict.FieldStatus, field.TypeEnum, value)
	}
	if du.mutation.StatusCleared() {
		_spec.ClearField(dict.FieldStatus, field.TypeEnum)
	}
	if value, ok := du.mutation.CreateBy(); ok {
		_spec.SetField(dict.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := du.mutation.AddedCreateBy(); ok {
		_spec.AddField(dict.FieldCreateBy, field.TypeUint32, value)
	}
	if du.mutation.CreateByCleared() {
		_spec.ClearField(dict.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := du.mutation.UpdateBy(); ok {
		_spec.SetField(dict.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := du.mutation.AddedUpdateBy(); ok {
		_spec.AddField(dict.FieldUpdateBy, field.TypeUint32, value)
	}
	if du.mutation.UpdateByCleared() {
		_spec.ClearField(dict.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := du.mutation.Remark(); ok {
		_spec.SetField(dict.FieldRemark, field.TypeString, value)
	}
	if du.mutation.RemarkCleared() {
		_spec.ClearField(dict.FieldRemark, field.TypeString)
	}
	if du.mutation.TenantIDCleared() {
		_spec.ClearField(dict.FieldTenantID, field.TypeUint32)
	}
	if value, ok := du.mutation.Key(); ok {
		_spec.SetField(dict.FieldKey, field.TypeString, value)
	}
	if du.mutation.KeyCleared() {
		_spec.ClearField(dict.FieldKey, field.TypeString)
	}
	if value, ok := du.mutation.Category(); ok {
		_spec.SetField(dict.FieldCategory, field.TypeString, value)
	}
	if du.mutation.CategoryCleared() {
		_spec.ClearField(dict.FieldCategory, field.TypeString)
	}
	if value, ok := du.mutation.CategoryDesc(); ok {
		_spec.SetField(dict.FieldCategoryDesc, field.TypeString, value)
	}
	if du.mutation.CategoryDescCleared() {
		_spec.ClearField(dict.FieldCategoryDesc, field.TypeString)
	}
	if value, ok := du.mutation.Value(); ok {
		_spec.SetField(dict.FieldValue, field.TypeString, value)
	}
	if du.mutation.ValueCleared() {
		_spec.ClearField(dict.FieldValue, field.TypeString)
	}
	if value, ok := du.mutation.ValueDesc(); ok {
		_spec.SetField(dict.FieldValueDesc, field.TypeString, value)
	}
	if du.mutation.ValueDescCleared() {
		_spec.ClearField(dict.FieldValueDesc, field.TypeString)
	}
	if value, ok := du.mutation.ValueDataType(); ok {
		_spec.SetField(dict.FieldValueDataType, field.TypeString, value)
	}
	if du.mutation.ValueDataTypeCleared() {
		_spec.ClearField(dict.FieldValueDataType, field.TypeString)
	}
	if value, ok := du.mutation.SortID(); ok {
		_spec.SetField(dict.FieldSortID, field.TypeInt32, value)
	}
	if value, ok := du.mutation.AddedSortID(); ok {
		_spec.AddField(dict.FieldSortID, field.TypeInt32, value)
	}
	if du.mutation.SortIDCleared() {
		_spec.ClearField(dict.FieldSortID, field.TypeInt32)
	}
	_spec.AddModifiers(du.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, du.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{dict.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	du.mutation.done = true
	return n, nil
}

// DictUpdateOne is the builder for updating a single Dict entity.
type DictUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DictMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (duo *DictUpdateOne) SetUpdateTime(t time.Time) *DictUpdateOne {
	duo.mutation.SetUpdateTime(t)
	return duo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableUpdateTime(t *time.Time) *DictUpdateOne {
	if t != nil {
		duo.SetUpdateTime(*t)
	}
	return duo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (duo *DictUpdateOne) ClearUpdateTime() *DictUpdateOne {
	duo.mutation.ClearUpdateTime()
	return duo
}

// SetDeleteTime sets the "delete_time" field.
func (duo *DictUpdateOne) SetDeleteTime(t time.Time) *DictUpdateOne {
	duo.mutation.SetDeleteTime(t)
	return duo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableDeleteTime(t *time.Time) *DictUpdateOne {
	if t != nil {
		duo.SetDeleteTime(*t)
	}
	return duo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (duo *DictUpdateOne) ClearDeleteTime() *DictUpdateOne {
	duo.mutation.ClearDeleteTime()
	return duo
}

// SetStatus sets the "status" field.
func (duo *DictUpdateOne) SetStatus(d dict.Status) *DictUpdateOne {
	duo.mutation.SetStatus(d)
	return duo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableStatus(d *dict.Status) *DictUpdateOne {
	if d != nil {
		duo.SetStatus(*d)
	}
	return duo
}

// ClearStatus clears the value of the "status" field.
func (duo *DictUpdateOne) ClearStatus() *DictUpdateOne {
	duo.mutation.ClearStatus()
	return duo
}

// SetCreateBy sets the "create_by" field.
func (duo *DictUpdateOne) SetCreateBy(u uint32) *DictUpdateOne {
	duo.mutation.ResetCreateBy()
	duo.mutation.SetCreateBy(u)
	return duo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableCreateBy(u *uint32) *DictUpdateOne {
	if u != nil {
		duo.SetCreateBy(*u)
	}
	return duo
}

// AddCreateBy adds u to the "create_by" field.
func (duo *DictUpdateOne) AddCreateBy(u int32) *DictUpdateOne {
	duo.mutation.AddCreateBy(u)
	return duo
}

// ClearCreateBy clears the value of the "create_by" field.
func (duo *DictUpdateOne) ClearCreateBy() *DictUpdateOne {
	duo.mutation.ClearCreateBy()
	return duo
}

// SetUpdateBy sets the "update_by" field.
func (duo *DictUpdateOne) SetUpdateBy(u uint32) *DictUpdateOne {
	duo.mutation.ResetUpdateBy()
	duo.mutation.SetUpdateBy(u)
	return duo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableUpdateBy(u *uint32) *DictUpdateOne {
	if u != nil {
		duo.SetUpdateBy(*u)
	}
	return duo
}

// AddUpdateBy adds u to the "update_by" field.
func (duo *DictUpdateOne) AddUpdateBy(u int32) *DictUpdateOne {
	duo.mutation.AddUpdateBy(u)
	return duo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (duo *DictUpdateOne) ClearUpdateBy() *DictUpdateOne {
	duo.mutation.ClearUpdateBy()
	return duo
}

// SetRemark sets the "remark" field.
func (duo *DictUpdateOne) SetRemark(s string) *DictUpdateOne {
	duo.mutation.SetRemark(s)
	return duo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableRemark(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetRemark(*s)
	}
	return duo
}

// ClearRemark clears the value of the "remark" field.
func (duo *DictUpdateOne) ClearRemark() *DictUpdateOne {
	duo.mutation.ClearRemark()
	return duo
}

// SetKey sets the "key" field.
func (duo *DictUpdateOne) SetKey(s string) *DictUpdateOne {
	duo.mutation.SetKey(s)
	return duo
}

// SetNillableKey sets the "key" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableKey(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetKey(*s)
	}
	return duo
}

// ClearKey clears the value of the "key" field.
func (duo *DictUpdateOne) ClearKey() *DictUpdateOne {
	duo.mutation.ClearKey()
	return duo
}

// SetCategory sets the "category" field.
func (duo *DictUpdateOne) SetCategory(s string) *DictUpdateOne {
	duo.mutation.SetCategory(s)
	return duo
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableCategory(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetCategory(*s)
	}
	return duo
}

// ClearCategory clears the value of the "category" field.
func (duo *DictUpdateOne) ClearCategory() *DictUpdateOne {
	duo.mutation.ClearCategory()
	return duo
}

// SetCategoryDesc sets the "category_desc" field.
func (duo *DictUpdateOne) SetCategoryDesc(s string) *DictUpdateOne {
	duo.mutation.SetCategoryDesc(s)
	return duo
}

// SetNillableCategoryDesc sets the "category_desc" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableCategoryDesc(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetCategoryDesc(*s)
	}
	return duo
}

// ClearCategoryDesc clears the value of the "category_desc" field.
func (duo *DictUpdateOne) ClearCategoryDesc() *DictUpdateOne {
	duo.mutation.ClearCategoryDesc()
	return duo
}

// SetValue sets the "value" field.
func (duo *DictUpdateOne) SetValue(s string) *DictUpdateOne {
	duo.mutation.SetValue(s)
	return duo
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableValue(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetValue(*s)
	}
	return duo
}

// ClearValue clears the value of the "value" field.
func (duo *DictUpdateOne) ClearValue() *DictUpdateOne {
	duo.mutation.ClearValue()
	return duo
}

// SetValueDesc sets the "value_desc" field.
func (duo *DictUpdateOne) SetValueDesc(s string) *DictUpdateOne {
	duo.mutation.SetValueDesc(s)
	return duo
}

// SetNillableValueDesc sets the "value_desc" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableValueDesc(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetValueDesc(*s)
	}
	return duo
}

// ClearValueDesc clears the value of the "value_desc" field.
func (duo *DictUpdateOne) ClearValueDesc() *DictUpdateOne {
	duo.mutation.ClearValueDesc()
	return duo
}

// SetValueDataType sets the "value_data_type" field.
func (duo *DictUpdateOne) SetValueDataType(s string) *DictUpdateOne {
	duo.mutation.SetValueDataType(s)
	return duo
}

// SetNillableValueDataType sets the "value_data_type" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableValueDataType(s *string) *DictUpdateOne {
	if s != nil {
		duo.SetValueDataType(*s)
	}
	return duo
}

// ClearValueDataType clears the value of the "value_data_type" field.
func (duo *DictUpdateOne) ClearValueDataType() *DictUpdateOne {
	duo.mutation.ClearValueDataType()
	return duo
}

// SetSortID sets the "sort_id" field.
func (duo *DictUpdateOne) SetSortID(i int32) *DictUpdateOne {
	duo.mutation.ResetSortID()
	duo.mutation.SetSortID(i)
	return duo
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (duo *DictUpdateOne) SetNillableSortID(i *int32) *DictUpdateOne {
	if i != nil {
		duo.SetSortID(*i)
	}
	return duo
}

// AddSortID adds i to the "sort_id" field.
func (duo *DictUpdateOne) AddSortID(i int32) *DictUpdateOne {
	duo.mutation.AddSortID(i)
	return duo
}

// ClearSortID clears the value of the "sort_id" field.
func (duo *DictUpdateOne) ClearSortID() *DictUpdateOne {
	duo.mutation.ClearSortID()
	return duo
}

// Mutation returns the DictMutation object of the builder.
func (duo *DictUpdateOne) Mutation() *DictMutation {
	return duo.mutation
}

// Where appends a list predicates to the DictUpdate builder.
func (duo *DictUpdateOne) Where(ps ...predicate.Dict) *DictUpdateOne {
	duo.mutation.Where(ps...)
	return duo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duo *DictUpdateOne) Select(field string, fields ...string) *DictUpdateOne {
	duo.fields = append([]string{field}, fields...)
	return duo
}

// Save executes the query and returns the updated Dict entity.
func (duo *DictUpdateOne) Save(ctx context.Context) (*Dict, error) {
	return withHooks(ctx, duo.sqlSave, duo.mutation, duo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duo *DictUpdateOne) SaveX(ctx context.Context) *Dict {
	node, err := duo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duo *DictUpdateOne) Exec(ctx context.Context) error {
	_, err := duo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duo *DictUpdateOne) ExecX(ctx context.Context) {
	if err := duo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duo *DictUpdateOne) check() error {
	if v, ok := duo.mutation.Status(); ok {
		if err := dict.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Dict.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (duo *DictUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DictUpdateOne {
	duo.modifiers = append(duo.modifiers, modifiers...)
	return duo
}

func (duo *DictUpdateOne) sqlSave(ctx context.Context) (_node *Dict, err error) {
	if err := duo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(dict.Table, dict.Columns, sqlgraph.NewFieldSpec(dict.FieldID, field.TypeUint32))
	id, ok := duo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Dict.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, dict.FieldID)
		for _, f := range fields {
			if !dict.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != dict.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if duo.mutation.CreateTimeCleared() {
		_spec.ClearField(dict.FieldCreateTime, field.TypeTime)
	}
	if value, ok := duo.mutation.UpdateTime(); ok {
		_spec.SetField(dict.FieldUpdateTime, field.TypeTime, value)
	}
	if duo.mutation.UpdateTimeCleared() {
		_spec.ClearField(dict.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := duo.mutation.DeleteTime(); ok {
		_spec.SetField(dict.FieldDeleteTime, field.TypeTime, value)
	}
	if duo.mutation.DeleteTimeCleared() {
		_spec.ClearField(dict.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := duo.mutation.Status(); ok {
		_spec.SetField(dict.FieldStatus, field.TypeEnum, value)
	}
	if duo.mutation.StatusCleared() {
		_spec.ClearField(dict.FieldStatus, field.TypeEnum)
	}
	if value, ok := duo.mutation.CreateBy(); ok {
		_spec.SetField(dict.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := duo.mutation.AddedCreateBy(); ok {
		_spec.AddField(dict.FieldCreateBy, field.TypeUint32, value)
	}
	if duo.mutation.CreateByCleared() {
		_spec.ClearField(dict.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := duo.mutation.UpdateBy(); ok {
		_spec.SetField(dict.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := duo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(dict.FieldUpdateBy, field.TypeUint32, value)
	}
	if duo.mutation.UpdateByCleared() {
		_spec.ClearField(dict.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := duo.mutation.Remark(); ok {
		_spec.SetField(dict.FieldRemark, field.TypeString, value)
	}
	if duo.mutation.RemarkCleared() {
		_spec.ClearField(dict.FieldRemark, field.TypeString)
	}
	if duo.mutation.TenantIDCleared() {
		_spec.ClearField(dict.FieldTenantID, field.TypeUint32)
	}
	if value, ok := duo.mutation.Key(); ok {
		_spec.SetField(dict.FieldKey, field.TypeString, value)
	}
	if duo.mutation.KeyCleared() {
		_spec.ClearField(dict.FieldKey, field.TypeString)
	}
	if value, ok := duo.mutation.Category(); ok {
		_spec.SetField(dict.FieldCategory, field.TypeString, value)
	}
	if duo.mutation.CategoryCleared() {
		_spec.ClearField(dict.FieldCategory, field.TypeString)
	}
	if value, ok := duo.mutation.CategoryDesc(); ok {
		_spec.SetField(dict.FieldCategoryDesc, field.TypeString, value)
	}
	if duo.mutation.CategoryDescCleared() {
		_spec.ClearField(dict.FieldCategoryDesc, field.TypeString)
	}
	if value, ok := duo.mutation.Value(); ok {
		_spec.SetField(dict.FieldValue, field.TypeString, value)
	}
	if duo.mutation.ValueCleared() {
		_spec.ClearField(dict.FieldValue, field.TypeString)
	}
	if value, ok := duo.mutation.ValueDesc(); ok {
		_spec.SetField(dict.FieldValueDesc, field.TypeString, value)
	}
	if duo.mutation.ValueDescCleared() {
		_spec.ClearField(dict.FieldValueDesc, field.TypeString)
	}
	if value, ok := duo.mutation.ValueDataType(); ok {
		_spec.SetField(dict.FieldValueDataType, field.TypeString, value)
	}
	if duo.mutation.ValueDataTypeCleared() {
		_spec.ClearField(dict.FieldValueDataType, field.TypeString)
	}
	if value, ok := duo.mutation.SortID(); ok {
		_spec.SetField(dict.FieldSortID, field.TypeInt32, value)
	}
	if value, ok := duo.mutation.AddedSortID(); ok {
		_spec.AddField(dict.FieldSortID, field.TypeInt32, value)
	}
	if duo.mutation.SortIDCleared() {
		_spec.ClearField(dict.FieldSortID, field.TypeInt32)
	}
	_spec.AddModifiers(duo.modifiers...)
	_node = &Dict{config: duo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{dict.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duo.mutation.done = true
	return _node, nil
}
