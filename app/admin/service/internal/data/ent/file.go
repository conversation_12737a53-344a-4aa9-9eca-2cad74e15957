// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/file"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 文件表
type File struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 创建者ID
	CreateBy *uint32 `json:"create_by,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 租户ID
	TenantID *uint32 `json:"tenant_id,omitempty"`
	// OSS供应商
	Provider *file.Provider `json:"provider,omitempty"`
	// 存储桶名称
	BucketName *string `json:"bucket_name,omitempty"`
	// 文件目录
	FileDirectory *string `json:"file_directory,omitempty"`
	// 文件Guid
	FileGUID *string `json:"file_guid,omitempty"`
	// 保存文件名
	SaveFileName *string `json:"save_file_name,omitempty"`
	// 文件名
	FileName *string `json:"file_name,omitempty"`
	// 文件扩展名
	Extension *string `json:"extension,omitempty"`
	// 文件字节长度
	Size *uint64 `json:"size,omitempty"`
	// 文件大小格式化
	SizeFormat *string `json:"size_format,omitempty"`
	// 链接地址
	LinkURL *string `json:"link_url,omitempty"`
	// md5码，防止上传重复文件
	Md5          *string `json:"md5,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*File) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case file.FieldID, file.FieldCreateBy, file.FieldTenantID, file.FieldSize:
			values[i] = new(sql.NullInt64)
		case file.FieldRemark, file.FieldProvider, file.FieldBucketName, file.FieldFileDirectory, file.FieldFileGUID, file.FieldSaveFileName, file.FieldFileName, file.FieldExtension, file.FieldSizeFormat, file.FieldLinkURL, file.FieldMd5:
			values[i] = new(sql.NullString)
		case file.FieldCreateTime, file.FieldUpdateTime, file.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the File fields.
func (f *File) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case file.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			f.ID = uint32(value.Int64)
		case file.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				f.CreateTime = new(time.Time)
				*f.CreateTime = value.Time
			}
		case file.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				f.UpdateTime = new(time.Time)
				*f.UpdateTime = value.Time
			}
		case file.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				f.DeleteTime = new(time.Time)
				*f.DeleteTime = value.Time
			}
		case file.FieldCreateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_by", values[i])
			} else if value.Valid {
				f.CreateBy = new(uint32)
				*f.CreateBy = uint32(value.Int64)
			}
		case file.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				f.Remark = new(string)
				*f.Remark = value.String
			}
		case file.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				f.TenantID = new(uint32)
				*f.TenantID = uint32(value.Int64)
			}
		case file.FieldProvider:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field provider", values[i])
			} else if value.Valid {
				f.Provider = new(file.Provider)
				*f.Provider = file.Provider(value.String)
			}
		case file.FieldBucketName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field bucket_name", values[i])
			} else if value.Valid {
				f.BucketName = new(string)
				*f.BucketName = value.String
			}
		case file.FieldFileDirectory:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_directory", values[i])
			} else if value.Valid {
				f.FileDirectory = new(string)
				*f.FileDirectory = value.String
			}
		case file.FieldFileGUID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_guid", values[i])
			} else if value.Valid {
				f.FileGUID = new(string)
				*f.FileGUID = value.String
			}
		case file.FieldSaveFileName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field save_file_name", values[i])
			} else if value.Valid {
				f.SaveFileName = new(string)
				*f.SaveFileName = value.String
			}
		case file.FieldFileName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_name", values[i])
			} else if value.Valid {
				f.FileName = new(string)
				*f.FileName = value.String
			}
		case file.FieldExtension:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field extension", values[i])
			} else if value.Valid {
				f.Extension = new(string)
				*f.Extension = value.String
			}
		case file.FieldSize:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field size", values[i])
			} else if value.Valid {
				f.Size = new(uint64)
				*f.Size = uint64(value.Int64)
			}
		case file.FieldSizeFormat:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field size_format", values[i])
			} else if value.Valid {
				f.SizeFormat = new(string)
				*f.SizeFormat = value.String
			}
		case file.FieldLinkURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field link_url", values[i])
			} else if value.Valid {
				f.LinkURL = new(string)
				*f.LinkURL = value.String
			}
		case file.FieldMd5:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field md5", values[i])
			} else if value.Valid {
				f.Md5 = new(string)
				*f.Md5 = value.String
			}
		default:
			f.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the File.
// This includes values selected through modifiers, order, etc.
func (f *File) Value(name string) (ent.Value, error) {
	return f.selectValues.Get(name)
}

// Update returns a builder for updating this File.
// Note that you need to call File.Unwrap() before calling this method if this File
// was returned from a transaction, and the transaction was committed or rolled back.
func (f *File) Update() *FileUpdateOne {
	return NewFileClient(f.config).UpdateOne(f)
}

// Unwrap unwraps the File entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (f *File) Unwrap() *File {
	_tx, ok := f.config.driver.(*txDriver)
	if !ok {
		panic("ent: File is not a transactional entity")
	}
	f.config.driver = _tx.drv
	return f
}

// String implements the fmt.Stringer.
func (f *File) String() string {
	var builder strings.Builder
	builder.WriteString("File(")
	builder.WriteString(fmt.Sprintf("id=%v, ", f.ID))
	if v := f.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := f.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := f.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := f.CreateBy; v != nil {
		builder.WriteString("create_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := f.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := f.Provider; v != nil {
		builder.WriteString("provider=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := f.BucketName; v != nil {
		builder.WriteString("bucket_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.FileDirectory; v != nil {
		builder.WriteString("file_directory=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.FileGUID; v != nil {
		builder.WriteString("file_guid=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.SaveFileName; v != nil {
		builder.WriteString("save_file_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.FileName; v != nil {
		builder.WriteString("file_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.Extension; v != nil {
		builder.WriteString("extension=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.Size; v != nil {
		builder.WriteString("size=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := f.SizeFormat; v != nil {
		builder.WriteString("size_format=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.LinkURL; v != nil {
		builder.WriteString("link_url=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := f.Md5; v != nil {
		builder.WriteString("md5=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// Files is a parsable slice of File.
type Files []*File
