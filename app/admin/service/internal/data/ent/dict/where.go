// Code generated by ent, DO NOT EDIT.

package dict

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldDeleteTime, v))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCreateBy, v))
}

// UpdateBy applies equality check predicate on the "update_by" field. It's identical to UpdateByEQ.
func UpdateBy(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldUpdateBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldRemark, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldTenantID, v))
}

// Key applies equality check predicate on the "key" field. It's identical to KeyEQ.
func Key(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldKey, v))
}

// Category applies equality check predicate on the "category" field. It's identical to CategoryEQ.
func Category(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCategory, v))
}

// CategoryDesc applies equality check predicate on the "category_desc" field. It's identical to CategoryDescEQ.
func CategoryDesc(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCategoryDesc, v))
}

// Value applies equality check predicate on the "value" field. It's identical to ValueEQ.
func Value(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldValue, v))
}

// ValueDesc applies equality check predicate on the "value_desc" field. It's identical to ValueDescEQ.
func ValueDesc(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldValueDesc, v))
}

// ValueDataType applies equality check predicate on the "value_data_type" field. It's identical to ValueDataTypeEQ.
func ValueDataType(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldValueDataType, v))
}

// SortID applies equality check predicate on the "sort_id" field. It's identical to SortIDEQ.
func SortID(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldSortID, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldDeleteTime))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldStatus))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldCreateBy))
}

// UpdateByEQ applies the EQ predicate on the "update_by" field.
func UpdateByEQ(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldUpdateBy, v))
}

// UpdateByNEQ applies the NEQ predicate on the "update_by" field.
func UpdateByNEQ(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldUpdateBy, v))
}

// UpdateByIn applies the In predicate on the "update_by" field.
func UpdateByIn(vs ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldUpdateBy, vs...))
}

// UpdateByNotIn applies the NotIn predicate on the "update_by" field.
func UpdateByNotIn(vs ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldUpdateBy, vs...))
}

// UpdateByGT applies the GT predicate on the "update_by" field.
func UpdateByGT(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldUpdateBy, v))
}

// UpdateByGTE applies the GTE predicate on the "update_by" field.
func UpdateByGTE(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldUpdateBy, v))
}

// UpdateByLT applies the LT predicate on the "update_by" field.
func UpdateByLT(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldUpdateBy, v))
}

// UpdateByLTE applies the LTE predicate on the "update_by" field.
func UpdateByLTE(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldUpdateBy, v))
}

// UpdateByIsNil applies the IsNil predicate on the "update_by" field.
func UpdateByIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldUpdateBy))
}

// UpdateByNotNil applies the NotNil predicate on the "update_by" field.
func UpdateByNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldUpdateBy))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldRemark, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...uint32) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v uint32) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldTenantID))
}

// KeyEQ applies the EQ predicate on the "key" field.
func KeyEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldKey, v))
}

// KeyNEQ applies the NEQ predicate on the "key" field.
func KeyNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldKey, v))
}

// KeyIn applies the In predicate on the "key" field.
func KeyIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldKey, vs...))
}

// KeyNotIn applies the NotIn predicate on the "key" field.
func KeyNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldKey, vs...))
}

// KeyGT applies the GT predicate on the "key" field.
func KeyGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldKey, v))
}

// KeyGTE applies the GTE predicate on the "key" field.
func KeyGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldKey, v))
}

// KeyLT applies the LT predicate on the "key" field.
func KeyLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldKey, v))
}

// KeyLTE applies the LTE predicate on the "key" field.
func KeyLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldKey, v))
}

// KeyContains applies the Contains predicate on the "key" field.
func KeyContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldKey, v))
}

// KeyHasPrefix applies the HasPrefix predicate on the "key" field.
func KeyHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldKey, v))
}

// KeyHasSuffix applies the HasSuffix predicate on the "key" field.
func KeyHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldKey, v))
}

// KeyIsNil applies the IsNil predicate on the "key" field.
func KeyIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldKey))
}

// KeyNotNil applies the NotNil predicate on the "key" field.
func KeyNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldKey))
}

// KeyEqualFold applies the EqualFold predicate on the "key" field.
func KeyEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldKey, v))
}

// KeyContainsFold applies the ContainsFold predicate on the "key" field.
func KeyContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldKey, v))
}

// CategoryEQ applies the EQ predicate on the "category" field.
func CategoryEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCategory, v))
}

// CategoryNEQ applies the NEQ predicate on the "category" field.
func CategoryNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldCategory, v))
}

// CategoryIn applies the In predicate on the "category" field.
func CategoryIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldCategory, vs...))
}

// CategoryNotIn applies the NotIn predicate on the "category" field.
func CategoryNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldCategory, vs...))
}

// CategoryGT applies the GT predicate on the "category" field.
func CategoryGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldCategory, v))
}

// CategoryGTE applies the GTE predicate on the "category" field.
func CategoryGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldCategory, v))
}

// CategoryLT applies the LT predicate on the "category" field.
func CategoryLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldCategory, v))
}

// CategoryLTE applies the LTE predicate on the "category" field.
func CategoryLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldCategory, v))
}

// CategoryContains applies the Contains predicate on the "category" field.
func CategoryContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldCategory, v))
}

// CategoryHasPrefix applies the HasPrefix predicate on the "category" field.
func CategoryHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldCategory, v))
}

// CategoryHasSuffix applies the HasSuffix predicate on the "category" field.
func CategoryHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldCategory, v))
}

// CategoryIsNil applies the IsNil predicate on the "category" field.
func CategoryIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldCategory))
}

// CategoryNotNil applies the NotNil predicate on the "category" field.
func CategoryNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldCategory))
}

// CategoryEqualFold applies the EqualFold predicate on the "category" field.
func CategoryEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldCategory, v))
}

// CategoryContainsFold applies the ContainsFold predicate on the "category" field.
func CategoryContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldCategory, v))
}

// CategoryDescEQ applies the EQ predicate on the "category_desc" field.
func CategoryDescEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldCategoryDesc, v))
}

// CategoryDescNEQ applies the NEQ predicate on the "category_desc" field.
func CategoryDescNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldCategoryDesc, v))
}

// CategoryDescIn applies the In predicate on the "category_desc" field.
func CategoryDescIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldCategoryDesc, vs...))
}

// CategoryDescNotIn applies the NotIn predicate on the "category_desc" field.
func CategoryDescNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldCategoryDesc, vs...))
}

// CategoryDescGT applies the GT predicate on the "category_desc" field.
func CategoryDescGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldCategoryDesc, v))
}

// CategoryDescGTE applies the GTE predicate on the "category_desc" field.
func CategoryDescGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldCategoryDesc, v))
}

// CategoryDescLT applies the LT predicate on the "category_desc" field.
func CategoryDescLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldCategoryDesc, v))
}

// CategoryDescLTE applies the LTE predicate on the "category_desc" field.
func CategoryDescLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldCategoryDesc, v))
}

// CategoryDescContains applies the Contains predicate on the "category_desc" field.
func CategoryDescContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldCategoryDesc, v))
}

// CategoryDescHasPrefix applies the HasPrefix predicate on the "category_desc" field.
func CategoryDescHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldCategoryDesc, v))
}

// CategoryDescHasSuffix applies the HasSuffix predicate on the "category_desc" field.
func CategoryDescHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldCategoryDesc, v))
}

// CategoryDescIsNil applies the IsNil predicate on the "category_desc" field.
func CategoryDescIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldCategoryDesc))
}

// CategoryDescNotNil applies the NotNil predicate on the "category_desc" field.
func CategoryDescNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldCategoryDesc))
}

// CategoryDescEqualFold applies the EqualFold predicate on the "category_desc" field.
func CategoryDescEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldCategoryDesc, v))
}

// CategoryDescContainsFold applies the ContainsFold predicate on the "category_desc" field.
func CategoryDescContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldCategoryDesc, v))
}

// ValueEQ applies the EQ predicate on the "value" field.
func ValueEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldValue, v))
}

// ValueNEQ applies the NEQ predicate on the "value" field.
func ValueNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldValue, v))
}

// ValueIn applies the In predicate on the "value" field.
func ValueIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldValue, vs...))
}

// ValueNotIn applies the NotIn predicate on the "value" field.
func ValueNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldValue, vs...))
}

// ValueGT applies the GT predicate on the "value" field.
func ValueGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldValue, v))
}

// ValueGTE applies the GTE predicate on the "value" field.
func ValueGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldValue, v))
}

// ValueLT applies the LT predicate on the "value" field.
func ValueLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldValue, v))
}

// ValueLTE applies the LTE predicate on the "value" field.
func ValueLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldValue, v))
}

// ValueContains applies the Contains predicate on the "value" field.
func ValueContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldValue, v))
}

// ValueHasPrefix applies the HasPrefix predicate on the "value" field.
func ValueHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldValue, v))
}

// ValueHasSuffix applies the HasSuffix predicate on the "value" field.
func ValueHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldValue, v))
}

// ValueIsNil applies the IsNil predicate on the "value" field.
func ValueIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldValue))
}

// ValueNotNil applies the NotNil predicate on the "value" field.
func ValueNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldValue))
}

// ValueEqualFold applies the EqualFold predicate on the "value" field.
func ValueEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldValue, v))
}

// ValueContainsFold applies the ContainsFold predicate on the "value" field.
func ValueContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldValue, v))
}

// ValueDescEQ applies the EQ predicate on the "value_desc" field.
func ValueDescEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldValueDesc, v))
}

// ValueDescNEQ applies the NEQ predicate on the "value_desc" field.
func ValueDescNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldValueDesc, v))
}

// ValueDescIn applies the In predicate on the "value_desc" field.
func ValueDescIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldValueDesc, vs...))
}

// ValueDescNotIn applies the NotIn predicate on the "value_desc" field.
func ValueDescNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldValueDesc, vs...))
}

// ValueDescGT applies the GT predicate on the "value_desc" field.
func ValueDescGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldValueDesc, v))
}

// ValueDescGTE applies the GTE predicate on the "value_desc" field.
func ValueDescGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldValueDesc, v))
}

// ValueDescLT applies the LT predicate on the "value_desc" field.
func ValueDescLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldValueDesc, v))
}

// ValueDescLTE applies the LTE predicate on the "value_desc" field.
func ValueDescLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldValueDesc, v))
}

// ValueDescContains applies the Contains predicate on the "value_desc" field.
func ValueDescContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldValueDesc, v))
}

// ValueDescHasPrefix applies the HasPrefix predicate on the "value_desc" field.
func ValueDescHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldValueDesc, v))
}

// ValueDescHasSuffix applies the HasSuffix predicate on the "value_desc" field.
func ValueDescHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldValueDesc, v))
}

// ValueDescIsNil applies the IsNil predicate on the "value_desc" field.
func ValueDescIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldValueDesc))
}

// ValueDescNotNil applies the NotNil predicate on the "value_desc" field.
func ValueDescNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldValueDesc))
}

// ValueDescEqualFold applies the EqualFold predicate on the "value_desc" field.
func ValueDescEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldValueDesc, v))
}

// ValueDescContainsFold applies the ContainsFold predicate on the "value_desc" field.
func ValueDescContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldValueDesc, v))
}

// ValueDataTypeEQ applies the EQ predicate on the "value_data_type" field.
func ValueDataTypeEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldValueDataType, v))
}

// ValueDataTypeNEQ applies the NEQ predicate on the "value_data_type" field.
func ValueDataTypeNEQ(v string) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldValueDataType, v))
}

// ValueDataTypeIn applies the In predicate on the "value_data_type" field.
func ValueDataTypeIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldValueDataType, vs...))
}

// ValueDataTypeNotIn applies the NotIn predicate on the "value_data_type" field.
func ValueDataTypeNotIn(vs ...string) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldValueDataType, vs...))
}

// ValueDataTypeGT applies the GT predicate on the "value_data_type" field.
func ValueDataTypeGT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldValueDataType, v))
}

// ValueDataTypeGTE applies the GTE predicate on the "value_data_type" field.
func ValueDataTypeGTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldValueDataType, v))
}

// ValueDataTypeLT applies the LT predicate on the "value_data_type" field.
func ValueDataTypeLT(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldValueDataType, v))
}

// ValueDataTypeLTE applies the LTE predicate on the "value_data_type" field.
func ValueDataTypeLTE(v string) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldValueDataType, v))
}

// ValueDataTypeContains applies the Contains predicate on the "value_data_type" field.
func ValueDataTypeContains(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContains(FieldValueDataType, v))
}

// ValueDataTypeHasPrefix applies the HasPrefix predicate on the "value_data_type" field.
func ValueDataTypeHasPrefix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasPrefix(FieldValueDataType, v))
}

// ValueDataTypeHasSuffix applies the HasSuffix predicate on the "value_data_type" field.
func ValueDataTypeHasSuffix(v string) predicate.Dict {
	return predicate.Dict(sql.FieldHasSuffix(FieldValueDataType, v))
}

// ValueDataTypeIsNil applies the IsNil predicate on the "value_data_type" field.
func ValueDataTypeIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldValueDataType))
}

// ValueDataTypeNotNil applies the NotNil predicate on the "value_data_type" field.
func ValueDataTypeNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldValueDataType))
}

// ValueDataTypeEqualFold applies the EqualFold predicate on the "value_data_type" field.
func ValueDataTypeEqualFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldEqualFold(FieldValueDataType, v))
}

// ValueDataTypeContainsFold applies the ContainsFold predicate on the "value_data_type" field.
func ValueDataTypeContainsFold(v string) predicate.Dict {
	return predicate.Dict(sql.FieldContainsFold(FieldValueDataType, v))
}

// SortIDEQ applies the EQ predicate on the "sort_id" field.
func SortIDEQ(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldEQ(FieldSortID, v))
}

// SortIDNEQ applies the NEQ predicate on the "sort_id" field.
func SortIDNEQ(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldNEQ(FieldSortID, v))
}

// SortIDIn applies the In predicate on the "sort_id" field.
func SortIDIn(vs ...int32) predicate.Dict {
	return predicate.Dict(sql.FieldIn(FieldSortID, vs...))
}

// SortIDNotIn applies the NotIn predicate on the "sort_id" field.
func SortIDNotIn(vs ...int32) predicate.Dict {
	return predicate.Dict(sql.FieldNotIn(FieldSortID, vs...))
}

// SortIDGT applies the GT predicate on the "sort_id" field.
func SortIDGT(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldGT(FieldSortID, v))
}

// SortIDGTE applies the GTE predicate on the "sort_id" field.
func SortIDGTE(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldGTE(FieldSortID, v))
}

// SortIDLT applies the LT predicate on the "sort_id" field.
func SortIDLT(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldLT(FieldSortID, v))
}

// SortIDLTE applies the LTE predicate on the "sort_id" field.
func SortIDLTE(v int32) predicate.Dict {
	return predicate.Dict(sql.FieldLTE(FieldSortID, v))
}

// SortIDIsNil applies the IsNil predicate on the "sort_id" field.
func SortIDIsNil() predicate.Dict {
	return predicate.Dict(sql.FieldIsNull(FieldSortID))
}

// SortIDNotNil applies the NotNil predicate on the "sort_id" field.
func SortIDNotNil() predicate.Dict {
	return predicate.Dict(sql.FieldNotNull(FieldSortID))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Dict) predicate.Dict {
	return predicate.Dict(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Dict) predicate.Dict {
	return predicate.Dict(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Dict) predicate.Dict {
	return predicate.Dict(sql.NotPredicates(p))
}
