// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagerecipient"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageRecipientCreate is the builder for creating a NotificationMessageRecipient entity.
type NotificationMessageRecipientCreate struct {
	config
	mutation *NotificationMessageRecipientMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (nmrc *NotificationMessageRecipientCreate) SetCreateTime(t time.Time) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetCreateTime(t)
	return nmrc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableCreateTime(t *time.Time) *NotificationMessageRecipientCreate {
	if t != nil {
		nmrc.SetCreateTime(*t)
	}
	return nmrc
}

// SetUpdateTime sets the "update_time" field.
func (nmrc *NotificationMessageRecipientCreate) SetUpdateTime(t time.Time) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetUpdateTime(t)
	return nmrc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableUpdateTime(t *time.Time) *NotificationMessageRecipientCreate {
	if t != nil {
		nmrc.SetUpdateTime(*t)
	}
	return nmrc
}

// SetDeleteTime sets the "delete_time" field.
func (nmrc *NotificationMessageRecipientCreate) SetDeleteTime(t time.Time) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetDeleteTime(t)
	return nmrc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableDeleteTime(t *time.Time) *NotificationMessageRecipientCreate {
	if t != nil {
		nmrc.SetDeleteTime(*t)
	}
	return nmrc
}

// SetTenantID sets the "tenant_id" field.
func (nmrc *NotificationMessageRecipientCreate) SetTenantID(u uint32) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetTenantID(u)
	return nmrc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableTenantID(u *uint32) *NotificationMessageRecipientCreate {
	if u != nil {
		nmrc.SetTenantID(*u)
	}
	return nmrc
}

// SetMessageID sets the "message_id" field.
func (nmrc *NotificationMessageRecipientCreate) SetMessageID(u uint32) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetMessageID(u)
	return nmrc
}

// SetNillableMessageID sets the "message_id" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableMessageID(u *uint32) *NotificationMessageRecipientCreate {
	if u != nil {
		nmrc.SetMessageID(*u)
	}
	return nmrc
}

// SetRecipientID sets the "recipient_id" field.
func (nmrc *NotificationMessageRecipientCreate) SetRecipientID(u uint32) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetRecipientID(u)
	return nmrc
}

// SetNillableRecipientID sets the "recipient_id" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableRecipientID(u *uint32) *NotificationMessageRecipientCreate {
	if u != nil {
		nmrc.SetRecipientID(*u)
	}
	return nmrc
}

// SetStatus sets the "status" field.
func (nmrc *NotificationMessageRecipientCreate) SetStatus(n notificationmessagerecipient.Status) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetStatus(n)
	return nmrc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nmrc *NotificationMessageRecipientCreate) SetNillableStatus(n *notificationmessagerecipient.Status) *NotificationMessageRecipientCreate {
	if n != nil {
		nmrc.SetStatus(*n)
	}
	return nmrc
}

// SetID sets the "id" field.
func (nmrc *NotificationMessageRecipientCreate) SetID(u uint32) *NotificationMessageRecipientCreate {
	nmrc.mutation.SetID(u)
	return nmrc
}

// Mutation returns the NotificationMessageRecipientMutation object of the builder.
func (nmrc *NotificationMessageRecipientCreate) Mutation() *NotificationMessageRecipientMutation {
	return nmrc.mutation
}

// Save creates the NotificationMessageRecipient in the database.
func (nmrc *NotificationMessageRecipientCreate) Save(ctx context.Context) (*NotificationMessageRecipient, error) {
	return withHooks(ctx, nmrc.sqlSave, nmrc.mutation, nmrc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (nmrc *NotificationMessageRecipientCreate) SaveX(ctx context.Context) *NotificationMessageRecipient {
	v, err := nmrc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nmrc *NotificationMessageRecipientCreate) Exec(ctx context.Context) error {
	_, err := nmrc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmrc *NotificationMessageRecipientCreate) ExecX(ctx context.Context) {
	if err := nmrc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmrc *NotificationMessageRecipientCreate) check() error {
	if v, ok := nmrc.mutation.TenantID(); ok {
		if err := notificationmessagerecipient.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageRecipient.tenant_id": %w`, err)}
		}
	}
	if v, ok := nmrc.mutation.Status(); ok {
		if err := notificationmessagerecipient.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageRecipient.status": %w`, err)}
		}
	}
	if v, ok := nmrc.mutation.ID(); ok {
		if err := notificationmessagerecipient.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageRecipient.id": %w`, err)}
		}
	}
	return nil
}

func (nmrc *NotificationMessageRecipientCreate) sqlSave(ctx context.Context) (*NotificationMessageRecipient, error) {
	if err := nmrc.check(); err != nil {
		return nil, err
	}
	_node, _spec := nmrc.createSpec()
	if err := sqlgraph.CreateNode(ctx, nmrc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	nmrc.mutation.id = &_node.ID
	nmrc.mutation.done = true
	return _node, nil
}

func (nmrc *NotificationMessageRecipientCreate) createSpec() (*NotificationMessageRecipient, *sqlgraph.CreateSpec) {
	var (
		_node = &NotificationMessageRecipient{config: nmrc.config}
		_spec = sqlgraph.NewCreateSpec(notificationmessagerecipient.Table, sqlgraph.NewFieldSpec(notificationmessagerecipient.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = nmrc.conflict
	if id, ok := nmrc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := nmrc.mutation.CreateTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := nmrc.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := nmrc.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := nmrc.mutation.TenantID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := nmrc.mutation.MessageID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldMessageID, field.TypeUint32, value)
		_node.MessageID = &value
	}
	if value, ok := nmrc.mutation.RecipientID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32, value)
		_node.RecipientID = &value
	}
	if value, ok := nmrc.mutation.Status(); ok {
		_spec.SetField(notificationmessagerecipient.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.NotificationMessageRecipient.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.NotificationMessageRecipientUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (nmrc *NotificationMessageRecipientCreate) OnConflict(opts ...sql.ConflictOption) *NotificationMessageRecipientUpsertOne {
	nmrc.conflict = opts
	return &NotificationMessageRecipientUpsertOne{
		create: nmrc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.NotificationMessageRecipient.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (nmrc *NotificationMessageRecipientCreate) OnConflictColumns(columns ...string) *NotificationMessageRecipientUpsertOne {
	nmrc.conflict = append(nmrc.conflict, sql.ConflictColumns(columns...))
	return &NotificationMessageRecipientUpsertOne{
		create: nmrc,
	}
}

type (
	// NotificationMessageRecipientUpsertOne is the builder for "upsert"-ing
	//  one NotificationMessageRecipient node.
	NotificationMessageRecipientUpsertOne struct {
		create *NotificationMessageRecipientCreate
	}

	// NotificationMessageRecipientUpsert is the "OnConflict" setter.
	NotificationMessageRecipientUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageRecipientUpsert) SetUpdateTime(v time.Time) *NotificationMessageRecipientUpsert {
	u.Set(notificationmessagerecipient.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsert) UpdateUpdateTime() *NotificationMessageRecipientUpsert {
	u.SetExcluded(notificationmessagerecipient.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageRecipientUpsert) ClearUpdateTime() *NotificationMessageRecipientUpsert {
	u.SetNull(notificationmessagerecipient.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageRecipientUpsert) SetDeleteTime(v time.Time) *NotificationMessageRecipientUpsert {
	u.Set(notificationmessagerecipient.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsert) UpdateDeleteTime() *NotificationMessageRecipientUpsert {
	u.SetExcluded(notificationmessagerecipient.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageRecipientUpsert) ClearDeleteTime() *NotificationMessageRecipientUpsert {
	u.SetNull(notificationmessagerecipient.FieldDeleteTime)
	return u
}

// SetMessageID sets the "message_id" field.
func (u *NotificationMessageRecipientUpsert) SetMessageID(v uint32) *NotificationMessageRecipientUpsert {
	u.Set(notificationmessagerecipient.FieldMessageID, v)
	return u
}

// UpdateMessageID sets the "message_id" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsert) UpdateMessageID() *NotificationMessageRecipientUpsert {
	u.SetExcluded(notificationmessagerecipient.FieldMessageID)
	return u
}

// AddMessageID adds v to the "message_id" field.
func (u *NotificationMessageRecipientUpsert) AddMessageID(v uint32) *NotificationMessageRecipientUpsert {
	u.Add(notificationmessagerecipient.FieldMessageID, v)
	return u
}

// ClearMessageID clears the value of the "message_id" field.
func (u *NotificationMessageRecipientUpsert) ClearMessageID() *NotificationMessageRecipientUpsert {
	u.SetNull(notificationmessagerecipient.FieldMessageID)
	return u
}

// SetRecipientID sets the "recipient_id" field.
func (u *NotificationMessageRecipientUpsert) SetRecipientID(v uint32) *NotificationMessageRecipientUpsert {
	u.Set(notificationmessagerecipient.FieldRecipientID, v)
	return u
}

// UpdateRecipientID sets the "recipient_id" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsert) UpdateRecipientID() *NotificationMessageRecipientUpsert {
	u.SetExcluded(notificationmessagerecipient.FieldRecipientID)
	return u
}

// AddRecipientID adds v to the "recipient_id" field.
func (u *NotificationMessageRecipientUpsert) AddRecipientID(v uint32) *NotificationMessageRecipientUpsert {
	u.Add(notificationmessagerecipient.FieldRecipientID, v)
	return u
}

// ClearRecipientID clears the value of the "recipient_id" field.
func (u *NotificationMessageRecipientUpsert) ClearRecipientID() *NotificationMessageRecipientUpsert {
	u.SetNull(notificationmessagerecipient.FieldRecipientID)
	return u
}

// SetStatus sets the "status" field.
func (u *NotificationMessageRecipientUpsert) SetStatus(v notificationmessagerecipient.Status) *NotificationMessageRecipientUpsert {
	u.Set(notificationmessagerecipient.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsert) UpdateStatus() *NotificationMessageRecipientUpsert {
	u.SetExcluded(notificationmessagerecipient.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *NotificationMessageRecipientUpsert) ClearStatus() *NotificationMessageRecipientUpsert {
	u.SetNull(notificationmessagerecipient.FieldStatus)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.NotificationMessageRecipient.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(notificationmessagerecipient.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *NotificationMessageRecipientUpsertOne) UpdateNewValues() *NotificationMessageRecipientUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(notificationmessagerecipient.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(notificationmessagerecipient.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(notificationmessagerecipient.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.NotificationMessageRecipient.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *NotificationMessageRecipientUpsertOne) Ignore() *NotificationMessageRecipientUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *NotificationMessageRecipientUpsertOne) DoNothing() *NotificationMessageRecipientUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the NotificationMessageRecipientCreate.OnConflict
// documentation for more info.
func (u *NotificationMessageRecipientUpsertOne) Update(set func(*NotificationMessageRecipientUpsert)) *NotificationMessageRecipientUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&NotificationMessageRecipientUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageRecipientUpsertOne) SetUpdateTime(v time.Time) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertOne) UpdateUpdateTime() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageRecipientUpsertOne) ClearUpdateTime() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageRecipientUpsertOne) SetDeleteTime(v time.Time) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertOne) UpdateDeleteTime() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageRecipientUpsertOne) ClearDeleteTime() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearDeleteTime()
	})
}

// SetMessageID sets the "message_id" field.
func (u *NotificationMessageRecipientUpsertOne) SetMessageID(v uint32) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetMessageID(v)
	})
}

// AddMessageID adds v to the "message_id" field.
func (u *NotificationMessageRecipientUpsertOne) AddMessageID(v uint32) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.AddMessageID(v)
	})
}

// UpdateMessageID sets the "message_id" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertOne) UpdateMessageID() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateMessageID()
	})
}

// ClearMessageID clears the value of the "message_id" field.
func (u *NotificationMessageRecipientUpsertOne) ClearMessageID() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearMessageID()
	})
}

// SetRecipientID sets the "recipient_id" field.
func (u *NotificationMessageRecipientUpsertOne) SetRecipientID(v uint32) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetRecipientID(v)
	})
}

// AddRecipientID adds v to the "recipient_id" field.
func (u *NotificationMessageRecipientUpsertOne) AddRecipientID(v uint32) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.AddRecipientID(v)
	})
}

// UpdateRecipientID sets the "recipient_id" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertOne) UpdateRecipientID() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateRecipientID()
	})
}

// ClearRecipientID clears the value of the "recipient_id" field.
func (u *NotificationMessageRecipientUpsertOne) ClearRecipientID() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearRecipientID()
	})
}

// SetStatus sets the "status" field.
func (u *NotificationMessageRecipientUpsertOne) SetStatus(v notificationmessagerecipient.Status) *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertOne) UpdateStatus() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *NotificationMessageRecipientUpsertOne) ClearStatus() *NotificationMessageRecipientUpsertOne {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearStatus()
	})
}

// Exec executes the query.
func (u *NotificationMessageRecipientUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for NotificationMessageRecipientCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *NotificationMessageRecipientUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *NotificationMessageRecipientUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *NotificationMessageRecipientUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// NotificationMessageRecipientCreateBulk is the builder for creating many NotificationMessageRecipient entities in bulk.
type NotificationMessageRecipientCreateBulk struct {
	config
	err      error
	builders []*NotificationMessageRecipientCreate
	conflict []sql.ConflictOption
}

// Save creates the NotificationMessageRecipient entities in the database.
func (nmrcb *NotificationMessageRecipientCreateBulk) Save(ctx context.Context) ([]*NotificationMessageRecipient, error) {
	if nmrcb.err != nil {
		return nil, nmrcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(nmrcb.builders))
	nodes := make([]*NotificationMessageRecipient, len(nmrcb.builders))
	mutators := make([]Mutator, len(nmrcb.builders))
	for i := range nmrcb.builders {
		func(i int, root context.Context) {
			builder := nmrcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotificationMessageRecipientMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, nmrcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = nmrcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, nmrcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, nmrcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (nmrcb *NotificationMessageRecipientCreateBulk) SaveX(ctx context.Context) []*NotificationMessageRecipient {
	v, err := nmrcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nmrcb *NotificationMessageRecipientCreateBulk) Exec(ctx context.Context) error {
	_, err := nmrcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmrcb *NotificationMessageRecipientCreateBulk) ExecX(ctx context.Context) {
	if err := nmrcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.NotificationMessageRecipient.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.NotificationMessageRecipientUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (nmrcb *NotificationMessageRecipientCreateBulk) OnConflict(opts ...sql.ConflictOption) *NotificationMessageRecipientUpsertBulk {
	nmrcb.conflict = opts
	return &NotificationMessageRecipientUpsertBulk{
		create: nmrcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.NotificationMessageRecipient.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (nmrcb *NotificationMessageRecipientCreateBulk) OnConflictColumns(columns ...string) *NotificationMessageRecipientUpsertBulk {
	nmrcb.conflict = append(nmrcb.conflict, sql.ConflictColumns(columns...))
	return &NotificationMessageRecipientUpsertBulk{
		create: nmrcb,
	}
}

// NotificationMessageRecipientUpsertBulk is the builder for "upsert"-ing
// a bulk of NotificationMessageRecipient nodes.
type NotificationMessageRecipientUpsertBulk struct {
	create *NotificationMessageRecipientCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.NotificationMessageRecipient.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(notificationmessagerecipient.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *NotificationMessageRecipientUpsertBulk) UpdateNewValues() *NotificationMessageRecipientUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(notificationmessagerecipient.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(notificationmessagerecipient.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(notificationmessagerecipient.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.NotificationMessageRecipient.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *NotificationMessageRecipientUpsertBulk) Ignore() *NotificationMessageRecipientUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *NotificationMessageRecipientUpsertBulk) DoNothing() *NotificationMessageRecipientUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the NotificationMessageRecipientCreateBulk.OnConflict
// documentation for more info.
func (u *NotificationMessageRecipientUpsertBulk) Update(set func(*NotificationMessageRecipientUpsert)) *NotificationMessageRecipientUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&NotificationMessageRecipientUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageRecipientUpsertBulk) SetUpdateTime(v time.Time) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertBulk) UpdateUpdateTime() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageRecipientUpsertBulk) ClearUpdateTime() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageRecipientUpsertBulk) SetDeleteTime(v time.Time) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertBulk) UpdateDeleteTime() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageRecipientUpsertBulk) ClearDeleteTime() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearDeleteTime()
	})
}

// SetMessageID sets the "message_id" field.
func (u *NotificationMessageRecipientUpsertBulk) SetMessageID(v uint32) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetMessageID(v)
	})
}

// AddMessageID adds v to the "message_id" field.
func (u *NotificationMessageRecipientUpsertBulk) AddMessageID(v uint32) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.AddMessageID(v)
	})
}

// UpdateMessageID sets the "message_id" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertBulk) UpdateMessageID() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateMessageID()
	})
}

// ClearMessageID clears the value of the "message_id" field.
func (u *NotificationMessageRecipientUpsertBulk) ClearMessageID() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearMessageID()
	})
}

// SetRecipientID sets the "recipient_id" field.
func (u *NotificationMessageRecipientUpsertBulk) SetRecipientID(v uint32) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetRecipientID(v)
	})
}

// AddRecipientID adds v to the "recipient_id" field.
func (u *NotificationMessageRecipientUpsertBulk) AddRecipientID(v uint32) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.AddRecipientID(v)
	})
}

// UpdateRecipientID sets the "recipient_id" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertBulk) UpdateRecipientID() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateRecipientID()
	})
}

// ClearRecipientID clears the value of the "recipient_id" field.
func (u *NotificationMessageRecipientUpsertBulk) ClearRecipientID() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearRecipientID()
	})
}

// SetStatus sets the "status" field.
func (u *NotificationMessageRecipientUpsertBulk) SetStatus(v notificationmessagerecipient.Status) *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *NotificationMessageRecipientUpsertBulk) UpdateStatus() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *NotificationMessageRecipientUpsertBulk) ClearStatus() *NotificationMessageRecipientUpsertBulk {
	return u.Update(func(s *NotificationMessageRecipientUpsert) {
		s.ClearStatus()
	})
}

// Exec executes the query.
func (u *NotificationMessageRecipientUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the NotificationMessageRecipientCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for NotificationMessageRecipientCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *NotificationMessageRecipientUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
