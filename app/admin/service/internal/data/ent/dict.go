// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/dict"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 字典表
type Dict struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 状态
	Status *dict.Status `json:"status,omitempty"`
	// 创建者ID
	CreateBy *uint32 `json:"create_by,omitempty"`
	// 更新者ID
	UpdateBy *uint32 `json:"update_by,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 租户ID
	TenantID *uint32 `json:"tenant_id,omitempty"`
	// 字典键
	Key *string `json:"key,omitempty"`
	// 字典类型
	Category *string `json:"category,omitempty"`
	// 字典类型名称
	CategoryDesc *string `json:"category_desc,omitempty"`
	// 字典值
	Value *string `json:"value,omitempty"`
	// 字典值名称
	ValueDesc *string `json:"value_desc,omitempty"`
	// 字典值数据类型
	ValueDataType *string `json:"value_data_type,omitempty"`
	// 排序ID
	SortID       *int32 `json:"sort_id,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Dict) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case dict.FieldID, dict.FieldCreateBy, dict.FieldUpdateBy, dict.FieldTenantID, dict.FieldSortID:
			values[i] = new(sql.NullInt64)
		case dict.FieldStatus, dict.FieldRemark, dict.FieldKey, dict.FieldCategory, dict.FieldCategoryDesc, dict.FieldValue, dict.FieldValueDesc, dict.FieldValueDataType:
			values[i] = new(sql.NullString)
		case dict.FieldCreateTime, dict.FieldUpdateTime, dict.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Dict fields.
func (d *Dict) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case dict.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			d.ID = uint32(value.Int64)
		case dict.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				d.CreateTime = new(time.Time)
				*d.CreateTime = value.Time
			}
		case dict.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				d.UpdateTime = new(time.Time)
				*d.UpdateTime = value.Time
			}
		case dict.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				d.DeleteTime = new(time.Time)
				*d.DeleteTime = value.Time
			}
		case dict.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				d.Status = new(dict.Status)
				*d.Status = dict.Status(value.String)
			}
		case dict.FieldCreateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_by", values[i])
			} else if value.Valid {
				d.CreateBy = new(uint32)
				*d.CreateBy = uint32(value.Int64)
			}
		case dict.FieldUpdateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field update_by", values[i])
			} else if value.Valid {
				d.UpdateBy = new(uint32)
				*d.UpdateBy = uint32(value.Int64)
			}
		case dict.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				d.Remark = new(string)
				*d.Remark = value.String
			}
		case dict.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				d.TenantID = new(uint32)
				*d.TenantID = uint32(value.Int64)
			}
		case dict.FieldKey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field key", values[i])
			} else if value.Valid {
				d.Key = new(string)
				*d.Key = value.String
			}
		case dict.FieldCategory:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field category", values[i])
			} else if value.Valid {
				d.Category = new(string)
				*d.Category = value.String
			}
		case dict.FieldCategoryDesc:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field category_desc", values[i])
			} else if value.Valid {
				d.CategoryDesc = new(string)
				*d.CategoryDesc = value.String
			}
		case dict.FieldValue:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field value", values[i])
			} else if value.Valid {
				d.Value = new(string)
				*d.Value = value.String
			}
		case dict.FieldValueDesc:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field value_desc", values[i])
			} else if value.Valid {
				d.ValueDesc = new(string)
				*d.ValueDesc = value.String
			}
		case dict.FieldValueDataType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field value_data_type", values[i])
			} else if value.Valid {
				d.ValueDataType = new(string)
				*d.ValueDataType = value.String
			}
		case dict.FieldSortID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort_id", values[i])
			} else if value.Valid {
				d.SortID = new(int32)
				*d.SortID = int32(value.Int64)
			}
		default:
			d.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// GetValue returns the ent.Value that was dynamically selected and assigned to the Dict.
// This includes values selected through modifiers, order, etc.
func (d *Dict) GetValue(name string) (ent.Value, error) {
	return d.selectValues.Get(name)
}

// Update returns a builder for updating this Dict.
// Note that you need to call Dict.Unwrap() before calling this method if this Dict
// was returned from a transaction, and the transaction was committed or rolled back.
func (d *Dict) Update() *DictUpdateOne {
	return NewDictClient(d.config).UpdateOne(d)
}

// Unwrap unwraps the Dict entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (d *Dict) Unwrap() *Dict {
	_tx, ok := d.config.driver.(*txDriver)
	if !ok {
		panic("ent: Dict is not a transactional entity")
	}
	d.config.driver = _tx.drv
	return d
}

// String implements the fmt.Stringer.
func (d *Dict) String() string {
	var builder strings.Builder
	builder.WriteString("Dict(")
	builder.WriteString(fmt.Sprintf("id=%v, ", d.ID))
	if v := d.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := d.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := d.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := d.Status; v != nil {
		builder.WriteString("status=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := d.CreateBy; v != nil {
		builder.WriteString("create_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := d.UpdateBy; v != nil {
		builder.WriteString("update_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := d.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := d.Key; v != nil {
		builder.WriteString("key=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.Category; v != nil {
		builder.WriteString("category=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.CategoryDesc; v != nil {
		builder.WriteString("category_desc=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.Value; v != nil {
		builder.WriteString("value=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.ValueDesc; v != nil {
		builder.WriteString("value_desc=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.ValueDataType; v != nil {
		builder.WriteString("value_data_type=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := d.SortID; v != nil {
		builder.WriteString("sort_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// Dicts is a parsable slice of Dict.
type Dicts []*Dict
