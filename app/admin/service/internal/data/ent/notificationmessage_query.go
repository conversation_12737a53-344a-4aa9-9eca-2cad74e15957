// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageQuery is the builder for querying NotificationMessage entities.
type NotificationMessageQuery struct {
	config
	ctx        *QueryContext
	order      []notificationmessage.OrderOption
	inters     []Interceptor
	predicates []predicate.NotificationMessage
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NotificationMessageQuery builder.
func (nmq *NotificationMessageQuery) Where(ps ...predicate.NotificationMessage) *NotificationMessageQuery {
	nmq.predicates = append(nmq.predicates, ps...)
	return nmq
}

// Limit the number of records to be returned by this query.
func (nmq *NotificationMessageQuery) Limit(limit int) *NotificationMessageQuery {
	nmq.ctx.Limit = &limit
	return nmq
}

// Offset to start from.
func (nmq *NotificationMessageQuery) Offset(offset int) *NotificationMessageQuery {
	nmq.ctx.Offset = &offset
	return nmq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (nmq *NotificationMessageQuery) Unique(unique bool) *NotificationMessageQuery {
	nmq.ctx.Unique = &unique
	return nmq
}

// Order specifies how the records should be ordered.
func (nmq *NotificationMessageQuery) Order(o ...notificationmessage.OrderOption) *NotificationMessageQuery {
	nmq.order = append(nmq.order, o...)
	return nmq
}

// First returns the first NotificationMessage entity from the query.
// Returns a *NotFoundError when no NotificationMessage was found.
func (nmq *NotificationMessageQuery) First(ctx context.Context) (*NotificationMessage, error) {
	nodes, err := nmq.Limit(1).All(setContextOp(ctx, nmq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{notificationmessage.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (nmq *NotificationMessageQuery) FirstX(ctx context.Context) *NotificationMessage {
	node, err := nmq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first NotificationMessage ID from the query.
// Returns a *NotFoundError when no NotificationMessage ID was found.
func (nmq *NotificationMessageQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = nmq.Limit(1).IDs(setContextOp(ctx, nmq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{notificationmessage.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (nmq *NotificationMessageQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := nmq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single NotificationMessage entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one NotificationMessage entity is found.
// Returns a *NotFoundError when no NotificationMessage entities are found.
func (nmq *NotificationMessageQuery) Only(ctx context.Context) (*NotificationMessage, error) {
	nodes, err := nmq.Limit(2).All(setContextOp(ctx, nmq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{notificationmessage.Label}
	default:
		return nil, &NotSingularError{notificationmessage.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (nmq *NotificationMessageQuery) OnlyX(ctx context.Context) *NotificationMessage {
	node, err := nmq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only NotificationMessage ID in the query.
// Returns a *NotSingularError when more than one NotificationMessage ID is found.
// Returns a *NotFoundError when no entities are found.
func (nmq *NotificationMessageQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = nmq.Limit(2).IDs(setContextOp(ctx, nmq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{notificationmessage.Label}
	default:
		err = &NotSingularError{notificationmessage.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (nmq *NotificationMessageQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := nmq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of NotificationMessages.
func (nmq *NotificationMessageQuery) All(ctx context.Context) ([]*NotificationMessage, error) {
	ctx = setContextOp(ctx, nmq.ctx, ent.OpQueryAll)
	if err := nmq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*NotificationMessage, *NotificationMessageQuery]()
	return withInterceptors[[]*NotificationMessage](ctx, nmq, qr, nmq.inters)
}

// AllX is like All, but panics if an error occurs.
func (nmq *NotificationMessageQuery) AllX(ctx context.Context) []*NotificationMessage {
	nodes, err := nmq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of NotificationMessage IDs.
func (nmq *NotificationMessageQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if nmq.ctx.Unique == nil && nmq.path != nil {
		nmq.Unique(true)
	}
	ctx = setContextOp(ctx, nmq.ctx, ent.OpQueryIDs)
	if err = nmq.Select(notificationmessage.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (nmq *NotificationMessageQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := nmq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (nmq *NotificationMessageQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, nmq.ctx, ent.OpQueryCount)
	if err := nmq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, nmq, querierCount[*NotificationMessageQuery](), nmq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (nmq *NotificationMessageQuery) CountX(ctx context.Context) int {
	count, err := nmq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (nmq *NotificationMessageQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, nmq.ctx, ent.OpQueryExist)
	switch _, err := nmq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (nmq *NotificationMessageQuery) ExistX(ctx context.Context) bool {
	exist, err := nmq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NotificationMessageQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (nmq *NotificationMessageQuery) Clone() *NotificationMessageQuery {
	if nmq == nil {
		return nil
	}
	return &NotificationMessageQuery{
		config:     nmq.config,
		ctx:        nmq.ctx.Clone(),
		order:      append([]notificationmessage.OrderOption{}, nmq.order...),
		inters:     append([]Interceptor{}, nmq.inters...),
		predicates: append([]predicate.NotificationMessage{}, nmq.predicates...),
		// clone intermediate query.
		sql:       nmq.sql.Clone(),
		path:      nmq.path,
		modifiers: append([]func(*sql.Selector){}, nmq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.NotificationMessage.Query().
//		GroupBy(notificationmessage.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (nmq *NotificationMessageQuery) GroupBy(field string, fields ...string) *NotificationMessageGroupBy {
	nmq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NotificationMessageGroupBy{build: nmq}
	grbuild.flds = &nmq.ctx.Fields
	grbuild.label = notificationmessage.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.NotificationMessage.Query().
//		Select(notificationmessage.FieldCreateTime).
//		Scan(ctx, &v)
func (nmq *NotificationMessageQuery) Select(fields ...string) *NotificationMessageSelect {
	nmq.ctx.Fields = append(nmq.ctx.Fields, fields...)
	sbuild := &NotificationMessageSelect{NotificationMessageQuery: nmq}
	sbuild.label = notificationmessage.Label
	sbuild.flds, sbuild.scan = &nmq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NotificationMessageSelect configured with the given aggregations.
func (nmq *NotificationMessageQuery) Aggregate(fns ...AggregateFunc) *NotificationMessageSelect {
	return nmq.Select().Aggregate(fns...)
}

func (nmq *NotificationMessageQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range nmq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, nmq); err != nil {
				return err
			}
		}
	}
	for _, f := range nmq.ctx.Fields {
		if !notificationmessage.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if nmq.path != nil {
		prev, err := nmq.path(ctx)
		if err != nil {
			return err
		}
		nmq.sql = prev
	}
	return nil
}

func (nmq *NotificationMessageQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*NotificationMessage, error) {
	var (
		nodes = []*NotificationMessage{}
		_spec = nmq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*NotificationMessage).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &NotificationMessage{config: nmq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(nmq.modifiers) > 0 {
		_spec.Modifiers = nmq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, nmq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (nmq *NotificationMessageQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := nmq.querySpec()
	if len(nmq.modifiers) > 0 {
		_spec.Modifiers = nmq.modifiers
	}
	_spec.Node.Columns = nmq.ctx.Fields
	if len(nmq.ctx.Fields) > 0 {
		_spec.Unique = nmq.ctx.Unique != nil && *nmq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, nmq.driver, _spec)
}

func (nmq *NotificationMessageQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(notificationmessage.Table, notificationmessage.Columns, sqlgraph.NewFieldSpec(notificationmessage.FieldID, field.TypeUint32))
	_spec.From = nmq.sql
	if unique := nmq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if nmq.path != nil {
		_spec.Unique = true
	}
	if fields := nmq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationmessage.FieldID)
		for i := range fields {
			if fields[i] != notificationmessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := nmq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := nmq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := nmq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := nmq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (nmq *NotificationMessageQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(nmq.driver.Dialect())
	t1 := builder.Table(notificationmessage.Table)
	columns := nmq.ctx.Fields
	if len(columns) == 0 {
		columns = notificationmessage.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if nmq.sql != nil {
		selector = nmq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if nmq.ctx.Unique != nil && *nmq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range nmq.modifiers {
		m(selector)
	}
	for _, p := range nmq.predicates {
		p(selector)
	}
	for _, p := range nmq.order {
		p(selector)
	}
	if offset := nmq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := nmq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (nmq *NotificationMessageQuery) ForUpdate(opts ...sql.LockOption) *NotificationMessageQuery {
	if nmq.driver.Dialect() == dialect.Postgres {
		nmq.Unique(false)
	}
	nmq.modifiers = append(nmq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return nmq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (nmq *NotificationMessageQuery) ForShare(opts ...sql.LockOption) *NotificationMessageQuery {
	if nmq.driver.Dialect() == dialect.Postgres {
		nmq.Unique(false)
	}
	nmq.modifiers = append(nmq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return nmq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (nmq *NotificationMessageQuery) Modify(modifiers ...func(s *sql.Selector)) *NotificationMessageSelect {
	nmq.modifiers = append(nmq.modifiers, modifiers...)
	return nmq.Select()
}

// NotificationMessageGroupBy is the group-by builder for NotificationMessage entities.
type NotificationMessageGroupBy struct {
	selector
	build *NotificationMessageQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (nmgb *NotificationMessageGroupBy) Aggregate(fns ...AggregateFunc) *NotificationMessageGroupBy {
	nmgb.fns = append(nmgb.fns, fns...)
	return nmgb
}

// Scan applies the selector query and scans the result into the given value.
func (nmgb *NotificationMessageGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nmgb.build.ctx, ent.OpQueryGroupBy)
	if err := nmgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationMessageQuery, *NotificationMessageGroupBy](ctx, nmgb.build, nmgb, nmgb.build.inters, v)
}

func (nmgb *NotificationMessageGroupBy) sqlScan(ctx context.Context, root *NotificationMessageQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(nmgb.fns))
	for _, fn := range nmgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*nmgb.flds)+len(nmgb.fns))
		for _, f := range *nmgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*nmgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nmgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NotificationMessageSelect is the builder for selecting fields of NotificationMessage entities.
type NotificationMessageSelect struct {
	*NotificationMessageQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (nms *NotificationMessageSelect) Aggregate(fns ...AggregateFunc) *NotificationMessageSelect {
	nms.fns = append(nms.fns, fns...)
	return nms
}

// Scan applies the selector query and scans the result into the given value.
func (nms *NotificationMessageSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nms.ctx, ent.OpQuerySelect)
	if err := nms.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationMessageQuery, *NotificationMessageSelect](ctx, nms.NotificationMessageQuery, nms, nms.inters, v)
}

func (nms *NotificationMessageSelect) sqlScan(ctx context.Context, root *NotificationMessageQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(nms.fns))
	for _, fn := range nms.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*nms.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nms.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (nms *NotificationMessageSelect) Modify(modifiers ...func(s *sql.Selector)) *NotificationMessageSelect {
	nms.modifiers = append(nms.modifiers, modifiers...)
	return nms
}
