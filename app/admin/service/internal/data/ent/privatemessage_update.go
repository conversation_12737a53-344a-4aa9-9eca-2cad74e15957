// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"kratos-admin/app/admin/service/internal/data/ent/privatemessage"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PrivateMessageUpdate is the builder for updating PrivateMessage entities.
type PrivateMessageUpdate struct {
	config
	hooks     []Hook
	mutation  *PrivateMessageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the PrivateMessageUpdate builder.
func (pmu *PrivateMessageUpdate) Where(ps ...predicate.PrivateMessage) *PrivateMessageUpdate {
	pmu.mutation.Where(ps...)
	return pmu
}

// SetUpdateTime sets the "update_time" field.
func (pmu *PrivateMessageUpdate) SetUpdateTime(t time.Time) *PrivateMessageUpdate {
	pmu.mutation.SetUpdateTime(t)
	return pmu
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableUpdateTime(t *time.Time) *PrivateMessageUpdate {
	if t != nil {
		pmu.SetUpdateTime(*t)
	}
	return pmu
}

// ClearUpdateTime clears the value of the "update_time" field.
func (pmu *PrivateMessageUpdate) ClearUpdateTime() *PrivateMessageUpdate {
	pmu.mutation.ClearUpdateTime()
	return pmu
}

// SetDeleteTime sets the "delete_time" field.
func (pmu *PrivateMessageUpdate) SetDeleteTime(t time.Time) *PrivateMessageUpdate {
	pmu.mutation.SetDeleteTime(t)
	return pmu
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableDeleteTime(t *time.Time) *PrivateMessageUpdate {
	if t != nil {
		pmu.SetDeleteTime(*t)
	}
	return pmu
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (pmu *PrivateMessageUpdate) ClearDeleteTime() *PrivateMessageUpdate {
	pmu.mutation.ClearDeleteTime()
	return pmu
}

// SetSubject sets the "subject" field.
func (pmu *PrivateMessageUpdate) SetSubject(s string) *PrivateMessageUpdate {
	pmu.mutation.SetSubject(s)
	return pmu
}

// SetNillableSubject sets the "subject" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableSubject(s *string) *PrivateMessageUpdate {
	if s != nil {
		pmu.SetSubject(*s)
	}
	return pmu
}

// ClearSubject clears the value of the "subject" field.
func (pmu *PrivateMessageUpdate) ClearSubject() *PrivateMessageUpdate {
	pmu.mutation.ClearSubject()
	return pmu
}

// SetContent sets the "content" field.
func (pmu *PrivateMessageUpdate) SetContent(s string) *PrivateMessageUpdate {
	pmu.mutation.SetContent(s)
	return pmu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableContent(s *string) *PrivateMessageUpdate {
	if s != nil {
		pmu.SetContent(*s)
	}
	return pmu
}

// ClearContent clears the value of the "content" field.
func (pmu *PrivateMessageUpdate) ClearContent() *PrivateMessageUpdate {
	pmu.mutation.ClearContent()
	return pmu
}

// SetStatus sets the "status" field.
func (pmu *PrivateMessageUpdate) SetStatus(pr privatemessage.Status) *PrivateMessageUpdate {
	pmu.mutation.SetStatus(pr)
	return pmu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableStatus(pr *privatemessage.Status) *PrivateMessageUpdate {
	if pr != nil {
		pmu.SetStatus(*pr)
	}
	return pmu
}

// ClearStatus clears the value of the "status" field.
func (pmu *PrivateMessageUpdate) ClearStatus() *PrivateMessageUpdate {
	pmu.mutation.ClearStatus()
	return pmu
}

// SetSenderID sets the "sender_id" field.
func (pmu *PrivateMessageUpdate) SetSenderID(u uint32) *PrivateMessageUpdate {
	pmu.mutation.ResetSenderID()
	pmu.mutation.SetSenderID(u)
	return pmu
}

// SetNillableSenderID sets the "sender_id" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableSenderID(u *uint32) *PrivateMessageUpdate {
	if u != nil {
		pmu.SetSenderID(*u)
	}
	return pmu
}

// AddSenderID adds u to the "sender_id" field.
func (pmu *PrivateMessageUpdate) AddSenderID(u int32) *PrivateMessageUpdate {
	pmu.mutation.AddSenderID(u)
	return pmu
}

// ClearSenderID clears the value of the "sender_id" field.
func (pmu *PrivateMessageUpdate) ClearSenderID() *PrivateMessageUpdate {
	pmu.mutation.ClearSenderID()
	return pmu
}

// SetReceiverID sets the "receiver_id" field.
func (pmu *PrivateMessageUpdate) SetReceiverID(u uint32) *PrivateMessageUpdate {
	pmu.mutation.ResetReceiverID()
	pmu.mutation.SetReceiverID(u)
	return pmu
}

// SetNillableReceiverID sets the "receiver_id" field if the given value is not nil.
func (pmu *PrivateMessageUpdate) SetNillableReceiverID(u *uint32) *PrivateMessageUpdate {
	if u != nil {
		pmu.SetReceiverID(*u)
	}
	return pmu
}

// AddReceiverID adds u to the "receiver_id" field.
func (pmu *PrivateMessageUpdate) AddReceiverID(u int32) *PrivateMessageUpdate {
	pmu.mutation.AddReceiverID(u)
	return pmu
}

// ClearReceiverID clears the value of the "receiver_id" field.
func (pmu *PrivateMessageUpdate) ClearReceiverID() *PrivateMessageUpdate {
	pmu.mutation.ClearReceiverID()
	return pmu
}

// Mutation returns the PrivateMessageMutation object of the builder.
func (pmu *PrivateMessageUpdate) Mutation() *PrivateMessageMutation {
	return pmu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pmu *PrivateMessageUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, pmu.sqlSave, pmu.mutation, pmu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pmu *PrivateMessageUpdate) SaveX(ctx context.Context) int {
	affected, err := pmu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pmu *PrivateMessageUpdate) Exec(ctx context.Context) error {
	_, err := pmu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pmu *PrivateMessageUpdate) ExecX(ctx context.Context) {
	if err := pmu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pmu *PrivateMessageUpdate) check() error {
	if v, ok := pmu.mutation.Status(); ok {
		if err := privatemessage.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PrivateMessage.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (pmu *PrivateMessageUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PrivateMessageUpdate {
	pmu.modifiers = append(pmu.modifiers, modifiers...)
	return pmu
}

func (pmu *PrivateMessageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := pmu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(privatemessage.Table, privatemessage.Columns, sqlgraph.NewFieldSpec(privatemessage.FieldID, field.TypeUint32))
	if ps := pmu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if pmu.mutation.CreateTimeCleared() {
		_spec.ClearField(privatemessage.FieldCreateTime, field.TypeTime)
	}
	if value, ok := pmu.mutation.UpdateTime(); ok {
		_spec.SetField(privatemessage.FieldUpdateTime, field.TypeTime, value)
	}
	if pmu.mutation.UpdateTimeCleared() {
		_spec.ClearField(privatemessage.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := pmu.mutation.DeleteTime(); ok {
		_spec.SetField(privatemessage.FieldDeleteTime, field.TypeTime, value)
	}
	if pmu.mutation.DeleteTimeCleared() {
		_spec.ClearField(privatemessage.FieldDeleteTime, field.TypeTime)
	}
	if pmu.mutation.TenantIDCleared() {
		_spec.ClearField(privatemessage.FieldTenantID, field.TypeUint32)
	}
	if value, ok := pmu.mutation.Subject(); ok {
		_spec.SetField(privatemessage.FieldSubject, field.TypeString, value)
	}
	if pmu.mutation.SubjectCleared() {
		_spec.ClearField(privatemessage.FieldSubject, field.TypeString)
	}
	if value, ok := pmu.mutation.Content(); ok {
		_spec.SetField(privatemessage.FieldContent, field.TypeString, value)
	}
	if pmu.mutation.ContentCleared() {
		_spec.ClearField(privatemessage.FieldContent, field.TypeString)
	}
	if value, ok := pmu.mutation.Status(); ok {
		_spec.SetField(privatemessage.FieldStatus, field.TypeEnum, value)
	}
	if pmu.mutation.StatusCleared() {
		_spec.ClearField(privatemessage.FieldStatus, field.TypeEnum)
	}
	if value, ok := pmu.mutation.SenderID(); ok {
		_spec.SetField(privatemessage.FieldSenderID, field.TypeUint32, value)
	}
	if value, ok := pmu.mutation.AddedSenderID(); ok {
		_spec.AddField(privatemessage.FieldSenderID, field.TypeUint32, value)
	}
	if pmu.mutation.SenderIDCleared() {
		_spec.ClearField(privatemessage.FieldSenderID, field.TypeUint32)
	}
	if value, ok := pmu.mutation.ReceiverID(); ok {
		_spec.SetField(privatemessage.FieldReceiverID, field.TypeUint32, value)
	}
	if value, ok := pmu.mutation.AddedReceiverID(); ok {
		_spec.AddField(privatemessage.FieldReceiverID, field.TypeUint32, value)
	}
	if pmu.mutation.ReceiverIDCleared() {
		_spec.ClearField(privatemessage.FieldReceiverID, field.TypeUint32)
	}
	_spec.AddModifiers(pmu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, pmu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{privatemessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pmu.mutation.done = true
	return n, nil
}

// PrivateMessageUpdateOne is the builder for updating a single PrivateMessage entity.
type PrivateMessageUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *PrivateMessageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (pmuo *PrivateMessageUpdateOne) SetUpdateTime(t time.Time) *PrivateMessageUpdateOne {
	pmuo.mutation.SetUpdateTime(t)
	return pmuo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableUpdateTime(t *time.Time) *PrivateMessageUpdateOne {
	if t != nil {
		pmuo.SetUpdateTime(*t)
	}
	return pmuo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (pmuo *PrivateMessageUpdateOne) ClearUpdateTime() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearUpdateTime()
	return pmuo
}

// SetDeleteTime sets the "delete_time" field.
func (pmuo *PrivateMessageUpdateOne) SetDeleteTime(t time.Time) *PrivateMessageUpdateOne {
	pmuo.mutation.SetDeleteTime(t)
	return pmuo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableDeleteTime(t *time.Time) *PrivateMessageUpdateOne {
	if t != nil {
		pmuo.SetDeleteTime(*t)
	}
	return pmuo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (pmuo *PrivateMessageUpdateOne) ClearDeleteTime() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearDeleteTime()
	return pmuo
}

// SetSubject sets the "subject" field.
func (pmuo *PrivateMessageUpdateOne) SetSubject(s string) *PrivateMessageUpdateOne {
	pmuo.mutation.SetSubject(s)
	return pmuo
}

// SetNillableSubject sets the "subject" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableSubject(s *string) *PrivateMessageUpdateOne {
	if s != nil {
		pmuo.SetSubject(*s)
	}
	return pmuo
}

// ClearSubject clears the value of the "subject" field.
func (pmuo *PrivateMessageUpdateOne) ClearSubject() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearSubject()
	return pmuo
}

// SetContent sets the "content" field.
func (pmuo *PrivateMessageUpdateOne) SetContent(s string) *PrivateMessageUpdateOne {
	pmuo.mutation.SetContent(s)
	return pmuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableContent(s *string) *PrivateMessageUpdateOne {
	if s != nil {
		pmuo.SetContent(*s)
	}
	return pmuo
}

// ClearContent clears the value of the "content" field.
func (pmuo *PrivateMessageUpdateOne) ClearContent() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearContent()
	return pmuo
}

// SetStatus sets the "status" field.
func (pmuo *PrivateMessageUpdateOne) SetStatus(pr privatemessage.Status) *PrivateMessageUpdateOne {
	pmuo.mutation.SetStatus(pr)
	return pmuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableStatus(pr *privatemessage.Status) *PrivateMessageUpdateOne {
	if pr != nil {
		pmuo.SetStatus(*pr)
	}
	return pmuo
}

// ClearStatus clears the value of the "status" field.
func (pmuo *PrivateMessageUpdateOne) ClearStatus() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearStatus()
	return pmuo
}

// SetSenderID sets the "sender_id" field.
func (pmuo *PrivateMessageUpdateOne) SetSenderID(u uint32) *PrivateMessageUpdateOne {
	pmuo.mutation.ResetSenderID()
	pmuo.mutation.SetSenderID(u)
	return pmuo
}

// SetNillableSenderID sets the "sender_id" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableSenderID(u *uint32) *PrivateMessageUpdateOne {
	if u != nil {
		pmuo.SetSenderID(*u)
	}
	return pmuo
}

// AddSenderID adds u to the "sender_id" field.
func (pmuo *PrivateMessageUpdateOne) AddSenderID(u int32) *PrivateMessageUpdateOne {
	pmuo.mutation.AddSenderID(u)
	return pmuo
}

// ClearSenderID clears the value of the "sender_id" field.
func (pmuo *PrivateMessageUpdateOne) ClearSenderID() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearSenderID()
	return pmuo
}

// SetReceiverID sets the "receiver_id" field.
func (pmuo *PrivateMessageUpdateOne) SetReceiverID(u uint32) *PrivateMessageUpdateOne {
	pmuo.mutation.ResetReceiverID()
	pmuo.mutation.SetReceiverID(u)
	return pmuo
}

// SetNillableReceiverID sets the "receiver_id" field if the given value is not nil.
func (pmuo *PrivateMessageUpdateOne) SetNillableReceiverID(u *uint32) *PrivateMessageUpdateOne {
	if u != nil {
		pmuo.SetReceiverID(*u)
	}
	return pmuo
}

// AddReceiverID adds u to the "receiver_id" field.
func (pmuo *PrivateMessageUpdateOne) AddReceiverID(u int32) *PrivateMessageUpdateOne {
	pmuo.mutation.AddReceiverID(u)
	return pmuo
}

// ClearReceiverID clears the value of the "receiver_id" field.
func (pmuo *PrivateMessageUpdateOne) ClearReceiverID() *PrivateMessageUpdateOne {
	pmuo.mutation.ClearReceiverID()
	return pmuo
}

// Mutation returns the PrivateMessageMutation object of the builder.
func (pmuo *PrivateMessageUpdateOne) Mutation() *PrivateMessageMutation {
	return pmuo.mutation
}

// Where appends a list predicates to the PrivateMessageUpdate builder.
func (pmuo *PrivateMessageUpdateOne) Where(ps ...predicate.PrivateMessage) *PrivateMessageUpdateOne {
	pmuo.mutation.Where(ps...)
	return pmuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (pmuo *PrivateMessageUpdateOne) Select(field string, fields ...string) *PrivateMessageUpdateOne {
	pmuo.fields = append([]string{field}, fields...)
	return pmuo
}

// Save executes the query and returns the updated PrivateMessage entity.
func (pmuo *PrivateMessageUpdateOne) Save(ctx context.Context) (*PrivateMessage, error) {
	return withHooks(ctx, pmuo.sqlSave, pmuo.mutation, pmuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pmuo *PrivateMessageUpdateOne) SaveX(ctx context.Context) *PrivateMessage {
	node, err := pmuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (pmuo *PrivateMessageUpdateOne) Exec(ctx context.Context) error {
	_, err := pmuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pmuo *PrivateMessageUpdateOne) ExecX(ctx context.Context) {
	if err := pmuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pmuo *PrivateMessageUpdateOne) check() error {
	if v, ok := pmuo.mutation.Status(); ok {
		if err := privatemessage.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PrivateMessage.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (pmuo *PrivateMessageUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PrivateMessageUpdateOne {
	pmuo.modifiers = append(pmuo.modifiers, modifiers...)
	return pmuo
}

func (pmuo *PrivateMessageUpdateOne) sqlSave(ctx context.Context) (_node *PrivateMessage, err error) {
	if err := pmuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(privatemessage.Table, privatemessage.Columns, sqlgraph.NewFieldSpec(privatemessage.FieldID, field.TypeUint32))
	id, ok := pmuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "PrivateMessage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := pmuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, privatemessage.FieldID)
		for _, f := range fields {
			if !privatemessage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != privatemessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := pmuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if pmuo.mutation.CreateTimeCleared() {
		_spec.ClearField(privatemessage.FieldCreateTime, field.TypeTime)
	}
	if value, ok := pmuo.mutation.UpdateTime(); ok {
		_spec.SetField(privatemessage.FieldUpdateTime, field.TypeTime, value)
	}
	if pmuo.mutation.UpdateTimeCleared() {
		_spec.ClearField(privatemessage.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := pmuo.mutation.DeleteTime(); ok {
		_spec.SetField(privatemessage.FieldDeleteTime, field.TypeTime, value)
	}
	if pmuo.mutation.DeleteTimeCleared() {
		_spec.ClearField(privatemessage.FieldDeleteTime, field.TypeTime)
	}
	if pmuo.mutation.TenantIDCleared() {
		_spec.ClearField(privatemessage.FieldTenantID, field.TypeUint32)
	}
	if value, ok := pmuo.mutation.Subject(); ok {
		_spec.SetField(privatemessage.FieldSubject, field.TypeString, value)
	}
	if pmuo.mutation.SubjectCleared() {
		_spec.ClearField(privatemessage.FieldSubject, field.TypeString)
	}
	if value, ok := pmuo.mutation.Content(); ok {
		_spec.SetField(privatemessage.FieldContent, field.TypeString, value)
	}
	if pmuo.mutation.ContentCleared() {
		_spec.ClearField(privatemessage.FieldContent, field.TypeString)
	}
	if value, ok := pmuo.mutation.Status(); ok {
		_spec.SetField(privatemessage.FieldStatus, field.TypeEnum, value)
	}
	if pmuo.mutation.StatusCleared() {
		_spec.ClearField(privatemessage.FieldStatus, field.TypeEnum)
	}
	if value, ok := pmuo.mutation.SenderID(); ok {
		_spec.SetField(privatemessage.FieldSenderID, field.TypeUint32, value)
	}
	if value, ok := pmuo.mutation.AddedSenderID(); ok {
		_spec.AddField(privatemessage.FieldSenderID, field.TypeUint32, value)
	}
	if pmuo.mutation.SenderIDCleared() {
		_spec.ClearField(privatemessage.FieldSenderID, field.TypeUint32)
	}
	if value, ok := pmuo.mutation.ReceiverID(); ok {
		_spec.SetField(privatemessage.FieldReceiverID, field.TypeUint32, value)
	}
	if value, ok := pmuo.mutation.AddedReceiverID(); ok {
		_spec.AddField(privatemessage.FieldReceiverID, field.TypeUint32, value)
	}
	if pmuo.mutation.ReceiverIDCleared() {
		_spec.ClearField(privatemessage.FieldReceiverID, field.TypeUint32)
	}
	_spec.AddModifiers(pmuo.modifiers...)
	_node = &PrivateMessage{config: pmuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, pmuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{privatemessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	pmuo.mutation.done = true
	return _node, nil
}
