// Code generated by ent, DO NOT EDIT.

package privacy

import (
	"context"

	"kratos-admin/app/admin/service/internal/data/ent"

	"entgo.io/ent/entql"
	"entgo.io/ent/privacy"
)

var (
	// Allow may be returned by rules to indicate that the policy
	// evaluation should terminate with allow decision.
	Allow = privacy.Allow

	// Deny may be returned by rules to indicate that the policy
	// evaluation should terminate with deny decision.
	Deny = privacy.Deny

	// Skip may be returned by rules to indicate that the policy
	// evaluation should continue to the next rule.
	Skip = privacy.Skip
)

// Allowf returns a formatted wrapped Allow decision.
func Allowf(format string, a ...any) error {
	return privacy.Allowf(format, a...)
}

// Denyf returns a formatted wrapped Deny decision.
func Denyf(format string, a ...any) error {
	return privacy.Denyf(format, a...)
}

// <PERSON><PERSON> returns a formatted wrapped Skip decision.
func Skipf(format string, a ...any) error {
	return privacy.Skipf(format, a...)
}

// DecisionContext creates a new context from the given parent context with
// a policy decision attach to it.
func DecisionContext(parent context.Context, decision error) context.Context {
	return privacy.DecisionContext(parent, decision)
}

// DecisionFromContext retrieves the policy decision from the context.
func DecisionFromContext(ctx context.Context) (error, bool) {
	return privacy.DecisionFromContext(ctx)
}

type (
	// Policy groups query and mutation policies.
	Policy = privacy.Policy

	// QueryRule defines the interface deciding whether a
	// query is allowed and optionally modify it.
	QueryRule = privacy.QueryRule
	// QueryPolicy combines multiple query rules into a single policy.
	QueryPolicy = privacy.QueryPolicy

	// MutationRule defines the interface which decides whether a
	// mutation is allowed and optionally modifies it.
	MutationRule = privacy.MutationRule
	// MutationPolicy combines multiple mutation rules into a single policy.
	MutationPolicy = privacy.MutationPolicy
	// MutationRuleFunc type is an adapter which allows the use of
	// ordinary functions as mutation rules.
	MutationRuleFunc = privacy.MutationRuleFunc

	// QueryMutationRule is an interface which groups query and mutation rules.
	QueryMutationRule = privacy.QueryMutationRule
)

// QueryRuleFunc type is an adapter to allow the use of
// ordinary functions as query rules.
type QueryRuleFunc func(context.Context, ent.Query) error

// Eval returns f(ctx, q).
func (f QueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	return f(ctx, q)
}

// AlwaysAllowRule returns a rule that returns an allow decision.
func AlwaysAllowRule() QueryMutationRule {
	return privacy.AlwaysAllowRule()
}

// AlwaysDenyRule returns a rule that returns a deny decision.
func AlwaysDenyRule() QueryMutationRule {
	return privacy.AlwaysDenyRule()
}

// ContextQueryMutationRule creates a query/mutation rule from a context eval func.
func ContextQueryMutationRule(eval func(context.Context) error) QueryMutationRule {
	return privacy.ContextQueryMutationRule(eval)
}

// OnMutationOperation evaluates the given rule only on a given mutation operation.
func OnMutationOperation(rule MutationRule, op ent.Op) MutationRule {
	return privacy.OnMutationOperation(rule, op)
}

// DenyMutationOperationRule returns a rule denying specified mutation operation.
func DenyMutationOperationRule(op ent.Op) MutationRule {
	rule := MutationRuleFunc(func(_ context.Context, m ent.Mutation) error {
		return Denyf("ent/privacy: operation %s is not allowed", m.Op())
	})
	return OnMutationOperation(rule, op)
}

// The AdminLoginLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AdminLoginLogQueryRuleFunc func(context.Context, *ent.AdminLoginLogQuery) error

// EvalQuery return f(ctx, q).
func (f AdminLoginLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AdminLoginLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AdminLoginLogQuery", q)
}

// The AdminLoginLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AdminLoginLogMutationRuleFunc func(context.Context, *ent.AdminLoginLogMutation) error

// EvalMutation calls f(ctx, m).
func (f AdminLoginLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AdminLoginLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AdminLoginLogMutation", m)
}

// The AdminLoginRestrictionQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AdminLoginRestrictionQueryRuleFunc func(context.Context, *ent.AdminLoginRestrictionQuery) error

// EvalQuery return f(ctx, q).
func (f AdminLoginRestrictionQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AdminLoginRestrictionQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AdminLoginRestrictionQuery", q)
}

// The AdminLoginRestrictionMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AdminLoginRestrictionMutationRuleFunc func(context.Context, *ent.AdminLoginRestrictionMutation) error

// EvalMutation calls f(ctx, m).
func (f AdminLoginRestrictionMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AdminLoginRestrictionMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AdminLoginRestrictionMutation", m)
}

// The AdminOperationLogQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type AdminOperationLogQueryRuleFunc func(context.Context, *ent.AdminOperationLogQuery) error

// EvalQuery return f(ctx, q).
func (f AdminOperationLogQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AdminOperationLogQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.AdminOperationLogQuery", q)
}

// The AdminOperationLogMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type AdminOperationLogMutationRuleFunc func(context.Context, *ent.AdminOperationLogMutation) error

// EvalMutation calls f(ctx, m).
func (f AdminOperationLogMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.AdminOperationLogMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.AdminOperationLogMutation", m)
}

// The ApiResourceQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type ApiResourceQueryRuleFunc func(context.Context, *ent.ApiResourceQuery) error

// EvalQuery return f(ctx, q).
func (f ApiResourceQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ApiResourceQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.ApiResourceQuery", q)
}

// The ApiResourceMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type ApiResourceMutationRuleFunc func(context.Context, *ent.ApiResourceMutation) error

// EvalMutation calls f(ctx, m).
func (f ApiResourceMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.ApiResourceMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.ApiResourceMutation", m)
}

// The DepartmentQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type DepartmentQueryRuleFunc func(context.Context, *ent.DepartmentQuery) error

// EvalQuery return f(ctx, q).
func (f DepartmentQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DepartmentQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.DepartmentQuery", q)
}

// The DepartmentMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type DepartmentMutationRuleFunc func(context.Context, *ent.DepartmentMutation) error

// EvalMutation calls f(ctx, m).
func (f DepartmentMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.DepartmentMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.DepartmentMutation", m)
}

// The DictQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type DictQueryRuleFunc func(context.Context, *ent.DictQuery) error

// EvalQuery return f(ctx, q).
func (f DictQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DictQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.DictQuery", q)
}

// The DictMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type DictMutationRuleFunc func(context.Context, *ent.DictMutation) error

// EvalMutation calls f(ctx, m).
func (f DictMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.DictMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.DictMutation", m)
}

// The FileQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type FileQueryRuleFunc func(context.Context, *ent.FileQuery) error

// EvalQuery return f(ctx, q).
func (f FileQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.FileQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.FileQuery", q)
}

// The FileMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type FileMutationRuleFunc func(context.Context, *ent.FileMutation) error

// EvalMutation calls f(ctx, m).
func (f FileMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.FileMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.FileMutation", m)
}

// The MenuQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type MenuQueryRuleFunc func(context.Context, *ent.MenuQuery) error

// EvalQuery return f(ctx, q).
func (f MenuQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MenuQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.MenuQuery", q)
}

// The MenuMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type MenuMutationRuleFunc func(context.Context, *ent.MenuMutation) error

// EvalMutation calls f(ctx, m).
func (f MenuMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.MenuMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.MenuMutation", m)
}

// The NotificationMessageQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type NotificationMessageQueryRuleFunc func(context.Context, *ent.NotificationMessageQuery) error

// EvalQuery return f(ctx, q).
func (f NotificationMessageQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NotificationMessageQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.NotificationMessageQuery", q)
}

// The NotificationMessageMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type NotificationMessageMutationRuleFunc func(context.Context, *ent.NotificationMessageMutation) error

// EvalMutation calls f(ctx, m).
func (f NotificationMessageMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.NotificationMessageMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.NotificationMessageMutation", m)
}

// The NotificationMessageCategoryQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type NotificationMessageCategoryQueryRuleFunc func(context.Context, *ent.NotificationMessageCategoryQuery) error

// EvalQuery return f(ctx, q).
func (f NotificationMessageCategoryQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NotificationMessageCategoryQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.NotificationMessageCategoryQuery", q)
}

// The NotificationMessageCategoryMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type NotificationMessageCategoryMutationRuleFunc func(context.Context, *ent.NotificationMessageCategoryMutation) error

// EvalMutation calls f(ctx, m).
func (f NotificationMessageCategoryMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.NotificationMessageCategoryMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.NotificationMessageCategoryMutation", m)
}

// The NotificationMessageRecipientQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type NotificationMessageRecipientQueryRuleFunc func(context.Context, *ent.NotificationMessageRecipientQuery) error

// EvalQuery return f(ctx, q).
func (f NotificationMessageRecipientQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.NotificationMessageRecipientQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.NotificationMessageRecipientQuery", q)
}

// The NotificationMessageRecipientMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type NotificationMessageRecipientMutationRuleFunc func(context.Context, *ent.NotificationMessageRecipientMutation) error

// EvalMutation calls f(ctx, m).
func (f NotificationMessageRecipientMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.NotificationMessageRecipientMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.NotificationMessageRecipientMutation", m)
}

// The OrganizationQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type OrganizationQueryRuleFunc func(context.Context, *ent.OrganizationQuery) error

// EvalQuery return f(ctx, q).
func (f OrganizationQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OrganizationQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.OrganizationQuery", q)
}

// The OrganizationMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type OrganizationMutationRuleFunc func(context.Context, *ent.OrganizationMutation) error

// EvalMutation calls f(ctx, m).
func (f OrganizationMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.OrganizationMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.OrganizationMutation", m)
}

// The PositionQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type PositionQueryRuleFunc func(context.Context, *ent.PositionQuery) error

// EvalQuery return f(ctx, q).
func (f PositionQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PositionQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.PositionQuery", q)
}

// The PositionMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type PositionMutationRuleFunc func(context.Context, *ent.PositionMutation) error

// EvalMutation calls f(ctx, m).
func (f PositionMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.PositionMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.PositionMutation", m)
}

// The PrivateMessageQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type PrivateMessageQueryRuleFunc func(context.Context, *ent.PrivateMessageQuery) error

// EvalQuery return f(ctx, q).
func (f PrivateMessageQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PrivateMessageQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.PrivateMessageQuery", q)
}

// The PrivateMessageMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type PrivateMessageMutationRuleFunc func(context.Context, *ent.PrivateMessageMutation) error

// EvalMutation calls f(ctx, m).
func (f PrivateMessageMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.PrivateMessageMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.PrivateMessageMutation", m)
}

// The RoleQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type RoleQueryRuleFunc func(context.Context, *ent.RoleQuery) error

// EvalQuery return f(ctx, q).
func (f RoleQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.RoleQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.RoleQuery", q)
}

// The RoleMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type RoleMutationRuleFunc func(context.Context, *ent.RoleMutation) error

// EvalMutation calls f(ctx, m).
func (f RoleMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.RoleMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.RoleMutation", m)
}

// The TaskQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type TaskQueryRuleFunc func(context.Context, *ent.TaskQuery) error

// EvalQuery return f(ctx, q).
func (f TaskQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TaskQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.TaskQuery", q)
}

// The TaskMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type TaskMutationRuleFunc func(context.Context, *ent.TaskMutation) error

// EvalMutation calls f(ctx, m).
func (f TaskMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.TaskMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.TaskMutation", m)
}

// The TenantQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type TenantQueryRuleFunc func(context.Context, *ent.TenantQuery) error

// EvalQuery return f(ctx, q).
func (f TenantQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TenantQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.TenantQuery", q)
}

// The TenantMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type TenantMutationRuleFunc func(context.Context, *ent.TenantMutation) error

// EvalMutation calls f(ctx, m).
func (f TenantMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.TenantMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.TenantMutation", m)
}

// The UserQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type UserQueryRuleFunc func(context.Context, *ent.UserQuery) error

// EvalQuery return f(ctx, q).
func (f UserQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.UserQuery", q)
}

// The UserMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type UserMutationRuleFunc func(context.Context, *ent.UserMutation) error

// EvalMutation calls f(ctx, m).
func (f UserMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.UserMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.UserMutation", m)
}

// The UserCredentialQueryRuleFunc type is an adapter to allow the use of ordinary
// functions as a query rule.
type UserCredentialQueryRuleFunc func(context.Context, *ent.UserCredentialQuery) error

// EvalQuery return f(ctx, q).
func (f UserCredentialQueryRuleFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserCredentialQuery); ok {
		return f(ctx, q)
	}
	return Denyf("ent/privacy: unexpected query type %T, expect *ent.UserCredentialQuery", q)
}

// The UserCredentialMutationRuleFunc type is an adapter to allow the use of ordinary
// functions as a mutation rule.
type UserCredentialMutationRuleFunc func(context.Context, *ent.UserCredentialMutation) error

// EvalMutation calls f(ctx, m).
func (f UserCredentialMutationRuleFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	if m, ok := m.(*ent.UserCredentialMutation); ok {
		return f(ctx, m)
	}
	return Denyf("ent/privacy: unexpected mutation type %T, expect *ent.UserCredentialMutation", m)
}

type (
	// Filter is the interface that wraps the Where function
	// for filtering nodes in queries and mutations.
	Filter interface {
		// Where applies a filter on the executed query/mutation.
		Where(entql.P)
	}

	// The FilterFunc type is an adapter that allows the use of ordinary
	// functions as filters for query and mutation types.
	FilterFunc func(context.Context, Filter) error
)

// EvalQuery calls f(ctx, q) if the query implements the Filter interface, otherwise it is denied.
func (f FilterFunc) EvalQuery(ctx context.Context, q ent.Query) error {
	fr, err := queryFilter(q)
	if err != nil {
		return err
	}
	return f(ctx, fr)
}

// EvalMutation calls f(ctx, q) if the mutation implements the Filter interface, otherwise it is denied.
func (f FilterFunc) EvalMutation(ctx context.Context, m ent.Mutation) error {
	fr, err := mutationFilter(m)
	if err != nil {
		return err
	}
	return f(ctx, fr)
}

var _ QueryMutationRule = FilterFunc(nil)

func queryFilter(q ent.Query) (Filter, error) {
	switch q := q.(type) {
	case *ent.AdminLoginLogQuery:
		return q.Filter(), nil
	case *ent.AdminLoginRestrictionQuery:
		return q.Filter(), nil
	case *ent.AdminOperationLogQuery:
		return q.Filter(), nil
	case *ent.ApiResourceQuery:
		return q.Filter(), nil
	case *ent.DepartmentQuery:
		return q.Filter(), nil
	case *ent.DictQuery:
		return q.Filter(), nil
	case *ent.FileQuery:
		return q.Filter(), nil
	case *ent.MenuQuery:
		return q.Filter(), nil
	case *ent.NotificationMessageQuery:
		return q.Filter(), nil
	case *ent.NotificationMessageCategoryQuery:
		return q.Filter(), nil
	case *ent.NotificationMessageRecipientQuery:
		return q.Filter(), nil
	case *ent.OrganizationQuery:
		return q.Filter(), nil
	case *ent.PositionQuery:
		return q.Filter(), nil
	case *ent.PrivateMessageQuery:
		return q.Filter(), nil
	case *ent.RoleQuery:
		return q.Filter(), nil
	case *ent.TaskQuery:
		return q.Filter(), nil
	case *ent.TenantQuery:
		return q.Filter(), nil
	case *ent.UserQuery:
		return q.Filter(), nil
	case *ent.UserCredentialQuery:
		return q.Filter(), nil
	default:
		return nil, Denyf("ent/privacy: unexpected query type %T for query filter", q)
	}
}

func mutationFilter(m ent.Mutation) (Filter, error) {
	switch m := m.(type) {
	case *ent.AdminLoginLogMutation:
		return m.Filter(), nil
	case *ent.AdminLoginRestrictionMutation:
		return m.Filter(), nil
	case *ent.AdminOperationLogMutation:
		return m.Filter(), nil
	case *ent.ApiResourceMutation:
		return m.Filter(), nil
	case *ent.DepartmentMutation:
		return m.Filter(), nil
	case *ent.DictMutation:
		return m.Filter(), nil
	case *ent.FileMutation:
		return m.Filter(), nil
	case *ent.MenuMutation:
		return m.Filter(), nil
	case *ent.NotificationMessageMutation:
		return m.Filter(), nil
	case *ent.NotificationMessageCategoryMutation:
		return m.Filter(), nil
	case *ent.NotificationMessageRecipientMutation:
		return m.Filter(), nil
	case *ent.OrganizationMutation:
		return m.Filter(), nil
	case *ent.PositionMutation:
		return m.Filter(), nil
	case *ent.PrivateMessageMutation:
		return m.Filter(), nil
	case *ent.RoleMutation:
		return m.Filter(), nil
	case *ent.TaskMutation:
		return m.Filter(), nil
	case *ent.TenantMutation:
		return m.Filter(), nil
	case *ent.UserMutation:
		return m.Filter(), nil
	case *ent.UserCredentialMutation:
		return m.Filter(), nil
	default:
		return nil, Denyf("ent/privacy: unexpected mutation type %T for mutation filter", m)
	}
}
