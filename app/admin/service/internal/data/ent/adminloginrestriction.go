// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 后台登录限制表
type AdminLoginRestriction struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 创建者ID
	CreateBy *uint32 `json:"create_by,omitempty"`
	// 更新者ID
	UpdateBy *uint32 `json:"update_by,omitempty"`
	// 目标用户ID
	TargetID *uint32 `json:"target_id,omitempty"`
	// 限制值（如IP地址、MAC地址或地区代码）
	Value *string `json:"value,omitempty"`
	// 限制原因
	Reason *string `json:"reason,omitempty"`
	// 限制类型
	Type *adminloginrestriction.Type `json:"type,omitempty"`
	// 限制方式
	Method       *adminloginrestriction.Method `json:"method,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AdminLoginRestriction) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case adminloginrestriction.FieldID, adminloginrestriction.FieldCreateBy, adminloginrestriction.FieldUpdateBy, adminloginrestriction.FieldTargetID:
			values[i] = new(sql.NullInt64)
		case adminloginrestriction.FieldValue, adminloginrestriction.FieldReason, adminloginrestriction.FieldType, adminloginrestriction.FieldMethod:
			values[i] = new(sql.NullString)
		case adminloginrestriction.FieldCreateTime, adminloginrestriction.FieldUpdateTime, adminloginrestriction.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AdminLoginRestriction fields.
func (alr *AdminLoginRestriction) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case adminloginrestriction.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			alr.ID = uint32(value.Int64)
		case adminloginrestriction.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				alr.CreateTime = new(time.Time)
				*alr.CreateTime = value.Time
			}
		case adminloginrestriction.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				alr.UpdateTime = new(time.Time)
				*alr.UpdateTime = value.Time
			}
		case adminloginrestriction.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				alr.DeleteTime = new(time.Time)
				*alr.DeleteTime = value.Time
			}
		case adminloginrestriction.FieldCreateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_by", values[i])
			} else if value.Valid {
				alr.CreateBy = new(uint32)
				*alr.CreateBy = uint32(value.Int64)
			}
		case adminloginrestriction.FieldUpdateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field update_by", values[i])
			} else if value.Valid {
				alr.UpdateBy = new(uint32)
				*alr.UpdateBy = uint32(value.Int64)
			}
		case adminloginrestriction.FieldTargetID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field target_id", values[i])
			} else if value.Valid {
				alr.TargetID = new(uint32)
				*alr.TargetID = uint32(value.Int64)
			}
		case adminloginrestriction.FieldValue:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field value", values[i])
			} else if value.Valid {
				alr.Value = new(string)
				*alr.Value = value.String
			}
		case adminloginrestriction.FieldReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reason", values[i])
			} else if value.Valid {
				alr.Reason = new(string)
				*alr.Reason = value.String
			}
		case adminloginrestriction.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				alr.Type = new(adminloginrestriction.Type)
				*alr.Type = adminloginrestriction.Type(value.String)
			}
		case adminloginrestriction.FieldMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field method", values[i])
			} else if value.Valid {
				alr.Method = new(adminloginrestriction.Method)
				*alr.Method = adminloginrestriction.Method(value.String)
			}
		default:
			alr.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// GetValue returns the ent.Value that was dynamically selected and assigned to the AdminLoginRestriction.
// This includes values selected through modifiers, order, etc.
func (alr *AdminLoginRestriction) GetValue(name string) (ent.Value, error) {
	return alr.selectValues.Get(name)
}

// Update returns a builder for updating this AdminLoginRestriction.
// Note that you need to call AdminLoginRestriction.Unwrap() before calling this method if this AdminLoginRestriction
// was returned from a transaction, and the transaction was committed or rolled back.
func (alr *AdminLoginRestriction) Update() *AdminLoginRestrictionUpdateOne {
	return NewAdminLoginRestrictionClient(alr.config).UpdateOne(alr)
}

// Unwrap unwraps the AdminLoginRestriction entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (alr *AdminLoginRestriction) Unwrap() *AdminLoginRestriction {
	_tx, ok := alr.config.driver.(*txDriver)
	if !ok {
		panic("ent: AdminLoginRestriction is not a transactional entity")
	}
	alr.config.driver = _tx.drv
	return alr
}

// String implements the fmt.Stringer.
func (alr *AdminLoginRestriction) String() string {
	var builder strings.Builder
	builder.WriteString("AdminLoginRestriction(")
	builder.WriteString(fmt.Sprintf("id=%v, ", alr.ID))
	if v := alr.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := alr.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := alr.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := alr.CreateBy; v != nil {
		builder.WriteString("create_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := alr.UpdateBy; v != nil {
		builder.WriteString("update_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := alr.TargetID; v != nil {
		builder.WriteString("target_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := alr.Value; v != nil {
		builder.WriteString("value=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := alr.Reason; v != nil {
		builder.WriteString("reason=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := alr.Type; v != nil {
		builder.WriteString("type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := alr.Method; v != nil {
		builder.WriteString("method=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// AdminLoginRestrictions is a parsable slice of AdminLoginRestriction.
type AdminLoginRestrictions []*AdminLoginRestriction
