// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/privatemessage"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 站内信私信消息表
type PrivateMessage struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 租户ID
	TenantID *uint32 `json:"tenant_id,omitempty"`
	// 主题
	Subject *string `json:"subject,omitempty"`
	// 内容
	Content *string `json:"content,omitempty"`
	// 消息状态
	Status *privatemessage.Status `json:"status,omitempty"`
	// 发送者用户ID
	SenderID *uint32 `json:"sender_id,omitempty"`
	// 接收者用户ID
	ReceiverID   *uint32 `json:"receiver_id,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PrivateMessage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case privatemessage.FieldID, privatemessage.FieldTenantID, privatemessage.FieldSenderID, privatemessage.FieldReceiverID:
			values[i] = new(sql.NullInt64)
		case privatemessage.FieldSubject, privatemessage.FieldContent, privatemessage.FieldStatus:
			values[i] = new(sql.NullString)
		case privatemessage.FieldCreateTime, privatemessage.FieldUpdateTime, privatemessage.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PrivateMessage fields.
func (pm *PrivateMessage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case privatemessage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			pm.ID = uint32(value.Int64)
		case privatemessage.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				pm.CreateTime = new(time.Time)
				*pm.CreateTime = value.Time
			}
		case privatemessage.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				pm.UpdateTime = new(time.Time)
				*pm.UpdateTime = value.Time
			}
		case privatemessage.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				pm.DeleteTime = new(time.Time)
				*pm.DeleteTime = value.Time
			}
		case privatemessage.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				pm.TenantID = new(uint32)
				*pm.TenantID = uint32(value.Int64)
			}
		case privatemessage.FieldSubject:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field subject", values[i])
			} else if value.Valid {
				pm.Subject = new(string)
				*pm.Subject = value.String
			}
		case privatemessage.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				pm.Content = new(string)
				*pm.Content = value.String
			}
		case privatemessage.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pm.Status = new(privatemessage.Status)
				*pm.Status = privatemessage.Status(value.String)
			}
		case privatemessage.FieldSenderID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sender_id", values[i])
			} else if value.Valid {
				pm.SenderID = new(uint32)
				*pm.SenderID = uint32(value.Int64)
			}
		case privatemessage.FieldReceiverID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field receiver_id", values[i])
			} else if value.Valid {
				pm.ReceiverID = new(uint32)
				*pm.ReceiverID = uint32(value.Int64)
			}
		default:
			pm.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PrivateMessage.
// This includes values selected through modifiers, order, etc.
func (pm *PrivateMessage) Value(name string) (ent.Value, error) {
	return pm.selectValues.Get(name)
}

// Update returns a builder for updating this PrivateMessage.
// Note that you need to call PrivateMessage.Unwrap() before calling this method if this PrivateMessage
// was returned from a transaction, and the transaction was committed or rolled back.
func (pm *PrivateMessage) Update() *PrivateMessageUpdateOne {
	return NewPrivateMessageClient(pm.config).UpdateOne(pm)
}

// Unwrap unwraps the PrivateMessage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pm *PrivateMessage) Unwrap() *PrivateMessage {
	_tx, ok := pm.config.driver.(*txDriver)
	if !ok {
		panic("ent: PrivateMessage is not a transactional entity")
	}
	pm.config.driver = _tx.drv
	return pm
}

// String implements the fmt.Stringer.
func (pm *PrivateMessage) String() string {
	var builder strings.Builder
	builder.WriteString("PrivateMessage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pm.ID))
	if v := pm.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := pm.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := pm.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := pm.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := pm.Subject; v != nil {
		builder.WriteString("subject=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := pm.Content; v != nil {
		builder.WriteString("content=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := pm.Status; v != nil {
		builder.WriteString("status=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := pm.SenderID; v != nil {
		builder.WriteString("sender_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := pm.ReceiverID; v != nil {
		builder.WriteString("receiver_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// PrivateMessages is a parsable slice of PrivateMessage.
type PrivateMessages []*PrivateMessage
