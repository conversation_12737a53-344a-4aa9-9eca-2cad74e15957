// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"kratos-admin/app/admin/service/internal/data/ent/migrate"

	"kratos-admin/app/admin/service/internal/data/ent/adminloginlog"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"kratos-admin/app/admin/service/internal/data/ent/apiresource"
	"kratos-admin/app/admin/service/internal/data/ent/department"
	"kratos-admin/app/admin/service/internal/data/ent/dict"
	"kratos-admin/app/admin/service/internal/data/ent/file"
	"kratos-admin/app/admin/service/internal/data/ent/menu"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagecategory"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagerecipient"
	"kratos-admin/app/admin/service/internal/data/ent/organization"
	"kratos-admin/app/admin/service/internal/data/ent/position"
	"kratos-admin/app/admin/service/internal/data/ent/privatemessage"
	"kratos-admin/app/admin/service/internal/data/ent/role"
	"kratos-admin/app/admin/service/internal/data/ent/task"
	"kratos-admin/app/admin/service/internal/data/ent/tenant"
	"kratos-admin/app/admin/service/internal/data/ent/user"
	"kratos-admin/app/admin/service/internal/data/ent/usercredential"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// AdminLoginLog is the client for interacting with the AdminLoginLog builders.
	AdminLoginLog *AdminLoginLogClient
	// AdminLoginRestriction is the client for interacting with the AdminLoginRestriction builders.
	AdminLoginRestriction *AdminLoginRestrictionClient
	// AdminOperationLog is the client for interacting with the AdminOperationLog builders.
	AdminOperationLog *AdminOperationLogClient
	// ApiResource is the client for interacting with the ApiResource builders.
	ApiResource *ApiResourceClient
	// Department is the client for interacting with the Department builders.
	Department *DepartmentClient
	// Dict is the client for interacting with the Dict builders.
	Dict *DictClient
	// File is the client for interacting with the File builders.
	File *FileClient
	// Menu is the client for interacting with the Menu builders.
	Menu *MenuClient
	// NotificationMessage is the client for interacting with the NotificationMessage builders.
	NotificationMessage *NotificationMessageClient
	// NotificationMessageCategory is the client for interacting with the NotificationMessageCategory builders.
	NotificationMessageCategory *NotificationMessageCategoryClient
	// NotificationMessageRecipient is the client for interacting with the NotificationMessageRecipient builders.
	NotificationMessageRecipient *NotificationMessageRecipientClient
	// Organization is the client for interacting with the Organization builders.
	Organization *OrganizationClient
	// Position is the client for interacting with the Position builders.
	Position *PositionClient
	// PrivateMessage is the client for interacting with the PrivateMessage builders.
	PrivateMessage *PrivateMessageClient
	// Role is the client for interacting with the Role builders.
	Role *RoleClient
	// Task is the client for interacting with the Task builders.
	Task *TaskClient
	// Tenant is the client for interacting with the Tenant builders.
	Tenant *TenantClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// UserCredential is the client for interacting with the UserCredential builders.
	UserCredential *UserCredentialClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.AdminLoginLog = NewAdminLoginLogClient(c.config)
	c.AdminLoginRestriction = NewAdminLoginRestrictionClient(c.config)
	c.AdminOperationLog = NewAdminOperationLogClient(c.config)
	c.ApiResource = NewApiResourceClient(c.config)
	c.Department = NewDepartmentClient(c.config)
	c.Dict = NewDictClient(c.config)
	c.File = NewFileClient(c.config)
	c.Menu = NewMenuClient(c.config)
	c.NotificationMessage = NewNotificationMessageClient(c.config)
	c.NotificationMessageCategory = NewNotificationMessageCategoryClient(c.config)
	c.NotificationMessageRecipient = NewNotificationMessageRecipientClient(c.config)
	c.Organization = NewOrganizationClient(c.config)
	c.Position = NewPositionClient(c.config)
	c.PrivateMessage = NewPrivateMessageClient(c.config)
	c.Role = NewRoleClient(c.config)
	c.Task = NewTaskClient(c.config)
	c.Tenant = NewTenantClient(c.config)
	c.User = NewUserClient(c.config)
	c.UserCredential = NewUserCredentialClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                          ctx,
		config:                       cfg,
		AdminLoginLog:                NewAdminLoginLogClient(cfg),
		AdminLoginRestriction:        NewAdminLoginRestrictionClient(cfg),
		AdminOperationLog:            NewAdminOperationLogClient(cfg),
		ApiResource:                  NewApiResourceClient(cfg),
		Department:                   NewDepartmentClient(cfg),
		Dict:                         NewDictClient(cfg),
		File:                         NewFileClient(cfg),
		Menu:                         NewMenuClient(cfg),
		NotificationMessage:          NewNotificationMessageClient(cfg),
		NotificationMessageCategory:  NewNotificationMessageCategoryClient(cfg),
		NotificationMessageRecipient: NewNotificationMessageRecipientClient(cfg),
		Organization:                 NewOrganizationClient(cfg),
		Position:                     NewPositionClient(cfg),
		PrivateMessage:               NewPrivateMessageClient(cfg),
		Role:                         NewRoleClient(cfg),
		Task:                         NewTaskClient(cfg),
		Tenant:                       NewTenantClient(cfg),
		User:                         NewUserClient(cfg),
		UserCredential:               NewUserCredentialClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                          ctx,
		config:                       cfg,
		AdminLoginLog:                NewAdminLoginLogClient(cfg),
		AdminLoginRestriction:        NewAdminLoginRestrictionClient(cfg),
		AdminOperationLog:            NewAdminOperationLogClient(cfg),
		ApiResource:                  NewApiResourceClient(cfg),
		Department:                   NewDepartmentClient(cfg),
		Dict:                         NewDictClient(cfg),
		File:                         NewFileClient(cfg),
		Menu:                         NewMenuClient(cfg),
		NotificationMessage:          NewNotificationMessageClient(cfg),
		NotificationMessageCategory:  NewNotificationMessageCategoryClient(cfg),
		NotificationMessageRecipient: NewNotificationMessageRecipientClient(cfg),
		Organization:                 NewOrganizationClient(cfg),
		Position:                     NewPositionClient(cfg),
		PrivateMessage:               NewPrivateMessageClient(cfg),
		Role:                         NewRoleClient(cfg),
		Task:                         NewTaskClient(cfg),
		Tenant:                       NewTenantClient(cfg),
		User:                         NewUserClient(cfg),
		UserCredential:               NewUserCredentialClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		AdminLoginLog.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.AdminLoginLog, c.AdminLoginRestriction, c.AdminOperationLog, c.ApiResource,
		c.Department, c.Dict, c.File, c.Menu, c.NotificationMessage,
		c.NotificationMessageCategory, c.NotificationMessageRecipient, c.Organization,
		c.Position, c.PrivateMessage, c.Role, c.Task, c.Tenant, c.User,
		c.UserCredential,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.AdminLoginLog, c.AdminLoginRestriction, c.AdminOperationLog, c.ApiResource,
		c.Department, c.Dict, c.File, c.Menu, c.NotificationMessage,
		c.NotificationMessageCategory, c.NotificationMessageRecipient, c.Organization,
		c.Position, c.PrivateMessage, c.Role, c.Task, c.Tenant, c.User,
		c.UserCredential,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AdminLoginLogMutation:
		return c.AdminLoginLog.mutate(ctx, m)
	case *AdminLoginRestrictionMutation:
		return c.AdminLoginRestriction.mutate(ctx, m)
	case *AdminOperationLogMutation:
		return c.AdminOperationLog.mutate(ctx, m)
	case *ApiResourceMutation:
		return c.ApiResource.mutate(ctx, m)
	case *DepartmentMutation:
		return c.Department.mutate(ctx, m)
	case *DictMutation:
		return c.Dict.mutate(ctx, m)
	case *FileMutation:
		return c.File.mutate(ctx, m)
	case *MenuMutation:
		return c.Menu.mutate(ctx, m)
	case *NotificationMessageMutation:
		return c.NotificationMessage.mutate(ctx, m)
	case *NotificationMessageCategoryMutation:
		return c.NotificationMessageCategory.mutate(ctx, m)
	case *NotificationMessageRecipientMutation:
		return c.NotificationMessageRecipient.mutate(ctx, m)
	case *OrganizationMutation:
		return c.Organization.mutate(ctx, m)
	case *PositionMutation:
		return c.Position.mutate(ctx, m)
	case *PrivateMessageMutation:
		return c.PrivateMessage.mutate(ctx, m)
	case *RoleMutation:
		return c.Role.mutate(ctx, m)
	case *TaskMutation:
		return c.Task.mutate(ctx, m)
	case *TenantMutation:
		return c.Tenant.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	case *UserCredentialMutation:
		return c.UserCredential.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AdminLoginLogClient is a client for the AdminLoginLog schema.
type AdminLoginLogClient struct {
	config
}

// NewAdminLoginLogClient returns a client for the AdminLoginLog from the given config.
func NewAdminLoginLogClient(c config) *AdminLoginLogClient {
	return &AdminLoginLogClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `adminloginlog.Hooks(f(g(h())))`.
func (c *AdminLoginLogClient) Use(hooks ...Hook) {
	c.hooks.AdminLoginLog = append(c.hooks.AdminLoginLog, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `adminloginlog.Intercept(f(g(h())))`.
func (c *AdminLoginLogClient) Intercept(interceptors ...Interceptor) {
	c.inters.AdminLoginLog = append(c.inters.AdminLoginLog, interceptors...)
}

// Create returns a builder for creating a AdminLoginLog entity.
func (c *AdminLoginLogClient) Create() *AdminLoginLogCreate {
	mutation := newAdminLoginLogMutation(c.config, OpCreate)
	return &AdminLoginLogCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AdminLoginLog entities.
func (c *AdminLoginLogClient) CreateBulk(builders ...*AdminLoginLogCreate) *AdminLoginLogCreateBulk {
	return &AdminLoginLogCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AdminLoginLogClient) MapCreateBulk(slice any, setFunc func(*AdminLoginLogCreate, int)) *AdminLoginLogCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AdminLoginLogCreateBulk{err: fmt.Errorf("calling to AdminLoginLogClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AdminLoginLogCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AdminLoginLogCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AdminLoginLog.
func (c *AdminLoginLogClient) Update() *AdminLoginLogUpdate {
	mutation := newAdminLoginLogMutation(c.config, OpUpdate)
	return &AdminLoginLogUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AdminLoginLogClient) UpdateOne(all *AdminLoginLog) *AdminLoginLogUpdateOne {
	mutation := newAdminLoginLogMutation(c.config, OpUpdateOne, withAdminLoginLog(all))
	return &AdminLoginLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AdminLoginLogClient) UpdateOneID(id uint32) *AdminLoginLogUpdateOne {
	mutation := newAdminLoginLogMutation(c.config, OpUpdateOne, withAdminLoginLogID(id))
	return &AdminLoginLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AdminLoginLog.
func (c *AdminLoginLogClient) Delete() *AdminLoginLogDelete {
	mutation := newAdminLoginLogMutation(c.config, OpDelete)
	return &AdminLoginLogDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AdminLoginLogClient) DeleteOne(all *AdminLoginLog) *AdminLoginLogDeleteOne {
	return c.DeleteOneID(all.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AdminLoginLogClient) DeleteOneID(id uint32) *AdminLoginLogDeleteOne {
	builder := c.Delete().Where(adminloginlog.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AdminLoginLogDeleteOne{builder}
}

// Query returns a query builder for AdminLoginLog.
func (c *AdminLoginLogClient) Query() *AdminLoginLogQuery {
	return &AdminLoginLogQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAdminLoginLog},
		inters: c.Interceptors(),
	}
}

// Get returns a AdminLoginLog entity by its id.
func (c *AdminLoginLogClient) Get(ctx context.Context, id uint32) (*AdminLoginLog, error) {
	return c.Query().Where(adminloginlog.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AdminLoginLogClient) GetX(ctx context.Context, id uint32) *AdminLoginLog {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AdminLoginLogClient) Hooks() []Hook {
	return c.hooks.AdminLoginLog
}

// Interceptors returns the client interceptors.
func (c *AdminLoginLogClient) Interceptors() []Interceptor {
	return c.inters.AdminLoginLog
}

func (c *AdminLoginLogClient) mutate(ctx context.Context, m *AdminLoginLogMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AdminLoginLogCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AdminLoginLogUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AdminLoginLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AdminLoginLogDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AdminLoginLog mutation op: %q", m.Op())
	}
}

// AdminLoginRestrictionClient is a client for the AdminLoginRestriction schema.
type AdminLoginRestrictionClient struct {
	config
}

// NewAdminLoginRestrictionClient returns a client for the AdminLoginRestriction from the given config.
func NewAdminLoginRestrictionClient(c config) *AdminLoginRestrictionClient {
	return &AdminLoginRestrictionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `adminloginrestriction.Hooks(f(g(h())))`.
func (c *AdminLoginRestrictionClient) Use(hooks ...Hook) {
	c.hooks.AdminLoginRestriction = append(c.hooks.AdminLoginRestriction, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `adminloginrestriction.Intercept(f(g(h())))`.
func (c *AdminLoginRestrictionClient) Intercept(interceptors ...Interceptor) {
	c.inters.AdminLoginRestriction = append(c.inters.AdminLoginRestriction, interceptors...)
}

// Create returns a builder for creating a AdminLoginRestriction entity.
func (c *AdminLoginRestrictionClient) Create() *AdminLoginRestrictionCreate {
	mutation := newAdminLoginRestrictionMutation(c.config, OpCreate)
	return &AdminLoginRestrictionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AdminLoginRestriction entities.
func (c *AdminLoginRestrictionClient) CreateBulk(builders ...*AdminLoginRestrictionCreate) *AdminLoginRestrictionCreateBulk {
	return &AdminLoginRestrictionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AdminLoginRestrictionClient) MapCreateBulk(slice any, setFunc func(*AdminLoginRestrictionCreate, int)) *AdminLoginRestrictionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AdminLoginRestrictionCreateBulk{err: fmt.Errorf("calling to AdminLoginRestrictionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AdminLoginRestrictionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AdminLoginRestrictionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AdminLoginRestriction.
func (c *AdminLoginRestrictionClient) Update() *AdminLoginRestrictionUpdate {
	mutation := newAdminLoginRestrictionMutation(c.config, OpUpdate)
	return &AdminLoginRestrictionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AdminLoginRestrictionClient) UpdateOne(alr *AdminLoginRestriction) *AdminLoginRestrictionUpdateOne {
	mutation := newAdminLoginRestrictionMutation(c.config, OpUpdateOne, withAdminLoginRestriction(alr))
	return &AdminLoginRestrictionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AdminLoginRestrictionClient) UpdateOneID(id uint32) *AdminLoginRestrictionUpdateOne {
	mutation := newAdminLoginRestrictionMutation(c.config, OpUpdateOne, withAdminLoginRestrictionID(id))
	return &AdminLoginRestrictionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AdminLoginRestriction.
func (c *AdminLoginRestrictionClient) Delete() *AdminLoginRestrictionDelete {
	mutation := newAdminLoginRestrictionMutation(c.config, OpDelete)
	return &AdminLoginRestrictionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AdminLoginRestrictionClient) DeleteOne(alr *AdminLoginRestriction) *AdminLoginRestrictionDeleteOne {
	return c.DeleteOneID(alr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AdminLoginRestrictionClient) DeleteOneID(id uint32) *AdminLoginRestrictionDeleteOne {
	builder := c.Delete().Where(adminloginrestriction.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AdminLoginRestrictionDeleteOne{builder}
}

// Query returns a query builder for AdminLoginRestriction.
func (c *AdminLoginRestrictionClient) Query() *AdminLoginRestrictionQuery {
	return &AdminLoginRestrictionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAdminLoginRestriction},
		inters: c.Interceptors(),
	}
}

// Get returns a AdminLoginRestriction entity by its id.
func (c *AdminLoginRestrictionClient) Get(ctx context.Context, id uint32) (*AdminLoginRestriction, error) {
	return c.Query().Where(adminloginrestriction.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AdminLoginRestrictionClient) GetX(ctx context.Context, id uint32) *AdminLoginRestriction {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AdminLoginRestrictionClient) Hooks() []Hook {
	return c.hooks.AdminLoginRestriction
}

// Interceptors returns the client interceptors.
func (c *AdminLoginRestrictionClient) Interceptors() []Interceptor {
	return c.inters.AdminLoginRestriction
}

func (c *AdminLoginRestrictionClient) mutate(ctx context.Context, m *AdminLoginRestrictionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AdminLoginRestrictionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AdminLoginRestrictionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AdminLoginRestrictionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AdminLoginRestrictionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AdminLoginRestriction mutation op: %q", m.Op())
	}
}

// AdminOperationLogClient is a client for the AdminOperationLog schema.
type AdminOperationLogClient struct {
	config
}

// NewAdminOperationLogClient returns a client for the AdminOperationLog from the given config.
func NewAdminOperationLogClient(c config) *AdminOperationLogClient {
	return &AdminOperationLogClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `adminoperationlog.Hooks(f(g(h())))`.
func (c *AdminOperationLogClient) Use(hooks ...Hook) {
	c.hooks.AdminOperationLog = append(c.hooks.AdminOperationLog, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `adminoperationlog.Intercept(f(g(h())))`.
func (c *AdminOperationLogClient) Intercept(interceptors ...Interceptor) {
	c.inters.AdminOperationLog = append(c.inters.AdminOperationLog, interceptors...)
}

// Create returns a builder for creating a AdminOperationLog entity.
func (c *AdminOperationLogClient) Create() *AdminOperationLogCreate {
	mutation := newAdminOperationLogMutation(c.config, OpCreate)
	return &AdminOperationLogCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AdminOperationLog entities.
func (c *AdminOperationLogClient) CreateBulk(builders ...*AdminOperationLogCreate) *AdminOperationLogCreateBulk {
	return &AdminOperationLogCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AdminOperationLogClient) MapCreateBulk(slice any, setFunc func(*AdminOperationLogCreate, int)) *AdminOperationLogCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AdminOperationLogCreateBulk{err: fmt.Errorf("calling to AdminOperationLogClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AdminOperationLogCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AdminOperationLogCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AdminOperationLog.
func (c *AdminOperationLogClient) Update() *AdminOperationLogUpdate {
	mutation := newAdminOperationLogMutation(c.config, OpUpdate)
	return &AdminOperationLogUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AdminOperationLogClient) UpdateOne(aol *AdminOperationLog) *AdminOperationLogUpdateOne {
	mutation := newAdminOperationLogMutation(c.config, OpUpdateOne, withAdminOperationLog(aol))
	return &AdminOperationLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AdminOperationLogClient) UpdateOneID(id uint32) *AdminOperationLogUpdateOne {
	mutation := newAdminOperationLogMutation(c.config, OpUpdateOne, withAdminOperationLogID(id))
	return &AdminOperationLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AdminOperationLog.
func (c *AdminOperationLogClient) Delete() *AdminOperationLogDelete {
	mutation := newAdminOperationLogMutation(c.config, OpDelete)
	return &AdminOperationLogDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AdminOperationLogClient) DeleteOne(aol *AdminOperationLog) *AdminOperationLogDeleteOne {
	return c.DeleteOneID(aol.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AdminOperationLogClient) DeleteOneID(id uint32) *AdminOperationLogDeleteOne {
	builder := c.Delete().Where(adminoperationlog.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AdminOperationLogDeleteOne{builder}
}

// Query returns a query builder for AdminOperationLog.
func (c *AdminOperationLogClient) Query() *AdminOperationLogQuery {
	return &AdminOperationLogQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAdminOperationLog},
		inters: c.Interceptors(),
	}
}

// Get returns a AdminOperationLog entity by its id.
func (c *AdminOperationLogClient) Get(ctx context.Context, id uint32) (*AdminOperationLog, error) {
	return c.Query().Where(adminoperationlog.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AdminOperationLogClient) GetX(ctx context.Context, id uint32) *AdminOperationLog {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AdminOperationLogClient) Hooks() []Hook {
	return c.hooks.AdminOperationLog
}

// Interceptors returns the client interceptors.
func (c *AdminOperationLogClient) Interceptors() []Interceptor {
	return c.inters.AdminOperationLog
}

func (c *AdminOperationLogClient) mutate(ctx context.Context, m *AdminOperationLogMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AdminOperationLogCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AdminOperationLogUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AdminOperationLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AdminOperationLogDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AdminOperationLog mutation op: %q", m.Op())
	}
}

// ApiResourceClient is a client for the ApiResource schema.
type ApiResourceClient struct {
	config
}

// NewApiResourceClient returns a client for the ApiResource from the given config.
func NewApiResourceClient(c config) *ApiResourceClient {
	return &ApiResourceClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `apiresource.Hooks(f(g(h())))`.
func (c *ApiResourceClient) Use(hooks ...Hook) {
	c.hooks.ApiResource = append(c.hooks.ApiResource, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `apiresource.Intercept(f(g(h())))`.
func (c *ApiResourceClient) Intercept(interceptors ...Interceptor) {
	c.inters.ApiResource = append(c.inters.ApiResource, interceptors...)
}

// Create returns a builder for creating a ApiResource entity.
func (c *ApiResourceClient) Create() *ApiResourceCreate {
	mutation := newApiResourceMutation(c.config, OpCreate)
	return &ApiResourceCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ApiResource entities.
func (c *ApiResourceClient) CreateBulk(builders ...*ApiResourceCreate) *ApiResourceCreateBulk {
	return &ApiResourceCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ApiResourceClient) MapCreateBulk(slice any, setFunc func(*ApiResourceCreate, int)) *ApiResourceCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ApiResourceCreateBulk{err: fmt.Errorf("calling to ApiResourceClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ApiResourceCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ApiResourceCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ApiResource.
func (c *ApiResourceClient) Update() *ApiResourceUpdate {
	mutation := newApiResourceMutation(c.config, OpUpdate)
	return &ApiResourceUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ApiResourceClient) UpdateOne(ar *ApiResource) *ApiResourceUpdateOne {
	mutation := newApiResourceMutation(c.config, OpUpdateOne, withApiResource(ar))
	return &ApiResourceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ApiResourceClient) UpdateOneID(id uint32) *ApiResourceUpdateOne {
	mutation := newApiResourceMutation(c.config, OpUpdateOne, withApiResourceID(id))
	return &ApiResourceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ApiResource.
func (c *ApiResourceClient) Delete() *ApiResourceDelete {
	mutation := newApiResourceMutation(c.config, OpDelete)
	return &ApiResourceDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ApiResourceClient) DeleteOne(ar *ApiResource) *ApiResourceDeleteOne {
	return c.DeleteOneID(ar.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ApiResourceClient) DeleteOneID(id uint32) *ApiResourceDeleteOne {
	builder := c.Delete().Where(apiresource.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ApiResourceDeleteOne{builder}
}

// Query returns a query builder for ApiResource.
func (c *ApiResourceClient) Query() *ApiResourceQuery {
	return &ApiResourceQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeApiResource},
		inters: c.Interceptors(),
	}
}

// Get returns a ApiResource entity by its id.
func (c *ApiResourceClient) Get(ctx context.Context, id uint32) (*ApiResource, error) {
	return c.Query().Where(apiresource.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ApiResourceClient) GetX(ctx context.Context, id uint32) *ApiResource {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ApiResourceClient) Hooks() []Hook {
	return c.hooks.ApiResource
}

// Interceptors returns the client interceptors.
func (c *ApiResourceClient) Interceptors() []Interceptor {
	return c.inters.ApiResource
}

func (c *ApiResourceClient) mutate(ctx context.Context, m *ApiResourceMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ApiResourceCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ApiResourceUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ApiResourceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ApiResourceDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ApiResource mutation op: %q", m.Op())
	}
}

// DepartmentClient is a client for the Department schema.
type DepartmentClient struct {
	config
}

// NewDepartmentClient returns a client for the Department from the given config.
func NewDepartmentClient(c config) *DepartmentClient {
	return &DepartmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `department.Hooks(f(g(h())))`.
func (c *DepartmentClient) Use(hooks ...Hook) {
	c.hooks.Department = append(c.hooks.Department, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `department.Intercept(f(g(h())))`.
func (c *DepartmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Department = append(c.inters.Department, interceptors...)
}

// Create returns a builder for creating a Department entity.
func (c *DepartmentClient) Create() *DepartmentCreate {
	mutation := newDepartmentMutation(c.config, OpCreate)
	return &DepartmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Department entities.
func (c *DepartmentClient) CreateBulk(builders ...*DepartmentCreate) *DepartmentCreateBulk {
	return &DepartmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepartmentClient) MapCreateBulk(slice any, setFunc func(*DepartmentCreate, int)) *DepartmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepartmentCreateBulk{err: fmt.Errorf("calling to DepartmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepartmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepartmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Department.
func (c *DepartmentClient) Update() *DepartmentUpdate {
	mutation := newDepartmentMutation(c.config, OpUpdate)
	return &DepartmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepartmentClient) UpdateOne(d *Department) *DepartmentUpdateOne {
	mutation := newDepartmentMutation(c.config, OpUpdateOne, withDepartment(d))
	return &DepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepartmentClient) UpdateOneID(id uint32) *DepartmentUpdateOne {
	mutation := newDepartmentMutation(c.config, OpUpdateOne, withDepartmentID(id))
	return &DepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Department.
func (c *DepartmentClient) Delete() *DepartmentDelete {
	mutation := newDepartmentMutation(c.config, OpDelete)
	return &DepartmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepartmentClient) DeleteOne(d *Department) *DepartmentDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepartmentClient) DeleteOneID(id uint32) *DepartmentDeleteOne {
	builder := c.Delete().Where(department.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepartmentDeleteOne{builder}
}

// Query returns a query builder for Department.
func (c *DepartmentClient) Query() *DepartmentQuery {
	return &DepartmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepartment},
		inters: c.Interceptors(),
	}
}

// Get returns a Department entity by its id.
func (c *DepartmentClient) Get(ctx context.Context, id uint32) (*Department, error) {
	return c.Query().Where(department.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepartmentClient) GetX(ctx context.Context, id uint32) *Department {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Department.
func (c *DepartmentClient) QueryParent(d *Department) *DepartmentQuery {
	query := (&DepartmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(department.Table, department.FieldID, id),
			sqlgraph.To(department.Table, department.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, department.ParentTable, department.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Department.
func (c *DepartmentClient) QueryChildren(d *Department) *DepartmentQuery {
	query := (&DepartmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(department.Table, department.FieldID, id),
			sqlgraph.To(department.Table, department.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, department.ChildrenTable, department.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepartmentClient) Hooks() []Hook {
	return c.hooks.Department
}

// Interceptors returns the client interceptors.
func (c *DepartmentClient) Interceptors() []Interceptor {
	return c.inters.Department
}

func (c *DepartmentClient) mutate(ctx context.Context, m *DepartmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepartmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepartmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepartmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepartmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Department mutation op: %q", m.Op())
	}
}

// DictClient is a client for the Dict schema.
type DictClient struct {
	config
}

// NewDictClient returns a client for the Dict from the given config.
func NewDictClient(c config) *DictClient {
	return &DictClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `dict.Hooks(f(g(h())))`.
func (c *DictClient) Use(hooks ...Hook) {
	c.hooks.Dict = append(c.hooks.Dict, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `dict.Intercept(f(g(h())))`.
func (c *DictClient) Intercept(interceptors ...Interceptor) {
	c.inters.Dict = append(c.inters.Dict, interceptors...)
}

// Create returns a builder for creating a Dict entity.
func (c *DictClient) Create() *DictCreate {
	mutation := newDictMutation(c.config, OpCreate)
	return &DictCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Dict entities.
func (c *DictClient) CreateBulk(builders ...*DictCreate) *DictCreateBulk {
	return &DictCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DictClient) MapCreateBulk(slice any, setFunc func(*DictCreate, int)) *DictCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DictCreateBulk{err: fmt.Errorf("calling to DictClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DictCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DictCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Dict.
func (c *DictClient) Update() *DictUpdate {
	mutation := newDictMutation(c.config, OpUpdate)
	return &DictUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DictClient) UpdateOne(d *Dict) *DictUpdateOne {
	mutation := newDictMutation(c.config, OpUpdateOne, withDict(d))
	return &DictUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DictClient) UpdateOneID(id uint32) *DictUpdateOne {
	mutation := newDictMutation(c.config, OpUpdateOne, withDictID(id))
	return &DictUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Dict.
func (c *DictClient) Delete() *DictDelete {
	mutation := newDictMutation(c.config, OpDelete)
	return &DictDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DictClient) DeleteOne(d *Dict) *DictDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DictClient) DeleteOneID(id uint32) *DictDeleteOne {
	builder := c.Delete().Where(dict.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DictDeleteOne{builder}
}

// Query returns a query builder for Dict.
func (c *DictClient) Query() *DictQuery {
	return &DictQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDict},
		inters: c.Interceptors(),
	}
}

// Get returns a Dict entity by its id.
func (c *DictClient) Get(ctx context.Context, id uint32) (*Dict, error) {
	return c.Query().Where(dict.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DictClient) GetX(ctx context.Context, id uint32) *Dict {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DictClient) Hooks() []Hook {
	return c.hooks.Dict
}

// Interceptors returns the client interceptors.
func (c *DictClient) Interceptors() []Interceptor {
	return c.inters.Dict
}

func (c *DictClient) mutate(ctx context.Context, m *DictMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DictCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DictUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DictUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DictDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Dict mutation op: %q", m.Op())
	}
}

// FileClient is a client for the File schema.
type FileClient struct {
	config
}

// NewFileClient returns a client for the File from the given config.
func NewFileClient(c config) *FileClient {
	return &FileClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `file.Hooks(f(g(h())))`.
func (c *FileClient) Use(hooks ...Hook) {
	c.hooks.File = append(c.hooks.File, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `file.Intercept(f(g(h())))`.
func (c *FileClient) Intercept(interceptors ...Interceptor) {
	c.inters.File = append(c.inters.File, interceptors...)
}

// Create returns a builder for creating a File entity.
func (c *FileClient) Create() *FileCreate {
	mutation := newFileMutation(c.config, OpCreate)
	return &FileCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of File entities.
func (c *FileClient) CreateBulk(builders ...*FileCreate) *FileCreateBulk {
	return &FileCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FileClient) MapCreateBulk(slice any, setFunc func(*FileCreate, int)) *FileCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FileCreateBulk{err: fmt.Errorf("calling to FileClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FileCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FileCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for File.
func (c *FileClient) Update() *FileUpdate {
	mutation := newFileMutation(c.config, OpUpdate)
	return &FileUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FileClient) UpdateOne(f *File) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFile(f))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FileClient) UpdateOneID(id uint32) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFileID(id))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for File.
func (c *FileClient) Delete() *FileDelete {
	mutation := newFileMutation(c.config, OpDelete)
	return &FileDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FileClient) DeleteOne(f *File) *FileDeleteOne {
	return c.DeleteOneID(f.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FileClient) DeleteOneID(id uint32) *FileDeleteOne {
	builder := c.Delete().Where(file.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FileDeleteOne{builder}
}

// Query returns a query builder for File.
func (c *FileClient) Query() *FileQuery {
	return &FileQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFile},
		inters: c.Interceptors(),
	}
}

// Get returns a File entity by its id.
func (c *FileClient) Get(ctx context.Context, id uint32) (*File, error) {
	return c.Query().Where(file.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FileClient) GetX(ctx context.Context, id uint32) *File {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *FileClient) Hooks() []Hook {
	return c.hooks.File
}

// Interceptors returns the client interceptors.
func (c *FileClient) Interceptors() []Interceptor {
	return c.inters.File
}

func (c *FileClient) mutate(ctx context.Context, m *FileMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FileCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FileUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FileDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown File mutation op: %q", m.Op())
	}
}

// MenuClient is a client for the Menu schema.
type MenuClient struct {
	config
}

// NewMenuClient returns a client for the Menu from the given config.
func NewMenuClient(c config) *MenuClient {
	return &MenuClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `menu.Hooks(f(g(h())))`.
func (c *MenuClient) Use(hooks ...Hook) {
	c.hooks.Menu = append(c.hooks.Menu, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `menu.Intercept(f(g(h())))`.
func (c *MenuClient) Intercept(interceptors ...Interceptor) {
	c.inters.Menu = append(c.inters.Menu, interceptors...)
}

// Create returns a builder for creating a Menu entity.
func (c *MenuClient) Create() *MenuCreate {
	mutation := newMenuMutation(c.config, OpCreate)
	return &MenuCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Menu entities.
func (c *MenuClient) CreateBulk(builders ...*MenuCreate) *MenuCreateBulk {
	return &MenuCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MenuClient) MapCreateBulk(slice any, setFunc func(*MenuCreate, int)) *MenuCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MenuCreateBulk{err: fmt.Errorf("calling to MenuClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MenuCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MenuCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Menu.
func (c *MenuClient) Update() *MenuUpdate {
	mutation := newMenuMutation(c.config, OpUpdate)
	return &MenuUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MenuClient) UpdateOne(m *Menu) *MenuUpdateOne {
	mutation := newMenuMutation(c.config, OpUpdateOne, withMenu(m))
	return &MenuUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MenuClient) UpdateOneID(id int32) *MenuUpdateOne {
	mutation := newMenuMutation(c.config, OpUpdateOne, withMenuID(id))
	return &MenuUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Menu.
func (c *MenuClient) Delete() *MenuDelete {
	mutation := newMenuMutation(c.config, OpDelete)
	return &MenuDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MenuClient) DeleteOne(m *Menu) *MenuDeleteOne {
	return c.DeleteOneID(m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MenuClient) DeleteOneID(id int32) *MenuDeleteOne {
	builder := c.Delete().Where(menu.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MenuDeleteOne{builder}
}

// Query returns a query builder for Menu.
func (c *MenuClient) Query() *MenuQuery {
	return &MenuQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMenu},
		inters: c.Interceptors(),
	}
}

// Get returns a Menu entity by its id.
func (c *MenuClient) Get(ctx context.Context, id int32) (*Menu, error) {
	return c.Query().Where(menu.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MenuClient) GetX(ctx context.Context, id int32) *Menu {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Menu.
func (c *MenuClient) QueryParent(m *Menu) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(menu.Table, menu.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, menu.ParentTable, menu.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Menu.
func (c *MenuClient) QueryChildren(m *Menu) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(menu.Table, menu.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, menu.ChildrenTable, menu.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MenuClient) Hooks() []Hook {
	return c.hooks.Menu
}

// Interceptors returns the client interceptors.
func (c *MenuClient) Interceptors() []Interceptor {
	return c.inters.Menu
}

func (c *MenuClient) mutate(ctx context.Context, m *MenuMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MenuCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MenuUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MenuUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MenuDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Menu mutation op: %q", m.Op())
	}
}

// NotificationMessageClient is a client for the NotificationMessage schema.
type NotificationMessageClient struct {
	config
}

// NewNotificationMessageClient returns a client for the NotificationMessage from the given config.
func NewNotificationMessageClient(c config) *NotificationMessageClient {
	return &NotificationMessageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notificationmessage.Hooks(f(g(h())))`.
func (c *NotificationMessageClient) Use(hooks ...Hook) {
	c.hooks.NotificationMessage = append(c.hooks.NotificationMessage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notificationmessage.Intercept(f(g(h())))`.
func (c *NotificationMessageClient) Intercept(interceptors ...Interceptor) {
	c.inters.NotificationMessage = append(c.inters.NotificationMessage, interceptors...)
}

// Create returns a builder for creating a NotificationMessage entity.
func (c *NotificationMessageClient) Create() *NotificationMessageCreate {
	mutation := newNotificationMessageMutation(c.config, OpCreate)
	return &NotificationMessageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of NotificationMessage entities.
func (c *NotificationMessageClient) CreateBulk(builders ...*NotificationMessageCreate) *NotificationMessageCreateBulk {
	return &NotificationMessageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationMessageClient) MapCreateBulk(slice any, setFunc func(*NotificationMessageCreate, int)) *NotificationMessageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationMessageCreateBulk{err: fmt.Errorf("calling to NotificationMessageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationMessageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationMessageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for NotificationMessage.
func (c *NotificationMessageClient) Update() *NotificationMessageUpdate {
	mutation := newNotificationMessageMutation(c.config, OpUpdate)
	return &NotificationMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationMessageClient) UpdateOne(nm *NotificationMessage) *NotificationMessageUpdateOne {
	mutation := newNotificationMessageMutation(c.config, OpUpdateOne, withNotificationMessage(nm))
	return &NotificationMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationMessageClient) UpdateOneID(id uint32) *NotificationMessageUpdateOne {
	mutation := newNotificationMessageMutation(c.config, OpUpdateOne, withNotificationMessageID(id))
	return &NotificationMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for NotificationMessage.
func (c *NotificationMessageClient) Delete() *NotificationMessageDelete {
	mutation := newNotificationMessageMutation(c.config, OpDelete)
	return &NotificationMessageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationMessageClient) DeleteOne(nm *NotificationMessage) *NotificationMessageDeleteOne {
	return c.DeleteOneID(nm.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationMessageClient) DeleteOneID(id uint32) *NotificationMessageDeleteOne {
	builder := c.Delete().Where(notificationmessage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationMessageDeleteOne{builder}
}

// Query returns a query builder for NotificationMessage.
func (c *NotificationMessageClient) Query() *NotificationMessageQuery {
	return &NotificationMessageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotificationMessage},
		inters: c.Interceptors(),
	}
}

// Get returns a NotificationMessage entity by its id.
func (c *NotificationMessageClient) Get(ctx context.Context, id uint32) (*NotificationMessage, error) {
	return c.Query().Where(notificationmessage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationMessageClient) GetX(ctx context.Context, id uint32) *NotificationMessage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *NotificationMessageClient) Hooks() []Hook {
	return c.hooks.NotificationMessage
}

// Interceptors returns the client interceptors.
func (c *NotificationMessageClient) Interceptors() []Interceptor {
	return c.inters.NotificationMessage
}

func (c *NotificationMessageClient) mutate(ctx context.Context, m *NotificationMessageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationMessageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationMessageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown NotificationMessage mutation op: %q", m.Op())
	}
}

// NotificationMessageCategoryClient is a client for the NotificationMessageCategory schema.
type NotificationMessageCategoryClient struct {
	config
}

// NewNotificationMessageCategoryClient returns a client for the NotificationMessageCategory from the given config.
func NewNotificationMessageCategoryClient(c config) *NotificationMessageCategoryClient {
	return &NotificationMessageCategoryClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notificationmessagecategory.Hooks(f(g(h())))`.
func (c *NotificationMessageCategoryClient) Use(hooks ...Hook) {
	c.hooks.NotificationMessageCategory = append(c.hooks.NotificationMessageCategory, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notificationmessagecategory.Intercept(f(g(h())))`.
func (c *NotificationMessageCategoryClient) Intercept(interceptors ...Interceptor) {
	c.inters.NotificationMessageCategory = append(c.inters.NotificationMessageCategory, interceptors...)
}

// Create returns a builder for creating a NotificationMessageCategory entity.
func (c *NotificationMessageCategoryClient) Create() *NotificationMessageCategoryCreate {
	mutation := newNotificationMessageCategoryMutation(c.config, OpCreate)
	return &NotificationMessageCategoryCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of NotificationMessageCategory entities.
func (c *NotificationMessageCategoryClient) CreateBulk(builders ...*NotificationMessageCategoryCreate) *NotificationMessageCategoryCreateBulk {
	return &NotificationMessageCategoryCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationMessageCategoryClient) MapCreateBulk(slice any, setFunc func(*NotificationMessageCategoryCreate, int)) *NotificationMessageCategoryCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationMessageCategoryCreateBulk{err: fmt.Errorf("calling to NotificationMessageCategoryClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationMessageCategoryCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationMessageCategoryCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for NotificationMessageCategory.
func (c *NotificationMessageCategoryClient) Update() *NotificationMessageCategoryUpdate {
	mutation := newNotificationMessageCategoryMutation(c.config, OpUpdate)
	return &NotificationMessageCategoryUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationMessageCategoryClient) UpdateOne(nmc *NotificationMessageCategory) *NotificationMessageCategoryUpdateOne {
	mutation := newNotificationMessageCategoryMutation(c.config, OpUpdateOne, withNotificationMessageCategory(nmc))
	return &NotificationMessageCategoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationMessageCategoryClient) UpdateOneID(id uint32) *NotificationMessageCategoryUpdateOne {
	mutation := newNotificationMessageCategoryMutation(c.config, OpUpdateOne, withNotificationMessageCategoryID(id))
	return &NotificationMessageCategoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for NotificationMessageCategory.
func (c *NotificationMessageCategoryClient) Delete() *NotificationMessageCategoryDelete {
	mutation := newNotificationMessageCategoryMutation(c.config, OpDelete)
	return &NotificationMessageCategoryDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationMessageCategoryClient) DeleteOne(nmc *NotificationMessageCategory) *NotificationMessageCategoryDeleteOne {
	return c.DeleteOneID(nmc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationMessageCategoryClient) DeleteOneID(id uint32) *NotificationMessageCategoryDeleteOne {
	builder := c.Delete().Where(notificationmessagecategory.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationMessageCategoryDeleteOne{builder}
}

// Query returns a query builder for NotificationMessageCategory.
func (c *NotificationMessageCategoryClient) Query() *NotificationMessageCategoryQuery {
	return &NotificationMessageCategoryQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotificationMessageCategory},
		inters: c.Interceptors(),
	}
}

// Get returns a NotificationMessageCategory entity by its id.
func (c *NotificationMessageCategoryClient) Get(ctx context.Context, id uint32) (*NotificationMessageCategory, error) {
	return c.Query().Where(notificationmessagecategory.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationMessageCategoryClient) GetX(ctx context.Context, id uint32) *NotificationMessageCategory {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a NotificationMessageCategory.
func (c *NotificationMessageCategoryClient) QueryParent(nmc *NotificationMessageCategory) *NotificationMessageCategoryQuery {
	query := (&NotificationMessageCategoryClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := nmc.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(notificationmessagecategory.Table, notificationmessagecategory.FieldID, id),
			sqlgraph.To(notificationmessagecategory.Table, notificationmessagecategory.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, notificationmessagecategory.ParentTable, notificationmessagecategory.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(nmc.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a NotificationMessageCategory.
func (c *NotificationMessageCategoryClient) QueryChildren(nmc *NotificationMessageCategory) *NotificationMessageCategoryQuery {
	query := (&NotificationMessageCategoryClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := nmc.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(notificationmessagecategory.Table, notificationmessagecategory.FieldID, id),
			sqlgraph.To(notificationmessagecategory.Table, notificationmessagecategory.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, notificationmessagecategory.ChildrenTable, notificationmessagecategory.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(nmc.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *NotificationMessageCategoryClient) Hooks() []Hook {
	return c.hooks.NotificationMessageCategory
}

// Interceptors returns the client interceptors.
func (c *NotificationMessageCategoryClient) Interceptors() []Interceptor {
	return c.inters.NotificationMessageCategory
}

func (c *NotificationMessageCategoryClient) mutate(ctx context.Context, m *NotificationMessageCategoryMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationMessageCategoryCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationMessageCategoryUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationMessageCategoryUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationMessageCategoryDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown NotificationMessageCategory mutation op: %q", m.Op())
	}
}

// NotificationMessageRecipientClient is a client for the NotificationMessageRecipient schema.
type NotificationMessageRecipientClient struct {
	config
}

// NewNotificationMessageRecipientClient returns a client for the NotificationMessageRecipient from the given config.
func NewNotificationMessageRecipientClient(c config) *NotificationMessageRecipientClient {
	return &NotificationMessageRecipientClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notificationmessagerecipient.Hooks(f(g(h())))`.
func (c *NotificationMessageRecipientClient) Use(hooks ...Hook) {
	c.hooks.NotificationMessageRecipient = append(c.hooks.NotificationMessageRecipient, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notificationmessagerecipient.Intercept(f(g(h())))`.
func (c *NotificationMessageRecipientClient) Intercept(interceptors ...Interceptor) {
	c.inters.NotificationMessageRecipient = append(c.inters.NotificationMessageRecipient, interceptors...)
}

// Create returns a builder for creating a NotificationMessageRecipient entity.
func (c *NotificationMessageRecipientClient) Create() *NotificationMessageRecipientCreate {
	mutation := newNotificationMessageRecipientMutation(c.config, OpCreate)
	return &NotificationMessageRecipientCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of NotificationMessageRecipient entities.
func (c *NotificationMessageRecipientClient) CreateBulk(builders ...*NotificationMessageRecipientCreate) *NotificationMessageRecipientCreateBulk {
	return &NotificationMessageRecipientCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationMessageRecipientClient) MapCreateBulk(slice any, setFunc func(*NotificationMessageRecipientCreate, int)) *NotificationMessageRecipientCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationMessageRecipientCreateBulk{err: fmt.Errorf("calling to NotificationMessageRecipientClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationMessageRecipientCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationMessageRecipientCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for NotificationMessageRecipient.
func (c *NotificationMessageRecipientClient) Update() *NotificationMessageRecipientUpdate {
	mutation := newNotificationMessageRecipientMutation(c.config, OpUpdate)
	return &NotificationMessageRecipientUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationMessageRecipientClient) UpdateOne(nmr *NotificationMessageRecipient) *NotificationMessageRecipientUpdateOne {
	mutation := newNotificationMessageRecipientMutation(c.config, OpUpdateOne, withNotificationMessageRecipient(nmr))
	return &NotificationMessageRecipientUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationMessageRecipientClient) UpdateOneID(id uint32) *NotificationMessageRecipientUpdateOne {
	mutation := newNotificationMessageRecipientMutation(c.config, OpUpdateOne, withNotificationMessageRecipientID(id))
	return &NotificationMessageRecipientUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for NotificationMessageRecipient.
func (c *NotificationMessageRecipientClient) Delete() *NotificationMessageRecipientDelete {
	mutation := newNotificationMessageRecipientMutation(c.config, OpDelete)
	return &NotificationMessageRecipientDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationMessageRecipientClient) DeleteOne(nmr *NotificationMessageRecipient) *NotificationMessageRecipientDeleteOne {
	return c.DeleteOneID(nmr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationMessageRecipientClient) DeleteOneID(id uint32) *NotificationMessageRecipientDeleteOne {
	builder := c.Delete().Where(notificationmessagerecipient.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationMessageRecipientDeleteOne{builder}
}

// Query returns a query builder for NotificationMessageRecipient.
func (c *NotificationMessageRecipientClient) Query() *NotificationMessageRecipientQuery {
	return &NotificationMessageRecipientQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotificationMessageRecipient},
		inters: c.Interceptors(),
	}
}

// Get returns a NotificationMessageRecipient entity by its id.
func (c *NotificationMessageRecipientClient) Get(ctx context.Context, id uint32) (*NotificationMessageRecipient, error) {
	return c.Query().Where(notificationmessagerecipient.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationMessageRecipientClient) GetX(ctx context.Context, id uint32) *NotificationMessageRecipient {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *NotificationMessageRecipientClient) Hooks() []Hook {
	return c.hooks.NotificationMessageRecipient
}

// Interceptors returns the client interceptors.
func (c *NotificationMessageRecipientClient) Interceptors() []Interceptor {
	return c.inters.NotificationMessageRecipient
}

func (c *NotificationMessageRecipientClient) mutate(ctx context.Context, m *NotificationMessageRecipientMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationMessageRecipientCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationMessageRecipientUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationMessageRecipientUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationMessageRecipientDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown NotificationMessageRecipient mutation op: %q", m.Op())
	}
}

// OrganizationClient is a client for the Organization schema.
type OrganizationClient struct {
	config
}

// NewOrganizationClient returns a client for the Organization from the given config.
func NewOrganizationClient(c config) *OrganizationClient {
	return &OrganizationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `organization.Hooks(f(g(h())))`.
func (c *OrganizationClient) Use(hooks ...Hook) {
	c.hooks.Organization = append(c.hooks.Organization, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `organization.Intercept(f(g(h())))`.
func (c *OrganizationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Organization = append(c.inters.Organization, interceptors...)
}

// Create returns a builder for creating a Organization entity.
func (c *OrganizationClient) Create() *OrganizationCreate {
	mutation := newOrganizationMutation(c.config, OpCreate)
	return &OrganizationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Organization entities.
func (c *OrganizationClient) CreateBulk(builders ...*OrganizationCreate) *OrganizationCreateBulk {
	return &OrganizationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OrganizationClient) MapCreateBulk(slice any, setFunc func(*OrganizationCreate, int)) *OrganizationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OrganizationCreateBulk{err: fmt.Errorf("calling to OrganizationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OrganizationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OrganizationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Organization.
func (c *OrganizationClient) Update() *OrganizationUpdate {
	mutation := newOrganizationMutation(c.config, OpUpdate)
	return &OrganizationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OrganizationClient) UpdateOne(o *Organization) *OrganizationUpdateOne {
	mutation := newOrganizationMutation(c.config, OpUpdateOne, withOrganization(o))
	return &OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OrganizationClient) UpdateOneID(id uint32) *OrganizationUpdateOne {
	mutation := newOrganizationMutation(c.config, OpUpdateOne, withOrganizationID(id))
	return &OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Organization.
func (c *OrganizationClient) Delete() *OrganizationDelete {
	mutation := newOrganizationMutation(c.config, OpDelete)
	return &OrganizationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OrganizationClient) DeleteOne(o *Organization) *OrganizationDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OrganizationClient) DeleteOneID(id uint32) *OrganizationDeleteOne {
	builder := c.Delete().Where(organization.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OrganizationDeleteOne{builder}
}

// Query returns a query builder for Organization.
func (c *OrganizationClient) Query() *OrganizationQuery {
	return &OrganizationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOrganization},
		inters: c.Interceptors(),
	}
}

// Get returns a Organization entity by its id.
func (c *OrganizationClient) Get(ctx context.Context, id uint32) (*Organization, error) {
	return c.Query().Where(organization.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OrganizationClient) GetX(ctx context.Context, id uint32) *Organization {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Organization.
func (c *OrganizationClient) QueryParent(o *Organization) *OrganizationQuery {
	query := (&OrganizationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, organization.ParentTable, organization.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Organization.
func (c *OrganizationClient) QueryChildren(o *Organization) *OrganizationQuery {
	query := (&OrganizationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, organization.ChildrenTable, organization.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *OrganizationClient) Hooks() []Hook {
	return c.hooks.Organization
}

// Interceptors returns the client interceptors.
func (c *OrganizationClient) Interceptors() []Interceptor {
	return c.inters.Organization
}

func (c *OrganizationClient) mutate(ctx context.Context, m *OrganizationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OrganizationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OrganizationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OrganizationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Organization mutation op: %q", m.Op())
	}
}

// PositionClient is a client for the Position schema.
type PositionClient struct {
	config
}

// NewPositionClient returns a client for the Position from the given config.
func NewPositionClient(c config) *PositionClient {
	return &PositionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `position.Hooks(f(g(h())))`.
func (c *PositionClient) Use(hooks ...Hook) {
	c.hooks.Position = append(c.hooks.Position, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `position.Intercept(f(g(h())))`.
func (c *PositionClient) Intercept(interceptors ...Interceptor) {
	c.inters.Position = append(c.inters.Position, interceptors...)
}

// Create returns a builder for creating a Position entity.
func (c *PositionClient) Create() *PositionCreate {
	mutation := newPositionMutation(c.config, OpCreate)
	return &PositionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Position entities.
func (c *PositionClient) CreateBulk(builders ...*PositionCreate) *PositionCreateBulk {
	return &PositionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PositionClient) MapCreateBulk(slice any, setFunc func(*PositionCreate, int)) *PositionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PositionCreateBulk{err: fmt.Errorf("calling to PositionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PositionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PositionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Position.
func (c *PositionClient) Update() *PositionUpdate {
	mutation := newPositionMutation(c.config, OpUpdate)
	return &PositionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PositionClient) UpdateOne(po *Position) *PositionUpdateOne {
	mutation := newPositionMutation(c.config, OpUpdateOne, withPosition(po))
	return &PositionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PositionClient) UpdateOneID(id uint32) *PositionUpdateOne {
	mutation := newPositionMutation(c.config, OpUpdateOne, withPositionID(id))
	return &PositionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Position.
func (c *PositionClient) Delete() *PositionDelete {
	mutation := newPositionMutation(c.config, OpDelete)
	return &PositionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PositionClient) DeleteOne(po *Position) *PositionDeleteOne {
	return c.DeleteOneID(po.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PositionClient) DeleteOneID(id uint32) *PositionDeleteOne {
	builder := c.Delete().Where(position.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PositionDeleteOne{builder}
}

// Query returns a query builder for Position.
func (c *PositionClient) Query() *PositionQuery {
	return &PositionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePosition},
		inters: c.Interceptors(),
	}
}

// Get returns a Position entity by its id.
func (c *PositionClient) Get(ctx context.Context, id uint32) (*Position, error) {
	return c.Query().Where(position.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PositionClient) GetX(ctx context.Context, id uint32) *Position {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Position.
func (c *PositionClient) QueryParent(po *Position) *PositionQuery {
	query := (&PositionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := po.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(position.Table, position.FieldID, id),
			sqlgraph.To(position.Table, position.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, position.ParentTable, position.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(po.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Position.
func (c *PositionClient) QueryChildren(po *Position) *PositionQuery {
	query := (&PositionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := po.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(position.Table, position.FieldID, id),
			sqlgraph.To(position.Table, position.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, position.ChildrenTable, position.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(po.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PositionClient) Hooks() []Hook {
	return c.hooks.Position
}

// Interceptors returns the client interceptors.
func (c *PositionClient) Interceptors() []Interceptor {
	return c.inters.Position
}

func (c *PositionClient) mutate(ctx context.Context, m *PositionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PositionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PositionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PositionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PositionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Position mutation op: %q", m.Op())
	}
}

// PrivateMessageClient is a client for the PrivateMessage schema.
type PrivateMessageClient struct {
	config
}

// NewPrivateMessageClient returns a client for the PrivateMessage from the given config.
func NewPrivateMessageClient(c config) *PrivateMessageClient {
	return &PrivateMessageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `privatemessage.Hooks(f(g(h())))`.
func (c *PrivateMessageClient) Use(hooks ...Hook) {
	c.hooks.PrivateMessage = append(c.hooks.PrivateMessage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `privatemessage.Intercept(f(g(h())))`.
func (c *PrivateMessageClient) Intercept(interceptors ...Interceptor) {
	c.inters.PrivateMessage = append(c.inters.PrivateMessage, interceptors...)
}

// Create returns a builder for creating a PrivateMessage entity.
func (c *PrivateMessageClient) Create() *PrivateMessageCreate {
	mutation := newPrivateMessageMutation(c.config, OpCreate)
	return &PrivateMessageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PrivateMessage entities.
func (c *PrivateMessageClient) CreateBulk(builders ...*PrivateMessageCreate) *PrivateMessageCreateBulk {
	return &PrivateMessageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PrivateMessageClient) MapCreateBulk(slice any, setFunc func(*PrivateMessageCreate, int)) *PrivateMessageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PrivateMessageCreateBulk{err: fmt.Errorf("calling to PrivateMessageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PrivateMessageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PrivateMessageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PrivateMessage.
func (c *PrivateMessageClient) Update() *PrivateMessageUpdate {
	mutation := newPrivateMessageMutation(c.config, OpUpdate)
	return &PrivateMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PrivateMessageClient) UpdateOne(pm *PrivateMessage) *PrivateMessageUpdateOne {
	mutation := newPrivateMessageMutation(c.config, OpUpdateOne, withPrivateMessage(pm))
	return &PrivateMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PrivateMessageClient) UpdateOneID(id uint32) *PrivateMessageUpdateOne {
	mutation := newPrivateMessageMutation(c.config, OpUpdateOne, withPrivateMessageID(id))
	return &PrivateMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PrivateMessage.
func (c *PrivateMessageClient) Delete() *PrivateMessageDelete {
	mutation := newPrivateMessageMutation(c.config, OpDelete)
	return &PrivateMessageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PrivateMessageClient) DeleteOne(pm *PrivateMessage) *PrivateMessageDeleteOne {
	return c.DeleteOneID(pm.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PrivateMessageClient) DeleteOneID(id uint32) *PrivateMessageDeleteOne {
	builder := c.Delete().Where(privatemessage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PrivateMessageDeleteOne{builder}
}

// Query returns a query builder for PrivateMessage.
func (c *PrivateMessageClient) Query() *PrivateMessageQuery {
	return &PrivateMessageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePrivateMessage},
		inters: c.Interceptors(),
	}
}

// Get returns a PrivateMessage entity by its id.
func (c *PrivateMessageClient) Get(ctx context.Context, id uint32) (*PrivateMessage, error) {
	return c.Query().Where(privatemessage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PrivateMessageClient) GetX(ctx context.Context, id uint32) *PrivateMessage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PrivateMessageClient) Hooks() []Hook {
	return c.hooks.PrivateMessage
}

// Interceptors returns the client interceptors.
func (c *PrivateMessageClient) Interceptors() []Interceptor {
	return c.inters.PrivateMessage
}

func (c *PrivateMessageClient) mutate(ctx context.Context, m *PrivateMessageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PrivateMessageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PrivateMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PrivateMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PrivateMessageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PrivateMessage mutation op: %q", m.Op())
	}
}

// RoleClient is a client for the Role schema.
type RoleClient struct {
	config
}

// NewRoleClient returns a client for the Role from the given config.
func NewRoleClient(c config) *RoleClient {
	return &RoleClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `role.Hooks(f(g(h())))`.
func (c *RoleClient) Use(hooks ...Hook) {
	c.hooks.Role = append(c.hooks.Role, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `role.Intercept(f(g(h())))`.
func (c *RoleClient) Intercept(interceptors ...Interceptor) {
	c.inters.Role = append(c.inters.Role, interceptors...)
}

// Create returns a builder for creating a Role entity.
func (c *RoleClient) Create() *RoleCreate {
	mutation := newRoleMutation(c.config, OpCreate)
	return &RoleCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Role entities.
func (c *RoleClient) CreateBulk(builders ...*RoleCreate) *RoleCreateBulk {
	return &RoleCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *RoleClient) MapCreateBulk(slice any, setFunc func(*RoleCreate, int)) *RoleCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &RoleCreateBulk{err: fmt.Errorf("calling to RoleClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*RoleCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &RoleCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Role.
func (c *RoleClient) Update() *RoleUpdate {
	mutation := newRoleMutation(c.config, OpUpdate)
	return &RoleUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *RoleClient) UpdateOne(r *Role) *RoleUpdateOne {
	mutation := newRoleMutation(c.config, OpUpdateOne, withRole(r))
	return &RoleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *RoleClient) UpdateOneID(id uint32) *RoleUpdateOne {
	mutation := newRoleMutation(c.config, OpUpdateOne, withRoleID(id))
	return &RoleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Role.
func (c *RoleClient) Delete() *RoleDelete {
	mutation := newRoleMutation(c.config, OpDelete)
	return &RoleDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *RoleClient) DeleteOne(r *Role) *RoleDeleteOne {
	return c.DeleteOneID(r.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *RoleClient) DeleteOneID(id uint32) *RoleDeleteOne {
	builder := c.Delete().Where(role.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &RoleDeleteOne{builder}
}

// Query returns a query builder for Role.
func (c *RoleClient) Query() *RoleQuery {
	return &RoleQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeRole},
		inters: c.Interceptors(),
	}
}

// Get returns a Role entity by its id.
func (c *RoleClient) Get(ctx context.Context, id uint32) (*Role, error) {
	return c.Query().Where(role.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *RoleClient) GetX(ctx context.Context, id uint32) *Role {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Role.
func (c *RoleClient) QueryParent(r *Role) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, role.ParentTable, role.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Role.
func (c *RoleClient) QueryChildren(r *Role) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, role.ChildrenTable, role.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *RoleClient) Hooks() []Hook {
	return c.hooks.Role
}

// Interceptors returns the client interceptors.
func (c *RoleClient) Interceptors() []Interceptor {
	return c.inters.Role
}

func (c *RoleClient) mutate(ctx context.Context, m *RoleMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&RoleCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&RoleUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&RoleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&RoleDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Role mutation op: %q", m.Op())
	}
}

// TaskClient is a client for the Task schema.
type TaskClient struct {
	config
}

// NewTaskClient returns a client for the Task from the given config.
func NewTaskClient(c config) *TaskClient {
	return &TaskClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `task.Hooks(f(g(h())))`.
func (c *TaskClient) Use(hooks ...Hook) {
	c.hooks.Task = append(c.hooks.Task, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `task.Intercept(f(g(h())))`.
func (c *TaskClient) Intercept(interceptors ...Interceptor) {
	c.inters.Task = append(c.inters.Task, interceptors...)
}

// Create returns a builder for creating a Task entity.
func (c *TaskClient) Create() *TaskCreate {
	mutation := newTaskMutation(c.config, OpCreate)
	return &TaskCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Task entities.
func (c *TaskClient) CreateBulk(builders ...*TaskCreate) *TaskCreateBulk {
	return &TaskCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskClient) MapCreateBulk(slice any, setFunc func(*TaskCreate, int)) *TaskCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskCreateBulk{err: fmt.Errorf("calling to TaskClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Task.
func (c *TaskClient) Update() *TaskUpdate {
	mutation := newTaskMutation(c.config, OpUpdate)
	return &TaskUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskClient) UpdateOne(t *Task) *TaskUpdateOne {
	mutation := newTaskMutation(c.config, OpUpdateOne, withTask(t))
	return &TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskClient) UpdateOneID(id uint32) *TaskUpdateOne {
	mutation := newTaskMutation(c.config, OpUpdateOne, withTaskID(id))
	return &TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Task.
func (c *TaskClient) Delete() *TaskDelete {
	mutation := newTaskMutation(c.config, OpDelete)
	return &TaskDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskClient) DeleteOne(t *Task) *TaskDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskClient) DeleteOneID(id uint32) *TaskDeleteOne {
	builder := c.Delete().Where(task.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskDeleteOne{builder}
}

// Query returns a query builder for Task.
func (c *TaskClient) Query() *TaskQuery {
	return &TaskQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTask},
		inters: c.Interceptors(),
	}
}

// Get returns a Task entity by its id.
func (c *TaskClient) Get(ctx context.Context, id uint32) (*Task, error) {
	return c.Query().Where(task.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskClient) GetX(ctx context.Context, id uint32) *Task {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TaskClient) Hooks() []Hook {
	return c.hooks.Task
}

// Interceptors returns the client interceptors.
func (c *TaskClient) Interceptors() []Interceptor {
	return c.inters.Task
}

func (c *TaskClient) mutate(ctx context.Context, m *TaskMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Task mutation op: %q", m.Op())
	}
}

// TenantClient is a client for the Tenant schema.
type TenantClient struct {
	config
}

// NewTenantClient returns a client for the Tenant from the given config.
func NewTenantClient(c config) *TenantClient {
	return &TenantClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tenant.Hooks(f(g(h())))`.
func (c *TenantClient) Use(hooks ...Hook) {
	c.hooks.Tenant = append(c.hooks.Tenant, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tenant.Intercept(f(g(h())))`.
func (c *TenantClient) Intercept(interceptors ...Interceptor) {
	c.inters.Tenant = append(c.inters.Tenant, interceptors...)
}

// Create returns a builder for creating a Tenant entity.
func (c *TenantClient) Create() *TenantCreate {
	mutation := newTenantMutation(c.config, OpCreate)
	return &TenantCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Tenant entities.
func (c *TenantClient) CreateBulk(builders ...*TenantCreate) *TenantCreateBulk {
	return &TenantCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TenantClient) MapCreateBulk(slice any, setFunc func(*TenantCreate, int)) *TenantCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TenantCreateBulk{err: fmt.Errorf("calling to TenantClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TenantCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TenantCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Tenant.
func (c *TenantClient) Update() *TenantUpdate {
	mutation := newTenantMutation(c.config, OpUpdate)
	return &TenantUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TenantClient) UpdateOne(t *Tenant) *TenantUpdateOne {
	mutation := newTenantMutation(c.config, OpUpdateOne, withTenant(t))
	return &TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TenantClient) UpdateOneID(id uint32) *TenantUpdateOne {
	mutation := newTenantMutation(c.config, OpUpdateOne, withTenantID(id))
	return &TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Tenant.
func (c *TenantClient) Delete() *TenantDelete {
	mutation := newTenantMutation(c.config, OpDelete)
	return &TenantDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TenantClient) DeleteOne(t *Tenant) *TenantDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TenantClient) DeleteOneID(id uint32) *TenantDeleteOne {
	builder := c.Delete().Where(tenant.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TenantDeleteOne{builder}
}

// Query returns a query builder for Tenant.
func (c *TenantClient) Query() *TenantQuery {
	return &TenantQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTenant},
		inters: c.Interceptors(),
	}
}

// Get returns a Tenant entity by its id.
func (c *TenantClient) Get(ctx context.Context, id uint32) (*Tenant, error) {
	return c.Query().Where(tenant.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TenantClient) GetX(ctx context.Context, id uint32) *Tenant {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TenantClient) Hooks() []Hook {
	return c.hooks.Tenant
}

// Interceptors returns the client interceptors.
func (c *TenantClient) Interceptors() []Interceptor {
	return c.inters.Tenant
}

func (c *TenantClient) mutate(ctx context.Context, m *TenantMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TenantCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TenantUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TenantDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Tenant mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id uint32) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id uint32) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id uint32) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id uint32) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	return c.hooks.User
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	return c.inters.User
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// UserCredentialClient is a client for the UserCredential schema.
type UserCredentialClient struct {
	config
}

// NewUserCredentialClient returns a client for the UserCredential from the given config.
func NewUserCredentialClient(c config) *UserCredentialClient {
	return &UserCredentialClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `usercredential.Hooks(f(g(h())))`.
func (c *UserCredentialClient) Use(hooks ...Hook) {
	c.hooks.UserCredential = append(c.hooks.UserCredential, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `usercredential.Intercept(f(g(h())))`.
func (c *UserCredentialClient) Intercept(interceptors ...Interceptor) {
	c.inters.UserCredential = append(c.inters.UserCredential, interceptors...)
}

// Create returns a builder for creating a UserCredential entity.
func (c *UserCredentialClient) Create() *UserCredentialCreate {
	mutation := newUserCredentialMutation(c.config, OpCreate)
	return &UserCredentialCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UserCredential entities.
func (c *UserCredentialClient) CreateBulk(builders ...*UserCredentialCreate) *UserCredentialCreateBulk {
	return &UserCredentialCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserCredentialClient) MapCreateBulk(slice any, setFunc func(*UserCredentialCreate, int)) *UserCredentialCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCredentialCreateBulk{err: fmt.Errorf("calling to UserCredentialClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCredentialCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCredentialCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UserCredential.
func (c *UserCredentialClient) Update() *UserCredentialUpdate {
	mutation := newUserCredentialMutation(c.config, OpUpdate)
	return &UserCredentialUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserCredentialClient) UpdateOne(uc *UserCredential) *UserCredentialUpdateOne {
	mutation := newUserCredentialMutation(c.config, OpUpdateOne, withUserCredential(uc))
	return &UserCredentialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserCredentialClient) UpdateOneID(id uint32) *UserCredentialUpdateOne {
	mutation := newUserCredentialMutation(c.config, OpUpdateOne, withUserCredentialID(id))
	return &UserCredentialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UserCredential.
func (c *UserCredentialClient) Delete() *UserCredentialDelete {
	mutation := newUserCredentialMutation(c.config, OpDelete)
	return &UserCredentialDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserCredentialClient) DeleteOne(uc *UserCredential) *UserCredentialDeleteOne {
	return c.DeleteOneID(uc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserCredentialClient) DeleteOneID(id uint32) *UserCredentialDeleteOne {
	builder := c.Delete().Where(usercredential.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserCredentialDeleteOne{builder}
}

// Query returns a query builder for UserCredential.
func (c *UserCredentialClient) Query() *UserCredentialQuery {
	return &UserCredentialQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUserCredential},
		inters: c.Interceptors(),
	}
}

// Get returns a UserCredential entity by its id.
func (c *UserCredentialClient) Get(ctx context.Context, id uint32) (*UserCredential, error) {
	return c.Query().Where(usercredential.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserCredentialClient) GetX(ctx context.Context, id uint32) *UserCredential {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *UserCredentialClient) Hooks() []Hook {
	return c.hooks.UserCredential
}

// Interceptors returns the client interceptors.
func (c *UserCredentialClient) Interceptors() []Interceptor {
	return c.inters.UserCredential
}

func (c *UserCredentialClient) mutate(ctx context.Context, m *UserCredentialMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCredentialCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserCredentialUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserCredentialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserCredentialDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown UserCredential mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		AdminLoginLog, AdminLoginRestriction, AdminOperationLog, ApiResource,
		Department, Dict, File, Menu, NotificationMessage, NotificationMessageCategory,
		NotificationMessageRecipient, Organization, Position, PrivateMessage, Role,
		Task, Tenant, User, UserCredential []ent.Hook
	}
	inters struct {
		AdminLoginLog, AdminLoginRestriction, AdminOperationLog, ApiResource,
		Department, Dict, File, Menu, NotificationMessage, NotificationMessageCategory,
		NotificationMessageRecipient, Organization, Position, PrivateMessage, Role,
		Task, Tenant, User, UserCredential []ent.Interceptor
	}
)
