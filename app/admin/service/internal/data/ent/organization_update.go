// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/organization"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationUpdate is the builder for updating Organization entities.
type OrganizationUpdate struct {
	config
	hooks     []Hook
	mutation  *OrganizationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the OrganizationUpdate builder.
func (ou *OrganizationUpdate) Where(ps ...predicate.Organization) *OrganizationUpdate {
	ou.mutation.Where(ps...)
	return ou
}

// SetUpdateTime sets the "update_time" field.
func (ou *OrganizationUpdate) SetUpdateTime(t time.Time) *OrganizationUpdate {
	ou.mutation.SetUpdateTime(t)
	return ou
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableUpdateTime(t *time.Time) *OrganizationUpdate {
	if t != nil {
		ou.SetUpdateTime(*t)
	}
	return ou
}

// ClearUpdateTime clears the value of the "update_time" field.
func (ou *OrganizationUpdate) ClearUpdateTime() *OrganizationUpdate {
	ou.mutation.ClearUpdateTime()
	return ou
}

// SetDeleteTime sets the "delete_time" field.
func (ou *OrganizationUpdate) SetDeleteTime(t time.Time) *OrganizationUpdate {
	ou.mutation.SetDeleteTime(t)
	return ou
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableDeleteTime(t *time.Time) *OrganizationUpdate {
	if t != nil {
		ou.SetDeleteTime(*t)
	}
	return ou
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (ou *OrganizationUpdate) ClearDeleteTime() *OrganizationUpdate {
	ou.mutation.ClearDeleteTime()
	return ou
}

// SetStatus sets the "status" field.
func (ou *OrganizationUpdate) SetStatus(o organization.Status) *OrganizationUpdate {
	ou.mutation.SetStatus(o)
	return ou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableStatus(o *organization.Status) *OrganizationUpdate {
	if o != nil {
		ou.SetStatus(*o)
	}
	return ou
}

// ClearStatus clears the value of the "status" field.
func (ou *OrganizationUpdate) ClearStatus() *OrganizationUpdate {
	ou.mutation.ClearStatus()
	return ou
}

// SetCreateBy sets the "create_by" field.
func (ou *OrganizationUpdate) SetCreateBy(u uint32) *OrganizationUpdate {
	ou.mutation.ResetCreateBy()
	ou.mutation.SetCreateBy(u)
	return ou
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableCreateBy(u *uint32) *OrganizationUpdate {
	if u != nil {
		ou.SetCreateBy(*u)
	}
	return ou
}

// AddCreateBy adds u to the "create_by" field.
func (ou *OrganizationUpdate) AddCreateBy(u int32) *OrganizationUpdate {
	ou.mutation.AddCreateBy(u)
	return ou
}

// ClearCreateBy clears the value of the "create_by" field.
func (ou *OrganizationUpdate) ClearCreateBy() *OrganizationUpdate {
	ou.mutation.ClearCreateBy()
	return ou
}

// SetUpdateBy sets the "update_by" field.
func (ou *OrganizationUpdate) SetUpdateBy(u uint32) *OrganizationUpdate {
	ou.mutation.ResetUpdateBy()
	ou.mutation.SetUpdateBy(u)
	return ou
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableUpdateBy(u *uint32) *OrganizationUpdate {
	if u != nil {
		ou.SetUpdateBy(*u)
	}
	return ou
}

// AddUpdateBy adds u to the "update_by" field.
func (ou *OrganizationUpdate) AddUpdateBy(u int32) *OrganizationUpdate {
	ou.mutation.AddUpdateBy(u)
	return ou
}

// ClearUpdateBy clears the value of the "update_by" field.
func (ou *OrganizationUpdate) ClearUpdateBy() *OrganizationUpdate {
	ou.mutation.ClearUpdateBy()
	return ou
}

// SetRemark sets the "remark" field.
func (ou *OrganizationUpdate) SetRemark(s string) *OrganizationUpdate {
	ou.mutation.SetRemark(s)
	return ou
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableRemark(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetRemark(*s)
	}
	return ou
}

// ClearRemark clears the value of the "remark" field.
func (ou *OrganizationUpdate) ClearRemark() *OrganizationUpdate {
	ou.mutation.ClearRemark()
	return ou
}

// SetName sets the "name" field.
func (ou *OrganizationUpdate) SetName(s string) *OrganizationUpdate {
	ou.mutation.SetName(s)
	return ou
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableName(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetName(*s)
	}
	return ou
}

// ClearName clears the value of the "name" field.
func (ou *OrganizationUpdate) ClearName() *OrganizationUpdate {
	ou.mutation.ClearName()
	return ou
}

// SetParentID sets the "parent_id" field.
func (ou *OrganizationUpdate) SetParentID(u uint32) *OrganizationUpdate {
	ou.mutation.SetParentID(u)
	return ou
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableParentID(u *uint32) *OrganizationUpdate {
	if u != nil {
		ou.SetParentID(*u)
	}
	return ou
}

// ClearParentID clears the value of the "parent_id" field.
func (ou *OrganizationUpdate) ClearParentID() *OrganizationUpdate {
	ou.mutation.ClearParentID()
	return ou
}

// SetSortID sets the "sort_id" field.
func (ou *OrganizationUpdate) SetSortID(i int32) *OrganizationUpdate {
	ou.mutation.ResetSortID()
	ou.mutation.SetSortID(i)
	return ou
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableSortID(i *int32) *OrganizationUpdate {
	if i != nil {
		ou.SetSortID(*i)
	}
	return ou
}

// AddSortID adds i to the "sort_id" field.
func (ou *OrganizationUpdate) AddSortID(i int32) *OrganizationUpdate {
	ou.mutation.AddSortID(i)
	return ou
}

// ClearSortID clears the value of the "sort_id" field.
func (ou *OrganizationUpdate) ClearSortID() *OrganizationUpdate {
	ou.mutation.ClearSortID()
	return ou
}

// SetParent sets the "parent" edge to the Organization entity.
func (ou *OrganizationUpdate) SetParent(o *Organization) *OrganizationUpdate {
	return ou.SetParentID(o.ID)
}

// AddChildIDs adds the "children" edge to the Organization entity by IDs.
func (ou *OrganizationUpdate) AddChildIDs(ids ...uint32) *OrganizationUpdate {
	ou.mutation.AddChildIDs(ids...)
	return ou
}

// AddChildren adds the "children" edges to the Organization entity.
func (ou *OrganizationUpdate) AddChildren(o ...*Organization) *OrganizationUpdate {
	ids := make([]uint32, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ou.AddChildIDs(ids...)
}

// Mutation returns the OrganizationMutation object of the builder.
func (ou *OrganizationUpdate) Mutation() *OrganizationMutation {
	return ou.mutation
}

// ClearParent clears the "parent" edge to the Organization entity.
func (ou *OrganizationUpdate) ClearParent() *OrganizationUpdate {
	ou.mutation.ClearParent()
	return ou
}

// ClearChildren clears all "children" edges to the Organization entity.
func (ou *OrganizationUpdate) ClearChildren() *OrganizationUpdate {
	ou.mutation.ClearChildren()
	return ou
}

// RemoveChildIDs removes the "children" edge to Organization entities by IDs.
func (ou *OrganizationUpdate) RemoveChildIDs(ids ...uint32) *OrganizationUpdate {
	ou.mutation.RemoveChildIDs(ids...)
	return ou
}

// RemoveChildren removes "children" edges to Organization entities.
func (ou *OrganizationUpdate) RemoveChildren(o ...*Organization) *OrganizationUpdate {
	ids := make([]uint32, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ou.RemoveChildIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ou *OrganizationUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, ou.sqlSave, ou.mutation, ou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ou *OrganizationUpdate) SaveX(ctx context.Context) int {
	affected, err := ou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ou *OrganizationUpdate) Exec(ctx context.Context) error {
	_, err := ou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ou *OrganizationUpdate) ExecX(ctx context.Context) {
	if err := ou.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ou *OrganizationUpdate) check() error {
	if v, ok := ou.mutation.Status(); ok {
		if err := organization.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Organization.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (ou *OrganizationUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *OrganizationUpdate {
	ou.modifiers = append(ou.modifiers, modifiers...)
	return ou
}

func (ou *OrganizationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ou.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(organization.Table, organization.Columns, sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32))
	if ps := ou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if ou.mutation.CreateTimeCleared() {
		_spec.ClearField(organization.FieldCreateTime, field.TypeTime)
	}
	if value, ok := ou.mutation.UpdateTime(); ok {
		_spec.SetField(organization.FieldUpdateTime, field.TypeTime, value)
	}
	if ou.mutation.UpdateTimeCleared() {
		_spec.ClearField(organization.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := ou.mutation.DeleteTime(); ok {
		_spec.SetField(organization.FieldDeleteTime, field.TypeTime, value)
	}
	if ou.mutation.DeleteTimeCleared() {
		_spec.ClearField(organization.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := ou.mutation.Status(); ok {
		_spec.SetField(organization.FieldStatus, field.TypeEnum, value)
	}
	if ou.mutation.StatusCleared() {
		_spec.ClearField(organization.FieldStatus, field.TypeEnum)
	}
	if value, ok := ou.mutation.CreateBy(); ok {
		_spec.SetField(organization.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := ou.mutation.AddedCreateBy(); ok {
		_spec.AddField(organization.FieldCreateBy, field.TypeUint32, value)
	}
	if ou.mutation.CreateByCleared() {
		_spec.ClearField(organization.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := ou.mutation.UpdateBy(); ok {
		_spec.SetField(organization.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := ou.mutation.AddedUpdateBy(); ok {
		_spec.AddField(organization.FieldUpdateBy, field.TypeUint32, value)
	}
	if ou.mutation.UpdateByCleared() {
		_spec.ClearField(organization.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := ou.mutation.Remark(); ok {
		_spec.SetField(organization.FieldRemark, field.TypeString, value)
	}
	if ou.mutation.RemarkCleared() {
		_spec.ClearField(organization.FieldRemark, field.TypeString)
	}
	if ou.mutation.TenantIDCleared() {
		_spec.ClearField(organization.FieldTenantID, field.TypeUint32)
	}
	if value, ok := ou.mutation.Name(); ok {
		_spec.SetField(organization.FieldName, field.TypeString, value)
	}
	if ou.mutation.NameCleared() {
		_spec.ClearField(organization.FieldName, field.TypeString)
	}
	if value, ok := ou.mutation.SortID(); ok {
		_spec.SetField(organization.FieldSortID, field.TypeInt32, value)
	}
	if value, ok := ou.mutation.AddedSortID(); ok {
		_spec.AddField(organization.FieldSortID, field.TypeInt32, value)
	}
	if ou.mutation.SortIDCleared() {
		_spec.ClearField(organization.FieldSortID, field.TypeInt32)
	}
	if ou.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ou.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !ou.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(ou.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, ou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{organization.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ou.mutation.done = true
	return n, nil
}

// OrganizationUpdateOne is the builder for updating a single Organization entity.
type OrganizationUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *OrganizationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (ouo *OrganizationUpdateOne) SetUpdateTime(t time.Time) *OrganizationUpdateOne {
	ouo.mutation.SetUpdateTime(t)
	return ouo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableUpdateTime(t *time.Time) *OrganizationUpdateOne {
	if t != nil {
		ouo.SetUpdateTime(*t)
	}
	return ouo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (ouo *OrganizationUpdateOne) ClearUpdateTime() *OrganizationUpdateOne {
	ouo.mutation.ClearUpdateTime()
	return ouo
}

// SetDeleteTime sets the "delete_time" field.
func (ouo *OrganizationUpdateOne) SetDeleteTime(t time.Time) *OrganizationUpdateOne {
	ouo.mutation.SetDeleteTime(t)
	return ouo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableDeleteTime(t *time.Time) *OrganizationUpdateOne {
	if t != nil {
		ouo.SetDeleteTime(*t)
	}
	return ouo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (ouo *OrganizationUpdateOne) ClearDeleteTime() *OrganizationUpdateOne {
	ouo.mutation.ClearDeleteTime()
	return ouo
}

// SetStatus sets the "status" field.
func (ouo *OrganizationUpdateOne) SetStatus(o organization.Status) *OrganizationUpdateOne {
	ouo.mutation.SetStatus(o)
	return ouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableStatus(o *organization.Status) *OrganizationUpdateOne {
	if o != nil {
		ouo.SetStatus(*o)
	}
	return ouo
}

// ClearStatus clears the value of the "status" field.
func (ouo *OrganizationUpdateOne) ClearStatus() *OrganizationUpdateOne {
	ouo.mutation.ClearStatus()
	return ouo
}

// SetCreateBy sets the "create_by" field.
func (ouo *OrganizationUpdateOne) SetCreateBy(u uint32) *OrganizationUpdateOne {
	ouo.mutation.ResetCreateBy()
	ouo.mutation.SetCreateBy(u)
	return ouo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableCreateBy(u *uint32) *OrganizationUpdateOne {
	if u != nil {
		ouo.SetCreateBy(*u)
	}
	return ouo
}

// AddCreateBy adds u to the "create_by" field.
func (ouo *OrganizationUpdateOne) AddCreateBy(u int32) *OrganizationUpdateOne {
	ouo.mutation.AddCreateBy(u)
	return ouo
}

// ClearCreateBy clears the value of the "create_by" field.
func (ouo *OrganizationUpdateOne) ClearCreateBy() *OrganizationUpdateOne {
	ouo.mutation.ClearCreateBy()
	return ouo
}

// SetUpdateBy sets the "update_by" field.
func (ouo *OrganizationUpdateOne) SetUpdateBy(u uint32) *OrganizationUpdateOne {
	ouo.mutation.ResetUpdateBy()
	ouo.mutation.SetUpdateBy(u)
	return ouo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableUpdateBy(u *uint32) *OrganizationUpdateOne {
	if u != nil {
		ouo.SetUpdateBy(*u)
	}
	return ouo
}

// AddUpdateBy adds u to the "update_by" field.
func (ouo *OrganizationUpdateOne) AddUpdateBy(u int32) *OrganizationUpdateOne {
	ouo.mutation.AddUpdateBy(u)
	return ouo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (ouo *OrganizationUpdateOne) ClearUpdateBy() *OrganizationUpdateOne {
	ouo.mutation.ClearUpdateBy()
	return ouo
}

// SetRemark sets the "remark" field.
func (ouo *OrganizationUpdateOne) SetRemark(s string) *OrganizationUpdateOne {
	ouo.mutation.SetRemark(s)
	return ouo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableRemark(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetRemark(*s)
	}
	return ouo
}

// ClearRemark clears the value of the "remark" field.
func (ouo *OrganizationUpdateOne) ClearRemark() *OrganizationUpdateOne {
	ouo.mutation.ClearRemark()
	return ouo
}

// SetName sets the "name" field.
func (ouo *OrganizationUpdateOne) SetName(s string) *OrganizationUpdateOne {
	ouo.mutation.SetName(s)
	return ouo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableName(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetName(*s)
	}
	return ouo
}

// ClearName clears the value of the "name" field.
func (ouo *OrganizationUpdateOne) ClearName() *OrganizationUpdateOne {
	ouo.mutation.ClearName()
	return ouo
}

// SetParentID sets the "parent_id" field.
func (ouo *OrganizationUpdateOne) SetParentID(u uint32) *OrganizationUpdateOne {
	ouo.mutation.SetParentID(u)
	return ouo
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableParentID(u *uint32) *OrganizationUpdateOne {
	if u != nil {
		ouo.SetParentID(*u)
	}
	return ouo
}

// ClearParentID clears the value of the "parent_id" field.
func (ouo *OrganizationUpdateOne) ClearParentID() *OrganizationUpdateOne {
	ouo.mutation.ClearParentID()
	return ouo
}

// SetSortID sets the "sort_id" field.
func (ouo *OrganizationUpdateOne) SetSortID(i int32) *OrganizationUpdateOne {
	ouo.mutation.ResetSortID()
	ouo.mutation.SetSortID(i)
	return ouo
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableSortID(i *int32) *OrganizationUpdateOne {
	if i != nil {
		ouo.SetSortID(*i)
	}
	return ouo
}

// AddSortID adds i to the "sort_id" field.
func (ouo *OrganizationUpdateOne) AddSortID(i int32) *OrganizationUpdateOne {
	ouo.mutation.AddSortID(i)
	return ouo
}

// ClearSortID clears the value of the "sort_id" field.
func (ouo *OrganizationUpdateOne) ClearSortID() *OrganizationUpdateOne {
	ouo.mutation.ClearSortID()
	return ouo
}

// SetParent sets the "parent" edge to the Organization entity.
func (ouo *OrganizationUpdateOne) SetParent(o *Organization) *OrganizationUpdateOne {
	return ouo.SetParentID(o.ID)
}

// AddChildIDs adds the "children" edge to the Organization entity by IDs.
func (ouo *OrganizationUpdateOne) AddChildIDs(ids ...uint32) *OrganizationUpdateOne {
	ouo.mutation.AddChildIDs(ids...)
	return ouo
}

// AddChildren adds the "children" edges to the Organization entity.
func (ouo *OrganizationUpdateOne) AddChildren(o ...*Organization) *OrganizationUpdateOne {
	ids := make([]uint32, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ouo.AddChildIDs(ids...)
}

// Mutation returns the OrganizationMutation object of the builder.
func (ouo *OrganizationUpdateOne) Mutation() *OrganizationMutation {
	return ouo.mutation
}

// ClearParent clears the "parent" edge to the Organization entity.
func (ouo *OrganizationUpdateOne) ClearParent() *OrganizationUpdateOne {
	ouo.mutation.ClearParent()
	return ouo
}

// ClearChildren clears all "children" edges to the Organization entity.
func (ouo *OrganizationUpdateOne) ClearChildren() *OrganizationUpdateOne {
	ouo.mutation.ClearChildren()
	return ouo
}

// RemoveChildIDs removes the "children" edge to Organization entities by IDs.
func (ouo *OrganizationUpdateOne) RemoveChildIDs(ids ...uint32) *OrganizationUpdateOne {
	ouo.mutation.RemoveChildIDs(ids...)
	return ouo
}

// RemoveChildren removes "children" edges to Organization entities.
func (ouo *OrganizationUpdateOne) RemoveChildren(o ...*Organization) *OrganizationUpdateOne {
	ids := make([]uint32, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ouo.RemoveChildIDs(ids...)
}

// Where appends a list predicates to the OrganizationUpdate builder.
func (ouo *OrganizationUpdateOne) Where(ps ...predicate.Organization) *OrganizationUpdateOne {
	ouo.mutation.Where(ps...)
	return ouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ouo *OrganizationUpdateOne) Select(field string, fields ...string) *OrganizationUpdateOne {
	ouo.fields = append([]string{field}, fields...)
	return ouo
}

// Save executes the query and returns the updated Organization entity.
func (ouo *OrganizationUpdateOne) Save(ctx context.Context) (*Organization, error) {
	return withHooks(ctx, ouo.sqlSave, ouo.mutation, ouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouo *OrganizationUpdateOne) SaveX(ctx context.Context) *Organization {
	node, err := ouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ouo *OrganizationUpdateOne) Exec(ctx context.Context) error {
	_, err := ouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouo *OrganizationUpdateOne) ExecX(ctx context.Context) {
	if err := ouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ouo *OrganizationUpdateOne) check() error {
	if v, ok := ouo.mutation.Status(); ok {
		if err := organization.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Organization.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (ouo *OrganizationUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *OrganizationUpdateOne {
	ouo.modifiers = append(ouo.modifiers, modifiers...)
	return ouo
}

func (ouo *OrganizationUpdateOne) sqlSave(ctx context.Context) (_node *Organization, err error) {
	if err := ouo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(organization.Table, organization.Columns, sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32))
	id, ok := ouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Organization.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, organization.FieldID)
		for _, f := range fields {
			if !organization.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != organization.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if ouo.mutation.CreateTimeCleared() {
		_spec.ClearField(organization.FieldCreateTime, field.TypeTime)
	}
	if value, ok := ouo.mutation.UpdateTime(); ok {
		_spec.SetField(organization.FieldUpdateTime, field.TypeTime, value)
	}
	if ouo.mutation.UpdateTimeCleared() {
		_spec.ClearField(organization.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := ouo.mutation.DeleteTime(); ok {
		_spec.SetField(organization.FieldDeleteTime, field.TypeTime, value)
	}
	if ouo.mutation.DeleteTimeCleared() {
		_spec.ClearField(organization.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := ouo.mutation.Status(); ok {
		_spec.SetField(organization.FieldStatus, field.TypeEnum, value)
	}
	if ouo.mutation.StatusCleared() {
		_spec.ClearField(organization.FieldStatus, field.TypeEnum)
	}
	if value, ok := ouo.mutation.CreateBy(); ok {
		_spec.SetField(organization.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := ouo.mutation.AddedCreateBy(); ok {
		_spec.AddField(organization.FieldCreateBy, field.TypeUint32, value)
	}
	if ouo.mutation.CreateByCleared() {
		_spec.ClearField(organization.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := ouo.mutation.UpdateBy(); ok {
		_spec.SetField(organization.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := ouo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(organization.FieldUpdateBy, field.TypeUint32, value)
	}
	if ouo.mutation.UpdateByCleared() {
		_spec.ClearField(organization.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := ouo.mutation.Remark(); ok {
		_spec.SetField(organization.FieldRemark, field.TypeString, value)
	}
	if ouo.mutation.RemarkCleared() {
		_spec.ClearField(organization.FieldRemark, field.TypeString)
	}
	if ouo.mutation.TenantIDCleared() {
		_spec.ClearField(organization.FieldTenantID, field.TypeUint32)
	}
	if value, ok := ouo.mutation.Name(); ok {
		_spec.SetField(organization.FieldName, field.TypeString, value)
	}
	if ouo.mutation.NameCleared() {
		_spec.ClearField(organization.FieldName, field.TypeString)
	}
	if value, ok := ouo.mutation.SortID(); ok {
		_spec.SetField(organization.FieldSortID, field.TypeInt32, value)
	}
	if value, ok := ouo.mutation.AddedSortID(); ok {
		_spec.AddField(organization.FieldSortID, field.TypeInt32, value)
	}
	if ouo.mutation.SortIDCleared() {
		_spec.ClearField(organization.FieldSortID, field.TypeInt32)
	}
	if ouo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !ouo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(ouo.modifiers...)
	_node = &Organization{config: ouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{organization.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ouo.mutation.done = true
	return _node, nil
}
