// Code generated by ent, DO NOT EDIT.

package usercredential

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldDeleteTime, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldTenantID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldUserID, v))
}

// Identifier applies equality check predicate on the "identifier" field. It's identical to IdentifierEQ.
func Identifier(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldIdentifier, v))
}

// Credential applies equality check predicate on the "credential" field. It's identical to CredentialEQ.
func Credential(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldCredential, v))
}

// IsPrimary applies equality check predicate on the "is_primary" field. It's identical to IsPrimaryEQ.
func IsPrimary(v bool) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldIsPrimary, v))
}

// ExtraInfo applies equality check predicate on the "extra_info" field. It's identical to ExtraInfoEQ.
func ExtraInfo(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldExtraInfo, v))
}

// ActivateToken applies equality check predicate on the "activate_token" field. It's identical to ActivateTokenEQ.
func ActivateToken(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldActivateToken, v))
}

// ResetToken applies equality check predicate on the "reset_token" field. It's identical to ResetTokenEQ.
func ResetToken(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldResetToken, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldDeleteTime))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldTenantID))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uint32) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldUserID))
}

// IdentityTypeEQ applies the EQ predicate on the "identity_type" field.
func IdentityTypeEQ(v IdentityType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldIdentityType, v))
}

// IdentityTypeNEQ applies the NEQ predicate on the "identity_type" field.
func IdentityTypeNEQ(v IdentityType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldIdentityType, v))
}

// IdentityTypeIn applies the In predicate on the "identity_type" field.
func IdentityTypeIn(vs ...IdentityType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldIdentityType, vs...))
}

// IdentityTypeNotIn applies the NotIn predicate on the "identity_type" field.
func IdentityTypeNotIn(vs ...IdentityType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldIdentityType, vs...))
}

// IdentityTypeIsNil applies the IsNil predicate on the "identity_type" field.
func IdentityTypeIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldIdentityType))
}

// IdentityTypeNotNil applies the NotNil predicate on the "identity_type" field.
func IdentityTypeNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldIdentityType))
}

// IdentifierEQ applies the EQ predicate on the "identifier" field.
func IdentifierEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldIdentifier, v))
}

// IdentifierNEQ applies the NEQ predicate on the "identifier" field.
func IdentifierNEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldIdentifier, v))
}

// IdentifierIn applies the In predicate on the "identifier" field.
func IdentifierIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldIdentifier, vs...))
}

// IdentifierNotIn applies the NotIn predicate on the "identifier" field.
func IdentifierNotIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldIdentifier, vs...))
}

// IdentifierGT applies the GT predicate on the "identifier" field.
func IdentifierGT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldIdentifier, v))
}

// IdentifierGTE applies the GTE predicate on the "identifier" field.
func IdentifierGTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldIdentifier, v))
}

// IdentifierLT applies the LT predicate on the "identifier" field.
func IdentifierLT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldIdentifier, v))
}

// IdentifierLTE applies the LTE predicate on the "identifier" field.
func IdentifierLTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldIdentifier, v))
}

// IdentifierContains applies the Contains predicate on the "identifier" field.
func IdentifierContains(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContains(FieldIdentifier, v))
}

// IdentifierHasPrefix applies the HasPrefix predicate on the "identifier" field.
func IdentifierHasPrefix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasPrefix(FieldIdentifier, v))
}

// IdentifierHasSuffix applies the HasSuffix predicate on the "identifier" field.
func IdentifierHasSuffix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasSuffix(FieldIdentifier, v))
}

// IdentifierIsNil applies the IsNil predicate on the "identifier" field.
func IdentifierIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldIdentifier))
}

// IdentifierNotNil applies the NotNil predicate on the "identifier" field.
func IdentifierNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldIdentifier))
}

// IdentifierEqualFold applies the EqualFold predicate on the "identifier" field.
func IdentifierEqualFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEqualFold(FieldIdentifier, v))
}

// IdentifierContainsFold applies the ContainsFold predicate on the "identifier" field.
func IdentifierContainsFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContainsFold(FieldIdentifier, v))
}

// CredentialTypeEQ applies the EQ predicate on the "credential_type" field.
func CredentialTypeEQ(v CredentialType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldCredentialType, v))
}

// CredentialTypeNEQ applies the NEQ predicate on the "credential_type" field.
func CredentialTypeNEQ(v CredentialType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldCredentialType, v))
}

// CredentialTypeIn applies the In predicate on the "credential_type" field.
func CredentialTypeIn(vs ...CredentialType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldCredentialType, vs...))
}

// CredentialTypeNotIn applies the NotIn predicate on the "credential_type" field.
func CredentialTypeNotIn(vs ...CredentialType) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldCredentialType, vs...))
}

// CredentialTypeIsNil applies the IsNil predicate on the "credential_type" field.
func CredentialTypeIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldCredentialType))
}

// CredentialTypeNotNil applies the NotNil predicate on the "credential_type" field.
func CredentialTypeNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldCredentialType))
}

// CredentialEQ applies the EQ predicate on the "credential" field.
func CredentialEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldCredential, v))
}

// CredentialNEQ applies the NEQ predicate on the "credential" field.
func CredentialNEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldCredential, v))
}

// CredentialIn applies the In predicate on the "credential" field.
func CredentialIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldCredential, vs...))
}

// CredentialNotIn applies the NotIn predicate on the "credential" field.
func CredentialNotIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldCredential, vs...))
}

// CredentialGT applies the GT predicate on the "credential" field.
func CredentialGT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldCredential, v))
}

// CredentialGTE applies the GTE predicate on the "credential" field.
func CredentialGTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldCredential, v))
}

// CredentialLT applies the LT predicate on the "credential" field.
func CredentialLT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldCredential, v))
}

// CredentialLTE applies the LTE predicate on the "credential" field.
func CredentialLTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldCredential, v))
}

// CredentialContains applies the Contains predicate on the "credential" field.
func CredentialContains(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContains(FieldCredential, v))
}

// CredentialHasPrefix applies the HasPrefix predicate on the "credential" field.
func CredentialHasPrefix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasPrefix(FieldCredential, v))
}

// CredentialHasSuffix applies the HasSuffix predicate on the "credential" field.
func CredentialHasSuffix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasSuffix(FieldCredential, v))
}

// CredentialIsNil applies the IsNil predicate on the "credential" field.
func CredentialIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldCredential))
}

// CredentialNotNil applies the NotNil predicate on the "credential" field.
func CredentialNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldCredential))
}

// CredentialEqualFold applies the EqualFold predicate on the "credential" field.
func CredentialEqualFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEqualFold(FieldCredential, v))
}

// CredentialContainsFold applies the ContainsFold predicate on the "credential" field.
func CredentialContainsFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContainsFold(FieldCredential, v))
}

// IsPrimaryEQ applies the EQ predicate on the "is_primary" field.
func IsPrimaryEQ(v bool) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldIsPrimary, v))
}

// IsPrimaryNEQ applies the NEQ predicate on the "is_primary" field.
func IsPrimaryNEQ(v bool) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldIsPrimary, v))
}

// IsPrimaryIsNil applies the IsNil predicate on the "is_primary" field.
func IsPrimaryIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldIsPrimary))
}

// IsPrimaryNotNil applies the NotNil predicate on the "is_primary" field.
func IsPrimaryNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldIsPrimary))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldStatus))
}

// ExtraInfoEQ applies the EQ predicate on the "extra_info" field.
func ExtraInfoEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldExtraInfo, v))
}

// ExtraInfoNEQ applies the NEQ predicate on the "extra_info" field.
func ExtraInfoNEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldExtraInfo, v))
}

// ExtraInfoIn applies the In predicate on the "extra_info" field.
func ExtraInfoIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldExtraInfo, vs...))
}

// ExtraInfoNotIn applies the NotIn predicate on the "extra_info" field.
func ExtraInfoNotIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldExtraInfo, vs...))
}

// ExtraInfoGT applies the GT predicate on the "extra_info" field.
func ExtraInfoGT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldExtraInfo, v))
}

// ExtraInfoGTE applies the GTE predicate on the "extra_info" field.
func ExtraInfoGTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldExtraInfo, v))
}

// ExtraInfoLT applies the LT predicate on the "extra_info" field.
func ExtraInfoLT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldExtraInfo, v))
}

// ExtraInfoLTE applies the LTE predicate on the "extra_info" field.
func ExtraInfoLTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldExtraInfo, v))
}

// ExtraInfoContains applies the Contains predicate on the "extra_info" field.
func ExtraInfoContains(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContains(FieldExtraInfo, v))
}

// ExtraInfoHasPrefix applies the HasPrefix predicate on the "extra_info" field.
func ExtraInfoHasPrefix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasPrefix(FieldExtraInfo, v))
}

// ExtraInfoHasSuffix applies the HasSuffix predicate on the "extra_info" field.
func ExtraInfoHasSuffix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasSuffix(FieldExtraInfo, v))
}

// ExtraInfoIsNil applies the IsNil predicate on the "extra_info" field.
func ExtraInfoIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldExtraInfo))
}

// ExtraInfoNotNil applies the NotNil predicate on the "extra_info" field.
func ExtraInfoNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldExtraInfo))
}

// ExtraInfoEqualFold applies the EqualFold predicate on the "extra_info" field.
func ExtraInfoEqualFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEqualFold(FieldExtraInfo, v))
}

// ExtraInfoContainsFold applies the ContainsFold predicate on the "extra_info" field.
func ExtraInfoContainsFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContainsFold(FieldExtraInfo, v))
}

// ActivateTokenEQ applies the EQ predicate on the "activate_token" field.
func ActivateTokenEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldActivateToken, v))
}

// ActivateTokenNEQ applies the NEQ predicate on the "activate_token" field.
func ActivateTokenNEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldActivateToken, v))
}

// ActivateTokenIn applies the In predicate on the "activate_token" field.
func ActivateTokenIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldActivateToken, vs...))
}

// ActivateTokenNotIn applies the NotIn predicate on the "activate_token" field.
func ActivateTokenNotIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldActivateToken, vs...))
}

// ActivateTokenGT applies the GT predicate on the "activate_token" field.
func ActivateTokenGT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldActivateToken, v))
}

// ActivateTokenGTE applies the GTE predicate on the "activate_token" field.
func ActivateTokenGTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldActivateToken, v))
}

// ActivateTokenLT applies the LT predicate on the "activate_token" field.
func ActivateTokenLT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldActivateToken, v))
}

// ActivateTokenLTE applies the LTE predicate on the "activate_token" field.
func ActivateTokenLTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldActivateToken, v))
}

// ActivateTokenContains applies the Contains predicate on the "activate_token" field.
func ActivateTokenContains(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContains(FieldActivateToken, v))
}

// ActivateTokenHasPrefix applies the HasPrefix predicate on the "activate_token" field.
func ActivateTokenHasPrefix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasPrefix(FieldActivateToken, v))
}

// ActivateTokenHasSuffix applies the HasSuffix predicate on the "activate_token" field.
func ActivateTokenHasSuffix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasSuffix(FieldActivateToken, v))
}

// ActivateTokenIsNil applies the IsNil predicate on the "activate_token" field.
func ActivateTokenIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldActivateToken))
}

// ActivateTokenNotNil applies the NotNil predicate on the "activate_token" field.
func ActivateTokenNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldActivateToken))
}

// ActivateTokenEqualFold applies the EqualFold predicate on the "activate_token" field.
func ActivateTokenEqualFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEqualFold(FieldActivateToken, v))
}

// ActivateTokenContainsFold applies the ContainsFold predicate on the "activate_token" field.
func ActivateTokenContainsFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContainsFold(FieldActivateToken, v))
}

// ResetTokenEQ applies the EQ predicate on the "reset_token" field.
func ResetTokenEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEQ(FieldResetToken, v))
}

// ResetTokenNEQ applies the NEQ predicate on the "reset_token" field.
func ResetTokenNEQ(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNEQ(FieldResetToken, v))
}

// ResetTokenIn applies the In predicate on the "reset_token" field.
func ResetTokenIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIn(FieldResetToken, vs...))
}

// ResetTokenNotIn applies the NotIn predicate on the "reset_token" field.
func ResetTokenNotIn(vs ...string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotIn(FieldResetToken, vs...))
}

// ResetTokenGT applies the GT predicate on the "reset_token" field.
func ResetTokenGT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGT(FieldResetToken, v))
}

// ResetTokenGTE applies the GTE predicate on the "reset_token" field.
func ResetTokenGTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldGTE(FieldResetToken, v))
}

// ResetTokenLT applies the LT predicate on the "reset_token" field.
func ResetTokenLT(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLT(FieldResetToken, v))
}

// ResetTokenLTE applies the LTE predicate on the "reset_token" field.
func ResetTokenLTE(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldLTE(FieldResetToken, v))
}

// ResetTokenContains applies the Contains predicate on the "reset_token" field.
func ResetTokenContains(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContains(FieldResetToken, v))
}

// ResetTokenHasPrefix applies the HasPrefix predicate on the "reset_token" field.
func ResetTokenHasPrefix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasPrefix(FieldResetToken, v))
}

// ResetTokenHasSuffix applies the HasSuffix predicate on the "reset_token" field.
func ResetTokenHasSuffix(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldHasSuffix(FieldResetToken, v))
}

// ResetTokenIsNil applies the IsNil predicate on the "reset_token" field.
func ResetTokenIsNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldIsNull(FieldResetToken))
}

// ResetTokenNotNil applies the NotNil predicate on the "reset_token" field.
func ResetTokenNotNil() predicate.UserCredential {
	return predicate.UserCredential(sql.FieldNotNull(FieldResetToken))
}

// ResetTokenEqualFold applies the EqualFold predicate on the "reset_token" field.
func ResetTokenEqualFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldEqualFold(FieldResetToken, v))
}

// ResetTokenContainsFold applies the ContainsFold predicate on the "reset_token" field.
func ResetTokenContainsFold(v string) predicate.UserCredential {
	return predicate.UserCredential(sql.FieldContainsFold(FieldResetToken, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.UserCredential) predicate.UserCredential {
	return predicate.UserCredential(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.UserCredential) predicate.UserCredential {
	return predicate.UserCredential(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.UserCredential) predicate.UserCredential {
	return predicate.UserCredential(sql.NotPredicates(p))
}
