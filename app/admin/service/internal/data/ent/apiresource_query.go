// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/apiresource"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ApiResourceQuery is the builder for querying ApiResource entities.
type ApiResourceQuery struct {
	config
	ctx        *QueryContext
	order      []apiresource.OrderOption
	inters     []Interceptor
	predicates []predicate.ApiResource
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ApiResourceQuery builder.
func (arq *ApiResourceQuery) Where(ps ...predicate.ApiResource) *ApiResourceQuery {
	arq.predicates = append(arq.predicates, ps...)
	return arq
}

// Limit the number of records to be returned by this query.
func (arq *ApiResourceQuery) Limit(limit int) *ApiResourceQuery {
	arq.ctx.Limit = &limit
	return arq
}

// Offset to start from.
func (arq *ApiResourceQuery) Offset(offset int) *ApiResourceQuery {
	arq.ctx.Offset = &offset
	return arq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (arq *ApiResourceQuery) Unique(unique bool) *ApiResourceQuery {
	arq.ctx.Unique = &unique
	return arq
}

// Order specifies how the records should be ordered.
func (arq *ApiResourceQuery) Order(o ...apiresource.OrderOption) *ApiResourceQuery {
	arq.order = append(arq.order, o...)
	return arq
}

// First returns the first ApiResource entity from the query.
// Returns a *NotFoundError when no ApiResource was found.
func (arq *ApiResourceQuery) First(ctx context.Context) (*ApiResource, error) {
	nodes, err := arq.Limit(1).All(setContextOp(ctx, arq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{apiresource.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (arq *ApiResourceQuery) FirstX(ctx context.Context) *ApiResource {
	node, err := arq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ApiResource ID from the query.
// Returns a *NotFoundError when no ApiResource ID was found.
func (arq *ApiResourceQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = arq.Limit(1).IDs(setContextOp(ctx, arq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{apiresource.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (arq *ApiResourceQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := arq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ApiResource entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ApiResource entity is found.
// Returns a *NotFoundError when no ApiResource entities are found.
func (arq *ApiResourceQuery) Only(ctx context.Context) (*ApiResource, error) {
	nodes, err := arq.Limit(2).All(setContextOp(ctx, arq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{apiresource.Label}
	default:
		return nil, &NotSingularError{apiresource.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (arq *ApiResourceQuery) OnlyX(ctx context.Context) *ApiResource {
	node, err := arq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ApiResource ID in the query.
// Returns a *NotSingularError when more than one ApiResource ID is found.
// Returns a *NotFoundError when no entities are found.
func (arq *ApiResourceQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = arq.Limit(2).IDs(setContextOp(ctx, arq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{apiresource.Label}
	default:
		err = &NotSingularError{apiresource.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (arq *ApiResourceQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := arq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ApiResources.
func (arq *ApiResourceQuery) All(ctx context.Context) ([]*ApiResource, error) {
	ctx = setContextOp(ctx, arq.ctx, ent.OpQueryAll)
	if err := arq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ApiResource, *ApiResourceQuery]()
	return withInterceptors[[]*ApiResource](ctx, arq, qr, arq.inters)
}

// AllX is like All, but panics if an error occurs.
func (arq *ApiResourceQuery) AllX(ctx context.Context) []*ApiResource {
	nodes, err := arq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ApiResource IDs.
func (arq *ApiResourceQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if arq.ctx.Unique == nil && arq.path != nil {
		arq.Unique(true)
	}
	ctx = setContextOp(ctx, arq.ctx, ent.OpQueryIDs)
	if err = arq.Select(apiresource.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (arq *ApiResourceQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := arq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (arq *ApiResourceQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, arq.ctx, ent.OpQueryCount)
	if err := arq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, arq, querierCount[*ApiResourceQuery](), arq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (arq *ApiResourceQuery) CountX(ctx context.Context) int {
	count, err := arq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (arq *ApiResourceQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, arq.ctx, ent.OpQueryExist)
	switch _, err := arq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (arq *ApiResourceQuery) ExistX(ctx context.Context) bool {
	exist, err := arq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ApiResourceQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (arq *ApiResourceQuery) Clone() *ApiResourceQuery {
	if arq == nil {
		return nil
	}
	return &ApiResourceQuery{
		config:     arq.config,
		ctx:        arq.ctx.Clone(),
		order:      append([]apiresource.OrderOption{}, arq.order...),
		inters:     append([]Interceptor{}, arq.inters...),
		predicates: append([]predicate.ApiResource{}, arq.predicates...),
		// clone intermediate query.
		sql:       arq.sql.Clone(),
		path:      arq.path,
		modifiers: append([]func(*sql.Selector){}, arq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ApiResource.Query().
//		GroupBy(apiresource.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (arq *ApiResourceQuery) GroupBy(field string, fields ...string) *ApiResourceGroupBy {
	arq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ApiResourceGroupBy{build: arq}
	grbuild.flds = &arq.ctx.Fields
	grbuild.label = apiresource.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.ApiResource.Query().
//		Select(apiresource.FieldCreateTime).
//		Scan(ctx, &v)
func (arq *ApiResourceQuery) Select(fields ...string) *ApiResourceSelect {
	arq.ctx.Fields = append(arq.ctx.Fields, fields...)
	sbuild := &ApiResourceSelect{ApiResourceQuery: arq}
	sbuild.label = apiresource.Label
	sbuild.flds, sbuild.scan = &arq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ApiResourceSelect configured with the given aggregations.
func (arq *ApiResourceQuery) Aggregate(fns ...AggregateFunc) *ApiResourceSelect {
	return arq.Select().Aggregate(fns...)
}

func (arq *ApiResourceQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range arq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, arq); err != nil {
				return err
			}
		}
	}
	for _, f := range arq.ctx.Fields {
		if !apiresource.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if arq.path != nil {
		prev, err := arq.path(ctx)
		if err != nil {
			return err
		}
		arq.sql = prev
	}
	return nil
}

func (arq *ApiResourceQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ApiResource, error) {
	var (
		nodes = []*ApiResource{}
		_spec = arq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ApiResource).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ApiResource{config: arq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(arq.modifiers) > 0 {
		_spec.Modifiers = arq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, arq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (arq *ApiResourceQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := arq.querySpec()
	if len(arq.modifiers) > 0 {
		_spec.Modifiers = arq.modifiers
	}
	_spec.Node.Columns = arq.ctx.Fields
	if len(arq.ctx.Fields) > 0 {
		_spec.Unique = arq.ctx.Unique != nil && *arq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, arq.driver, _spec)
}

func (arq *ApiResourceQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(apiresource.Table, apiresource.Columns, sqlgraph.NewFieldSpec(apiresource.FieldID, field.TypeUint32))
	_spec.From = arq.sql
	if unique := arq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if arq.path != nil {
		_spec.Unique = true
	}
	if fields := arq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, apiresource.FieldID)
		for i := range fields {
			if fields[i] != apiresource.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := arq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := arq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := arq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := arq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (arq *ApiResourceQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(arq.driver.Dialect())
	t1 := builder.Table(apiresource.Table)
	columns := arq.ctx.Fields
	if len(columns) == 0 {
		columns = apiresource.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if arq.sql != nil {
		selector = arq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if arq.ctx.Unique != nil && *arq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range arq.modifiers {
		m(selector)
	}
	for _, p := range arq.predicates {
		p(selector)
	}
	for _, p := range arq.order {
		p(selector)
	}
	if offset := arq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := arq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (arq *ApiResourceQuery) ForUpdate(opts ...sql.LockOption) *ApiResourceQuery {
	if arq.driver.Dialect() == dialect.Postgres {
		arq.Unique(false)
	}
	arq.modifiers = append(arq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return arq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (arq *ApiResourceQuery) ForShare(opts ...sql.LockOption) *ApiResourceQuery {
	if arq.driver.Dialect() == dialect.Postgres {
		arq.Unique(false)
	}
	arq.modifiers = append(arq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return arq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (arq *ApiResourceQuery) Modify(modifiers ...func(s *sql.Selector)) *ApiResourceSelect {
	arq.modifiers = append(arq.modifiers, modifiers...)
	return arq.Select()
}

// ApiResourceGroupBy is the group-by builder for ApiResource entities.
type ApiResourceGroupBy struct {
	selector
	build *ApiResourceQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (argb *ApiResourceGroupBy) Aggregate(fns ...AggregateFunc) *ApiResourceGroupBy {
	argb.fns = append(argb.fns, fns...)
	return argb
}

// Scan applies the selector query and scans the result into the given value.
func (argb *ApiResourceGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, argb.build.ctx, ent.OpQueryGroupBy)
	if err := argb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ApiResourceQuery, *ApiResourceGroupBy](ctx, argb.build, argb, argb.build.inters, v)
}

func (argb *ApiResourceGroupBy) sqlScan(ctx context.Context, root *ApiResourceQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(argb.fns))
	for _, fn := range argb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*argb.flds)+len(argb.fns))
		for _, f := range *argb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*argb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := argb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ApiResourceSelect is the builder for selecting fields of ApiResource entities.
type ApiResourceSelect struct {
	*ApiResourceQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ars *ApiResourceSelect) Aggregate(fns ...AggregateFunc) *ApiResourceSelect {
	ars.fns = append(ars.fns, fns...)
	return ars
}

// Scan applies the selector query and scans the result into the given value.
func (ars *ApiResourceSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ars.ctx, ent.OpQuerySelect)
	if err := ars.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ApiResourceQuery, *ApiResourceSelect](ctx, ars.ApiResourceQuery, ars, ars.inters, v)
}

func (ars *ApiResourceSelect) sqlScan(ctx context.Context, root *ApiResourceQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ars.fns))
	for _, fn := range ars.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ars.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ars.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ars *ApiResourceSelect) Modify(modifiers ...func(s *sql.Selector)) *ApiResourceSelect {
	ars.modifiers = append(ars.modifiers, modifiers...)
	return ars
}
