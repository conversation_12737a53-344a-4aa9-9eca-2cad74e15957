// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/department"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DepartmentCreate is the builder for creating a Department entity.
type DepartmentCreate struct {
	config
	mutation *DepartmentMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (dc *DepartmentCreate) SetCreateTime(t time.Time) *DepartmentCreate {
	dc.mutation.SetCreateTime(t)
	return dc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableCreateTime(t *time.Time) *DepartmentCreate {
	if t != nil {
		dc.SetCreateTime(*t)
	}
	return dc
}

// SetUpdateTime sets the "update_time" field.
func (dc *DepartmentCreate) SetUpdateTime(t time.Time) *DepartmentCreate {
	dc.mutation.SetUpdateTime(t)
	return dc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableUpdateTime(t *time.Time) *DepartmentCreate {
	if t != nil {
		dc.SetUpdateTime(*t)
	}
	return dc
}

// SetDeleteTime sets the "delete_time" field.
func (dc *DepartmentCreate) SetDeleteTime(t time.Time) *DepartmentCreate {
	dc.mutation.SetDeleteTime(t)
	return dc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableDeleteTime(t *time.Time) *DepartmentCreate {
	if t != nil {
		dc.SetDeleteTime(*t)
	}
	return dc
}

// SetStatus sets the "status" field.
func (dc *DepartmentCreate) SetStatus(d department.Status) *DepartmentCreate {
	dc.mutation.SetStatus(d)
	return dc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableStatus(d *department.Status) *DepartmentCreate {
	if d != nil {
		dc.SetStatus(*d)
	}
	return dc
}

// SetCreateBy sets the "create_by" field.
func (dc *DepartmentCreate) SetCreateBy(u uint32) *DepartmentCreate {
	dc.mutation.SetCreateBy(u)
	return dc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableCreateBy(u *uint32) *DepartmentCreate {
	if u != nil {
		dc.SetCreateBy(*u)
	}
	return dc
}

// SetUpdateBy sets the "update_by" field.
func (dc *DepartmentCreate) SetUpdateBy(u uint32) *DepartmentCreate {
	dc.mutation.SetUpdateBy(u)
	return dc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableUpdateBy(u *uint32) *DepartmentCreate {
	if u != nil {
		dc.SetUpdateBy(*u)
	}
	return dc
}

// SetRemark sets the "remark" field.
func (dc *DepartmentCreate) SetRemark(s string) *DepartmentCreate {
	dc.mutation.SetRemark(s)
	return dc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableRemark(s *string) *DepartmentCreate {
	if s != nil {
		dc.SetRemark(*s)
	}
	return dc
}

// SetTenantID sets the "tenant_id" field.
func (dc *DepartmentCreate) SetTenantID(u uint32) *DepartmentCreate {
	dc.mutation.SetTenantID(u)
	return dc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableTenantID(u *uint32) *DepartmentCreate {
	if u != nil {
		dc.SetTenantID(*u)
	}
	return dc
}

// SetName sets the "name" field.
func (dc *DepartmentCreate) SetName(s string) *DepartmentCreate {
	dc.mutation.SetName(s)
	return dc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableName(s *string) *DepartmentCreate {
	if s != nil {
		dc.SetName(*s)
	}
	return dc
}

// SetParentID sets the "parent_id" field.
func (dc *DepartmentCreate) SetParentID(u uint32) *DepartmentCreate {
	dc.mutation.SetParentID(u)
	return dc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableParentID(u *uint32) *DepartmentCreate {
	if u != nil {
		dc.SetParentID(*u)
	}
	return dc
}

// SetOrganizationID sets the "organization_id" field.
func (dc *DepartmentCreate) SetOrganizationID(u uint32) *DepartmentCreate {
	dc.mutation.SetOrganizationID(u)
	return dc
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableOrganizationID(u *uint32) *DepartmentCreate {
	if u != nil {
		dc.SetOrganizationID(*u)
	}
	return dc
}

// SetSortID sets the "sort_id" field.
func (dc *DepartmentCreate) SetSortID(i int32) *DepartmentCreate {
	dc.mutation.SetSortID(i)
	return dc
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (dc *DepartmentCreate) SetNillableSortID(i *int32) *DepartmentCreate {
	if i != nil {
		dc.SetSortID(*i)
	}
	return dc
}

// SetID sets the "id" field.
func (dc *DepartmentCreate) SetID(u uint32) *DepartmentCreate {
	dc.mutation.SetID(u)
	return dc
}

// SetParent sets the "parent" edge to the Department entity.
func (dc *DepartmentCreate) SetParent(d *Department) *DepartmentCreate {
	return dc.SetParentID(d.ID)
}

// AddChildIDs adds the "children" edge to the Department entity by IDs.
func (dc *DepartmentCreate) AddChildIDs(ids ...uint32) *DepartmentCreate {
	dc.mutation.AddChildIDs(ids...)
	return dc
}

// AddChildren adds the "children" edges to the Department entity.
func (dc *DepartmentCreate) AddChildren(d ...*Department) *DepartmentCreate {
	ids := make([]uint32, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return dc.AddChildIDs(ids...)
}

// Mutation returns the DepartmentMutation object of the builder.
func (dc *DepartmentCreate) Mutation() *DepartmentMutation {
	return dc.mutation
}

// Save creates the Department in the database.
func (dc *DepartmentCreate) Save(ctx context.Context) (*Department, error) {
	dc.defaults()
	return withHooks(ctx, dc.sqlSave, dc.mutation, dc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dc *DepartmentCreate) SaveX(ctx context.Context) *Department {
	v, err := dc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dc *DepartmentCreate) Exec(ctx context.Context) error {
	_, err := dc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dc *DepartmentCreate) ExecX(ctx context.Context) {
	if err := dc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dc *DepartmentCreate) defaults() {
	if _, ok := dc.mutation.Status(); !ok {
		v := department.DefaultStatus
		dc.mutation.SetStatus(v)
	}
	if _, ok := dc.mutation.Remark(); !ok {
		v := department.DefaultRemark
		dc.mutation.SetRemark(v)
	}
	if _, ok := dc.mutation.Name(); !ok {
		v := department.DefaultName
		dc.mutation.SetName(v)
	}
	if _, ok := dc.mutation.SortID(); !ok {
		v := department.DefaultSortID
		dc.mutation.SetSortID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dc *DepartmentCreate) check() error {
	if v, ok := dc.mutation.Status(); ok {
		if err := department.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Department.status": %w`, err)}
		}
	}
	if v, ok := dc.mutation.TenantID(); ok {
		if err := department.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "Department.tenant_id": %w`, err)}
		}
	}
	if v, ok := dc.mutation.ID(); ok {
		if err := department.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Department.id": %w`, err)}
		}
	}
	return nil
}

func (dc *DepartmentCreate) sqlSave(ctx context.Context) (*Department, error) {
	if err := dc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	dc.mutation.id = &_node.ID
	dc.mutation.done = true
	return _node, nil
}

func (dc *DepartmentCreate) createSpec() (*Department, *sqlgraph.CreateSpec) {
	var (
		_node = &Department{config: dc.config}
		_spec = sqlgraph.NewCreateSpec(department.Table, sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = dc.conflict
	if id, ok := dc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := dc.mutation.CreateTime(); ok {
		_spec.SetField(department.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := dc.mutation.UpdateTime(); ok {
		_spec.SetField(department.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := dc.mutation.DeleteTime(); ok {
		_spec.SetField(department.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := dc.mutation.Status(); ok {
		_spec.SetField(department.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := dc.mutation.CreateBy(); ok {
		_spec.SetField(department.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := dc.mutation.UpdateBy(); ok {
		_spec.SetField(department.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := dc.mutation.Remark(); ok {
		_spec.SetField(department.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := dc.mutation.TenantID(); ok {
		_spec.SetField(department.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := dc.mutation.Name(); ok {
		_spec.SetField(department.FieldName, field.TypeString, value)
		_node.Name = &value
	}
	if value, ok := dc.mutation.OrganizationID(); ok {
		_spec.SetField(department.FieldOrganizationID, field.TypeUint32, value)
		_node.OrganizationID = &value
	}
	if value, ok := dc.mutation.SortID(); ok {
		_spec.SetField(department.FieldSortID, field.TypeInt32, value)
		_node.SortID = &value
	}
	if nodes := dc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   department.ParentTable,
			Columns: []string{department.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Department.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepartmentUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (dc *DepartmentCreate) OnConflict(opts ...sql.ConflictOption) *DepartmentUpsertOne {
	dc.conflict = opts
	return &DepartmentUpsertOne{
		create: dc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Department.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (dc *DepartmentCreate) OnConflictColumns(columns ...string) *DepartmentUpsertOne {
	dc.conflict = append(dc.conflict, sql.ConflictColumns(columns...))
	return &DepartmentUpsertOne{
		create: dc,
	}
}

type (
	// DepartmentUpsertOne is the builder for "upsert"-ing
	//  one Department node.
	DepartmentUpsertOne struct {
		create *DepartmentCreate
	}

	// DepartmentUpsert is the "OnConflict" setter.
	DepartmentUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *DepartmentUpsert) SetUpdateTime(v time.Time) *DepartmentUpsert {
	u.Set(department.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateUpdateTime() *DepartmentUpsert {
	u.SetExcluded(department.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *DepartmentUpsert) ClearUpdateTime() *DepartmentUpsert {
	u.SetNull(department.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *DepartmentUpsert) SetDeleteTime(v time.Time) *DepartmentUpsert {
	u.Set(department.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateDeleteTime() *DepartmentUpsert {
	u.SetExcluded(department.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *DepartmentUpsert) ClearDeleteTime() *DepartmentUpsert {
	u.SetNull(department.FieldDeleteTime)
	return u
}

// SetStatus sets the "status" field.
func (u *DepartmentUpsert) SetStatus(v department.Status) *DepartmentUpsert {
	u.Set(department.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateStatus() *DepartmentUpsert {
	u.SetExcluded(department.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *DepartmentUpsert) ClearStatus() *DepartmentUpsert {
	u.SetNull(department.FieldStatus)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *DepartmentUpsert) SetCreateBy(v uint32) *DepartmentUpsert {
	u.Set(department.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateCreateBy() *DepartmentUpsert {
	u.SetExcluded(department.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *DepartmentUpsert) AddCreateBy(v uint32) *DepartmentUpsert {
	u.Add(department.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *DepartmentUpsert) ClearCreateBy() *DepartmentUpsert {
	u.SetNull(department.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *DepartmentUpsert) SetUpdateBy(v uint32) *DepartmentUpsert {
	u.Set(department.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateUpdateBy() *DepartmentUpsert {
	u.SetExcluded(department.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *DepartmentUpsert) AddUpdateBy(v uint32) *DepartmentUpsert {
	u.Add(department.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *DepartmentUpsert) ClearUpdateBy() *DepartmentUpsert {
	u.SetNull(department.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *DepartmentUpsert) SetRemark(v string) *DepartmentUpsert {
	u.Set(department.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateRemark() *DepartmentUpsert {
	u.SetExcluded(department.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *DepartmentUpsert) ClearRemark() *DepartmentUpsert {
	u.SetNull(department.FieldRemark)
	return u
}

// SetName sets the "name" field.
func (u *DepartmentUpsert) SetName(v string) *DepartmentUpsert {
	u.Set(department.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateName() *DepartmentUpsert {
	u.SetExcluded(department.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *DepartmentUpsert) ClearName() *DepartmentUpsert {
	u.SetNull(department.FieldName)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *DepartmentUpsert) SetParentID(v uint32) *DepartmentUpsert {
	u.Set(department.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateParentID() *DepartmentUpsert {
	u.SetExcluded(department.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *DepartmentUpsert) ClearParentID() *DepartmentUpsert {
	u.SetNull(department.FieldParentID)
	return u
}

// SetOrganizationID sets the "organization_id" field.
func (u *DepartmentUpsert) SetOrganizationID(v uint32) *DepartmentUpsert {
	u.Set(department.FieldOrganizationID, v)
	return u
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateOrganizationID() *DepartmentUpsert {
	u.SetExcluded(department.FieldOrganizationID)
	return u
}

// AddOrganizationID adds v to the "organization_id" field.
func (u *DepartmentUpsert) AddOrganizationID(v uint32) *DepartmentUpsert {
	u.Add(department.FieldOrganizationID, v)
	return u
}

// ClearOrganizationID clears the value of the "organization_id" field.
func (u *DepartmentUpsert) ClearOrganizationID() *DepartmentUpsert {
	u.SetNull(department.FieldOrganizationID)
	return u
}

// SetSortID sets the "sort_id" field.
func (u *DepartmentUpsert) SetSortID(v int32) *DepartmentUpsert {
	u.Set(department.FieldSortID, v)
	return u
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *DepartmentUpsert) UpdateSortID() *DepartmentUpsert {
	u.SetExcluded(department.FieldSortID)
	return u
}

// AddSortID adds v to the "sort_id" field.
func (u *DepartmentUpsert) AddSortID(v int32) *DepartmentUpsert {
	u.Add(department.FieldSortID, v)
	return u
}

// ClearSortID clears the value of the "sort_id" field.
func (u *DepartmentUpsert) ClearSortID() *DepartmentUpsert {
	u.SetNull(department.FieldSortID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Department.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(department.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepartmentUpsertOne) UpdateNewValues() *DepartmentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(department.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(department.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(department.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Department.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DepartmentUpsertOne) Ignore() *DepartmentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepartmentUpsertOne) DoNothing() *DepartmentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepartmentCreate.OnConflict
// documentation for more info.
func (u *DepartmentUpsertOne) Update(set func(*DepartmentUpsert)) *DepartmentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepartmentUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *DepartmentUpsertOne) SetUpdateTime(v time.Time) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateUpdateTime() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *DepartmentUpsertOne) ClearUpdateTime() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *DepartmentUpsertOne) SetDeleteTime(v time.Time) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateDeleteTime() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *DepartmentUpsertOne) ClearDeleteTime() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *DepartmentUpsertOne) SetStatus(v department.Status) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateStatus() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *DepartmentUpsertOne) ClearStatus() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *DepartmentUpsertOne) SetCreateBy(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *DepartmentUpsertOne) AddCreateBy(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateCreateBy() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *DepartmentUpsertOne) ClearCreateBy() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *DepartmentUpsertOne) SetUpdateBy(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *DepartmentUpsertOne) AddUpdateBy(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateUpdateBy() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *DepartmentUpsertOne) ClearUpdateBy() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *DepartmentUpsertOne) SetRemark(v string) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateRemark() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *DepartmentUpsertOne) ClearRemark() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *DepartmentUpsertOne) SetName(v string) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateName() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *DepartmentUpsertOne) ClearName() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearName()
	})
}

// SetParentID sets the "parent_id" field.
func (u *DepartmentUpsertOne) SetParentID(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateParentID() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *DepartmentUpsertOne) ClearParentID() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearParentID()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *DepartmentUpsertOne) SetOrganizationID(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetOrganizationID(v)
	})
}

// AddOrganizationID adds v to the "organization_id" field.
func (u *DepartmentUpsertOne) AddOrganizationID(v uint32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateOrganizationID() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateOrganizationID()
	})
}

// ClearOrganizationID clears the value of the "organization_id" field.
func (u *DepartmentUpsertOne) ClearOrganizationID() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearOrganizationID()
	})
}

// SetSortID sets the "sort_id" field.
func (u *DepartmentUpsertOne) SetSortID(v int32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *DepartmentUpsertOne) AddSortID(v int32) *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *DepartmentUpsertOne) UpdateSortID() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *DepartmentUpsertOne) ClearSortID() *DepartmentUpsertOne {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearSortID()
	})
}

// Exec executes the query.
func (u *DepartmentUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepartmentCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepartmentUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DepartmentUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DepartmentUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DepartmentCreateBulk is the builder for creating many Department entities in bulk.
type DepartmentCreateBulk struct {
	config
	err      error
	builders []*DepartmentCreate
	conflict []sql.ConflictOption
}

// Save creates the Department entities in the database.
func (dcb *DepartmentCreateBulk) Save(ctx context.Context) ([]*Department, error) {
	if dcb.err != nil {
		return nil, dcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dcb.builders))
	nodes := make([]*Department, len(dcb.builders))
	mutators := make([]Mutator, len(dcb.builders))
	for i := range dcb.builders {
		func(i int, root context.Context) {
			builder := dcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DepartmentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = dcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dcb *DepartmentCreateBulk) SaveX(ctx context.Context) []*Department {
	v, err := dcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dcb *DepartmentCreateBulk) Exec(ctx context.Context) error {
	_, err := dcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dcb *DepartmentCreateBulk) ExecX(ctx context.Context) {
	if err := dcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Department.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepartmentUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (dcb *DepartmentCreateBulk) OnConflict(opts ...sql.ConflictOption) *DepartmentUpsertBulk {
	dcb.conflict = opts
	return &DepartmentUpsertBulk{
		create: dcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Department.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (dcb *DepartmentCreateBulk) OnConflictColumns(columns ...string) *DepartmentUpsertBulk {
	dcb.conflict = append(dcb.conflict, sql.ConflictColumns(columns...))
	return &DepartmentUpsertBulk{
		create: dcb,
	}
}

// DepartmentUpsertBulk is the builder for "upsert"-ing
// a bulk of Department nodes.
type DepartmentUpsertBulk struct {
	create *DepartmentCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Department.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(department.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepartmentUpsertBulk) UpdateNewValues() *DepartmentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(department.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(department.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(department.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Department.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DepartmentUpsertBulk) Ignore() *DepartmentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepartmentUpsertBulk) DoNothing() *DepartmentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepartmentCreateBulk.OnConflict
// documentation for more info.
func (u *DepartmentUpsertBulk) Update(set func(*DepartmentUpsert)) *DepartmentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepartmentUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *DepartmentUpsertBulk) SetUpdateTime(v time.Time) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateUpdateTime() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *DepartmentUpsertBulk) ClearUpdateTime() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *DepartmentUpsertBulk) SetDeleteTime(v time.Time) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateDeleteTime() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *DepartmentUpsertBulk) ClearDeleteTime() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *DepartmentUpsertBulk) SetStatus(v department.Status) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateStatus() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *DepartmentUpsertBulk) ClearStatus() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *DepartmentUpsertBulk) SetCreateBy(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *DepartmentUpsertBulk) AddCreateBy(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateCreateBy() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *DepartmentUpsertBulk) ClearCreateBy() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *DepartmentUpsertBulk) SetUpdateBy(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *DepartmentUpsertBulk) AddUpdateBy(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateUpdateBy() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *DepartmentUpsertBulk) ClearUpdateBy() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *DepartmentUpsertBulk) SetRemark(v string) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateRemark() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *DepartmentUpsertBulk) ClearRemark() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *DepartmentUpsertBulk) SetName(v string) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateName() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *DepartmentUpsertBulk) ClearName() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearName()
	})
}

// SetParentID sets the "parent_id" field.
func (u *DepartmentUpsertBulk) SetParentID(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateParentID() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *DepartmentUpsertBulk) ClearParentID() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearParentID()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *DepartmentUpsertBulk) SetOrganizationID(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetOrganizationID(v)
	})
}

// AddOrganizationID adds v to the "organization_id" field.
func (u *DepartmentUpsertBulk) AddOrganizationID(v uint32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateOrganizationID() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateOrganizationID()
	})
}

// ClearOrganizationID clears the value of the "organization_id" field.
func (u *DepartmentUpsertBulk) ClearOrganizationID() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearOrganizationID()
	})
}

// SetSortID sets the "sort_id" field.
func (u *DepartmentUpsertBulk) SetSortID(v int32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *DepartmentUpsertBulk) AddSortID(v int32) *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *DepartmentUpsertBulk) UpdateSortID() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *DepartmentUpsertBulk) ClearSortID() *DepartmentUpsertBulk {
	return u.Update(func(s *DepartmentUpsert) {
		s.ClearSortID()
	})
}

// Exec executes the query.
func (u *DepartmentUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DepartmentCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepartmentCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepartmentUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
