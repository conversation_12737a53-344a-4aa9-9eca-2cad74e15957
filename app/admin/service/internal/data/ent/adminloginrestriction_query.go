// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminLoginRestrictionQuery is the builder for querying AdminLoginRestriction entities.
type AdminLoginRestrictionQuery struct {
	config
	ctx        *QueryContext
	order      []adminloginrestriction.OrderOption
	inters     []Interceptor
	predicates []predicate.AdminLoginRestriction
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AdminLoginRestrictionQuery builder.
func (alrq *AdminLoginRestrictionQuery) Where(ps ...predicate.AdminLoginRestriction) *AdminLoginRestrictionQuery {
	alrq.predicates = append(alrq.predicates, ps...)
	return alrq
}

// Limit the number of records to be returned by this query.
func (alrq *AdminLoginRestrictionQuery) Limit(limit int) *AdminLoginRestrictionQuery {
	alrq.ctx.Limit = &limit
	return alrq
}

// Offset to start from.
func (alrq *AdminLoginRestrictionQuery) Offset(offset int) *AdminLoginRestrictionQuery {
	alrq.ctx.Offset = &offset
	return alrq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (alrq *AdminLoginRestrictionQuery) Unique(unique bool) *AdminLoginRestrictionQuery {
	alrq.ctx.Unique = &unique
	return alrq
}

// Order specifies how the records should be ordered.
func (alrq *AdminLoginRestrictionQuery) Order(o ...adminloginrestriction.OrderOption) *AdminLoginRestrictionQuery {
	alrq.order = append(alrq.order, o...)
	return alrq
}

// First returns the first AdminLoginRestriction entity from the query.
// Returns a *NotFoundError when no AdminLoginRestriction was found.
func (alrq *AdminLoginRestrictionQuery) First(ctx context.Context) (*AdminLoginRestriction, error) {
	nodes, err := alrq.Limit(1).All(setContextOp(ctx, alrq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{adminloginrestriction.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) FirstX(ctx context.Context) *AdminLoginRestriction {
	node, err := alrq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AdminLoginRestriction ID from the query.
// Returns a *NotFoundError when no AdminLoginRestriction ID was found.
func (alrq *AdminLoginRestrictionQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = alrq.Limit(1).IDs(setContextOp(ctx, alrq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{adminloginrestriction.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := alrq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AdminLoginRestriction entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AdminLoginRestriction entity is found.
// Returns a *NotFoundError when no AdminLoginRestriction entities are found.
func (alrq *AdminLoginRestrictionQuery) Only(ctx context.Context) (*AdminLoginRestriction, error) {
	nodes, err := alrq.Limit(2).All(setContextOp(ctx, alrq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{adminloginrestriction.Label}
	default:
		return nil, &NotSingularError{adminloginrestriction.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) OnlyX(ctx context.Context) *AdminLoginRestriction {
	node, err := alrq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AdminLoginRestriction ID in the query.
// Returns a *NotSingularError when more than one AdminLoginRestriction ID is found.
// Returns a *NotFoundError when no entities are found.
func (alrq *AdminLoginRestrictionQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = alrq.Limit(2).IDs(setContextOp(ctx, alrq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{adminloginrestriction.Label}
	default:
		err = &NotSingularError{adminloginrestriction.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := alrq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AdminLoginRestrictions.
func (alrq *AdminLoginRestrictionQuery) All(ctx context.Context) ([]*AdminLoginRestriction, error) {
	ctx = setContextOp(ctx, alrq.ctx, ent.OpQueryAll)
	if err := alrq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AdminLoginRestriction, *AdminLoginRestrictionQuery]()
	return withInterceptors[[]*AdminLoginRestriction](ctx, alrq, qr, alrq.inters)
}

// AllX is like All, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) AllX(ctx context.Context) []*AdminLoginRestriction {
	nodes, err := alrq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AdminLoginRestriction IDs.
func (alrq *AdminLoginRestrictionQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if alrq.ctx.Unique == nil && alrq.path != nil {
		alrq.Unique(true)
	}
	ctx = setContextOp(ctx, alrq.ctx, ent.OpQueryIDs)
	if err = alrq.Select(adminloginrestriction.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := alrq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (alrq *AdminLoginRestrictionQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, alrq.ctx, ent.OpQueryCount)
	if err := alrq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, alrq, querierCount[*AdminLoginRestrictionQuery](), alrq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) CountX(ctx context.Context) int {
	count, err := alrq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (alrq *AdminLoginRestrictionQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, alrq.ctx, ent.OpQueryExist)
	switch _, err := alrq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (alrq *AdminLoginRestrictionQuery) ExistX(ctx context.Context) bool {
	exist, err := alrq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AdminLoginRestrictionQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (alrq *AdminLoginRestrictionQuery) Clone() *AdminLoginRestrictionQuery {
	if alrq == nil {
		return nil
	}
	return &AdminLoginRestrictionQuery{
		config:     alrq.config,
		ctx:        alrq.ctx.Clone(),
		order:      append([]adminloginrestriction.OrderOption{}, alrq.order...),
		inters:     append([]Interceptor{}, alrq.inters...),
		predicates: append([]predicate.AdminLoginRestriction{}, alrq.predicates...),
		// clone intermediate query.
		sql:       alrq.sql.Clone(),
		path:      alrq.path,
		modifiers: append([]func(*sql.Selector){}, alrq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AdminLoginRestriction.Query().
//		GroupBy(adminloginrestriction.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (alrq *AdminLoginRestrictionQuery) GroupBy(field string, fields ...string) *AdminLoginRestrictionGroupBy {
	alrq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AdminLoginRestrictionGroupBy{build: alrq}
	grbuild.flds = &alrq.ctx.Fields
	grbuild.label = adminloginrestriction.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.AdminLoginRestriction.Query().
//		Select(adminloginrestriction.FieldCreateTime).
//		Scan(ctx, &v)
func (alrq *AdminLoginRestrictionQuery) Select(fields ...string) *AdminLoginRestrictionSelect {
	alrq.ctx.Fields = append(alrq.ctx.Fields, fields...)
	sbuild := &AdminLoginRestrictionSelect{AdminLoginRestrictionQuery: alrq}
	sbuild.label = adminloginrestriction.Label
	sbuild.flds, sbuild.scan = &alrq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AdminLoginRestrictionSelect configured with the given aggregations.
func (alrq *AdminLoginRestrictionQuery) Aggregate(fns ...AggregateFunc) *AdminLoginRestrictionSelect {
	return alrq.Select().Aggregate(fns...)
}

func (alrq *AdminLoginRestrictionQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range alrq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, alrq); err != nil {
				return err
			}
		}
	}
	for _, f := range alrq.ctx.Fields {
		if !adminloginrestriction.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if alrq.path != nil {
		prev, err := alrq.path(ctx)
		if err != nil {
			return err
		}
		alrq.sql = prev
	}
	return nil
}

func (alrq *AdminLoginRestrictionQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AdminLoginRestriction, error) {
	var (
		nodes = []*AdminLoginRestriction{}
		_spec = alrq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AdminLoginRestriction).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AdminLoginRestriction{config: alrq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(alrq.modifiers) > 0 {
		_spec.Modifiers = alrq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, alrq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (alrq *AdminLoginRestrictionQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := alrq.querySpec()
	if len(alrq.modifiers) > 0 {
		_spec.Modifiers = alrq.modifiers
	}
	_spec.Node.Columns = alrq.ctx.Fields
	if len(alrq.ctx.Fields) > 0 {
		_spec.Unique = alrq.ctx.Unique != nil && *alrq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, alrq.driver, _spec)
}

func (alrq *AdminLoginRestrictionQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(adminloginrestriction.Table, adminloginrestriction.Columns, sqlgraph.NewFieldSpec(adminloginrestriction.FieldID, field.TypeUint32))
	_spec.From = alrq.sql
	if unique := alrq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if alrq.path != nil {
		_spec.Unique = true
	}
	if fields := alrq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, adminloginrestriction.FieldID)
		for i := range fields {
			if fields[i] != adminloginrestriction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := alrq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := alrq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := alrq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := alrq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (alrq *AdminLoginRestrictionQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(alrq.driver.Dialect())
	t1 := builder.Table(adminloginrestriction.Table)
	columns := alrq.ctx.Fields
	if len(columns) == 0 {
		columns = adminloginrestriction.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if alrq.sql != nil {
		selector = alrq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if alrq.ctx.Unique != nil && *alrq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range alrq.modifiers {
		m(selector)
	}
	for _, p := range alrq.predicates {
		p(selector)
	}
	for _, p := range alrq.order {
		p(selector)
	}
	if offset := alrq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := alrq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (alrq *AdminLoginRestrictionQuery) ForUpdate(opts ...sql.LockOption) *AdminLoginRestrictionQuery {
	if alrq.driver.Dialect() == dialect.Postgres {
		alrq.Unique(false)
	}
	alrq.modifiers = append(alrq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return alrq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (alrq *AdminLoginRestrictionQuery) ForShare(opts ...sql.LockOption) *AdminLoginRestrictionQuery {
	if alrq.driver.Dialect() == dialect.Postgres {
		alrq.Unique(false)
	}
	alrq.modifiers = append(alrq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return alrq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (alrq *AdminLoginRestrictionQuery) Modify(modifiers ...func(s *sql.Selector)) *AdminLoginRestrictionSelect {
	alrq.modifiers = append(alrq.modifiers, modifiers...)
	return alrq.Select()
}

// AdminLoginRestrictionGroupBy is the group-by builder for AdminLoginRestriction entities.
type AdminLoginRestrictionGroupBy struct {
	selector
	build *AdminLoginRestrictionQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (alrgb *AdminLoginRestrictionGroupBy) Aggregate(fns ...AggregateFunc) *AdminLoginRestrictionGroupBy {
	alrgb.fns = append(alrgb.fns, fns...)
	return alrgb
}

// Scan applies the selector query and scans the result into the given value.
func (alrgb *AdminLoginRestrictionGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, alrgb.build.ctx, ent.OpQueryGroupBy)
	if err := alrgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AdminLoginRestrictionQuery, *AdminLoginRestrictionGroupBy](ctx, alrgb.build, alrgb, alrgb.build.inters, v)
}

func (alrgb *AdminLoginRestrictionGroupBy) sqlScan(ctx context.Context, root *AdminLoginRestrictionQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(alrgb.fns))
	for _, fn := range alrgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*alrgb.flds)+len(alrgb.fns))
		for _, f := range *alrgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*alrgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := alrgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AdminLoginRestrictionSelect is the builder for selecting fields of AdminLoginRestriction entities.
type AdminLoginRestrictionSelect struct {
	*AdminLoginRestrictionQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (alrs *AdminLoginRestrictionSelect) Aggregate(fns ...AggregateFunc) *AdminLoginRestrictionSelect {
	alrs.fns = append(alrs.fns, fns...)
	return alrs
}

// Scan applies the selector query and scans the result into the given value.
func (alrs *AdminLoginRestrictionSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, alrs.ctx, ent.OpQuerySelect)
	if err := alrs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AdminLoginRestrictionQuery, *AdminLoginRestrictionSelect](ctx, alrs.AdminLoginRestrictionQuery, alrs, alrs.inters, v)
}

func (alrs *AdminLoginRestrictionSelect) sqlScan(ctx context.Context, root *AdminLoginRestrictionQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(alrs.fns))
	for _, fn := range alrs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*alrs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := alrs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (alrs *AdminLoginRestrictionSelect) Modify(modifiers ...func(s *sql.Selector)) *AdminLoginRestrictionSelect {
	alrs.modifiers = append(alrs.modifiers, modifiers...)
	return alrs
}
