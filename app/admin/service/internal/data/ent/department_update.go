// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/department"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DepartmentUpdate is the builder for updating Department entities.
type DepartmentUpdate struct {
	config
	hooks     []Hook
	mutation  *DepartmentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DepartmentUpdate builder.
func (du *DepartmentUpdate) Where(ps ...predicate.Department) *DepartmentUpdate {
	du.mutation.Where(ps...)
	return du
}

// SetUpdateTime sets the "update_time" field.
func (du *DepartmentUpdate) SetUpdateTime(t time.Time) *DepartmentUpdate {
	du.mutation.SetUpdateTime(t)
	return du
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableUpdateTime(t *time.Time) *DepartmentUpdate {
	if t != nil {
		du.SetUpdateTime(*t)
	}
	return du
}

// ClearUpdateTime clears the value of the "update_time" field.
func (du *DepartmentUpdate) ClearUpdateTime() *DepartmentUpdate {
	du.mutation.ClearUpdateTime()
	return du
}

// SetDeleteTime sets the "delete_time" field.
func (du *DepartmentUpdate) SetDeleteTime(t time.Time) *DepartmentUpdate {
	du.mutation.SetDeleteTime(t)
	return du
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableDeleteTime(t *time.Time) *DepartmentUpdate {
	if t != nil {
		du.SetDeleteTime(*t)
	}
	return du
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (du *DepartmentUpdate) ClearDeleteTime() *DepartmentUpdate {
	du.mutation.ClearDeleteTime()
	return du
}

// SetStatus sets the "status" field.
func (du *DepartmentUpdate) SetStatus(d department.Status) *DepartmentUpdate {
	du.mutation.SetStatus(d)
	return du
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableStatus(d *department.Status) *DepartmentUpdate {
	if d != nil {
		du.SetStatus(*d)
	}
	return du
}

// ClearStatus clears the value of the "status" field.
func (du *DepartmentUpdate) ClearStatus() *DepartmentUpdate {
	du.mutation.ClearStatus()
	return du
}

// SetCreateBy sets the "create_by" field.
func (du *DepartmentUpdate) SetCreateBy(u uint32) *DepartmentUpdate {
	du.mutation.ResetCreateBy()
	du.mutation.SetCreateBy(u)
	return du
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableCreateBy(u *uint32) *DepartmentUpdate {
	if u != nil {
		du.SetCreateBy(*u)
	}
	return du
}

// AddCreateBy adds u to the "create_by" field.
func (du *DepartmentUpdate) AddCreateBy(u int32) *DepartmentUpdate {
	du.mutation.AddCreateBy(u)
	return du
}

// ClearCreateBy clears the value of the "create_by" field.
func (du *DepartmentUpdate) ClearCreateBy() *DepartmentUpdate {
	du.mutation.ClearCreateBy()
	return du
}

// SetUpdateBy sets the "update_by" field.
func (du *DepartmentUpdate) SetUpdateBy(u uint32) *DepartmentUpdate {
	du.mutation.ResetUpdateBy()
	du.mutation.SetUpdateBy(u)
	return du
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableUpdateBy(u *uint32) *DepartmentUpdate {
	if u != nil {
		du.SetUpdateBy(*u)
	}
	return du
}

// AddUpdateBy adds u to the "update_by" field.
func (du *DepartmentUpdate) AddUpdateBy(u int32) *DepartmentUpdate {
	du.mutation.AddUpdateBy(u)
	return du
}

// ClearUpdateBy clears the value of the "update_by" field.
func (du *DepartmentUpdate) ClearUpdateBy() *DepartmentUpdate {
	du.mutation.ClearUpdateBy()
	return du
}

// SetRemark sets the "remark" field.
func (du *DepartmentUpdate) SetRemark(s string) *DepartmentUpdate {
	du.mutation.SetRemark(s)
	return du
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableRemark(s *string) *DepartmentUpdate {
	if s != nil {
		du.SetRemark(*s)
	}
	return du
}

// ClearRemark clears the value of the "remark" field.
func (du *DepartmentUpdate) ClearRemark() *DepartmentUpdate {
	du.mutation.ClearRemark()
	return du
}

// SetName sets the "name" field.
func (du *DepartmentUpdate) SetName(s string) *DepartmentUpdate {
	du.mutation.SetName(s)
	return du
}

// SetNillableName sets the "name" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableName(s *string) *DepartmentUpdate {
	if s != nil {
		du.SetName(*s)
	}
	return du
}

// ClearName clears the value of the "name" field.
func (du *DepartmentUpdate) ClearName() *DepartmentUpdate {
	du.mutation.ClearName()
	return du
}

// SetParentID sets the "parent_id" field.
func (du *DepartmentUpdate) SetParentID(u uint32) *DepartmentUpdate {
	du.mutation.SetParentID(u)
	return du
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableParentID(u *uint32) *DepartmentUpdate {
	if u != nil {
		du.SetParentID(*u)
	}
	return du
}

// ClearParentID clears the value of the "parent_id" field.
func (du *DepartmentUpdate) ClearParentID() *DepartmentUpdate {
	du.mutation.ClearParentID()
	return du
}

// SetOrganizationID sets the "organization_id" field.
func (du *DepartmentUpdate) SetOrganizationID(u uint32) *DepartmentUpdate {
	du.mutation.ResetOrganizationID()
	du.mutation.SetOrganizationID(u)
	return du
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableOrganizationID(u *uint32) *DepartmentUpdate {
	if u != nil {
		du.SetOrganizationID(*u)
	}
	return du
}

// AddOrganizationID adds u to the "organization_id" field.
func (du *DepartmentUpdate) AddOrganizationID(u int32) *DepartmentUpdate {
	du.mutation.AddOrganizationID(u)
	return du
}

// ClearOrganizationID clears the value of the "organization_id" field.
func (du *DepartmentUpdate) ClearOrganizationID() *DepartmentUpdate {
	du.mutation.ClearOrganizationID()
	return du
}

// SetSortID sets the "sort_id" field.
func (du *DepartmentUpdate) SetSortID(i int32) *DepartmentUpdate {
	du.mutation.ResetSortID()
	du.mutation.SetSortID(i)
	return du
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (du *DepartmentUpdate) SetNillableSortID(i *int32) *DepartmentUpdate {
	if i != nil {
		du.SetSortID(*i)
	}
	return du
}

// AddSortID adds i to the "sort_id" field.
func (du *DepartmentUpdate) AddSortID(i int32) *DepartmentUpdate {
	du.mutation.AddSortID(i)
	return du
}

// ClearSortID clears the value of the "sort_id" field.
func (du *DepartmentUpdate) ClearSortID() *DepartmentUpdate {
	du.mutation.ClearSortID()
	return du
}

// SetParent sets the "parent" edge to the Department entity.
func (du *DepartmentUpdate) SetParent(d *Department) *DepartmentUpdate {
	return du.SetParentID(d.ID)
}

// AddChildIDs adds the "children" edge to the Department entity by IDs.
func (du *DepartmentUpdate) AddChildIDs(ids ...uint32) *DepartmentUpdate {
	du.mutation.AddChildIDs(ids...)
	return du
}

// AddChildren adds the "children" edges to the Department entity.
func (du *DepartmentUpdate) AddChildren(d ...*Department) *DepartmentUpdate {
	ids := make([]uint32, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.AddChildIDs(ids...)
}

// Mutation returns the DepartmentMutation object of the builder.
func (du *DepartmentUpdate) Mutation() *DepartmentMutation {
	return du.mutation
}

// ClearParent clears the "parent" edge to the Department entity.
func (du *DepartmentUpdate) ClearParent() *DepartmentUpdate {
	du.mutation.ClearParent()
	return du
}

// ClearChildren clears all "children" edges to the Department entity.
func (du *DepartmentUpdate) ClearChildren() *DepartmentUpdate {
	du.mutation.ClearChildren()
	return du
}

// RemoveChildIDs removes the "children" edge to Department entities by IDs.
func (du *DepartmentUpdate) RemoveChildIDs(ids ...uint32) *DepartmentUpdate {
	du.mutation.RemoveChildIDs(ids...)
	return du
}

// RemoveChildren removes "children" edges to Department entities.
func (du *DepartmentUpdate) RemoveChildren(d ...*Department) *DepartmentUpdate {
	ids := make([]uint32, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return du.RemoveChildIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (du *DepartmentUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, du.sqlSave, du.mutation, du.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (du *DepartmentUpdate) SaveX(ctx context.Context) int {
	affected, err := du.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (du *DepartmentUpdate) Exec(ctx context.Context) error {
	_, err := du.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (du *DepartmentUpdate) ExecX(ctx context.Context) {
	if err := du.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (du *DepartmentUpdate) check() error {
	if v, ok := du.mutation.Status(); ok {
		if err := department.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Department.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (du *DepartmentUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepartmentUpdate {
	du.modifiers = append(du.modifiers, modifiers...)
	return du
}

func (du *DepartmentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := du.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(department.Table, department.Columns, sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32))
	if ps := du.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if du.mutation.CreateTimeCleared() {
		_spec.ClearField(department.FieldCreateTime, field.TypeTime)
	}
	if value, ok := du.mutation.UpdateTime(); ok {
		_spec.SetField(department.FieldUpdateTime, field.TypeTime, value)
	}
	if du.mutation.UpdateTimeCleared() {
		_spec.ClearField(department.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := du.mutation.DeleteTime(); ok {
		_spec.SetField(department.FieldDeleteTime, field.TypeTime, value)
	}
	if du.mutation.DeleteTimeCleared() {
		_spec.ClearField(department.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := du.mutation.Status(); ok {
		_spec.SetField(department.FieldStatus, field.TypeEnum, value)
	}
	if du.mutation.StatusCleared() {
		_spec.ClearField(department.FieldStatus, field.TypeEnum)
	}
	if value, ok := du.mutation.CreateBy(); ok {
		_spec.SetField(department.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := du.mutation.AddedCreateBy(); ok {
		_spec.AddField(department.FieldCreateBy, field.TypeUint32, value)
	}
	if du.mutation.CreateByCleared() {
		_spec.ClearField(department.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := du.mutation.UpdateBy(); ok {
		_spec.SetField(department.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := du.mutation.AddedUpdateBy(); ok {
		_spec.AddField(department.FieldUpdateBy, field.TypeUint32, value)
	}
	if du.mutation.UpdateByCleared() {
		_spec.ClearField(department.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := du.mutation.Remark(); ok {
		_spec.SetField(department.FieldRemark, field.TypeString, value)
	}
	if du.mutation.RemarkCleared() {
		_spec.ClearField(department.FieldRemark, field.TypeString)
	}
	if du.mutation.TenantIDCleared() {
		_spec.ClearField(department.FieldTenantID, field.TypeUint32)
	}
	if value, ok := du.mutation.Name(); ok {
		_spec.SetField(department.FieldName, field.TypeString, value)
	}
	if du.mutation.NameCleared() {
		_spec.ClearField(department.FieldName, field.TypeString)
	}
	if value, ok := du.mutation.OrganizationID(); ok {
		_spec.SetField(department.FieldOrganizationID, field.TypeUint32, value)
	}
	if value, ok := du.mutation.AddedOrganizationID(); ok {
		_spec.AddField(department.FieldOrganizationID, field.TypeUint32, value)
	}
	if du.mutation.OrganizationIDCleared() {
		_spec.ClearField(department.FieldOrganizationID, field.TypeUint32)
	}
	if value, ok := du.mutation.SortID(); ok {
		_spec.SetField(department.FieldSortID, field.TypeInt32, value)
	}
	if value, ok := du.mutation.AddedSortID(); ok {
		_spec.AddField(department.FieldSortID, field.TypeInt32, value)
	}
	if du.mutation.SortIDCleared() {
		_spec.ClearField(department.FieldSortID, field.TypeInt32)
	}
	if du.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   department.ParentTable,
			Columns: []string{department.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   department.ParentTable,
			Columns: []string{department.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !du.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(du.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, du.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{department.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	du.mutation.done = true
	return n, nil
}

// DepartmentUpdateOne is the builder for updating a single Department entity.
type DepartmentUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DepartmentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (duo *DepartmentUpdateOne) SetUpdateTime(t time.Time) *DepartmentUpdateOne {
	duo.mutation.SetUpdateTime(t)
	return duo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableUpdateTime(t *time.Time) *DepartmentUpdateOne {
	if t != nil {
		duo.SetUpdateTime(*t)
	}
	return duo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (duo *DepartmentUpdateOne) ClearUpdateTime() *DepartmentUpdateOne {
	duo.mutation.ClearUpdateTime()
	return duo
}

// SetDeleteTime sets the "delete_time" field.
func (duo *DepartmentUpdateOne) SetDeleteTime(t time.Time) *DepartmentUpdateOne {
	duo.mutation.SetDeleteTime(t)
	return duo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableDeleteTime(t *time.Time) *DepartmentUpdateOne {
	if t != nil {
		duo.SetDeleteTime(*t)
	}
	return duo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (duo *DepartmentUpdateOne) ClearDeleteTime() *DepartmentUpdateOne {
	duo.mutation.ClearDeleteTime()
	return duo
}

// SetStatus sets the "status" field.
func (duo *DepartmentUpdateOne) SetStatus(d department.Status) *DepartmentUpdateOne {
	duo.mutation.SetStatus(d)
	return duo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableStatus(d *department.Status) *DepartmentUpdateOne {
	if d != nil {
		duo.SetStatus(*d)
	}
	return duo
}

// ClearStatus clears the value of the "status" field.
func (duo *DepartmentUpdateOne) ClearStatus() *DepartmentUpdateOne {
	duo.mutation.ClearStatus()
	return duo
}

// SetCreateBy sets the "create_by" field.
func (duo *DepartmentUpdateOne) SetCreateBy(u uint32) *DepartmentUpdateOne {
	duo.mutation.ResetCreateBy()
	duo.mutation.SetCreateBy(u)
	return duo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableCreateBy(u *uint32) *DepartmentUpdateOne {
	if u != nil {
		duo.SetCreateBy(*u)
	}
	return duo
}

// AddCreateBy adds u to the "create_by" field.
func (duo *DepartmentUpdateOne) AddCreateBy(u int32) *DepartmentUpdateOne {
	duo.mutation.AddCreateBy(u)
	return duo
}

// ClearCreateBy clears the value of the "create_by" field.
func (duo *DepartmentUpdateOne) ClearCreateBy() *DepartmentUpdateOne {
	duo.mutation.ClearCreateBy()
	return duo
}

// SetUpdateBy sets the "update_by" field.
func (duo *DepartmentUpdateOne) SetUpdateBy(u uint32) *DepartmentUpdateOne {
	duo.mutation.ResetUpdateBy()
	duo.mutation.SetUpdateBy(u)
	return duo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableUpdateBy(u *uint32) *DepartmentUpdateOne {
	if u != nil {
		duo.SetUpdateBy(*u)
	}
	return duo
}

// AddUpdateBy adds u to the "update_by" field.
func (duo *DepartmentUpdateOne) AddUpdateBy(u int32) *DepartmentUpdateOne {
	duo.mutation.AddUpdateBy(u)
	return duo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (duo *DepartmentUpdateOne) ClearUpdateBy() *DepartmentUpdateOne {
	duo.mutation.ClearUpdateBy()
	return duo
}

// SetRemark sets the "remark" field.
func (duo *DepartmentUpdateOne) SetRemark(s string) *DepartmentUpdateOne {
	duo.mutation.SetRemark(s)
	return duo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableRemark(s *string) *DepartmentUpdateOne {
	if s != nil {
		duo.SetRemark(*s)
	}
	return duo
}

// ClearRemark clears the value of the "remark" field.
func (duo *DepartmentUpdateOne) ClearRemark() *DepartmentUpdateOne {
	duo.mutation.ClearRemark()
	return duo
}

// SetName sets the "name" field.
func (duo *DepartmentUpdateOne) SetName(s string) *DepartmentUpdateOne {
	duo.mutation.SetName(s)
	return duo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableName(s *string) *DepartmentUpdateOne {
	if s != nil {
		duo.SetName(*s)
	}
	return duo
}

// ClearName clears the value of the "name" field.
func (duo *DepartmentUpdateOne) ClearName() *DepartmentUpdateOne {
	duo.mutation.ClearName()
	return duo
}

// SetParentID sets the "parent_id" field.
func (duo *DepartmentUpdateOne) SetParentID(u uint32) *DepartmentUpdateOne {
	duo.mutation.SetParentID(u)
	return duo
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableParentID(u *uint32) *DepartmentUpdateOne {
	if u != nil {
		duo.SetParentID(*u)
	}
	return duo
}

// ClearParentID clears the value of the "parent_id" field.
func (duo *DepartmentUpdateOne) ClearParentID() *DepartmentUpdateOne {
	duo.mutation.ClearParentID()
	return duo
}

// SetOrganizationID sets the "organization_id" field.
func (duo *DepartmentUpdateOne) SetOrganizationID(u uint32) *DepartmentUpdateOne {
	duo.mutation.ResetOrganizationID()
	duo.mutation.SetOrganizationID(u)
	return duo
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableOrganizationID(u *uint32) *DepartmentUpdateOne {
	if u != nil {
		duo.SetOrganizationID(*u)
	}
	return duo
}

// AddOrganizationID adds u to the "organization_id" field.
func (duo *DepartmentUpdateOne) AddOrganizationID(u int32) *DepartmentUpdateOne {
	duo.mutation.AddOrganizationID(u)
	return duo
}

// ClearOrganizationID clears the value of the "organization_id" field.
func (duo *DepartmentUpdateOne) ClearOrganizationID() *DepartmentUpdateOne {
	duo.mutation.ClearOrganizationID()
	return duo
}

// SetSortID sets the "sort_id" field.
func (duo *DepartmentUpdateOne) SetSortID(i int32) *DepartmentUpdateOne {
	duo.mutation.ResetSortID()
	duo.mutation.SetSortID(i)
	return duo
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (duo *DepartmentUpdateOne) SetNillableSortID(i *int32) *DepartmentUpdateOne {
	if i != nil {
		duo.SetSortID(*i)
	}
	return duo
}

// AddSortID adds i to the "sort_id" field.
func (duo *DepartmentUpdateOne) AddSortID(i int32) *DepartmentUpdateOne {
	duo.mutation.AddSortID(i)
	return duo
}

// ClearSortID clears the value of the "sort_id" field.
func (duo *DepartmentUpdateOne) ClearSortID() *DepartmentUpdateOne {
	duo.mutation.ClearSortID()
	return duo
}

// SetParent sets the "parent" edge to the Department entity.
func (duo *DepartmentUpdateOne) SetParent(d *Department) *DepartmentUpdateOne {
	return duo.SetParentID(d.ID)
}

// AddChildIDs adds the "children" edge to the Department entity by IDs.
func (duo *DepartmentUpdateOne) AddChildIDs(ids ...uint32) *DepartmentUpdateOne {
	duo.mutation.AddChildIDs(ids...)
	return duo
}

// AddChildren adds the "children" edges to the Department entity.
func (duo *DepartmentUpdateOne) AddChildren(d ...*Department) *DepartmentUpdateOne {
	ids := make([]uint32, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.AddChildIDs(ids...)
}

// Mutation returns the DepartmentMutation object of the builder.
func (duo *DepartmentUpdateOne) Mutation() *DepartmentMutation {
	return duo.mutation
}

// ClearParent clears the "parent" edge to the Department entity.
func (duo *DepartmentUpdateOne) ClearParent() *DepartmentUpdateOne {
	duo.mutation.ClearParent()
	return duo
}

// ClearChildren clears all "children" edges to the Department entity.
func (duo *DepartmentUpdateOne) ClearChildren() *DepartmentUpdateOne {
	duo.mutation.ClearChildren()
	return duo
}

// RemoveChildIDs removes the "children" edge to Department entities by IDs.
func (duo *DepartmentUpdateOne) RemoveChildIDs(ids ...uint32) *DepartmentUpdateOne {
	duo.mutation.RemoveChildIDs(ids...)
	return duo
}

// RemoveChildren removes "children" edges to Department entities.
func (duo *DepartmentUpdateOne) RemoveChildren(d ...*Department) *DepartmentUpdateOne {
	ids := make([]uint32, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return duo.RemoveChildIDs(ids...)
}

// Where appends a list predicates to the DepartmentUpdate builder.
func (duo *DepartmentUpdateOne) Where(ps ...predicate.Department) *DepartmentUpdateOne {
	duo.mutation.Where(ps...)
	return duo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duo *DepartmentUpdateOne) Select(field string, fields ...string) *DepartmentUpdateOne {
	duo.fields = append([]string{field}, fields...)
	return duo
}

// Save executes the query and returns the updated Department entity.
func (duo *DepartmentUpdateOne) Save(ctx context.Context) (*Department, error) {
	return withHooks(ctx, duo.sqlSave, duo.mutation, duo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duo *DepartmentUpdateOne) SaveX(ctx context.Context) *Department {
	node, err := duo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duo *DepartmentUpdateOne) Exec(ctx context.Context) error {
	_, err := duo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duo *DepartmentUpdateOne) ExecX(ctx context.Context) {
	if err := duo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duo *DepartmentUpdateOne) check() error {
	if v, ok := duo.mutation.Status(); ok {
		if err := department.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Department.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (duo *DepartmentUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepartmentUpdateOne {
	duo.modifiers = append(duo.modifiers, modifiers...)
	return duo
}

func (duo *DepartmentUpdateOne) sqlSave(ctx context.Context) (_node *Department, err error) {
	if err := duo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(department.Table, department.Columns, sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32))
	id, ok := duo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Department.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, department.FieldID)
		for _, f := range fields {
			if !department.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != department.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if duo.mutation.CreateTimeCleared() {
		_spec.ClearField(department.FieldCreateTime, field.TypeTime)
	}
	if value, ok := duo.mutation.UpdateTime(); ok {
		_spec.SetField(department.FieldUpdateTime, field.TypeTime, value)
	}
	if duo.mutation.UpdateTimeCleared() {
		_spec.ClearField(department.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := duo.mutation.DeleteTime(); ok {
		_spec.SetField(department.FieldDeleteTime, field.TypeTime, value)
	}
	if duo.mutation.DeleteTimeCleared() {
		_spec.ClearField(department.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := duo.mutation.Status(); ok {
		_spec.SetField(department.FieldStatus, field.TypeEnum, value)
	}
	if duo.mutation.StatusCleared() {
		_spec.ClearField(department.FieldStatus, field.TypeEnum)
	}
	if value, ok := duo.mutation.CreateBy(); ok {
		_spec.SetField(department.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := duo.mutation.AddedCreateBy(); ok {
		_spec.AddField(department.FieldCreateBy, field.TypeUint32, value)
	}
	if duo.mutation.CreateByCleared() {
		_spec.ClearField(department.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := duo.mutation.UpdateBy(); ok {
		_spec.SetField(department.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := duo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(department.FieldUpdateBy, field.TypeUint32, value)
	}
	if duo.mutation.UpdateByCleared() {
		_spec.ClearField(department.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := duo.mutation.Remark(); ok {
		_spec.SetField(department.FieldRemark, field.TypeString, value)
	}
	if duo.mutation.RemarkCleared() {
		_spec.ClearField(department.FieldRemark, field.TypeString)
	}
	if duo.mutation.TenantIDCleared() {
		_spec.ClearField(department.FieldTenantID, field.TypeUint32)
	}
	if value, ok := duo.mutation.Name(); ok {
		_spec.SetField(department.FieldName, field.TypeString, value)
	}
	if duo.mutation.NameCleared() {
		_spec.ClearField(department.FieldName, field.TypeString)
	}
	if value, ok := duo.mutation.OrganizationID(); ok {
		_spec.SetField(department.FieldOrganizationID, field.TypeUint32, value)
	}
	if value, ok := duo.mutation.AddedOrganizationID(); ok {
		_spec.AddField(department.FieldOrganizationID, field.TypeUint32, value)
	}
	if duo.mutation.OrganizationIDCleared() {
		_spec.ClearField(department.FieldOrganizationID, field.TypeUint32)
	}
	if value, ok := duo.mutation.SortID(); ok {
		_spec.SetField(department.FieldSortID, field.TypeInt32, value)
	}
	if value, ok := duo.mutation.AddedSortID(); ok {
		_spec.AddField(department.FieldSortID, field.TypeInt32, value)
	}
	if duo.mutation.SortIDCleared() {
		_spec.ClearField(department.FieldSortID, field.TypeInt32)
	}
	if duo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   department.ParentTable,
			Columns: []string{department.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   department.ParentTable,
			Columns: []string{department.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !duo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   department.ChildrenTable,
			Columns: []string{department.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(department.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(duo.modifiers...)
	_node = &Department{config: duo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{department.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duo.mutation.done = true
	return _node, nil
}
