// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	servicev1 "kratos-admin/api/gen/go/admin/service/v1"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"kratos-admin/app/admin/service/internal/data/ent/task"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TaskUpdate is the builder for updating Task entities.
type TaskUpdate struct {
	config
	hooks     []Hook
	mutation  *TaskMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TaskUpdate builder.
func (tu *TaskUpdate) Where(ps ...predicate.Task) *TaskUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetUpdateTime sets the "update_time" field.
func (tu *TaskUpdate) SetUpdateTime(t time.Time) *TaskUpdate {
	tu.mutation.SetUpdateTime(t)
	return tu
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableUpdateTime(t *time.Time) *TaskUpdate {
	if t != nil {
		tu.SetUpdateTime(*t)
	}
	return tu
}

// ClearUpdateTime clears the value of the "update_time" field.
func (tu *TaskUpdate) ClearUpdateTime() *TaskUpdate {
	tu.mutation.ClearUpdateTime()
	return tu
}

// SetDeleteTime sets the "delete_time" field.
func (tu *TaskUpdate) SetDeleteTime(t time.Time) *TaskUpdate {
	tu.mutation.SetDeleteTime(t)
	return tu
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableDeleteTime(t *time.Time) *TaskUpdate {
	if t != nil {
		tu.SetDeleteTime(*t)
	}
	return tu
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (tu *TaskUpdate) ClearDeleteTime() *TaskUpdate {
	tu.mutation.ClearDeleteTime()
	return tu
}

// SetCreateBy sets the "create_by" field.
func (tu *TaskUpdate) SetCreateBy(u uint32) *TaskUpdate {
	tu.mutation.ResetCreateBy()
	tu.mutation.SetCreateBy(u)
	return tu
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableCreateBy(u *uint32) *TaskUpdate {
	if u != nil {
		tu.SetCreateBy(*u)
	}
	return tu
}

// AddCreateBy adds u to the "create_by" field.
func (tu *TaskUpdate) AddCreateBy(u int32) *TaskUpdate {
	tu.mutation.AddCreateBy(u)
	return tu
}

// ClearCreateBy clears the value of the "create_by" field.
func (tu *TaskUpdate) ClearCreateBy() *TaskUpdate {
	tu.mutation.ClearCreateBy()
	return tu
}

// SetUpdateBy sets the "update_by" field.
func (tu *TaskUpdate) SetUpdateBy(u uint32) *TaskUpdate {
	tu.mutation.ResetUpdateBy()
	tu.mutation.SetUpdateBy(u)
	return tu
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableUpdateBy(u *uint32) *TaskUpdate {
	if u != nil {
		tu.SetUpdateBy(*u)
	}
	return tu
}

// AddUpdateBy adds u to the "update_by" field.
func (tu *TaskUpdate) AddUpdateBy(u int32) *TaskUpdate {
	tu.mutation.AddUpdateBy(u)
	return tu
}

// ClearUpdateBy clears the value of the "update_by" field.
func (tu *TaskUpdate) ClearUpdateBy() *TaskUpdate {
	tu.mutation.ClearUpdateBy()
	return tu
}

// SetRemark sets the "remark" field.
func (tu *TaskUpdate) SetRemark(s string) *TaskUpdate {
	tu.mutation.SetRemark(s)
	return tu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableRemark(s *string) *TaskUpdate {
	if s != nil {
		tu.SetRemark(*s)
	}
	return tu
}

// ClearRemark clears the value of the "remark" field.
func (tu *TaskUpdate) ClearRemark() *TaskUpdate {
	tu.mutation.ClearRemark()
	return tu
}

// SetType sets the "type" field.
func (tu *TaskUpdate) SetType(t task.Type) *TaskUpdate {
	tu.mutation.SetType(t)
	return tu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableType(t *task.Type) *TaskUpdate {
	if t != nil {
		tu.SetType(*t)
	}
	return tu
}

// ClearType clears the value of the "type" field.
func (tu *TaskUpdate) ClearType() *TaskUpdate {
	tu.mutation.ClearType()
	return tu
}

// SetTypeName sets the "type_name" field.
func (tu *TaskUpdate) SetTypeName(s string) *TaskUpdate {
	tu.mutation.SetTypeName(s)
	return tu
}

// SetNillableTypeName sets the "type_name" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableTypeName(s *string) *TaskUpdate {
	if s != nil {
		tu.SetTypeName(*s)
	}
	return tu
}

// ClearTypeName clears the value of the "type_name" field.
func (tu *TaskUpdate) ClearTypeName() *TaskUpdate {
	tu.mutation.ClearTypeName()
	return tu
}

// SetTaskPayload sets the "task_payload" field.
func (tu *TaskUpdate) SetTaskPayload(s string) *TaskUpdate {
	tu.mutation.SetTaskPayload(s)
	return tu
}

// SetNillableTaskPayload sets the "task_payload" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableTaskPayload(s *string) *TaskUpdate {
	if s != nil {
		tu.SetTaskPayload(*s)
	}
	return tu
}

// ClearTaskPayload clears the value of the "task_payload" field.
func (tu *TaskUpdate) ClearTaskPayload() *TaskUpdate {
	tu.mutation.ClearTaskPayload()
	return tu
}

// SetCronSpec sets the "cron_spec" field.
func (tu *TaskUpdate) SetCronSpec(s string) *TaskUpdate {
	tu.mutation.SetCronSpec(s)
	return tu
}

// SetNillableCronSpec sets the "cron_spec" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableCronSpec(s *string) *TaskUpdate {
	if s != nil {
		tu.SetCronSpec(*s)
	}
	return tu
}

// ClearCronSpec clears the value of the "cron_spec" field.
func (tu *TaskUpdate) ClearCronSpec() *TaskUpdate {
	tu.mutation.ClearCronSpec()
	return tu
}

// SetTaskOptions sets the "task_options" field.
func (tu *TaskUpdate) SetTaskOptions(so *servicev1.TaskOption) *TaskUpdate {
	tu.mutation.SetTaskOptions(so)
	return tu
}

// ClearTaskOptions clears the value of the "task_options" field.
func (tu *TaskUpdate) ClearTaskOptions() *TaskUpdate {
	tu.mutation.ClearTaskOptions()
	return tu
}

// SetEnable sets the "enable" field.
func (tu *TaskUpdate) SetEnable(b bool) *TaskUpdate {
	tu.mutation.SetEnable(b)
	return tu
}

// SetNillableEnable sets the "enable" field if the given value is not nil.
func (tu *TaskUpdate) SetNillableEnable(b *bool) *TaskUpdate {
	if b != nil {
		tu.SetEnable(*b)
	}
	return tu
}

// ClearEnable clears the value of the "enable" field.
func (tu *TaskUpdate) ClearEnable() *TaskUpdate {
	tu.mutation.ClearEnable()
	return tu
}

// Mutation returns the TaskMutation object of the builder.
func (tu *TaskUpdate) Mutation() *TaskMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TaskUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TaskUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TaskUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TaskUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *TaskUpdate) check() error {
	if v, ok := tu.mutation.GetType(); ok {
		if err := task.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Task.type": %w`, err)}
		}
	}
	if v, ok := tu.mutation.TaskOptions(); ok {
		if err := v.Validate(); err != nil {
			return &ValidationError{Name: "task_options", err: fmt.Errorf(`ent: validator failed for field "Task.task_options": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tu *TaskUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TaskUpdate {
	tu.modifiers = append(tu.modifiers, modifiers...)
	return tu
}

func (tu *TaskUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(task.Table, task.Columns, sqlgraph.NewFieldSpec(task.FieldID, field.TypeUint32))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if tu.mutation.CreateTimeCleared() {
		_spec.ClearField(task.FieldCreateTime, field.TypeTime)
	}
	if value, ok := tu.mutation.UpdateTime(); ok {
		_spec.SetField(task.FieldUpdateTime, field.TypeTime, value)
	}
	if tu.mutation.UpdateTimeCleared() {
		_spec.ClearField(task.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := tu.mutation.DeleteTime(); ok {
		_spec.SetField(task.FieldDeleteTime, field.TypeTime, value)
	}
	if tu.mutation.DeleteTimeCleared() {
		_spec.ClearField(task.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := tu.mutation.CreateBy(); ok {
		_spec.SetField(task.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := tu.mutation.AddedCreateBy(); ok {
		_spec.AddField(task.FieldCreateBy, field.TypeUint32, value)
	}
	if tu.mutation.CreateByCleared() {
		_spec.ClearField(task.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := tu.mutation.UpdateBy(); ok {
		_spec.SetField(task.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := tu.mutation.AddedUpdateBy(); ok {
		_spec.AddField(task.FieldUpdateBy, field.TypeUint32, value)
	}
	if tu.mutation.UpdateByCleared() {
		_spec.ClearField(task.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := tu.mutation.Remark(); ok {
		_spec.SetField(task.FieldRemark, field.TypeString, value)
	}
	if tu.mutation.RemarkCleared() {
		_spec.ClearField(task.FieldRemark, field.TypeString)
	}
	if tu.mutation.TenantIDCleared() {
		_spec.ClearField(task.FieldTenantID, field.TypeUint32)
	}
	if value, ok := tu.mutation.GetType(); ok {
		_spec.SetField(task.FieldType, field.TypeEnum, value)
	}
	if tu.mutation.TypeCleared() {
		_spec.ClearField(task.FieldType, field.TypeEnum)
	}
	if value, ok := tu.mutation.TypeName(); ok {
		_spec.SetField(task.FieldTypeName, field.TypeString, value)
	}
	if tu.mutation.TypeNameCleared() {
		_spec.ClearField(task.FieldTypeName, field.TypeString)
	}
	if value, ok := tu.mutation.TaskPayload(); ok {
		_spec.SetField(task.FieldTaskPayload, field.TypeString, value)
	}
	if tu.mutation.TaskPayloadCleared() {
		_spec.ClearField(task.FieldTaskPayload, field.TypeString)
	}
	if value, ok := tu.mutation.CronSpec(); ok {
		_spec.SetField(task.FieldCronSpec, field.TypeString, value)
	}
	if tu.mutation.CronSpecCleared() {
		_spec.ClearField(task.FieldCronSpec, field.TypeString)
	}
	if value, ok := tu.mutation.TaskOptions(); ok {
		_spec.SetField(task.FieldTaskOptions, field.TypeJSON, value)
	}
	if tu.mutation.TaskOptionsCleared() {
		_spec.ClearField(task.FieldTaskOptions, field.TypeJSON)
	}
	if value, ok := tu.mutation.Enable(); ok {
		_spec.SetField(task.FieldEnable, field.TypeBool, value)
	}
	if tu.mutation.EnableCleared() {
		_spec.ClearField(task.FieldEnable, field.TypeBool)
	}
	_spec.AddModifiers(tu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{task.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TaskUpdateOne is the builder for updating a single Task entity.
type TaskUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TaskMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (tuo *TaskUpdateOne) SetUpdateTime(t time.Time) *TaskUpdateOne {
	tuo.mutation.SetUpdateTime(t)
	return tuo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableUpdateTime(t *time.Time) *TaskUpdateOne {
	if t != nil {
		tuo.SetUpdateTime(*t)
	}
	return tuo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (tuo *TaskUpdateOne) ClearUpdateTime() *TaskUpdateOne {
	tuo.mutation.ClearUpdateTime()
	return tuo
}

// SetDeleteTime sets the "delete_time" field.
func (tuo *TaskUpdateOne) SetDeleteTime(t time.Time) *TaskUpdateOne {
	tuo.mutation.SetDeleteTime(t)
	return tuo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableDeleteTime(t *time.Time) *TaskUpdateOne {
	if t != nil {
		tuo.SetDeleteTime(*t)
	}
	return tuo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (tuo *TaskUpdateOne) ClearDeleteTime() *TaskUpdateOne {
	tuo.mutation.ClearDeleteTime()
	return tuo
}

// SetCreateBy sets the "create_by" field.
func (tuo *TaskUpdateOne) SetCreateBy(u uint32) *TaskUpdateOne {
	tuo.mutation.ResetCreateBy()
	tuo.mutation.SetCreateBy(u)
	return tuo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableCreateBy(u *uint32) *TaskUpdateOne {
	if u != nil {
		tuo.SetCreateBy(*u)
	}
	return tuo
}

// AddCreateBy adds u to the "create_by" field.
func (tuo *TaskUpdateOne) AddCreateBy(u int32) *TaskUpdateOne {
	tuo.mutation.AddCreateBy(u)
	return tuo
}

// ClearCreateBy clears the value of the "create_by" field.
func (tuo *TaskUpdateOne) ClearCreateBy() *TaskUpdateOne {
	tuo.mutation.ClearCreateBy()
	return tuo
}

// SetUpdateBy sets the "update_by" field.
func (tuo *TaskUpdateOne) SetUpdateBy(u uint32) *TaskUpdateOne {
	tuo.mutation.ResetUpdateBy()
	tuo.mutation.SetUpdateBy(u)
	return tuo
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableUpdateBy(u *uint32) *TaskUpdateOne {
	if u != nil {
		tuo.SetUpdateBy(*u)
	}
	return tuo
}

// AddUpdateBy adds u to the "update_by" field.
func (tuo *TaskUpdateOne) AddUpdateBy(u int32) *TaskUpdateOne {
	tuo.mutation.AddUpdateBy(u)
	return tuo
}

// ClearUpdateBy clears the value of the "update_by" field.
func (tuo *TaskUpdateOne) ClearUpdateBy() *TaskUpdateOne {
	tuo.mutation.ClearUpdateBy()
	return tuo
}

// SetRemark sets the "remark" field.
func (tuo *TaskUpdateOne) SetRemark(s string) *TaskUpdateOne {
	tuo.mutation.SetRemark(s)
	return tuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableRemark(s *string) *TaskUpdateOne {
	if s != nil {
		tuo.SetRemark(*s)
	}
	return tuo
}

// ClearRemark clears the value of the "remark" field.
func (tuo *TaskUpdateOne) ClearRemark() *TaskUpdateOne {
	tuo.mutation.ClearRemark()
	return tuo
}

// SetType sets the "type" field.
func (tuo *TaskUpdateOne) SetType(t task.Type) *TaskUpdateOne {
	tuo.mutation.SetType(t)
	return tuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableType(t *task.Type) *TaskUpdateOne {
	if t != nil {
		tuo.SetType(*t)
	}
	return tuo
}

// ClearType clears the value of the "type" field.
func (tuo *TaskUpdateOne) ClearType() *TaskUpdateOne {
	tuo.mutation.ClearType()
	return tuo
}

// SetTypeName sets the "type_name" field.
func (tuo *TaskUpdateOne) SetTypeName(s string) *TaskUpdateOne {
	tuo.mutation.SetTypeName(s)
	return tuo
}

// SetNillableTypeName sets the "type_name" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableTypeName(s *string) *TaskUpdateOne {
	if s != nil {
		tuo.SetTypeName(*s)
	}
	return tuo
}

// ClearTypeName clears the value of the "type_name" field.
func (tuo *TaskUpdateOne) ClearTypeName() *TaskUpdateOne {
	tuo.mutation.ClearTypeName()
	return tuo
}

// SetTaskPayload sets the "task_payload" field.
func (tuo *TaskUpdateOne) SetTaskPayload(s string) *TaskUpdateOne {
	tuo.mutation.SetTaskPayload(s)
	return tuo
}

// SetNillableTaskPayload sets the "task_payload" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableTaskPayload(s *string) *TaskUpdateOne {
	if s != nil {
		tuo.SetTaskPayload(*s)
	}
	return tuo
}

// ClearTaskPayload clears the value of the "task_payload" field.
func (tuo *TaskUpdateOne) ClearTaskPayload() *TaskUpdateOne {
	tuo.mutation.ClearTaskPayload()
	return tuo
}

// SetCronSpec sets the "cron_spec" field.
func (tuo *TaskUpdateOne) SetCronSpec(s string) *TaskUpdateOne {
	tuo.mutation.SetCronSpec(s)
	return tuo
}

// SetNillableCronSpec sets the "cron_spec" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableCronSpec(s *string) *TaskUpdateOne {
	if s != nil {
		tuo.SetCronSpec(*s)
	}
	return tuo
}

// ClearCronSpec clears the value of the "cron_spec" field.
func (tuo *TaskUpdateOne) ClearCronSpec() *TaskUpdateOne {
	tuo.mutation.ClearCronSpec()
	return tuo
}

// SetTaskOptions sets the "task_options" field.
func (tuo *TaskUpdateOne) SetTaskOptions(so *servicev1.TaskOption) *TaskUpdateOne {
	tuo.mutation.SetTaskOptions(so)
	return tuo
}

// ClearTaskOptions clears the value of the "task_options" field.
func (tuo *TaskUpdateOne) ClearTaskOptions() *TaskUpdateOne {
	tuo.mutation.ClearTaskOptions()
	return tuo
}

// SetEnable sets the "enable" field.
func (tuo *TaskUpdateOne) SetEnable(b bool) *TaskUpdateOne {
	tuo.mutation.SetEnable(b)
	return tuo
}

// SetNillableEnable sets the "enable" field if the given value is not nil.
func (tuo *TaskUpdateOne) SetNillableEnable(b *bool) *TaskUpdateOne {
	if b != nil {
		tuo.SetEnable(*b)
	}
	return tuo
}

// ClearEnable clears the value of the "enable" field.
func (tuo *TaskUpdateOne) ClearEnable() *TaskUpdateOne {
	tuo.mutation.ClearEnable()
	return tuo
}

// Mutation returns the TaskMutation object of the builder.
func (tuo *TaskUpdateOne) Mutation() *TaskMutation {
	return tuo.mutation
}

// Where appends a list predicates to the TaskUpdate builder.
func (tuo *TaskUpdateOne) Where(ps ...predicate.Task) *TaskUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TaskUpdateOne) Select(field string, fields ...string) *TaskUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Task entity.
func (tuo *TaskUpdateOne) Save(ctx context.Context) (*Task, error) {
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TaskUpdateOne) SaveX(ctx context.Context) *Task {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TaskUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TaskUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TaskUpdateOne) check() error {
	if v, ok := tuo.mutation.GetType(); ok {
		if err := task.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Task.type": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.TaskOptions(); ok {
		if err := v.Validate(); err != nil {
			return &ValidationError{Name: "task_options", err: fmt.Errorf(`ent: validator failed for field "Task.task_options": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tuo *TaskUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TaskUpdateOne {
	tuo.modifiers = append(tuo.modifiers, modifiers...)
	return tuo
}

func (tuo *TaskUpdateOne) sqlSave(ctx context.Context) (_node *Task, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(task.Table, task.Columns, sqlgraph.NewFieldSpec(task.FieldID, field.TypeUint32))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Task.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, task.FieldID)
		for _, f := range fields {
			if !task.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != task.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if tuo.mutation.CreateTimeCleared() {
		_spec.ClearField(task.FieldCreateTime, field.TypeTime)
	}
	if value, ok := tuo.mutation.UpdateTime(); ok {
		_spec.SetField(task.FieldUpdateTime, field.TypeTime, value)
	}
	if tuo.mutation.UpdateTimeCleared() {
		_spec.ClearField(task.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := tuo.mutation.DeleteTime(); ok {
		_spec.SetField(task.FieldDeleteTime, field.TypeTime, value)
	}
	if tuo.mutation.DeleteTimeCleared() {
		_spec.ClearField(task.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := tuo.mutation.CreateBy(); ok {
		_spec.SetField(task.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := tuo.mutation.AddedCreateBy(); ok {
		_spec.AddField(task.FieldCreateBy, field.TypeUint32, value)
	}
	if tuo.mutation.CreateByCleared() {
		_spec.ClearField(task.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := tuo.mutation.UpdateBy(); ok {
		_spec.SetField(task.FieldUpdateBy, field.TypeUint32, value)
	}
	if value, ok := tuo.mutation.AddedUpdateBy(); ok {
		_spec.AddField(task.FieldUpdateBy, field.TypeUint32, value)
	}
	if tuo.mutation.UpdateByCleared() {
		_spec.ClearField(task.FieldUpdateBy, field.TypeUint32)
	}
	if value, ok := tuo.mutation.Remark(); ok {
		_spec.SetField(task.FieldRemark, field.TypeString, value)
	}
	if tuo.mutation.RemarkCleared() {
		_spec.ClearField(task.FieldRemark, field.TypeString)
	}
	if tuo.mutation.TenantIDCleared() {
		_spec.ClearField(task.FieldTenantID, field.TypeUint32)
	}
	if value, ok := tuo.mutation.GetType(); ok {
		_spec.SetField(task.FieldType, field.TypeEnum, value)
	}
	if tuo.mutation.TypeCleared() {
		_spec.ClearField(task.FieldType, field.TypeEnum)
	}
	if value, ok := tuo.mutation.TypeName(); ok {
		_spec.SetField(task.FieldTypeName, field.TypeString, value)
	}
	if tuo.mutation.TypeNameCleared() {
		_spec.ClearField(task.FieldTypeName, field.TypeString)
	}
	if value, ok := tuo.mutation.TaskPayload(); ok {
		_spec.SetField(task.FieldTaskPayload, field.TypeString, value)
	}
	if tuo.mutation.TaskPayloadCleared() {
		_spec.ClearField(task.FieldTaskPayload, field.TypeString)
	}
	if value, ok := tuo.mutation.CronSpec(); ok {
		_spec.SetField(task.FieldCronSpec, field.TypeString, value)
	}
	if tuo.mutation.CronSpecCleared() {
		_spec.ClearField(task.FieldCronSpec, field.TypeString)
	}
	if value, ok := tuo.mutation.TaskOptions(); ok {
		_spec.SetField(task.FieldTaskOptions, field.TypeJSON, value)
	}
	if tuo.mutation.TaskOptionsCleared() {
		_spec.ClearField(task.FieldTaskOptions, field.TypeJSON)
	}
	if value, ok := tuo.mutation.Enable(); ok {
		_spec.SetField(task.FieldEnable, field.TypeBool, value)
	}
	if tuo.mutation.EnableCleared() {
		_spec.ClearField(task.FieldEnable, field.TypeBool)
	}
	_spec.AddModifiers(tuo.modifiers...)
	_node = &Task{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{task.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
