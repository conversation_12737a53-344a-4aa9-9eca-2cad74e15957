// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagecategory"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageCategoryDelete is the builder for deleting a NotificationMessageCategory entity.
type NotificationMessageCategoryDelete struct {
	config
	hooks    []Hook
	mutation *NotificationMessageCategoryMutation
}

// Where appends a list predicates to the NotificationMessageCategoryDelete builder.
func (nmcd *NotificationMessageCategoryDelete) Where(ps ...predicate.NotificationMessageCategory) *NotificationMessageCategoryDelete {
	nmcd.mutation.Where(ps...)
	return nmcd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (nmcd *NotificationMessageCategoryDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, nmcd.sqlExec, nmcd.mutation, nmcd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (nmcd *NotificationMessageCategoryDelete) ExecX(ctx context.Context) int {
	n, err := nmcd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (nmcd *NotificationMessageCategoryDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(notificationmessagecategory.Table, sqlgraph.NewFieldSpec(notificationmessagecategory.FieldID, field.TypeUint32))
	if ps := nmcd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, nmcd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	nmcd.mutation.done = true
	return affected, err
}

// NotificationMessageCategoryDeleteOne is the builder for deleting a single NotificationMessageCategory entity.
type NotificationMessageCategoryDeleteOne struct {
	nmcd *NotificationMessageCategoryDelete
}

// Where appends a list predicates to the NotificationMessageCategoryDelete builder.
func (nmcdo *NotificationMessageCategoryDeleteOne) Where(ps ...predicate.NotificationMessageCategory) *NotificationMessageCategoryDeleteOne {
	nmcdo.nmcd.mutation.Where(ps...)
	return nmcdo
}

// Exec executes the deletion query.
func (nmcdo *NotificationMessageCategoryDeleteOne) Exec(ctx context.Context) error {
	n, err := nmcdo.nmcd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{notificationmessagecategory.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (nmcdo *NotificationMessageCategoryDeleteOne) ExecX(ctx context.Context) {
	if err := nmcdo.Exec(ctx); err != nil {
		panic(err)
	}
}
