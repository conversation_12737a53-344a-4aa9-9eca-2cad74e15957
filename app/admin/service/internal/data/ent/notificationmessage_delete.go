// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageDelete is the builder for deleting a NotificationMessage entity.
type NotificationMessageDelete struct {
	config
	hooks    []Hook
	mutation *NotificationMessageMutation
}

// Where appends a list predicates to the NotificationMessageDelete builder.
func (nmd *NotificationMessageDelete) Where(ps ...predicate.NotificationMessage) *NotificationMessageDelete {
	nmd.mutation.Where(ps...)
	return nmd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (nmd *NotificationMessageDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, nmd.sqlExec, nmd.mutation, nmd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (nmd *NotificationMessageDelete) ExecX(ctx context.Context) int {
	n, err := nmd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (nmd *NotificationMessageDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(notificationmessage.Table, sqlgraph.NewFieldSpec(notificationmessage.FieldID, field.TypeUint32))
	if ps := nmd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, nmd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	nmd.mutation.done = true
	return affected, err
}

// NotificationMessageDeleteOne is the builder for deleting a single NotificationMessage entity.
type NotificationMessageDeleteOne struct {
	nmd *NotificationMessageDelete
}

// Where appends a list predicates to the NotificationMessageDelete builder.
func (nmdo *NotificationMessageDeleteOne) Where(ps ...predicate.NotificationMessage) *NotificationMessageDeleteOne {
	nmdo.nmd.mutation.Where(ps...)
	return nmdo
}

// Exec executes the deletion query.
func (nmdo *NotificationMessageDeleteOne) Exec(ctx context.Context) error {
	n, err := nmdo.nmd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{notificationmessage.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (nmdo *NotificationMessageDeleteOne) ExecX(ctx context.Context) {
	if err := nmdo.Exec(ctx); err != nil {
		panic(err)
	}
}
