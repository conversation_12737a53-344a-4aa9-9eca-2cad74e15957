// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 后台操作日志表
type AdminOperationLog struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 请求ID
	RequestID *string `json:"request_id,omitempty"`
	// 请求方法
	Method *string `json:"method,omitempty"`
	// 操作方法
	Operation *string `json:"operation,omitempty"`
	// 请求路径
	Path *string `json:"path,omitempty"`
	// 请求源
	Referer *string `json:"referer,omitempty"`
	// 请求URI
	RequestURI *string `json:"request_uri,omitempty"`
	// 请求体
	RequestBody *string `json:"request_body,omitempty"`
	// 请求头
	RequestHeader *string `json:"request_header,omitempty"`
	// 响应信息
	Response *string `json:"response,omitempty"`
	// 操作耗时
	CostTime *float64 `json:"cost_time,omitempty"`
	// 操作者用户ID
	UserID *uint32 `json:"user_id,omitempty"`
	// 操作者账号名
	Username *string `json:"username,omitempty"`
	// 操作者IP
	ClientIP *string `json:"client_ip,omitempty"`
	// 状态码
	StatusCode *int32 `json:"status_code,omitempty"`
	// 操作失败原因
	Reason *string `json:"reason,omitempty"`
	// 操作成功
	Success *bool `json:"success,omitempty"`
	// 操作地理位置
	Location *string `json:"location,omitempty"`
	// 浏览器的用户代理信息
	UserAgent *string `json:"user_agent,omitempty"`
	// 浏览器名称
	BrowserName *string `json:"browser_name,omitempty"`
	// 浏览器版本
	BrowserVersion *string `json:"browser_version,omitempty"`
	// 客户端ID
	ClientID *string `json:"client_id,omitempty"`
	// 客户端名称
	ClientName *string `json:"client_name,omitempty"`
	// 操作系统名称
	OsName *string `json:"os_name,omitempty"`
	// 操作系统版本
	OsVersion    *string `json:"os_version,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AdminOperationLog) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case adminoperationlog.FieldSuccess:
			values[i] = new(sql.NullBool)
		case adminoperationlog.FieldCostTime:
			values[i] = new(sql.NullFloat64)
		case adminoperationlog.FieldID, adminoperationlog.FieldUserID, adminoperationlog.FieldStatusCode:
			values[i] = new(sql.NullInt64)
		case adminoperationlog.FieldRequestID, adminoperationlog.FieldMethod, adminoperationlog.FieldOperation, adminoperationlog.FieldPath, adminoperationlog.FieldReferer, adminoperationlog.FieldRequestURI, adminoperationlog.FieldRequestBody, adminoperationlog.FieldRequestHeader, adminoperationlog.FieldResponse, adminoperationlog.FieldUsername, adminoperationlog.FieldClientIP, adminoperationlog.FieldReason, adminoperationlog.FieldLocation, adminoperationlog.FieldUserAgent, adminoperationlog.FieldBrowserName, adminoperationlog.FieldBrowserVersion, adminoperationlog.FieldClientID, adminoperationlog.FieldClientName, adminoperationlog.FieldOsName, adminoperationlog.FieldOsVersion:
			values[i] = new(sql.NullString)
		case adminoperationlog.FieldCreateTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AdminOperationLog fields.
func (aol *AdminOperationLog) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case adminoperationlog.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aol.ID = uint32(value.Int64)
		case adminoperationlog.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				aol.CreateTime = new(time.Time)
				*aol.CreateTime = value.Time
			}
		case adminoperationlog.FieldRequestID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_id", values[i])
			} else if value.Valid {
				aol.RequestID = new(string)
				*aol.RequestID = value.String
			}
		case adminoperationlog.FieldMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field method", values[i])
			} else if value.Valid {
				aol.Method = new(string)
				*aol.Method = value.String
			}
		case adminoperationlog.FieldOperation:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field operation", values[i])
			} else if value.Valid {
				aol.Operation = new(string)
				*aol.Operation = value.String
			}
		case adminoperationlog.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				aol.Path = new(string)
				*aol.Path = value.String
			}
		case adminoperationlog.FieldReferer:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field referer", values[i])
			} else if value.Valid {
				aol.Referer = new(string)
				*aol.Referer = value.String
			}
		case adminoperationlog.FieldRequestURI:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_uri", values[i])
			} else if value.Valid {
				aol.RequestURI = new(string)
				*aol.RequestURI = value.String
			}
		case adminoperationlog.FieldRequestBody:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_body", values[i])
			} else if value.Valid {
				aol.RequestBody = new(string)
				*aol.RequestBody = value.String
			}
		case adminoperationlog.FieldRequestHeader:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_header", values[i])
			} else if value.Valid {
				aol.RequestHeader = new(string)
				*aol.RequestHeader = value.String
			}
		case adminoperationlog.FieldResponse:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field response", values[i])
			} else if value.Valid {
				aol.Response = new(string)
				*aol.Response = value.String
			}
		case adminoperationlog.FieldCostTime:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field cost_time", values[i])
			} else if value.Valid {
				aol.CostTime = new(float64)
				*aol.CostTime = value.Float64
			}
		case adminoperationlog.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				aol.UserID = new(uint32)
				*aol.UserID = uint32(value.Int64)
			}
		case adminoperationlog.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				aol.Username = new(string)
				*aol.Username = value.String
			}
		case adminoperationlog.FieldClientIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_ip", values[i])
			} else if value.Valid {
				aol.ClientIP = new(string)
				*aol.ClientIP = value.String
			}
		case adminoperationlog.FieldStatusCode:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status_code", values[i])
			} else if value.Valid {
				aol.StatusCode = new(int32)
				*aol.StatusCode = int32(value.Int64)
			}
		case adminoperationlog.FieldReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reason", values[i])
			} else if value.Valid {
				aol.Reason = new(string)
				*aol.Reason = value.String
			}
		case adminoperationlog.FieldSuccess:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field success", values[i])
			} else if value.Valid {
				aol.Success = new(bool)
				*aol.Success = value.Bool
			}
		case adminoperationlog.FieldLocation:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field location", values[i])
			} else if value.Valid {
				aol.Location = new(string)
				*aol.Location = value.String
			}
		case adminoperationlog.FieldUserAgent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_agent", values[i])
			} else if value.Valid {
				aol.UserAgent = new(string)
				*aol.UserAgent = value.String
			}
		case adminoperationlog.FieldBrowserName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field browser_name", values[i])
			} else if value.Valid {
				aol.BrowserName = new(string)
				*aol.BrowserName = value.String
			}
		case adminoperationlog.FieldBrowserVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field browser_version", values[i])
			} else if value.Valid {
				aol.BrowserVersion = new(string)
				*aol.BrowserVersion = value.String
			}
		case adminoperationlog.FieldClientID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_id", values[i])
			} else if value.Valid {
				aol.ClientID = new(string)
				*aol.ClientID = value.String
			}
		case adminoperationlog.FieldClientName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_name", values[i])
			} else if value.Valid {
				aol.ClientName = new(string)
				*aol.ClientName = value.String
			}
		case adminoperationlog.FieldOsName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field os_name", values[i])
			} else if value.Valid {
				aol.OsName = new(string)
				*aol.OsName = value.String
			}
		case adminoperationlog.FieldOsVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field os_version", values[i])
			} else if value.Valid {
				aol.OsVersion = new(string)
				*aol.OsVersion = value.String
			}
		default:
			aol.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AdminOperationLog.
// This includes values selected through modifiers, order, etc.
func (aol *AdminOperationLog) Value(name string) (ent.Value, error) {
	return aol.selectValues.Get(name)
}

// Update returns a builder for updating this AdminOperationLog.
// Note that you need to call AdminOperationLog.Unwrap() before calling this method if this AdminOperationLog
// was returned from a transaction, and the transaction was committed or rolled back.
func (aol *AdminOperationLog) Update() *AdminOperationLogUpdateOne {
	return NewAdminOperationLogClient(aol.config).UpdateOne(aol)
}

// Unwrap unwraps the AdminOperationLog entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aol *AdminOperationLog) Unwrap() *AdminOperationLog {
	_tx, ok := aol.config.driver.(*txDriver)
	if !ok {
		panic("ent: AdminOperationLog is not a transactional entity")
	}
	aol.config.driver = _tx.drv
	return aol
}

// String implements the fmt.Stringer.
func (aol *AdminOperationLog) String() string {
	var builder strings.Builder
	builder.WriteString("AdminOperationLog(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aol.ID))
	if v := aol.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := aol.RequestID; v != nil {
		builder.WriteString("request_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.Method; v != nil {
		builder.WriteString("method=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.Operation; v != nil {
		builder.WriteString("operation=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.Path; v != nil {
		builder.WriteString("path=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.Referer; v != nil {
		builder.WriteString("referer=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.RequestURI; v != nil {
		builder.WriteString("request_uri=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.RequestBody; v != nil {
		builder.WriteString("request_body=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.RequestHeader; v != nil {
		builder.WriteString("request_header=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.Response; v != nil {
		builder.WriteString("response=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.CostTime; v != nil {
		builder.WriteString("cost_time=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := aol.UserID; v != nil {
		builder.WriteString("user_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := aol.Username; v != nil {
		builder.WriteString("username=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.ClientIP; v != nil {
		builder.WriteString("client_ip=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.StatusCode; v != nil {
		builder.WriteString("status_code=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := aol.Reason; v != nil {
		builder.WriteString("reason=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.Success; v != nil {
		builder.WriteString("success=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := aol.Location; v != nil {
		builder.WriteString("location=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.UserAgent; v != nil {
		builder.WriteString("user_agent=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.BrowserName; v != nil {
		builder.WriteString("browser_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.BrowserVersion; v != nil {
		builder.WriteString("browser_version=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.ClientID; v != nil {
		builder.WriteString("client_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.ClientName; v != nil {
		builder.WriteString("client_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.OsName; v != nil {
		builder.WriteString("os_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := aol.OsVersion; v != nil {
		builder.WriteString("os_version=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// AdminOperationLogs is a parsable slice of AdminOperationLog.
type AdminOperationLogs []*AdminOperationLog
