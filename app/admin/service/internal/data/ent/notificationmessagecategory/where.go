// Code generated by ent, DO NOT EDIT.

package notificationmessagecategory

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldDeleteTime, v))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldCreateBy, v))
}

// UpdateBy applies equality check predicate on the "update_by" field. It's identical to UpdateByEQ.
func UpdateBy(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldUpdateBy, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldRemark, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldTenantID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldCode, v))
}

// SortID applies equality check predicate on the "sort_id" field. It's identical to SortIDEQ.
func SortID(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldSortID, v))
}

// Enable applies equality check predicate on the "enable" field. It's identical to EnableEQ.
func Enable(v bool) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldEnable, v))
}

// ParentID applies equality check predicate on the "parent_id" field. It's identical to ParentIDEQ.
func ParentID(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldParentID, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldDeleteTime))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldCreateBy))
}

// UpdateByEQ applies the EQ predicate on the "update_by" field.
func UpdateByEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldUpdateBy, v))
}

// UpdateByNEQ applies the NEQ predicate on the "update_by" field.
func UpdateByNEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldUpdateBy, v))
}

// UpdateByIn applies the In predicate on the "update_by" field.
func UpdateByIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldUpdateBy, vs...))
}

// UpdateByNotIn applies the NotIn predicate on the "update_by" field.
func UpdateByNotIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldUpdateBy, vs...))
}

// UpdateByGT applies the GT predicate on the "update_by" field.
func UpdateByGT(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldUpdateBy, v))
}

// UpdateByGTE applies the GTE predicate on the "update_by" field.
func UpdateByGTE(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldUpdateBy, v))
}

// UpdateByLT applies the LT predicate on the "update_by" field.
func UpdateByLT(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldUpdateBy, v))
}

// UpdateByLTE applies the LTE predicate on the "update_by" field.
func UpdateByLTE(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldUpdateBy, v))
}

// UpdateByIsNil applies the IsNil predicate on the "update_by" field.
func UpdateByIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldUpdateBy))
}

// UpdateByNotNil applies the NotNil predicate on the "update_by" field.
func UpdateByNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldUpdateBy))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldContainsFold(FieldRemark, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldTenantID))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldContainsFold(FieldName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldHasSuffix(FieldCode, v))
}

// CodeIsNil applies the IsNil predicate on the "code" field.
func CodeIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldCode))
}

// CodeNotNil applies the NotNil predicate on the "code" field.
func CodeNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldCode))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldContainsFold(FieldCode, v))
}

// SortIDEQ applies the EQ predicate on the "sort_id" field.
func SortIDEQ(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldSortID, v))
}

// SortIDNEQ applies the NEQ predicate on the "sort_id" field.
func SortIDNEQ(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldSortID, v))
}

// SortIDIn applies the In predicate on the "sort_id" field.
func SortIDIn(vs ...int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldSortID, vs...))
}

// SortIDNotIn applies the NotIn predicate on the "sort_id" field.
func SortIDNotIn(vs ...int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldSortID, vs...))
}

// SortIDGT applies the GT predicate on the "sort_id" field.
func SortIDGT(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGT(FieldSortID, v))
}

// SortIDGTE applies the GTE predicate on the "sort_id" field.
func SortIDGTE(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldGTE(FieldSortID, v))
}

// SortIDLT applies the LT predicate on the "sort_id" field.
func SortIDLT(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLT(FieldSortID, v))
}

// SortIDLTE applies the LTE predicate on the "sort_id" field.
func SortIDLTE(v int32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldLTE(FieldSortID, v))
}

// SortIDIsNil applies the IsNil predicate on the "sort_id" field.
func SortIDIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldSortID))
}

// SortIDNotNil applies the NotNil predicate on the "sort_id" field.
func SortIDNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldSortID))
}

// EnableEQ applies the EQ predicate on the "enable" field.
func EnableEQ(v bool) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldEnable, v))
}

// EnableNEQ applies the NEQ predicate on the "enable" field.
func EnableNEQ(v bool) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldEnable, v))
}

// EnableIsNil applies the IsNil predicate on the "enable" field.
func EnableIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldEnable))
}

// EnableNotNil applies the NotNil predicate on the "enable" field.
func EnableNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldEnable))
}

// ParentIDEQ applies the EQ predicate on the "parent_id" field.
func ParentIDEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldEQ(FieldParentID, v))
}

// ParentIDNEQ applies the NEQ predicate on the "parent_id" field.
func ParentIDNEQ(v uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNEQ(FieldParentID, v))
}

// ParentIDIn applies the In predicate on the "parent_id" field.
func ParentIDIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIn(FieldParentID, vs...))
}

// ParentIDNotIn applies the NotIn predicate on the "parent_id" field.
func ParentIDNotIn(vs ...uint32) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotIn(FieldParentID, vs...))
}

// ParentIDIsNil applies the IsNil predicate on the "parent_id" field.
func ParentIDIsNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldIsNull(FieldParentID))
}

// ParentIDNotNil applies the NotNil predicate on the "parent_id" field.
func ParentIDNotNil() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.FieldNotNull(FieldParentID))
}

// HasParent applies the HasEdge predicate on the "parent" edge.
func HasParent() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasParentWith applies the HasEdge predicate on the "parent" edge with a given conditions (other predicates).
func HasParentWith(preds ...predicate.NotificationMessageCategory) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(func(s *sql.Selector) {
		step := newParentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasChildren applies the HasEdge predicate on the "children" edge.
func HasChildren() predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasChildrenWith applies the HasEdge predicate on the "children" edge with a given conditions (other predicates).
func HasChildrenWith(preds ...predicate.NotificationMessageCategory) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(func(s *sql.Selector) {
		step := newChildrenStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.NotificationMessageCategory) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.NotificationMessageCategory) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.NotificationMessageCategory) predicate.NotificationMessageCategory {
	return predicate.NotificationMessageCategory(sql.NotPredicates(p))
}
