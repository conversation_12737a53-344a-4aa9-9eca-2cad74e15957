// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageCreate is the builder for creating a NotificationMessage entity.
type NotificationMessageCreate struct {
	config
	mutation *NotificationMessageMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (nmc *NotificationMessageCreate) SetCreateTime(t time.Time) *NotificationMessageCreate {
	nmc.mutation.SetCreateTime(t)
	return nmc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableCreateTime(t *time.Time) *NotificationMessageCreate {
	if t != nil {
		nmc.SetCreateTime(*t)
	}
	return nmc
}

// SetUpdateTime sets the "update_time" field.
func (nmc *NotificationMessageCreate) SetUpdateTime(t time.Time) *NotificationMessageCreate {
	nmc.mutation.SetUpdateTime(t)
	return nmc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableUpdateTime(t *time.Time) *NotificationMessageCreate {
	if t != nil {
		nmc.SetUpdateTime(*t)
	}
	return nmc
}

// SetDeleteTime sets the "delete_time" field.
func (nmc *NotificationMessageCreate) SetDeleteTime(t time.Time) *NotificationMessageCreate {
	nmc.mutation.SetDeleteTime(t)
	return nmc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableDeleteTime(t *time.Time) *NotificationMessageCreate {
	if t != nil {
		nmc.SetDeleteTime(*t)
	}
	return nmc
}

// SetCreateBy sets the "create_by" field.
func (nmc *NotificationMessageCreate) SetCreateBy(u uint32) *NotificationMessageCreate {
	nmc.mutation.SetCreateBy(u)
	return nmc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableCreateBy(u *uint32) *NotificationMessageCreate {
	if u != nil {
		nmc.SetCreateBy(*u)
	}
	return nmc
}

// SetUpdateBy sets the "update_by" field.
func (nmc *NotificationMessageCreate) SetUpdateBy(u uint32) *NotificationMessageCreate {
	nmc.mutation.SetUpdateBy(u)
	return nmc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableUpdateBy(u *uint32) *NotificationMessageCreate {
	if u != nil {
		nmc.SetUpdateBy(*u)
	}
	return nmc
}

// SetTenantID sets the "tenant_id" field.
func (nmc *NotificationMessageCreate) SetTenantID(u uint32) *NotificationMessageCreate {
	nmc.mutation.SetTenantID(u)
	return nmc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableTenantID(u *uint32) *NotificationMessageCreate {
	if u != nil {
		nmc.SetTenantID(*u)
	}
	return nmc
}

// SetSubject sets the "subject" field.
func (nmc *NotificationMessageCreate) SetSubject(s string) *NotificationMessageCreate {
	nmc.mutation.SetSubject(s)
	return nmc
}

// SetNillableSubject sets the "subject" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableSubject(s *string) *NotificationMessageCreate {
	if s != nil {
		nmc.SetSubject(*s)
	}
	return nmc
}

// SetContent sets the "content" field.
func (nmc *NotificationMessageCreate) SetContent(s string) *NotificationMessageCreate {
	nmc.mutation.SetContent(s)
	return nmc
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableContent(s *string) *NotificationMessageCreate {
	if s != nil {
		nmc.SetContent(*s)
	}
	return nmc
}

// SetCategoryID sets the "category_id" field.
func (nmc *NotificationMessageCreate) SetCategoryID(u uint32) *NotificationMessageCreate {
	nmc.mutation.SetCategoryID(u)
	return nmc
}

// SetNillableCategoryID sets the "category_id" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableCategoryID(u *uint32) *NotificationMessageCreate {
	if u != nil {
		nmc.SetCategoryID(*u)
	}
	return nmc
}

// SetStatus sets the "status" field.
func (nmc *NotificationMessageCreate) SetStatus(n notificationmessage.Status) *NotificationMessageCreate {
	nmc.mutation.SetStatus(n)
	return nmc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nmc *NotificationMessageCreate) SetNillableStatus(n *notificationmessage.Status) *NotificationMessageCreate {
	if n != nil {
		nmc.SetStatus(*n)
	}
	return nmc
}

// SetID sets the "id" field.
func (nmc *NotificationMessageCreate) SetID(u uint32) *NotificationMessageCreate {
	nmc.mutation.SetID(u)
	return nmc
}

// Mutation returns the NotificationMessageMutation object of the builder.
func (nmc *NotificationMessageCreate) Mutation() *NotificationMessageMutation {
	return nmc.mutation
}

// Save creates the NotificationMessage in the database.
func (nmc *NotificationMessageCreate) Save(ctx context.Context) (*NotificationMessage, error) {
	return withHooks(ctx, nmc.sqlSave, nmc.mutation, nmc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (nmc *NotificationMessageCreate) SaveX(ctx context.Context) *NotificationMessage {
	v, err := nmc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nmc *NotificationMessageCreate) Exec(ctx context.Context) error {
	_, err := nmc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmc *NotificationMessageCreate) ExecX(ctx context.Context) {
	if err := nmc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmc *NotificationMessageCreate) check() error {
	if v, ok := nmc.mutation.TenantID(); ok {
		if err := notificationmessage.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "NotificationMessage.tenant_id": %w`, err)}
		}
	}
	if v, ok := nmc.mutation.Status(); ok {
		if err := notificationmessage.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "NotificationMessage.status": %w`, err)}
		}
	}
	if v, ok := nmc.mutation.ID(); ok {
		if err := notificationmessage.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "NotificationMessage.id": %w`, err)}
		}
	}
	return nil
}

func (nmc *NotificationMessageCreate) sqlSave(ctx context.Context) (*NotificationMessage, error) {
	if err := nmc.check(); err != nil {
		return nil, err
	}
	_node, _spec := nmc.createSpec()
	if err := sqlgraph.CreateNode(ctx, nmc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	nmc.mutation.id = &_node.ID
	nmc.mutation.done = true
	return _node, nil
}

func (nmc *NotificationMessageCreate) createSpec() (*NotificationMessage, *sqlgraph.CreateSpec) {
	var (
		_node = &NotificationMessage{config: nmc.config}
		_spec = sqlgraph.NewCreateSpec(notificationmessage.Table, sqlgraph.NewFieldSpec(notificationmessage.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = nmc.conflict
	if id, ok := nmc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := nmc.mutation.CreateTime(); ok {
		_spec.SetField(notificationmessage.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := nmc.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessage.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := nmc.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessage.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := nmc.mutation.CreateBy(); ok {
		_spec.SetField(notificationmessage.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := nmc.mutation.UpdateBy(); ok {
		_spec.SetField(notificationmessage.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := nmc.mutation.TenantID(); ok {
		_spec.SetField(notificationmessage.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := nmc.mutation.Subject(); ok {
		_spec.SetField(notificationmessage.FieldSubject, field.TypeString, value)
		_node.Subject = &value
	}
	if value, ok := nmc.mutation.Content(); ok {
		_spec.SetField(notificationmessage.FieldContent, field.TypeString, value)
		_node.Content = &value
	}
	if value, ok := nmc.mutation.CategoryID(); ok {
		_spec.SetField(notificationmessage.FieldCategoryID, field.TypeUint32, value)
		_node.CategoryID = &value
	}
	if value, ok := nmc.mutation.Status(); ok {
		_spec.SetField(notificationmessage.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.NotificationMessage.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.NotificationMessageUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (nmc *NotificationMessageCreate) OnConflict(opts ...sql.ConflictOption) *NotificationMessageUpsertOne {
	nmc.conflict = opts
	return &NotificationMessageUpsertOne{
		create: nmc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.NotificationMessage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (nmc *NotificationMessageCreate) OnConflictColumns(columns ...string) *NotificationMessageUpsertOne {
	nmc.conflict = append(nmc.conflict, sql.ConflictColumns(columns...))
	return &NotificationMessageUpsertOne{
		create: nmc,
	}
}

type (
	// NotificationMessageUpsertOne is the builder for "upsert"-ing
	//  one NotificationMessage node.
	NotificationMessageUpsertOne struct {
		create *NotificationMessageCreate
	}

	// NotificationMessageUpsert is the "OnConflict" setter.
	NotificationMessageUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageUpsert) SetUpdateTime(v time.Time) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateUpdateTime() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageUpsert) ClearUpdateTime() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageUpsert) SetDeleteTime(v time.Time) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateDeleteTime() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageUpsert) ClearDeleteTime() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *NotificationMessageUpsert) SetCreateBy(v uint32) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateCreateBy() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *NotificationMessageUpsert) AddCreateBy(v uint32) *NotificationMessageUpsert {
	u.Add(notificationmessage.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *NotificationMessageUpsert) ClearCreateBy() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *NotificationMessageUpsert) SetUpdateBy(v uint32) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateUpdateBy() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *NotificationMessageUpsert) AddUpdateBy(v uint32) *NotificationMessageUpsert {
	u.Add(notificationmessage.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *NotificationMessageUpsert) ClearUpdateBy() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldUpdateBy)
	return u
}

// SetSubject sets the "subject" field.
func (u *NotificationMessageUpsert) SetSubject(v string) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldSubject, v)
	return u
}

// UpdateSubject sets the "subject" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateSubject() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldSubject)
	return u
}

// ClearSubject clears the value of the "subject" field.
func (u *NotificationMessageUpsert) ClearSubject() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldSubject)
	return u
}

// SetContent sets the "content" field.
func (u *NotificationMessageUpsert) SetContent(v string) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldContent, v)
	return u
}

// UpdateContent sets the "content" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateContent() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldContent)
	return u
}

// ClearContent clears the value of the "content" field.
func (u *NotificationMessageUpsert) ClearContent() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldContent)
	return u
}

// SetCategoryID sets the "category_id" field.
func (u *NotificationMessageUpsert) SetCategoryID(v uint32) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldCategoryID, v)
	return u
}

// UpdateCategoryID sets the "category_id" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateCategoryID() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldCategoryID)
	return u
}

// AddCategoryID adds v to the "category_id" field.
func (u *NotificationMessageUpsert) AddCategoryID(v uint32) *NotificationMessageUpsert {
	u.Add(notificationmessage.FieldCategoryID, v)
	return u
}

// ClearCategoryID clears the value of the "category_id" field.
func (u *NotificationMessageUpsert) ClearCategoryID() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldCategoryID)
	return u
}

// SetStatus sets the "status" field.
func (u *NotificationMessageUpsert) SetStatus(v notificationmessage.Status) *NotificationMessageUpsert {
	u.Set(notificationmessage.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *NotificationMessageUpsert) UpdateStatus() *NotificationMessageUpsert {
	u.SetExcluded(notificationmessage.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *NotificationMessageUpsert) ClearStatus() *NotificationMessageUpsert {
	u.SetNull(notificationmessage.FieldStatus)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.NotificationMessage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(notificationmessage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *NotificationMessageUpsertOne) UpdateNewValues() *NotificationMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(notificationmessage.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(notificationmessage.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(notificationmessage.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.NotificationMessage.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *NotificationMessageUpsertOne) Ignore() *NotificationMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *NotificationMessageUpsertOne) DoNothing() *NotificationMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the NotificationMessageCreate.OnConflict
// documentation for more info.
func (u *NotificationMessageUpsertOne) Update(set func(*NotificationMessageUpsert)) *NotificationMessageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&NotificationMessageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageUpsertOne) SetUpdateTime(v time.Time) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateUpdateTime() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageUpsertOne) ClearUpdateTime() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageUpsertOne) SetDeleteTime(v time.Time) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateDeleteTime() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageUpsertOne) ClearDeleteTime() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *NotificationMessageUpsertOne) SetCreateBy(v uint32) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *NotificationMessageUpsertOne) AddCreateBy(v uint32) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateCreateBy() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *NotificationMessageUpsertOne) ClearCreateBy() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *NotificationMessageUpsertOne) SetUpdateBy(v uint32) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *NotificationMessageUpsertOne) AddUpdateBy(v uint32) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateUpdateBy() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *NotificationMessageUpsertOne) ClearUpdateBy() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearUpdateBy()
	})
}

// SetSubject sets the "subject" field.
func (u *NotificationMessageUpsertOne) SetSubject(v string) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetSubject(v)
	})
}

// UpdateSubject sets the "subject" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateSubject() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateSubject()
	})
}

// ClearSubject clears the value of the "subject" field.
func (u *NotificationMessageUpsertOne) ClearSubject() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearSubject()
	})
}

// SetContent sets the "content" field.
func (u *NotificationMessageUpsertOne) SetContent(v string) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetContent(v)
	})
}

// UpdateContent sets the "content" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateContent() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateContent()
	})
}

// ClearContent clears the value of the "content" field.
func (u *NotificationMessageUpsertOne) ClearContent() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearContent()
	})
}

// SetCategoryID sets the "category_id" field.
func (u *NotificationMessageUpsertOne) SetCategoryID(v uint32) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetCategoryID(v)
	})
}

// AddCategoryID adds v to the "category_id" field.
func (u *NotificationMessageUpsertOne) AddCategoryID(v uint32) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.AddCategoryID(v)
	})
}

// UpdateCategoryID sets the "category_id" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateCategoryID() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateCategoryID()
	})
}

// ClearCategoryID clears the value of the "category_id" field.
func (u *NotificationMessageUpsertOne) ClearCategoryID() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearCategoryID()
	})
}

// SetStatus sets the "status" field.
func (u *NotificationMessageUpsertOne) SetStatus(v notificationmessage.Status) *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *NotificationMessageUpsertOne) UpdateStatus() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *NotificationMessageUpsertOne) ClearStatus() *NotificationMessageUpsertOne {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearStatus()
	})
}

// Exec executes the query.
func (u *NotificationMessageUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for NotificationMessageCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *NotificationMessageUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *NotificationMessageUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *NotificationMessageUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// NotificationMessageCreateBulk is the builder for creating many NotificationMessage entities in bulk.
type NotificationMessageCreateBulk struct {
	config
	err      error
	builders []*NotificationMessageCreate
	conflict []sql.ConflictOption
}

// Save creates the NotificationMessage entities in the database.
func (nmcb *NotificationMessageCreateBulk) Save(ctx context.Context) ([]*NotificationMessage, error) {
	if nmcb.err != nil {
		return nil, nmcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(nmcb.builders))
	nodes := make([]*NotificationMessage, len(nmcb.builders))
	mutators := make([]Mutator, len(nmcb.builders))
	for i := range nmcb.builders {
		func(i int, root context.Context) {
			builder := nmcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotificationMessageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, nmcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = nmcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, nmcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, nmcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (nmcb *NotificationMessageCreateBulk) SaveX(ctx context.Context) []*NotificationMessage {
	v, err := nmcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nmcb *NotificationMessageCreateBulk) Exec(ctx context.Context) error {
	_, err := nmcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmcb *NotificationMessageCreateBulk) ExecX(ctx context.Context) {
	if err := nmcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.NotificationMessage.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.NotificationMessageUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (nmcb *NotificationMessageCreateBulk) OnConflict(opts ...sql.ConflictOption) *NotificationMessageUpsertBulk {
	nmcb.conflict = opts
	return &NotificationMessageUpsertBulk{
		create: nmcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.NotificationMessage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (nmcb *NotificationMessageCreateBulk) OnConflictColumns(columns ...string) *NotificationMessageUpsertBulk {
	nmcb.conflict = append(nmcb.conflict, sql.ConflictColumns(columns...))
	return &NotificationMessageUpsertBulk{
		create: nmcb,
	}
}

// NotificationMessageUpsertBulk is the builder for "upsert"-ing
// a bulk of NotificationMessage nodes.
type NotificationMessageUpsertBulk struct {
	create *NotificationMessageCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.NotificationMessage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(notificationmessage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *NotificationMessageUpsertBulk) UpdateNewValues() *NotificationMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(notificationmessage.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(notificationmessage.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(notificationmessage.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.NotificationMessage.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *NotificationMessageUpsertBulk) Ignore() *NotificationMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *NotificationMessageUpsertBulk) DoNothing() *NotificationMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the NotificationMessageCreateBulk.OnConflict
// documentation for more info.
func (u *NotificationMessageUpsertBulk) Update(set func(*NotificationMessageUpsert)) *NotificationMessageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&NotificationMessageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *NotificationMessageUpsertBulk) SetUpdateTime(v time.Time) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateUpdateTime() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *NotificationMessageUpsertBulk) ClearUpdateTime() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *NotificationMessageUpsertBulk) SetDeleteTime(v time.Time) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateDeleteTime() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *NotificationMessageUpsertBulk) ClearDeleteTime() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *NotificationMessageUpsertBulk) SetCreateBy(v uint32) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *NotificationMessageUpsertBulk) AddCreateBy(v uint32) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateCreateBy() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *NotificationMessageUpsertBulk) ClearCreateBy() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *NotificationMessageUpsertBulk) SetUpdateBy(v uint32) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *NotificationMessageUpsertBulk) AddUpdateBy(v uint32) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateUpdateBy() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *NotificationMessageUpsertBulk) ClearUpdateBy() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearUpdateBy()
	})
}

// SetSubject sets the "subject" field.
func (u *NotificationMessageUpsertBulk) SetSubject(v string) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetSubject(v)
	})
}

// UpdateSubject sets the "subject" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateSubject() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateSubject()
	})
}

// ClearSubject clears the value of the "subject" field.
func (u *NotificationMessageUpsertBulk) ClearSubject() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearSubject()
	})
}

// SetContent sets the "content" field.
func (u *NotificationMessageUpsertBulk) SetContent(v string) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetContent(v)
	})
}

// UpdateContent sets the "content" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateContent() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateContent()
	})
}

// ClearContent clears the value of the "content" field.
func (u *NotificationMessageUpsertBulk) ClearContent() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearContent()
	})
}

// SetCategoryID sets the "category_id" field.
func (u *NotificationMessageUpsertBulk) SetCategoryID(v uint32) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetCategoryID(v)
	})
}

// AddCategoryID adds v to the "category_id" field.
func (u *NotificationMessageUpsertBulk) AddCategoryID(v uint32) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.AddCategoryID(v)
	})
}

// UpdateCategoryID sets the "category_id" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateCategoryID() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateCategoryID()
	})
}

// ClearCategoryID clears the value of the "category_id" field.
func (u *NotificationMessageUpsertBulk) ClearCategoryID() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearCategoryID()
	})
}

// SetStatus sets the "status" field.
func (u *NotificationMessageUpsertBulk) SetStatus(v notificationmessage.Status) *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *NotificationMessageUpsertBulk) UpdateStatus() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *NotificationMessageUpsertBulk) ClearStatus() *NotificationMessageUpsertBulk {
	return u.Update(func(s *NotificationMessageUpsert) {
		s.ClearStatus()
	})
}

// Exec executes the query.
func (u *NotificationMessageUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the NotificationMessageCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for NotificationMessageCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *NotificationMessageUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
