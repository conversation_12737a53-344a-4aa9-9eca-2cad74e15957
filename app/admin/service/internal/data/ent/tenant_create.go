// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TenantCreate is the builder for creating a Tenant entity.
type TenantCreate struct {
	config
	mutation *TenantMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (tc *TenantCreate) SetCreateTime(t time.Time) *TenantCreate {
	tc.mutation.SetCreateTime(t)
	return tc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (tc *TenantCreate) SetNillableCreateTime(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetCreateTime(*t)
	}
	return tc
}

// SetUpdateTime sets the "update_time" field.
func (tc *TenantCreate) SetUpdateTime(t time.Time) *TenantCreate {
	tc.mutation.SetUpdateTime(t)
	return tc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (tc *TenantCreate) SetNillableUpdateTime(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetUpdateTime(*t)
	}
	return tc
}

// SetDeleteTime sets the "delete_time" field.
func (tc *TenantCreate) SetDeleteTime(t time.Time) *TenantCreate {
	tc.mutation.SetDeleteTime(t)
	return tc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (tc *TenantCreate) SetNillableDeleteTime(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetDeleteTime(*t)
	}
	return tc
}

// SetStatus sets the "status" field.
func (tc *TenantCreate) SetStatus(t tenant.Status) *TenantCreate {
	tc.mutation.SetStatus(t)
	return tc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tc *TenantCreate) SetNillableStatus(t *tenant.Status) *TenantCreate {
	if t != nil {
		tc.SetStatus(*t)
	}
	return tc
}

// SetCreateBy sets the "create_by" field.
func (tc *TenantCreate) SetCreateBy(u uint32) *TenantCreate {
	tc.mutation.SetCreateBy(u)
	return tc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (tc *TenantCreate) SetNillableCreateBy(u *uint32) *TenantCreate {
	if u != nil {
		tc.SetCreateBy(*u)
	}
	return tc
}

// SetUpdateBy sets the "update_by" field.
func (tc *TenantCreate) SetUpdateBy(u uint32) *TenantCreate {
	tc.mutation.SetUpdateBy(u)
	return tc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (tc *TenantCreate) SetNillableUpdateBy(u *uint32) *TenantCreate {
	if u != nil {
		tc.SetUpdateBy(*u)
	}
	return tc
}

// SetRemark sets the "remark" field.
func (tc *TenantCreate) SetRemark(s string) *TenantCreate {
	tc.mutation.SetRemark(s)
	return tc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (tc *TenantCreate) SetNillableRemark(s *string) *TenantCreate {
	if s != nil {
		tc.SetRemark(*s)
	}
	return tc
}

// SetName sets the "name" field.
func (tc *TenantCreate) SetName(s string) *TenantCreate {
	tc.mutation.SetName(s)
	return tc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tc *TenantCreate) SetNillableName(s *string) *TenantCreate {
	if s != nil {
		tc.SetName(*s)
	}
	return tc
}

// SetCode sets the "code" field.
func (tc *TenantCreate) SetCode(s string) *TenantCreate {
	tc.mutation.SetCode(s)
	return tc
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tc *TenantCreate) SetNillableCode(s *string) *TenantCreate {
	if s != nil {
		tc.SetCode(*s)
	}
	return tc
}

// SetMemberCount sets the "member_count" field.
func (tc *TenantCreate) SetMemberCount(i int32) *TenantCreate {
	tc.mutation.SetMemberCount(i)
	return tc
}

// SetNillableMemberCount sets the "member_count" field if the given value is not nil.
func (tc *TenantCreate) SetNillableMemberCount(i *int32) *TenantCreate {
	if i != nil {
		tc.SetMemberCount(*i)
	}
	return tc
}

// SetSubscriptionAt sets the "subscription_at" field.
func (tc *TenantCreate) SetSubscriptionAt(t time.Time) *TenantCreate {
	tc.mutation.SetSubscriptionAt(t)
	return tc
}

// SetNillableSubscriptionAt sets the "subscription_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableSubscriptionAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetSubscriptionAt(*t)
	}
	return tc
}

// SetUnsubscribeAt sets the "unsubscribe_at" field.
func (tc *TenantCreate) SetUnsubscribeAt(t time.Time) *TenantCreate {
	tc.mutation.SetUnsubscribeAt(t)
	return tc
}

// SetNillableUnsubscribeAt sets the "unsubscribe_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableUnsubscribeAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetUnsubscribeAt(*t)
	}
	return tc
}

// SetID sets the "id" field.
func (tc *TenantCreate) SetID(u uint32) *TenantCreate {
	tc.mutation.SetID(u)
	return tc
}

// Mutation returns the TenantMutation object of the builder.
func (tc *TenantCreate) Mutation() *TenantMutation {
	return tc.mutation
}

// Save creates the Tenant in the database.
func (tc *TenantCreate) Save(ctx context.Context) (*Tenant, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TenantCreate) SaveX(ctx context.Context) *Tenant {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TenantCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TenantCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TenantCreate) defaults() {
	if _, ok := tc.mutation.Status(); !ok {
		v := tenant.DefaultStatus
		tc.mutation.SetStatus(v)
	}
	if _, ok := tc.mutation.Remark(); !ok {
		v := tenant.DefaultRemark
		tc.mutation.SetRemark(v)
	}
	if _, ok := tc.mutation.MemberCount(); !ok {
		v := tenant.DefaultMemberCount
		tc.mutation.SetMemberCount(v)
	}
	if _, ok := tc.mutation.SubscriptionAt(); !ok {
		v := tenant.DefaultSubscriptionAt()
		tc.mutation.SetSubscriptionAt(v)
	}
	if _, ok := tc.mutation.UnsubscribeAt(); !ok {
		v := tenant.DefaultUnsubscribeAt()
		tc.mutation.SetUnsubscribeAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TenantCreate) check() error {
	if v, ok := tc.mutation.Status(); ok {
		if err := tenant.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Tenant.status": %w`, err)}
		}
	}
	if v, ok := tc.mutation.Name(); ok {
		if err := tenant.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Tenant.name": %w`, err)}
		}
	}
	if v, ok := tc.mutation.Code(); ok {
		if err := tenant.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Tenant.code": %w`, err)}
		}
	}
	if v, ok := tc.mutation.ID(); ok {
		if err := tenant.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Tenant.id": %w`, err)}
		}
	}
	return nil
}

func (tc *TenantCreate) sqlSave(ctx context.Context) (*Tenant, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TenantCreate) createSpec() (*Tenant, *sqlgraph.CreateSpec) {
	var (
		_node = &Tenant{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(tenant.Table, sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = tc.conflict
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.CreateTime(); ok {
		_spec.SetField(tenant.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := tc.mutation.UpdateTime(); ok {
		_spec.SetField(tenant.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := tc.mutation.DeleteTime(); ok {
		_spec.SetField(tenant.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := tc.mutation.Status(); ok {
		_spec.SetField(tenant.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := tc.mutation.CreateBy(); ok {
		_spec.SetField(tenant.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := tc.mutation.UpdateBy(); ok {
		_spec.SetField(tenant.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := tc.mutation.Remark(); ok {
		_spec.SetField(tenant.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := tc.mutation.Name(); ok {
		_spec.SetField(tenant.FieldName, field.TypeString, value)
		_node.Name = &value
	}
	if value, ok := tc.mutation.Code(); ok {
		_spec.SetField(tenant.FieldCode, field.TypeString, value)
		_node.Code = &value
	}
	if value, ok := tc.mutation.MemberCount(); ok {
		_spec.SetField(tenant.FieldMemberCount, field.TypeInt32, value)
		_node.MemberCount = &value
	}
	if value, ok := tc.mutation.SubscriptionAt(); ok {
		_spec.SetField(tenant.FieldSubscriptionAt, field.TypeTime, value)
		_node.SubscriptionAt = &value
	}
	if value, ok := tc.mutation.UnsubscribeAt(); ok {
		_spec.SetField(tenant.FieldUnsubscribeAt, field.TypeTime, value)
		_node.UnsubscribeAt = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tenant.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TenantUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (tc *TenantCreate) OnConflict(opts ...sql.ConflictOption) *TenantUpsertOne {
	tc.conflict = opts
	return &TenantUpsertOne{
		create: tc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tc *TenantCreate) OnConflictColumns(columns ...string) *TenantUpsertOne {
	tc.conflict = append(tc.conflict, sql.ConflictColumns(columns...))
	return &TenantUpsertOne{
		create: tc,
	}
}

type (
	// TenantUpsertOne is the builder for "upsert"-ing
	//  one Tenant node.
	TenantUpsertOne struct {
		create *TenantCreate
	}

	// TenantUpsert is the "OnConflict" setter.
	TenantUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *TenantUpsert) SetUpdateTime(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *TenantUpsert) UpdateUpdateTime() *TenantUpsert {
	u.SetExcluded(tenant.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *TenantUpsert) ClearUpdateTime() *TenantUpsert {
	u.SetNull(tenant.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *TenantUpsert) SetDeleteTime(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *TenantUpsert) UpdateDeleteTime() *TenantUpsert {
	u.SetExcluded(tenant.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *TenantUpsert) ClearDeleteTime() *TenantUpsert {
	u.SetNull(tenant.FieldDeleteTime)
	return u
}

// SetStatus sets the "status" field.
func (u *TenantUpsert) SetStatus(v tenant.Status) *TenantUpsert {
	u.Set(tenant.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TenantUpsert) UpdateStatus() *TenantUpsert {
	u.SetExcluded(tenant.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *TenantUpsert) ClearStatus() *TenantUpsert {
	u.SetNull(tenant.FieldStatus)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *TenantUpsert) SetCreateBy(v uint32) *TenantUpsert {
	u.Set(tenant.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *TenantUpsert) UpdateCreateBy() *TenantUpsert {
	u.SetExcluded(tenant.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *TenantUpsert) AddCreateBy(v uint32) *TenantUpsert {
	u.Add(tenant.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *TenantUpsert) ClearCreateBy() *TenantUpsert {
	u.SetNull(tenant.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *TenantUpsert) SetUpdateBy(v uint32) *TenantUpsert {
	u.Set(tenant.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *TenantUpsert) UpdateUpdateBy() *TenantUpsert {
	u.SetExcluded(tenant.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *TenantUpsert) AddUpdateBy(v uint32) *TenantUpsert {
	u.Add(tenant.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *TenantUpsert) ClearUpdateBy() *TenantUpsert {
	u.SetNull(tenant.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *TenantUpsert) SetRemark(v string) *TenantUpsert {
	u.Set(tenant.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *TenantUpsert) UpdateRemark() *TenantUpsert {
	u.SetExcluded(tenant.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *TenantUpsert) ClearRemark() *TenantUpsert {
	u.SetNull(tenant.FieldRemark)
	return u
}

// SetName sets the "name" field.
func (u *TenantUpsert) SetName(v string) *TenantUpsert {
	u.Set(tenant.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TenantUpsert) UpdateName() *TenantUpsert {
	u.SetExcluded(tenant.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *TenantUpsert) ClearName() *TenantUpsert {
	u.SetNull(tenant.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *TenantUpsert) SetCode(v string) *TenantUpsert {
	u.Set(tenant.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *TenantUpsert) UpdateCode() *TenantUpsert {
	u.SetExcluded(tenant.FieldCode)
	return u
}

// ClearCode clears the value of the "code" field.
func (u *TenantUpsert) ClearCode() *TenantUpsert {
	u.SetNull(tenant.FieldCode)
	return u
}

// SetMemberCount sets the "member_count" field.
func (u *TenantUpsert) SetMemberCount(v int32) *TenantUpsert {
	u.Set(tenant.FieldMemberCount, v)
	return u
}

// UpdateMemberCount sets the "member_count" field to the value that was provided on create.
func (u *TenantUpsert) UpdateMemberCount() *TenantUpsert {
	u.SetExcluded(tenant.FieldMemberCount)
	return u
}

// AddMemberCount adds v to the "member_count" field.
func (u *TenantUpsert) AddMemberCount(v int32) *TenantUpsert {
	u.Add(tenant.FieldMemberCount, v)
	return u
}

// ClearMemberCount clears the value of the "member_count" field.
func (u *TenantUpsert) ClearMemberCount() *TenantUpsert {
	u.SetNull(tenant.FieldMemberCount)
	return u
}

// SetSubscriptionAt sets the "subscription_at" field.
func (u *TenantUpsert) SetSubscriptionAt(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldSubscriptionAt, v)
	return u
}

// UpdateSubscriptionAt sets the "subscription_at" field to the value that was provided on create.
func (u *TenantUpsert) UpdateSubscriptionAt() *TenantUpsert {
	u.SetExcluded(tenant.FieldSubscriptionAt)
	return u
}

// ClearSubscriptionAt clears the value of the "subscription_at" field.
func (u *TenantUpsert) ClearSubscriptionAt() *TenantUpsert {
	u.SetNull(tenant.FieldSubscriptionAt)
	return u
}

// SetUnsubscribeAt sets the "unsubscribe_at" field.
func (u *TenantUpsert) SetUnsubscribeAt(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldUnsubscribeAt, v)
	return u
}

// UpdateUnsubscribeAt sets the "unsubscribe_at" field to the value that was provided on create.
func (u *TenantUpsert) UpdateUnsubscribeAt() *TenantUpsert {
	u.SetExcluded(tenant.FieldUnsubscribeAt)
	return u
}

// ClearUnsubscribeAt clears the value of the "unsubscribe_at" field.
func (u *TenantUpsert) ClearUnsubscribeAt() *TenantUpsert {
	u.SetNull(tenant.FieldUnsubscribeAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tenant.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TenantUpsertOne) UpdateNewValues() *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(tenant.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(tenant.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tenant.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TenantUpsertOne) Ignore() *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TenantUpsertOne) DoNothing() *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TenantCreate.OnConflict
// documentation for more info.
func (u *TenantUpsertOne) Update(set func(*TenantUpsert)) *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TenantUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *TenantUpsertOne) SetUpdateTime(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateUpdateTime() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *TenantUpsertOne) ClearUpdateTime() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *TenantUpsertOne) SetDeleteTime(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateDeleteTime() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *TenantUpsertOne) ClearDeleteTime() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *TenantUpsertOne) SetStatus(v tenant.Status) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateStatus() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *TenantUpsertOne) ClearStatus() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *TenantUpsertOne) SetCreateBy(v uint32) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *TenantUpsertOne) AddCreateBy(v uint32) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateCreateBy() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *TenantUpsertOne) ClearCreateBy() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *TenantUpsertOne) SetUpdateBy(v uint32) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *TenantUpsertOne) AddUpdateBy(v uint32) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateUpdateBy() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *TenantUpsertOne) ClearUpdateBy() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *TenantUpsertOne) SetRemark(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateRemark() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *TenantUpsertOne) ClearRemark() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *TenantUpsertOne) SetName(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateName() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *TenantUpsertOne) ClearName() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *TenantUpsertOne) SetCode(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateCode() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateCode()
	})
}

// ClearCode clears the value of the "code" field.
func (u *TenantUpsertOne) ClearCode() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearCode()
	})
}

// SetMemberCount sets the "member_count" field.
func (u *TenantUpsertOne) SetMemberCount(v int32) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetMemberCount(v)
	})
}

// AddMemberCount adds v to the "member_count" field.
func (u *TenantUpsertOne) AddMemberCount(v int32) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddMemberCount(v)
	})
}

// UpdateMemberCount sets the "member_count" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateMemberCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMemberCount()
	})
}

// ClearMemberCount clears the value of the "member_count" field.
func (u *TenantUpsertOne) ClearMemberCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMemberCount()
	})
}

// SetSubscriptionAt sets the "subscription_at" field.
func (u *TenantUpsertOne) SetSubscriptionAt(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetSubscriptionAt(v)
	})
}

// UpdateSubscriptionAt sets the "subscription_at" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateSubscriptionAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSubscriptionAt()
	})
}

// ClearSubscriptionAt clears the value of the "subscription_at" field.
func (u *TenantUpsertOne) ClearSubscriptionAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSubscriptionAt()
	})
}

// SetUnsubscribeAt sets the "unsubscribe_at" field.
func (u *TenantUpsertOne) SetUnsubscribeAt(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetUnsubscribeAt(v)
	})
}

// UpdateUnsubscribeAt sets the "unsubscribe_at" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateUnsubscribeAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUnsubscribeAt()
	})
}

// ClearUnsubscribeAt clears the value of the "unsubscribe_at" field.
func (u *TenantUpsertOne) ClearUnsubscribeAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearUnsubscribeAt()
	})
}

// Exec executes the query.
func (u *TenantUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TenantCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TenantUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TenantUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TenantUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TenantCreateBulk is the builder for creating many Tenant entities in bulk.
type TenantCreateBulk struct {
	config
	err      error
	builders []*TenantCreate
	conflict []sql.ConflictOption
}

// Save creates the Tenant entities in the database.
func (tcb *TenantCreateBulk) Save(ctx context.Context) ([]*Tenant, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Tenant, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TenantMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TenantCreateBulk) SaveX(ctx context.Context) []*Tenant {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TenantCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TenantCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tenant.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TenantUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (tcb *TenantCreateBulk) OnConflict(opts ...sql.ConflictOption) *TenantUpsertBulk {
	tcb.conflict = opts
	return &TenantUpsertBulk{
		create: tcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tcb *TenantCreateBulk) OnConflictColumns(columns ...string) *TenantUpsertBulk {
	tcb.conflict = append(tcb.conflict, sql.ConflictColumns(columns...))
	return &TenantUpsertBulk{
		create: tcb,
	}
}

// TenantUpsertBulk is the builder for "upsert"-ing
// a bulk of Tenant nodes.
type TenantUpsertBulk struct {
	create *TenantCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tenant.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TenantUpsertBulk) UpdateNewValues() *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(tenant.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(tenant.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TenantUpsertBulk) Ignore() *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TenantUpsertBulk) DoNothing() *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TenantCreateBulk.OnConflict
// documentation for more info.
func (u *TenantUpsertBulk) Update(set func(*TenantUpsert)) *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TenantUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *TenantUpsertBulk) SetUpdateTime(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateUpdateTime() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *TenantUpsertBulk) ClearUpdateTime() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *TenantUpsertBulk) SetDeleteTime(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateDeleteTime() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *TenantUpsertBulk) ClearDeleteTime() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *TenantUpsertBulk) SetStatus(v tenant.Status) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateStatus() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *TenantUpsertBulk) ClearStatus() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *TenantUpsertBulk) SetCreateBy(v uint32) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *TenantUpsertBulk) AddCreateBy(v uint32) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateCreateBy() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *TenantUpsertBulk) ClearCreateBy() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *TenantUpsertBulk) SetUpdateBy(v uint32) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *TenantUpsertBulk) AddUpdateBy(v uint32) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateUpdateBy() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *TenantUpsertBulk) ClearUpdateBy() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *TenantUpsertBulk) SetRemark(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateRemark() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *TenantUpsertBulk) ClearRemark() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *TenantUpsertBulk) SetName(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateName() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *TenantUpsertBulk) ClearName() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *TenantUpsertBulk) SetCode(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateCode() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateCode()
	})
}

// ClearCode clears the value of the "code" field.
func (u *TenantUpsertBulk) ClearCode() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearCode()
	})
}

// SetMemberCount sets the "member_count" field.
func (u *TenantUpsertBulk) SetMemberCount(v int32) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetMemberCount(v)
	})
}

// AddMemberCount adds v to the "member_count" field.
func (u *TenantUpsertBulk) AddMemberCount(v int32) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddMemberCount(v)
	})
}

// UpdateMemberCount sets the "member_count" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateMemberCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMemberCount()
	})
}

// ClearMemberCount clears the value of the "member_count" field.
func (u *TenantUpsertBulk) ClearMemberCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMemberCount()
	})
}

// SetSubscriptionAt sets the "subscription_at" field.
func (u *TenantUpsertBulk) SetSubscriptionAt(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetSubscriptionAt(v)
	})
}

// UpdateSubscriptionAt sets the "subscription_at" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateSubscriptionAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSubscriptionAt()
	})
}

// ClearSubscriptionAt clears the value of the "subscription_at" field.
func (u *TenantUpsertBulk) ClearSubscriptionAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSubscriptionAt()
	})
}

// SetUnsubscribeAt sets the "unsubscribe_at" field.
func (u *TenantUpsertBulk) SetUnsubscribeAt(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetUnsubscribeAt(v)
	})
}

// UpdateUnsubscribeAt sets the "unsubscribe_at" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateUnsubscribeAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUnsubscribeAt()
	})
}

// ClearUnsubscribeAt clears the value of the "unsubscribe_at" field.
func (u *TenantUpsertBulk) ClearUnsubscribeAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearUnsubscribeAt()
	})
}

// Exec executes the query.
func (u *TenantUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TenantCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TenantCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TenantUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
