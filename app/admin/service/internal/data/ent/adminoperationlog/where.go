// Code generated by ent, DO NOT EDIT.

package adminoperationlog

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldCreateTime, v))
}

// RequestID applies equality check predicate on the "request_id" field. It's identical to RequestIDEQ.
func RequestID(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestID, v))
}

// Method applies equality check predicate on the "method" field. It's identical to MethodEQ.
func Method(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldMethod, v))
}

// Operation applies equality check predicate on the "operation" field. It's identical to OperationEQ.
func Operation(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldOperation, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldPath, v))
}

// Referer applies equality check predicate on the "referer" field. It's identical to RefererEQ.
func Referer(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldReferer, v))
}

// RequestURI applies equality check predicate on the "request_uri" field. It's identical to RequestURIEQ.
func RequestURI(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestURI, v))
}

// RequestBody applies equality check predicate on the "request_body" field. It's identical to RequestBodyEQ.
func RequestBody(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestBody, v))
}

// RequestHeader applies equality check predicate on the "request_header" field. It's identical to RequestHeaderEQ.
func RequestHeader(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestHeader, v))
}

// Response applies equality check predicate on the "response" field. It's identical to ResponseEQ.
func Response(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldResponse, v))
}

// CostTime applies equality check predicate on the "cost_time" field. It's identical to CostTimeEQ.
func CostTime(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldCostTime, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldUserID, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldUsername, v))
}

// ClientIP applies equality check predicate on the "client_ip" field. It's identical to ClientIPEQ.
func ClientIP(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldClientIP, v))
}

// StatusCode applies equality check predicate on the "status_code" field. It's identical to StatusCodeEQ.
func StatusCode(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldStatusCode, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldReason, v))
}

// Success applies equality check predicate on the "success" field. It's identical to SuccessEQ.
func Success(v bool) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldSuccess, v))
}

// Location applies equality check predicate on the "location" field. It's identical to LocationEQ.
func Location(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldLocation, v))
}

// UserAgent applies equality check predicate on the "user_agent" field. It's identical to UserAgentEQ.
func UserAgent(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldUserAgent, v))
}

// BrowserName applies equality check predicate on the "browser_name" field. It's identical to BrowserNameEQ.
func BrowserName(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldBrowserName, v))
}

// BrowserVersion applies equality check predicate on the "browser_version" field. It's identical to BrowserVersionEQ.
func BrowserVersion(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldBrowserVersion, v))
}

// ClientID applies equality check predicate on the "client_id" field. It's identical to ClientIDEQ.
func ClientID(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldClientID, v))
}

// ClientName applies equality check predicate on the "client_name" field. It's identical to ClientNameEQ.
func ClientName(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldClientName, v))
}

// OsName applies equality check predicate on the "os_name" field. It's identical to OsNameEQ.
func OsName(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldOsName, v))
}

// OsVersion applies equality check predicate on the "os_version" field. It's identical to OsVersionEQ.
func OsVersion(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldOsVersion, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldCreateTime))
}

// RequestIDEQ applies the EQ predicate on the "request_id" field.
func RequestIDEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestID, v))
}

// RequestIDNEQ applies the NEQ predicate on the "request_id" field.
func RequestIDNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldRequestID, v))
}

// RequestIDIn applies the In predicate on the "request_id" field.
func RequestIDIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldRequestID, vs...))
}

// RequestIDNotIn applies the NotIn predicate on the "request_id" field.
func RequestIDNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldRequestID, vs...))
}

// RequestIDGT applies the GT predicate on the "request_id" field.
func RequestIDGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldRequestID, v))
}

// RequestIDGTE applies the GTE predicate on the "request_id" field.
func RequestIDGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldRequestID, v))
}

// RequestIDLT applies the LT predicate on the "request_id" field.
func RequestIDLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldRequestID, v))
}

// RequestIDLTE applies the LTE predicate on the "request_id" field.
func RequestIDLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldRequestID, v))
}

// RequestIDContains applies the Contains predicate on the "request_id" field.
func RequestIDContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldRequestID, v))
}

// RequestIDHasPrefix applies the HasPrefix predicate on the "request_id" field.
func RequestIDHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldRequestID, v))
}

// RequestIDHasSuffix applies the HasSuffix predicate on the "request_id" field.
func RequestIDHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldRequestID, v))
}

// RequestIDIsNil applies the IsNil predicate on the "request_id" field.
func RequestIDIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldRequestID))
}

// RequestIDNotNil applies the NotNil predicate on the "request_id" field.
func RequestIDNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldRequestID))
}

// RequestIDEqualFold applies the EqualFold predicate on the "request_id" field.
func RequestIDEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldRequestID, v))
}

// RequestIDContainsFold applies the ContainsFold predicate on the "request_id" field.
func RequestIDContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldRequestID, v))
}

// MethodEQ applies the EQ predicate on the "method" field.
func MethodEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldMethod, v))
}

// MethodNEQ applies the NEQ predicate on the "method" field.
func MethodNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldMethod, v))
}

// MethodIn applies the In predicate on the "method" field.
func MethodIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldMethod, vs...))
}

// MethodNotIn applies the NotIn predicate on the "method" field.
func MethodNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldMethod, vs...))
}

// MethodGT applies the GT predicate on the "method" field.
func MethodGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldMethod, v))
}

// MethodGTE applies the GTE predicate on the "method" field.
func MethodGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldMethod, v))
}

// MethodLT applies the LT predicate on the "method" field.
func MethodLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldMethod, v))
}

// MethodLTE applies the LTE predicate on the "method" field.
func MethodLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldMethod, v))
}

// MethodContains applies the Contains predicate on the "method" field.
func MethodContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldMethod, v))
}

// MethodHasPrefix applies the HasPrefix predicate on the "method" field.
func MethodHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldMethod, v))
}

// MethodHasSuffix applies the HasSuffix predicate on the "method" field.
func MethodHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldMethod, v))
}

// MethodIsNil applies the IsNil predicate on the "method" field.
func MethodIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldMethod))
}

// MethodNotNil applies the NotNil predicate on the "method" field.
func MethodNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldMethod))
}

// MethodEqualFold applies the EqualFold predicate on the "method" field.
func MethodEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldMethod, v))
}

// MethodContainsFold applies the ContainsFold predicate on the "method" field.
func MethodContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldMethod, v))
}

// OperationEQ applies the EQ predicate on the "operation" field.
func OperationEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldOperation, v))
}

// OperationNEQ applies the NEQ predicate on the "operation" field.
func OperationNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldOperation, v))
}

// OperationIn applies the In predicate on the "operation" field.
func OperationIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldOperation, vs...))
}

// OperationNotIn applies the NotIn predicate on the "operation" field.
func OperationNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldOperation, vs...))
}

// OperationGT applies the GT predicate on the "operation" field.
func OperationGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldOperation, v))
}

// OperationGTE applies the GTE predicate on the "operation" field.
func OperationGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldOperation, v))
}

// OperationLT applies the LT predicate on the "operation" field.
func OperationLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldOperation, v))
}

// OperationLTE applies the LTE predicate on the "operation" field.
func OperationLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldOperation, v))
}

// OperationContains applies the Contains predicate on the "operation" field.
func OperationContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldOperation, v))
}

// OperationHasPrefix applies the HasPrefix predicate on the "operation" field.
func OperationHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldOperation, v))
}

// OperationHasSuffix applies the HasSuffix predicate on the "operation" field.
func OperationHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldOperation, v))
}

// OperationIsNil applies the IsNil predicate on the "operation" field.
func OperationIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldOperation))
}

// OperationNotNil applies the NotNil predicate on the "operation" field.
func OperationNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldOperation))
}

// OperationEqualFold applies the EqualFold predicate on the "operation" field.
func OperationEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldOperation, v))
}

// OperationContainsFold applies the ContainsFold predicate on the "operation" field.
func OperationContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldOperation, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldPath, v))
}

// PathIsNil applies the IsNil predicate on the "path" field.
func PathIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldPath))
}

// PathNotNil applies the NotNil predicate on the "path" field.
func PathNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldPath))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldPath, v))
}

// RefererEQ applies the EQ predicate on the "referer" field.
func RefererEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldReferer, v))
}

// RefererNEQ applies the NEQ predicate on the "referer" field.
func RefererNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldReferer, v))
}

// RefererIn applies the In predicate on the "referer" field.
func RefererIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldReferer, vs...))
}

// RefererNotIn applies the NotIn predicate on the "referer" field.
func RefererNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldReferer, vs...))
}

// RefererGT applies the GT predicate on the "referer" field.
func RefererGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldReferer, v))
}

// RefererGTE applies the GTE predicate on the "referer" field.
func RefererGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldReferer, v))
}

// RefererLT applies the LT predicate on the "referer" field.
func RefererLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldReferer, v))
}

// RefererLTE applies the LTE predicate on the "referer" field.
func RefererLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldReferer, v))
}

// RefererContains applies the Contains predicate on the "referer" field.
func RefererContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldReferer, v))
}

// RefererHasPrefix applies the HasPrefix predicate on the "referer" field.
func RefererHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldReferer, v))
}

// RefererHasSuffix applies the HasSuffix predicate on the "referer" field.
func RefererHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldReferer, v))
}

// RefererIsNil applies the IsNil predicate on the "referer" field.
func RefererIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldReferer))
}

// RefererNotNil applies the NotNil predicate on the "referer" field.
func RefererNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldReferer))
}

// RefererEqualFold applies the EqualFold predicate on the "referer" field.
func RefererEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldReferer, v))
}

// RefererContainsFold applies the ContainsFold predicate on the "referer" field.
func RefererContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldReferer, v))
}

// RequestURIEQ applies the EQ predicate on the "request_uri" field.
func RequestURIEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestURI, v))
}

// RequestURINEQ applies the NEQ predicate on the "request_uri" field.
func RequestURINEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldRequestURI, v))
}

// RequestURIIn applies the In predicate on the "request_uri" field.
func RequestURIIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldRequestURI, vs...))
}

// RequestURINotIn applies the NotIn predicate on the "request_uri" field.
func RequestURINotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldRequestURI, vs...))
}

// RequestURIGT applies the GT predicate on the "request_uri" field.
func RequestURIGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldRequestURI, v))
}

// RequestURIGTE applies the GTE predicate on the "request_uri" field.
func RequestURIGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldRequestURI, v))
}

// RequestURILT applies the LT predicate on the "request_uri" field.
func RequestURILT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldRequestURI, v))
}

// RequestURILTE applies the LTE predicate on the "request_uri" field.
func RequestURILTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldRequestURI, v))
}

// RequestURIContains applies the Contains predicate on the "request_uri" field.
func RequestURIContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldRequestURI, v))
}

// RequestURIHasPrefix applies the HasPrefix predicate on the "request_uri" field.
func RequestURIHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldRequestURI, v))
}

// RequestURIHasSuffix applies the HasSuffix predicate on the "request_uri" field.
func RequestURIHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldRequestURI, v))
}

// RequestURIIsNil applies the IsNil predicate on the "request_uri" field.
func RequestURIIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldRequestURI))
}

// RequestURINotNil applies the NotNil predicate on the "request_uri" field.
func RequestURINotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldRequestURI))
}

// RequestURIEqualFold applies the EqualFold predicate on the "request_uri" field.
func RequestURIEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldRequestURI, v))
}

// RequestURIContainsFold applies the ContainsFold predicate on the "request_uri" field.
func RequestURIContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldRequestURI, v))
}

// RequestBodyEQ applies the EQ predicate on the "request_body" field.
func RequestBodyEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestBody, v))
}

// RequestBodyNEQ applies the NEQ predicate on the "request_body" field.
func RequestBodyNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldRequestBody, v))
}

// RequestBodyIn applies the In predicate on the "request_body" field.
func RequestBodyIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldRequestBody, vs...))
}

// RequestBodyNotIn applies the NotIn predicate on the "request_body" field.
func RequestBodyNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldRequestBody, vs...))
}

// RequestBodyGT applies the GT predicate on the "request_body" field.
func RequestBodyGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldRequestBody, v))
}

// RequestBodyGTE applies the GTE predicate on the "request_body" field.
func RequestBodyGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldRequestBody, v))
}

// RequestBodyLT applies the LT predicate on the "request_body" field.
func RequestBodyLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldRequestBody, v))
}

// RequestBodyLTE applies the LTE predicate on the "request_body" field.
func RequestBodyLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldRequestBody, v))
}

// RequestBodyContains applies the Contains predicate on the "request_body" field.
func RequestBodyContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldRequestBody, v))
}

// RequestBodyHasPrefix applies the HasPrefix predicate on the "request_body" field.
func RequestBodyHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldRequestBody, v))
}

// RequestBodyHasSuffix applies the HasSuffix predicate on the "request_body" field.
func RequestBodyHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldRequestBody, v))
}

// RequestBodyIsNil applies the IsNil predicate on the "request_body" field.
func RequestBodyIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldRequestBody))
}

// RequestBodyNotNil applies the NotNil predicate on the "request_body" field.
func RequestBodyNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldRequestBody))
}

// RequestBodyEqualFold applies the EqualFold predicate on the "request_body" field.
func RequestBodyEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldRequestBody, v))
}

// RequestBodyContainsFold applies the ContainsFold predicate on the "request_body" field.
func RequestBodyContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldRequestBody, v))
}

// RequestHeaderEQ applies the EQ predicate on the "request_header" field.
func RequestHeaderEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldRequestHeader, v))
}

// RequestHeaderNEQ applies the NEQ predicate on the "request_header" field.
func RequestHeaderNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldRequestHeader, v))
}

// RequestHeaderIn applies the In predicate on the "request_header" field.
func RequestHeaderIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldRequestHeader, vs...))
}

// RequestHeaderNotIn applies the NotIn predicate on the "request_header" field.
func RequestHeaderNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldRequestHeader, vs...))
}

// RequestHeaderGT applies the GT predicate on the "request_header" field.
func RequestHeaderGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldRequestHeader, v))
}

// RequestHeaderGTE applies the GTE predicate on the "request_header" field.
func RequestHeaderGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldRequestHeader, v))
}

// RequestHeaderLT applies the LT predicate on the "request_header" field.
func RequestHeaderLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldRequestHeader, v))
}

// RequestHeaderLTE applies the LTE predicate on the "request_header" field.
func RequestHeaderLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldRequestHeader, v))
}

// RequestHeaderContains applies the Contains predicate on the "request_header" field.
func RequestHeaderContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldRequestHeader, v))
}

// RequestHeaderHasPrefix applies the HasPrefix predicate on the "request_header" field.
func RequestHeaderHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldRequestHeader, v))
}

// RequestHeaderHasSuffix applies the HasSuffix predicate on the "request_header" field.
func RequestHeaderHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldRequestHeader, v))
}

// RequestHeaderIsNil applies the IsNil predicate on the "request_header" field.
func RequestHeaderIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldRequestHeader))
}

// RequestHeaderNotNil applies the NotNil predicate on the "request_header" field.
func RequestHeaderNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldRequestHeader))
}

// RequestHeaderEqualFold applies the EqualFold predicate on the "request_header" field.
func RequestHeaderEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldRequestHeader, v))
}

// RequestHeaderContainsFold applies the ContainsFold predicate on the "request_header" field.
func RequestHeaderContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldRequestHeader, v))
}

// ResponseEQ applies the EQ predicate on the "response" field.
func ResponseEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldResponse, v))
}

// ResponseNEQ applies the NEQ predicate on the "response" field.
func ResponseNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldResponse, v))
}

// ResponseIn applies the In predicate on the "response" field.
func ResponseIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldResponse, vs...))
}

// ResponseNotIn applies the NotIn predicate on the "response" field.
func ResponseNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldResponse, vs...))
}

// ResponseGT applies the GT predicate on the "response" field.
func ResponseGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldResponse, v))
}

// ResponseGTE applies the GTE predicate on the "response" field.
func ResponseGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldResponse, v))
}

// ResponseLT applies the LT predicate on the "response" field.
func ResponseLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldResponse, v))
}

// ResponseLTE applies the LTE predicate on the "response" field.
func ResponseLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldResponse, v))
}

// ResponseContains applies the Contains predicate on the "response" field.
func ResponseContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldResponse, v))
}

// ResponseHasPrefix applies the HasPrefix predicate on the "response" field.
func ResponseHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldResponse, v))
}

// ResponseHasSuffix applies the HasSuffix predicate on the "response" field.
func ResponseHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldResponse, v))
}

// ResponseIsNil applies the IsNil predicate on the "response" field.
func ResponseIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldResponse))
}

// ResponseNotNil applies the NotNil predicate on the "response" field.
func ResponseNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldResponse))
}

// ResponseEqualFold applies the EqualFold predicate on the "response" field.
func ResponseEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldResponse, v))
}

// ResponseContainsFold applies the ContainsFold predicate on the "response" field.
func ResponseContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldResponse, v))
}

// CostTimeEQ applies the EQ predicate on the "cost_time" field.
func CostTimeEQ(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldCostTime, v))
}

// CostTimeNEQ applies the NEQ predicate on the "cost_time" field.
func CostTimeNEQ(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldCostTime, v))
}

// CostTimeIn applies the In predicate on the "cost_time" field.
func CostTimeIn(vs ...float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldCostTime, vs...))
}

// CostTimeNotIn applies the NotIn predicate on the "cost_time" field.
func CostTimeNotIn(vs ...float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldCostTime, vs...))
}

// CostTimeGT applies the GT predicate on the "cost_time" field.
func CostTimeGT(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldCostTime, v))
}

// CostTimeGTE applies the GTE predicate on the "cost_time" field.
func CostTimeGTE(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldCostTime, v))
}

// CostTimeLT applies the LT predicate on the "cost_time" field.
func CostTimeLT(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldCostTime, v))
}

// CostTimeLTE applies the LTE predicate on the "cost_time" field.
func CostTimeLTE(v float64) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldCostTime, v))
}

// CostTimeIsNil applies the IsNil predicate on the "cost_time" field.
func CostTimeIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldCostTime))
}

// CostTimeNotNil applies the NotNil predicate on the "cost_time" field.
func CostTimeNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldCostTime))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uint32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldUserID))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameIsNil applies the IsNil predicate on the "username" field.
func UsernameIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldUsername))
}

// UsernameNotNil applies the NotNil predicate on the "username" field.
func UsernameNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldUsername))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldUsername, v))
}

// ClientIPEQ applies the EQ predicate on the "client_ip" field.
func ClientIPEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldClientIP, v))
}

// ClientIPNEQ applies the NEQ predicate on the "client_ip" field.
func ClientIPNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldClientIP, v))
}

// ClientIPIn applies the In predicate on the "client_ip" field.
func ClientIPIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldClientIP, vs...))
}

// ClientIPNotIn applies the NotIn predicate on the "client_ip" field.
func ClientIPNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldClientIP, vs...))
}

// ClientIPGT applies the GT predicate on the "client_ip" field.
func ClientIPGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldClientIP, v))
}

// ClientIPGTE applies the GTE predicate on the "client_ip" field.
func ClientIPGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldClientIP, v))
}

// ClientIPLT applies the LT predicate on the "client_ip" field.
func ClientIPLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldClientIP, v))
}

// ClientIPLTE applies the LTE predicate on the "client_ip" field.
func ClientIPLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldClientIP, v))
}

// ClientIPContains applies the Contains predicate on the "client_ip" field.
func ClientIPContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldClientIP, v))
}

// ClientIPHasPrefix applies the HasPrefix predicate on the "client_ip" field.
func ClientIPHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldClientIP, v))
}

// ClientIPHasSuffix applies the HasSuffix predicate on the "client_ip" field.
func ClientIPHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldClientIP, v))
}

// ClientIPIsNil applies the IsNil predicate on the "client_ip" field.
func ClientIPIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldClientIP))
}

// ClientIPNotNil applies the NotNil predicate on the "client_ip" field.
func ClientIPNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldClientIP))
}

// ClientIPEqualFold applies the EqualFold predicate on the "client_ip" field.
func ClientIPEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldClientIP, v))
}

// ClientIPContainsFold applies the ContainsFold predicate on the "client_ip" field.
func ClientIPContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldClientIP, v))
}

// StatusCodeEQ applies the EQ predicate on the "status_code" field.
func StatusCodeEQ(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldStatusCode, v))
}

// StatusCodeNEQ applies the NEQ predicate on the "status_code" field.
func StatusCodeNEQ(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldStatusCode, v))
}

// StatusCodeIn applies the In predicate on the "status_code" field.
func StatusCodeIn(vs ...int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldStatusCode, vs...))
}

// StatusCodeNotIn applies the NotIn predicate on the "status_code" field.
func StatusCodeNotIn(vs ...int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldStatusCode, vs...))
}

// StatusCodeGT applies the GT predicate on the "status_code" field.
func StatusCodeGT(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldStatusCode, v))
}

// StatusCodeGTE applies the GTE predicate on the "status_code" field.
func StatusCodeGTE(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldStatusCode, v))
}

// StatusCodeLT applies the LT predicate on the "status_code" field.
func StatusCodeLT(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldStatusCode, v))
}

// StatusCodeLTE applies the LTE predicate on the "status_code" field.
func StatusCodeLTE(v int32) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldStatusCode, v))
}

// StatusCodeIsNil applies the IsNil predicate on the "status_code" field.
func StatusCodeIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldStatusCode))
}

// StatusCodeNotNil applies the NotNil predicate on the "status_code" field.
func StatusCodeNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldStatusCode))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonIsNil applies the IsNil predicate on the "reason" field.
func ReasonIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldReason))
}

// ReasonNotNil applies the NotNil predicate on the "reason" field.
func ReasonNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldReason))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldReason, v))
}

// SuccessEQ applies the EQ predicate on the "success" field.
func SuccessEQ(v bool) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldSuccess, v))
}

// SuccessNEQ applies the NEQ predicate on the "success" field.
func SuccessNEQ(v bool) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldSuccess, v))
}

// SuccessIsNil applies the IsNil predicate on the "success" field.
func SuccessIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldSuccess))
}

// SuccessNotNil applies the NotNil predicate on the "success" field.
func SuccessNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldSuccess))
}

// LocationEQ applies the EQ predicate on the "location" field.
func LocationEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldLocation, v))
}

// LocationNEQ applies the NEQ predicate on the "location" field.
func LocationNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldLocation, v))
}

// LocationIn applies the In predicate on the "location" field.
func LocationIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldLocation, vs...))
}

// LocationNotIn applies the NotIn predicate on the "location" field.
func LocationNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldLocation, vs...))
}

// LocationGT applies the GT predicate on the "location" field.
func LocationGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldLocation, v))
}

// LocationGTE applies the GTE predicate on the "location" field.
func LocationGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldLocation, v))
}

// LocationLT applies the LT predicate on the "location" field.
func LocationLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldLocation, v))
}

// LocationLTE applies the LTE predicate on the "location" field.
func LocationLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldLocation, v))
}

// LocationContains applies the Contains predicate on the "location" field.
func LocationContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldLocation, v))
}

// LocationHasPrefix applies the HasPrefix predicate on the "location" field.
func LocationHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldLocation, v))
}

// LocationHasSuffix applies the HasSuffix predicate on the "location" field.
func LocationHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldLocation, v))
}

// LocationIsNil applies the IsNil predicate on the "location" field.
func LocationIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldLocation))
}

// LocationNotNil applies the NotNil predicate on the "location" field.
func LocationNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldLocation))
}

// LocationEqualFold applies the EqualFold predicate on the "location" field.
func LocationEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldLocation, v))
}

// LocationContainsFold applies the ContainsFold predicate on the "location" field.
func LocationContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldLocation, v))
}

// UserAgentEQ applies the EQ predicate on the "user_agent" field.
func UserAgentEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldUserAgent, v))
}

// UserAgentNEQ applies the NEQ predicate on the "user_agent" field.
func UserAgentNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldUserAgent, v))
}

// UserAgentIn applies the In predicate on the "user_agent" field.
func UserAgentIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldUserAgent, vs...))
}

// UserAgentNotIn applies the NotIn predicate on the "user_agent" field.
func UserAgentNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldUserAgent, vs...))
}

// UserAgentGT applies the GT predicate on the "user_agent" field.
func UserAgentGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldUserAgent, v))
}

// UserAgentGTE applies the GTE predicate on the "user_agent" field.
func UserAgentGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldUserAgent, v))
}

// UserAgentLT applies the LT predicate on the "user_agent" field.
func UserAgentLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldUserAgent, v))
}

// UserAgentLTE applies the LTE predicate on the "user_agent" field.
func UserAgentLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldUserAgent, v))
}

// UserAgentContains applies the Contains predicate on the "user_agent" field.
func UserAgentContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldUserAgent, v))
}

// UserAgentHasPrefix applies the HasPrefix predicate on the "user_agent" field.
func UserAgentHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldUserAgent, v))
}

// UserAgentHasSuffix applies the HasSuffix predicate on the "user_agent" field.
func UserAgentHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldUserAgent, v))
}

// UserAgentIsNil applies the IsNil predicate on the "user_agent" field.
func UserAgentIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldUserAgent))
}

// UserAgentNotNil applies the NotNil predicate on the "user_agent" field.
func UserAgentNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldUserAgent))
}

// UserAgentEqualFold applies the EqualFold predicate on the "user_agent" field.
func UserAgentEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldUserAgent, v))
}

// UserAgentContainsFold applies the ContainsFold predicate on the "user_agent" field.
func UserAgentContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldUserAgent, v))
}

// BrowserNameEQ applies the EQ predicate on the "browser_name" field.
func BrowserNameEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldBrowserName, v))
}

// BrowserNameNEQ applies the NEQ predicate on the "browser_name" field.
func BrowserNameNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldBrowserName, v))
}

// BrowserNameIn applies the In predicate on the "browser_name" field.
func BrowserNameIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldBrowserName, vs...))
}

// BrowserNameNotIn applies the NotIn predicate on the "browser_name" field.
func BrowserNameNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldBrowserName, vs...))
}

// BrowserNameGT applies the GT predicate on the "browser_name" field.
func BrowserNameGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldBrowserName, v))
}

// BrowserNameGTE applies the GTE predicate on the "browser_name" field.
func BrowserNameGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldBrowserName, v))
}

// BrowserNameLT applies the LT predicate on the "browser_name" field.
func BrowserNameLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldBrowserName, v))
}

// BrowserNameLTE applies the LTE predicate on the "browser_name" field.
func BrowserNameLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldBrowserName, v))
}

// BrowserNameContains applies the Contains predicate on the "browser_name" field.
func BrowserNameContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldBrowserName, v))
}

// BrowserNameHasPrefix applies the HasPrefix predicate on the "browser_name" field.
func BrowserNameHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldBrowserName, v))
}

// BrowserNameHasSuffix applies the HasSuffix predicate on the "browser_name" field.
func BrowserNameHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldBrowserName, v))
}

// BrowserNameIsNil applies the IsNil predicate on the "browser_name" field.
func BrowserNameIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldBrowserName))
}

// BrowserNameNotNil applies the NotNil predicate on the "browser_name" field.
func BrowserNameNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldBrowserName))
}

// BrowserNameEqualFold applies the EqualFold predicate on the "browser_name" field.
func BrowserNameEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldBrowserName, v))
}

// BrowserNameContainsFold applies the ContainsFold predicate on the "browser_name" field.
func BrowserNameContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldBrowserName, v))
}

// BrowserVersionEQ applies the EQ predicate on the "browser_version" field.
func BrowserVersionEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldBrowserVersion, v))
}

// BrowserVersionNEQ applies the NEQ predicate on the "browser_version" field.
func BrowserVersionNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldBrowserVersion, v))
}

// BrowserVersionIn applies the In predicate on the "browser_version" field.
func BrowserVersionIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldBrowserVersion, vs...))
}

// BrowserVersionNotIn applies the NotIn predicate on the "browser_version" field.
func BrowserVersionNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldBrowserVersion, vs...))
}

// BrowserVersionGT applies the GT predicate on the "browser_version" field.
func BrowserVersionGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldBrowserVersion, v))
}

// BrowserVersionGTE applies the GTE predicate on the "browser_version" field.
func BrowserVersionGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldBrowserVersion, v))
}

// BrowserVersionLT applies the LT predicate on the "browser_version" field.
func BrowserVersionLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldBrowserVersion, v))
}

// BrowserVersionLTE applies the LTE predicate on the "browser_version" field.
func BrowserVersionLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldBrowserVersion, v))
}

// BrowserVersionContains applies the Contains predicate on the "browser_version" field.
func BrowserVersionContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldBrowserVersion, v))
}

// BrowserVersionHasPrefix applies the HasPrefix predicate on the "browser_version" field.
func BrowserVersionHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldBrowserVersion, v))
}

// BrowserVersionHasSuffix applies the HasSuffix predicate on the "browser_version" field.
func BrowserVersionHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldBrowserVersion, v))
}

// BrowserVersionIsNil applies the IsNil predicate on the "browser_version" field.
func BrowserVersionIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldBrowserVersion))
}

// BrowserVersionNotNil applies the NotNil predicate on the "browser_version" field.
func BrowserVersionNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldBrowserVersion))
}

// BrowserVersionEqualFold applies the EqualFold predicate on the "browser_version" field.
func BrowserVersionEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldBrowserVersion, v))
}

// BrowserVersionContainsFold applies the ContainsFold predicate on the "browser_version" field.
func BrowserVersionContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldBrowserVersion, v))
}

// ClientIDEQ applies the EQ predicate on the "client_id" field.
func ClientIDEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldClientID, v))
}

// ClientIDNEQ applies the NEQ predicate on the "client_id" field.
func ClientIDNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldClientID, v))
}

// ClientIDIn applies the In predicate on the "client_id" field.
func ClientIDIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldClientID, vs...))
}

// ClientIDNotIn applies the NotIn predicate on the "client_id" field.
func ClientIDNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldClientID, vs...))
}

// ClientIDGT applies the GT predicate on the "client_id" field.
func ClientIDGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldClientID, v))
}

// ClientIDGTE applies the GTE predicate on the "client_id" field.
func ClientIDGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldClientID, v))
}

// ClientIDLT applies the LT predicate on the "client_id" field.
func ClientIDLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldClientID, v))
}

// ClientIDLTE applies the LTE predicate on the "client_id" field.
func ClientIDLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldClientID, v))
}

// ClientIDContains applies the Contains predicate on the "client_id" field.
func ClientIDContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldClientID, v))
}

// ClientIDHasPrefix applies the HasPrefix predicate on the "client_id" field.
func ClientIDHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldClientID, v))
}

// ClientIDHasSuffix applies the HasSuffix predicate on the "client_id" field.
func ClientIDHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldClientID, v))
}

// ClientIDIsNil applies the IsNil predicate on the "client_id" field.
func ClientIDIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldClientID))
}

// ClientIDNotNil applies the NotNil predicate on the "client_id" field.
func ClientIDNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldClientID))
}

// ClientIDEqualFold applies the EqualFold predicate on the "client_id" field.
func ClientIDEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldClientID, v))
}

// ClientIDContainsFold applies the ContainsFold predicate on the "client_id" field.
func ClientIDContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldClientID, v))
}

// ClientNameEQ applies the EQ predicate on the "client_name" field.
func ClientNameEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldClientName, v))
}

// ClientNameNEQ applies the NEQ predicate on the "client_name" field.
func ClientNameNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldClientName, v))
}

// ClientNameIn applies the In predicate on the "client_name" field.
func ClientNameIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldClientName, vs...))
}

// ClientNameNotIn applies the NotIn predicate on the "client_name" field.
func ClientNameNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldClientName, vs...))
}

// ClientNameGT applies the GT predicate on the "client_name" field.
func ClientNameGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldClientName, v))
}

// ClientNameGTE applies the GTE predicate on the "client_name" field.
func ClientNameGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldClientName, v))
}

// ClientNameLT applies the LT predicate on the "client_name" field.
func ClientNameLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldClientName, v))
}

// ClientNameLTE applies the LTE predicate on the "client_name" field.
func ClientNameLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldClientName, v))
}

// ClientNameContains applies the Contains predicate on the "client_name" field.
func ClientNameContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldClientName, v))
}

// ClientNameHasPrefix applies the HasPrefix predicate on the "client_name" field.
func ClientNameHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldClientName, v))
}

// ClientNameHasSuffix applies the HasSuffix predicate on the "client_name" field.
func ClientNameHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldClientName, v))
}

// ClientNameIsNil applies the IsNil predicate on the "client_name" field.
func ClientNameIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldClientName))
}

// ClientNameNotNil applies the NotNil predicate on the "client_name" field.
func ClientNameNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldClientName))
}

// ClientNameEqualFold applies the EqualFold predicate on the "client_name" field.
func ClientNameEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldClientName, v))
}

// ClientNameContainsFold applies the ContainsFold predicate on the "client_name" field.
func ClientNameContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldClientName, v))
}

// OsNameEQ applies the EQ predicate on the "os_name" field.
func OsNameEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldOsName, v))
}

// OsNameNEQ applies the NEQ predicate on the "os_name" field.
func OsNameNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldOsName, v))
}

// OsNameIn applies the In predicate on the "os_name" field.
func OsNameIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldOsName, vs...))
}

// OsNameNotIn applies the NotIn predicate on the "os_name" field.
func OsNameNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldOsName, vs...))
}

// OsNameGT applies the GT predicate on the "os_name" field.
func OsNameGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldOsName, v))
}

// OsNameGTE applies the GTE predicate on the "os_name" field.
func OsNameGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldOsName, v))
}

// OsNameLT applies the LT predicate on the "os_name" field.
func OsNameLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldOsName, v))
}

// OsNameLTE applies the LTE predicate on the "os_name" field.
func OsNameLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldOsName, v))
}

// OsNameContains applies the Contains predicate on the "os_name" field.
func OsNameContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldOsName, v))
}

// OsNameHasPrefix applies the HasPrefix predicate on the "os_name" field.
func OsNameHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldOsName, v))
}

// OsNameHasSuffix applies the HasSuffix predicate on the "os_name" field.
func OsNameHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldOsName, v))
}

// OsNameIsNil applies the IsNil predicate on the "os_name" field.
func OsNameIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldOsName))
}

// OsNameNotNil applies the NotNil predicate on the "os_name" field.
func OsNameNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldOsName))
}

// OsNameEqualFold applies the EqualFold predicate on the "os_name" field.
func OsNameEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldOsName, v))
}

// OsNameContainsFold applies the ContainsFold predicate on the "os_name" field.
func OsNameContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldOsName, v))
}

// OsVersionEQ applies the EQ predicate on the "os_version" field.
func OsVersionEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEQ(FieldOsVersion, v))
}

// OsVersionNEQ applies the NEQ predicate on the "os_version" field.
func OsVersionNEQ(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNEQ(FieldOsVersion, v))
}

// OsVersionIn applies the In predicate on the "os_version" field.
func OsVersionIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIn(FieldOsVersion, vs...))
}

// OsVersionNotIn applies the NotIn predicate on the "os_version" field.
func OsVersionNotIn(vs ...string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotIn(FieldOsVersion, vs...))
}

// OsVersionGT applies the GT predicate on the "os_version" field.
func OsVersionGT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGT(FieldOsVersion, v))
}

// OsVersionGTE applies the GTE predicate on the "os_version" field.
func OsVersionGTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldGTE(FieldOsVersion, v))
}

// OsVersionLT applies the LT predicate on the "os_version" field.
func OsVersionLT(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLT(FieldOsVersion, v))
}

// OsVersionLTE applies the LTE predicate on the "os_version" field.
func OsVersionLTE(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldLTE(FieldOsVersion, v))
}

// OsVersionContains applies the Contains predicate on the "os_version" field.
func OsVersionContains(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContains(FieldOsVersion, v))
}

// OsVersionHasPrefix applies the HasPrefix predicate on the "os_version" field.
func OsVersionHasPrefix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasPrefix(FieldOsVersion, v))
}

// OsVersionHasSuffix applies the HasSuffix predicate on the "os_version" field.
func OsVersionHasSuffix(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldHasSuffix(FieldOsVersion, v))
}

// OsVersionIsNil applies the IsNil predicate on the "os_version" field.
func OsVersionIsNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldIsNull(FieldOsVersion))
}

// OsVersionNotNil applies the NotNil predicate on the "os_version" field.
func OsVersionNotNil() predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldNotNull(FieldOsVersion))
}

// OsVersionEqualFold applies the EqualFold predicate on the "os_version" field.
func OsVersionEqualFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldEqualFold(FieldOsVersion, v))
}

// OsVersionContainsFold applies the ContainsFold predicate on the "os_version" field.
func OsVersionContainsFold(v string) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.FieldContainsFold(FieldOsVersion, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AdminOperationLog) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AdminOperationLog) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AdminOperationLog) predicate.AdminOperationLog {
	return predicate.AdminOperationLog(sql.NotPredicates(p))
}
