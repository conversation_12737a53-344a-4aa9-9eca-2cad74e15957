// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginlog"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminLoginLogUpdate is the builder for updating AdminLoginLog entities.
type AdminLoginLogUpdate struct {
	config
	hooks     []Hook
	mutation  *AdminLoginLogMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AdminLoginLogUpdate builder.
func (allu *AdminLoginLogUpdate) Where(ps ...predicate.AdminLoginLog) *AdminLoginLogUpdate {
	allu.mutation.Where(ps...)
	return allu
}

// SetLoginIP sets the "login_ip" field.
func (allu *AdminLoginLogUpdate) SetLoginIP(s string) *AdminLoginLogUpdate {
	allu.mutation.SetLoginIP(s)
	return allu
}

// SetNillableLoginIP sets the "login_ip" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableLoginIP(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetLoginIP(*s)
	}
	return allu
}

// ClearLoginIP clears the value of the "login_ip" field.
func (allu *AdminLoginLogUpdate) ClearLoginIP() *AdminLoginLogUpdate {
	allu.mutation.ClearLoginIP()
	return allu
}

// SetLoginMAC sets the "login_mac" field.
func (allu *AdminLoginLogUpdate) SetLoginMAC(s string) *AdminLoginLogUpdate {
	allu.mutation.SetLoginMAC(s)
	return allu
}

// SetNillableLoginMAC sets the "login_mac" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableLoginMAC(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetLoginMAC(*s)
	}
	return allu
}

// ClearLoginMAC clears the value of the "login_mac" field.
func (allu *AdminLoginLogUpdate) ClearLoginMAC() *AdminLoginLogUpdate {
	allu.mutation.ClearLoginMAC()
	return allu
}

// SetLoginTime sets the "login_time" field.
func (allu *AdminLoginLogUpdate) SetLoginTime(t time.Time) *AdminLoginLogUpdate {
	allu.mutation.SetLoginTime(t)
	return allu
}

// SetNillableLoginTime sets the "login_time" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableLoginTime(t *time.Time) *AdminLoginLogUpdate {
	if t != nil {
		allu.SetLoginTime(*t)
	}
	return allu
}

// ClearLoginTime clears the value of the "login_time" field.
func (allu *AdminLoginLogUpdate) ClearLoginTime() *AdminLoginLogUpdate {
	allu.mutation.ClearLoginTime()
	return allu
}

// SetUserAgent sets the "user_agent" field.
func (allu *AdminLoginLogUpdate) SetUserAgent(s string) *AdminLoginLogUpdate {
	allu.mutation.SetUserAgent(s)
	return allu
}

// SetNillableUserAgent sets the "user_agent" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableUserAgent(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetUserAgent(*s)
	}
	return allu
}

// ClearUserAgent clears the value of the "user_agent" field.
func (allu *AdminLoginLogUpdate) ClearUserAgent() *AdminLoginLogUpdate {
	allu.mutation.ClearUserAgent()
	return allu
}

// SetBrowserName sets the "browser_name" field.
func (allu *AdminLoginLogUpdate) SetBrowserName(s string) *AdminLoginLogUpdate {
	allu.mutation.SetBrowserName(s)
	return allu
}

// SetNillableBrowserName sets the "browser_name" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableBrowserName(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetBrowserName(*s)
	}
	return allu
}

// ClearBrowserName clears the value of the "browser_name" field.
func (allu *AdminLoginLogUpdate) ClearBrowserName() *AdminLoginLogUpdate {
	allu.mutation.ClearBrowserName()
	return allu
}

// SetBrowserVersion sets the "browser_version" field.
func (allu *AdminLoginLogUpdate) SetBrowserVersion(s string) *AdminLoginLogUpdate {
	allu.mutation.SetBrowserVersion(s)
	return allu
}

// SetNillableBrowserVersion sets the "browser_version" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableBrowserVersion(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetBrowserVersion(*s)
	}
	return allu
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (allu *AdminLoginLogUpdate) ClearBrowserVersion() *AdminLoginLogUpdate {
	allu.mutation.ClearBrowserVersion()
	return allu
}

// SetClientID sets the "client_id" field.
func (allu *AdminLoginLogUpdate) SetClientID(s string) *AdminLoginLogUpdate {
	allu.mutation.SetClientID(s)
	return allu
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableClientID(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetClientID(*s)
	}
	return allu
}

// ClearClientID clears the value of the "client_id" field.
func (allu *AdminLoginLogUpdate) ClearClientID() *AdminLoginLogUpdate {
	allu.mutation.ClearClientID()
	return allu
}

// SetClientName sets the "client_name" field.
func (allu *AdminLoginLogUpdate) SetClientName(s string) *AdminLoginLogUpdate {
	allu.mutation.SetClientName(s)
	return allu
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableClientName(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetClientName(*s)
	}
	return allu
}

// ClearClientName clears the value of the "client_name" field.
func (allu *AdminLoginLogUpdate) ClearClientName() *AdminLoginLogUpdate {
	allu.mutation.ClearClientName()
	return allu
}

// SetOsName sets the "os_name" field.
func (allu *AdminLoginLogUpdate) SetOsName(s string) *AdminLoginLogUpdate {
	allu.mutation.SetOsName(s)
	return allu
}

// SetNillableOsName sets the "os_name" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableOsName(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetOsName(*s)
	}
	return allu
}

// ClearOsName clears the value of the "os_name" field.
func (allu *AdminLoginLogUpdate) ClearOsName() *AdminLoginLogUpdate {
	allu.mutation.ClearOsName()
	return allu
}

// SetOsVersion sets the "os_version" field.
func (allu *AdminLoginLogUpdate) SetOsVersion(s string) *AdminLoginLogUpdate {
	allu.mutation.SetOsVersion(s)
	return allu
}

// SetNillableOsVersion sets the "os_version" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableOsVersion(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetOsVersion(*s)
	}
	return allu
}

// ClearOsVersion clears the value of the "os_version" field.
func (allu *AdminLoginLogUpdate) ClearOsVersion() *AdminLoginLogUpdate {
	allu.mutation.ClearOsVersion()
	return allu
}

// SetUserID sets the "user_id" field.
func (allu *AdminLoginLogUpdate) SetUserID(u uint32) *AdminLoginLogUpdate {
	allu.mutation.ResetUserID()
	allu.mutation.SetUserID(u)
	return allu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableUserID(u *uint32) *AdminLoginLogUpdate {
	if u != nil {
		allu.SetUserID(*u)
	}
	return allu
}

// AddUserID adds u to the "user_id" field.
func (allu *AdminLoginLogUpdate) AddUserID(u int32) *AdminLoginLogUpdate {
	allu.mutation.AddUserID(u)
	return allu
}

// ClearUserID clears the value of the "user_id" field.
func (allu *AdminLoginLogUpdate) ClearUserID() *AdminLoginLogUpdate {
	allu.mutation.ClearUserID()
	return allu
}

// SetUsername sets the "username" field.
func (allu *AdminLoginLogUpdate) SetUsername(s string) *AdminLoginLogUpdate {
	allu.mutation.SetUsername(s)
	return allu
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableUsername(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetUsername(*s)
	}
	return allu
}

// ClearUsername clears the value of the "username" field.
func (allu *AdminLoginLogUpdate) ClearUsername() *AdminLoginLogUpdate {
	allu.mutation.ClearUsername()
	return allu
}

// SetStatusCode sets the "status_code" field.
func (allu *AdminLoginLogUpdate) SetStatusCode(i int32) *AdminLoginLogUpdate {
	allu.mutation.ResetStatusCode()
	allu.mutation.SetStatusCode(i)
	return allu
}

// SetNillableStatusCode sets the "status_code" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableStatusCode(i *int32) *AdminLoginLogUpdate {
	if i != nil {
		allu.SetStatusCode(*i)
	}
	return allu
}

// AddStatusCode adds i to the "status_code" field.
func (allu *AdminLoginLogUpdate) AddStatusCode(i int32) *AdminLoginLogUpdate {
	allu.mutation.AddStatusCode(i)
	return allu
}

// ClearStatusCode clears the value of the "status_code" field.
func (allu *AdminLoginLogUpdate) ClearStatusCode() *AdminLoginLogUpdate {
	allu.mutation.ClearStatusCode()
	return allu
}

// SetSuccess sets the "success" field.
func (allu *AdminLoginLogUpdate) SetSuccess(b bool) *AdminLoginLogUpdate {
	allu.mutation.SetSuccess(b)
	return allu
}

// SetNillableSuccess sets the "success" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableSuccess(b *bool) *AdminLoginLogUpdate {
	if b != nil {
		allu.SetSuccess(*b)
	}
	return allu
}

// ClearSuccess clears the value of the "success" field.
func (allu *AdminLoginLogUpdate) ClearSuccess() *AdminLoginLogUpdate {
	allu.mutation.ClearSuccess()
	return allu
}

// SetReason sets the "reason" field.
func (allu *AdminLoginLogUpdate) SetReason(s string) *AdminLoginLogUpdate {
	allu.mutation.SetReason(s)
	return allu
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableReason(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetReason(*s)
	}
	return allu
}

// ClearReason clears the value of the "reason" field.
func (allu *AdminLoginLogUpdate) ClearReason() *AdminLoginLogUpdate {
	allu.mutation.ClearReason()
	return allu
}

// SetLocation sets the "location" field.
func (allu *AdminLoginLogUpdate) SetLocation(s string) *AdminLoginLogUpdate {
	allu.mutation.SetLocation(s)
	return allu
}

// SetNillableLocation sets the "location" field if the given value is not nil.
func (allu *AdminLoginLogUpdate) SetNillableLocation(s *string) *AdminLoginLogUpdate {
	if s != nil {
		allu.SetLocation(*s)
	}
	return allu
}

// ClearLocation clears the value of the "location" field.
func (allu *AdminLoginLogUpdate) ClearLocation() *AdminLoginLogUpdate {
	allu.mutation.ClearLocation()
	return allu
}

// Mutation returns the AdminLoginLogMutation object of the builder.
func (allu *AdminLoginLogUpdate) Mutation() *AdminLoginLogMutation {
	return allu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (allu *AdminLoginLogUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, allu.sqlSave, allu.mutation, allu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (allu *AdminLoginLogUpdate) SaveX(ctx context.Context) int {
	affected, err := allu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (allu *AdminLoginLogUpdate) Exec(ctx context.Context) error {
	_, err := allu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (allu *AdminLoginLogUpdate) ExecX(ctx context.Context) {
	if err := allu.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (allu *AdminLoginLogUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AdminLoginLogUpdate {
	allu.modifiers = append(allu.modifiers, modifiers...)
	return allu
}

func (allu *AdminLoginLogUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(adminloginlog.Table, adminloginlog.Columns, sqlgraph.NewFieldSpec(adminloginlog.FieldID, field.TypeUint32))
	if ps := allu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if allu.mutation.CreateTimeCleared() {
		_spec.ClearField(adminloginlog.FieldCreateTime, field.TypeTime)
	}
	if value, ok := allu.mutation.LoginIP(); ok {
		_spec.SetField(adminloginlog.FieldLoginIP, field.TypeString, value)
	}
	if allu.mutation.LoginIPCleared() {
		_spec.ClearField(adminloginlog.FieldLoginIP, field.TypeString)
	}
	if value, ok := allu.mutation.LoginMAC(); ok {
		_spec.SetField(adminloginlog.FieldLoginMAC, field.TypeString, value)
	}
	if allu.mutation.LoginMACCleared() {
		_spec.ClearField(adminloginlog.FieldLoginMAC, field.TypeString)
	}
	if value, ok := allu.mutation.LoginTime(); ok {
		_spec.SetField(adminloginlog.FieldLoginTime, field.TypeTime, value)
	}
	if allu.mutation.LoginTimeCleared() {
		_spec.ClearField(adminloginlog.FieldLoginTime, field.TypeTime)
	}
	if value, ok := allu.mutation.UserAgent(); ok {
		_spec.SetField(adminloginlog.FieldUserAgent, field.TypeString, value)
	}
	if allu.mutation.UserAgentCleared() {
		_spec.ClearField(adminloginlog.FieldUserAgent, field.TypeString)
	}
	if value, ok := allu.mutation.BrowserName(); ok {
		_spec.SetField(adminloginlog.FieldBrowserName, field.TypeString, value)
	}
	if allu.mutation.BrowserNameCleared() {
		_spec.ClearField(adminloginlog.FieldBrowserName, field.TypeString)
	}
	if value, ok := allu.mutation.BrowserVersion(); ok {
		_spec.SetField(adminloginlog.FieldBrowserVersion, field.TypeString, value)
	}
	if allu.mutation.BrowserVersionCleared() {
		_spec.ClearField(adminloginlog.FieldBrowserVersion, field.TypeString)
	}
	if value, ok := allu.mutation.ClientID(); ok {
		_spec.SetField(adminloginlog.FieldClientID, field.TypeString, value)
	}
	if allu.mutation.ClientIDCleared() {
		_spec.ClearField(adminloginlog.FieldClientID, field.TypeString)
	}
	if value, ok := allu.mutation.ClientName(); ok {
		_spec.SetField(adminloginlog.FieldClientName, field.TypeString, value)
	}
	if allu.mutation.ClientNameCleared() {
		_spec.ClearField(adminloginlog.FieldClientName, field.TypeString)
	}
	if value, ok := allu.mutation.OsName(); ok {
		_spec.SetField(adminloginlog.FieldOsName, field.TypeString, value)
	}
	if allu.mutation.OsNameCleared() {
		_spec.ClearField(adminloginlog.FieldOsName, field.TypeString)
	}
	if value, ok := allu.mutation.OsVersion(); ok {
		_spec.SetField(adminloginlog.FieldOsVersion, field.TypeString, value)
	}
	if allu.mutation.OsVersionCleared() {
		_spec.ClearField(adminloginlog.FieldOsVersion, field.TypeString)
	}
	if value, ok := allu.mutation.UserID(); ok {
		_spec.SetField(adminloginlog.FieldUserID, field.TypeUint32, value)
	}
	if value, ok := allu.mutation.AddedUserID(); ok {
		_spec.AddField(adminloginlog.FieldUserID, field.TypeUint32, value)
	}
	if allu.mutation.UserIDCleared() {
		_spec.ClearField(adminloginlog.FieldUserID, field.TypeUint32)
	}
	if value, ok := allu.mutation.Username(); ok {
		_spec.SetField(adminloginlog.FieldUsername, field.TypeString, value)
	}
	if allu.mutation.UsernameCleared() {
		_spec.ClearField(adminloginlog.FieldUsername, field.TypeString)
	}
	if value, ok := allu.mutation.StatusCode(); ok {
		_spec.SetField(adminloginlog.FieldStatusCode, field.TypeInt32, value)
	}
	if value, ok := allu.mutation.AddedStatusCode(); ok {
		_spec.AddField(adminloginlog.FieldStatusCode, field.TypeInt32, value)
	}
	if allu.mutation.StatusCodeCleared() {
		_spec.ClearField(adminloginlog.FieldStatusCode, field.TypeInt32)
	}
	if value, ok := allu.mutation.Success(); ok {
		_spec.SetField(adminloginlog.FieldSuccess, field.TypeBool, value)
	}
	if allu.mutation.SuccessCleared() {
		_spec.ClearField(adminloginlog.FieldSuccess, field.TypeBool)
	}
	if value, ok := allu.mutation.Reason(); ok {
		_spec.SetField(adminloginlog.FieldReason, field.TypeString, value)
	}
	if allu.mutation.ReasonCleared() {
		_spec.ClearField(adminloginlog.FieldReason, field.TypeString)
	}
	if value, ok := allu.mutation.Location(); ok {
		_spec.SetField(adminloginlog.FieldLocation, field.TypeString, value)
	}
	if allu.mutation.LocationCleared() {
		_spec.ClearField(adminloginlog.FieldLocation, field.TypeString)
	}
	_spec.AddModifiers(allu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, allu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{adminloginlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	allu.mutation.done = true
	return n, nil
}

// AdminLoginLogUpdateOne is the builder for updating a single AdminLoginLog entity.
type AdminLoginLogUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AdminLoginLogMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetLoginIP sets the "login_ip" field.
func (alluo *AdminLoginLogUpdateOne) SetLoginIP(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetLoginIP(s)
	return alluo
}

// SetNillableLoginIP sets the "login_ip" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableLoginIP(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetLoginIP(*s)
	}
	return alluo
}

// ClearLoginIP clears the value of the "login_ip" field.
func (alluo *AdminLoginLogUpdateOne) ClearLoginIP() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearLoginIP()
	return alluo
}

// SetLoginMAC sets the "login_mac" field.
func (alluo *AdminLoginLogUpdateOne) SetLoginMAC(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetLoginMAC(s)
	return alluo
}

// SetNillableLoginMAC sets the "login_mac" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableLoginMAC(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetLoginMAC(*s)
	}
	return alluo
}

// ClearLoginMAC clears the value of the "login_mac" field.
func (alluo *AdminLoginLogUpdateOne) ClearLoginMAC() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearLoginMAC()
	return alluo
}

// SetLoginTime sets the "login_time" field.
func (alluo *AdminLoginLogUpdateOne) SetLoginTime(t time.Time) *AdminLoginLogUpdateOne {
	alluo.mutation.SetLoginTime(t)
	return alluo
}

// SetNillableLoginTime sets the "login_time" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableLoginTime(t *time.Time) *AdminLoginLogUpdateOne {
	if t != nil {
		alluo.SetLoginTime(*t)
	}
	return alluo
}

// ClearLoginTime clears the value of the "login_time" field.
func (alluo *AdminLoginLogUpdateOne) ClearLoginTime() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearLoginTime()
	return alluo
}

// SetUserAgent sets the "user_agent" field.
func (alluo *AdminLoginLogUpdateOne) SetUserAgent(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetUserAgent(s)
	return alluo
}

// SetNillableUserAgent sets the "user_agent" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableUserAgent(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetUserAgent(*s)
	}
	return alluo
}

// ClearUserAgent clears the value of the "user_agent" field.
func (alluo *AdminLoginLogUpdateOne) ClearUserAgent() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearUserAgent()
	return alluo
}

// SetBrowserName sets the "browser_name" field.
func (alluo *AdminLoginLogUpdateOne) SetBrowserName(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetBrowserName(s)
	return alluo
}

// SetNillableBrowserName sets the "browser_name" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableBrowserName(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetBrowserName(*s)
	}
	return alluo
}

// ClearBrowserName clears the value of the "browser_name" field.
func (alluo *AdminLoginLogUpdateOne) ClearBrowserName() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearBrowserName()
	return alluo
}

// SetBrowserVersion sets the "browser_version" field.
func (alluo *AdminLoginLogUpdateOne) SetBrowserVersion(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetBrowserVersion(s)
	return alluo
}

// SetNillableBrowserVersion sets the "browser_version" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableBrowserVersion(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetBrowserVersion(*s)
	}
	return alluo
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (alluo *AdminLoginLogUpdateOne) ClearBrowserVersion() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearBrowserVersion()
	return alluo
}

// SetClientID sets the "client_id" field.
func (alluo *AdminLoginLogUpdateOne) SetClientID(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetClientID(s)
	return alluo
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableClientID(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetClientID(*s)
	}
	return alluo
}

// ClearClientID clears the value of the "client_id" field.
func (alluo *AdminLoginLogUpdateOne) ClearClientID() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearClientID()
	return alluo
}

// SetClientName sets the "client_name" field.
func (alluo *AdminLoginLogUpdateOne) SetClientName(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetClientName(s)
	return alluo
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableClientName(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetClientName(*s)
	}
	return alluo
}

// ClearClientName clears the value of the "client_name" field.
func (alluo *AdminLoginLogUpdateOne) ClearClientName() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearClientName()
	return alluo
}

// SetOsName sets the "os_name" field.
func (alluo *AdminLoginLogUpdateOne) SetOsName(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetOsName(s)
	return alluo
}

// SetNillableOsName sets the "os_name" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableOsName(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetOsName(*s)
	}
	return alluo
}

// ClearOsName clears the value of the "os_name" field.
func (alluo *AdminLoginLogUpdateOne) ClearOsName() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearOsName()
	return alluo
}

// SetOsVersion sets the "os_version" field.
func (alluo *AdminLoginLogUpdateOne) SetOsVersion(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetOsVersion(s)
	return alluo
}

// SetNillableOsVersion sets the "os_version" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableOsVersion(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetOsVersion(*s)
	}
	return alluo
}

// ClearOsVersion clears the value of the "os_version" field.
func (alluo *AdminLoginLogUpdateOne) ClearOsVersion() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearOsVersion()
	return alluo
}

// SetUserID sets the "user_id" field.
func (alluo *AdminLoginLogUpdateOne) SetUserID(u uint32) *AdminLoginLogUpdateOne {
	alluo.mutation.ResetUserID()
	alluo.mutation.SetUserID(u)
	return alluo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableUserID(u *uint32) *AdminLoginLogUpdateOne {
	if u != nil {
		alluo.SetUserID(*u)
	}
	return alluo
}

// AddUserID adds u to the "user_id" field.
func (alluo *AdminLoginLogUpdateOne) AddUserID(u int32) *AdminLoginLogUpdateOne {
	alluo.mutation.AddUserID(u)
	return alluo
}

// ClearUserID clears the value of the "user_id" field.
func (alluo *AdminLoginLogUpdateOne) ClearUserID() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearUserID()
	return alluo
}

// SetUsername sets the "username" field.
func (alluo *AdminLoginLogUpdateOne) SetUsername(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetUsername(s)
	return alluo
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableUsername(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetUsername(*s)
	}
	return alluo
}

// ClearUsername clears the value of the "username" field.
func (alluo *AdminLoginLogUpdateOne) ClearUsername() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearUsername()
	return alluo
}

// SetStatusCode sets the "status_code" field.
func (alluo *AdminLoginLogUpdateOne) SetStatusCode(i int32) *AdminLoginLogUpdateOne {
	alluo.mutation.ResetStatusCode()
	alluo.mutation.SetStatusCode(i)
	return alluo
}

// SetNillableStatusCode sets the "status_code" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableStatusCode(i *int32) *AdminLoginLogUpdateOne {
	if i != nil {
		alluo.SetStatusCode(*i)
	}
	return alluo
}

// AddStatusCode adds i to the "status_code" field.
func (alluo *AdminLoginLogUpdateOne) AddStatusCode(i int32) *AdminLoginLogUpdateOne {
	alluo.mutation.AddStatusCode(i)
	return alluo
}

// ClearStatusCode clears the value of the "status_code" field.
func (alluo *AdminLoginLogUpdateOne) ClearStatusCode() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearStatusCode()
	return alluo
}

// SetSuccess sets the "success" field.
func (alluo *AdminLoginLogUpdateOne) SetSuccess(b bool) *AdminLoginLogUpdateOne {
	alluo.mutation.SetSuccess(b)
	return alluo
}

// SetNillableSuccess sets the "success" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableSuccess(b *bool) *AdminLoginLogUpdateOne {
	if b != nil {
		alluo.SetSuccess(*b)
	}
	return alluo
}

// ClearSuccess clears the value of the "success" field.
func (alluo *AdminLoginLogUpdateOne) ClearSuccess() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearSuccess()
	return alluo
}

// SetReason sets the "reason" field.
func (alluo *AdminLoginLogUpdateOne) SetReason(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetReason(s)
	return alluo
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableReason(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetReason(*s)
	}
	return alluo
}

// ClearReason clears the value of the "reason" field.
func (alluo *AdminLoginLogUpdateOne) ClearReason() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearReason()
	return alluo
}

// SetLocation sets the "location" field.
func (alluo *AdminLoginLogUpdateOne) SetLocation(s string) *AdminLoginLogUpdateOne {
	alluo.mutation.SetLocation(s)
	return alluo
}

// SetNillableLocation sets the "location" field if the given value is not nil.
func (alluo *AdminLoginLogUpdateOne) SetNillableLocation(s *string) *AdminLoginLogUpdateOne {
	if s != nil {
		alluo.SetLocation(*s)
	}
	return alluo
}

// ClearLocation clears the value of the "location" field.
func (alluo *AdminLoginLogUpdateOne) ClearLocation() *AdminLoginLogUpdateOne {
	alluo.mutation.ClearLocation()
	return alluo
}

// Mutation returns the AdminLoginLogMutation object of the builder.
func (alluo *AdminLoginLogUpdateOne) Mutation() *AdminLoginLogMutation {
	return alluo.mutation
}

// Where appends a list predicates to the AdminLoginLogUpdate builder.
func (alluo *AdminLoginLogUpdateOne) Where(ps ...predicate.AdminLoginLog) *AdminLoginLogUpdateOne {
	alluo.mutation.Where(ps...)
	return alluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (alluo *AdminLoginLogUpdateOne) Select(field string, fields ...string) *AdminLoginLogUpdateOne {
	alluo.fields = append([]string{field}, fields...)
	return alluo
}

// Save executes the query and returns the updated AdminLoginLog entity.
func (alluo *AdminLoginLogUpdateOne) Save(ctx context.Context) (*AdminLoginLog, error) {
	return withHooks(ctx, alluo.sqlSave, alluo.mutation, alluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (alluo *AdminLoginLogUpdateOne) SaveX(ctx context.Context) *AdminLoginLog {
	node, err := alluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (alluo *AdminLoginLogUpdateOne) Exec(ctx context.Context) error {
	_, err := alluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (alluo *AdminLoginLogUpdateOne) ExecX(ctx context.Context) {
	if err := alluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (alluo *AdminLoginLogUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AdminLoginLogUpdateOne {
	alluo.modifiers = append(alluo.modifiers, modifiers...)
	return alluo
}

func (alluo *AdminLoginLogUpdateOne) sqlSave(ctx context.Context) (_node *AdminLoginLog, err error) {
	_spec := sqlgraph.NewUpdateSpec(adminloginlog.Table, adminloginlog.Columns, sqlgraph.NewFieldSpec(adminloginlog.FieldID, field.TypeUint32))
	id, ok := alluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AdminLoginLog.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := alluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, adminloginlog.FieldID)
		for _, f := range fields {
			if !adminloginlog.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != adminloginlog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := alluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if alluo.mutation.CreateTimeCleared() {
		_spec.ClearField(adminloginlog.FieldCreateTime, field.TypeTime)
	}
	if value, ok := alluo.mutation.LoginIP(); ok {
		_spec.SetField(adminloginlog.FieldLoginIP, field.TypeString, value)
	}
	if alluo.mutation.LoginIPCleared() {
		_spec.ClearField(adminloginlog.FieldLoginIP, field.TypeString)
	}
	if value, ok := alluo.mutation.LoginMAC(); ok {
		_spec.SetField(adminloginlog.FieldLoginMAC, field.TypeString, value)
	}
	if alluo.mutation.LoginMACCleared() {
		_spec.ClearField(adminloginlog.FieldLoginMAC, field.TypeString)
	}
	if value, ok := alluo.mutation.LoginTime(); ok {
		_spec.SetField(adminloginlog.FieldLoginTime, field.TypeTime, value)
	}
	if alluo.mutation.LoginTimeCleared() {
		_spec.ClearField(adminloginlog.FieldLoginTime, field.TypeTime)
	}
	if value, ok := alluo.mutation.UserAgent(); ok {
		_spec.SetField(adminloginlog.FieldUserAgent, field.TypeString, value)
	}
	if alluo.mutation.UserAgentCleared() {
		_spec.ClearField(adminloginlog.FieldUserAgent, field.TypeString)
	}
	if value, ok := alluo.mutation.BrowserName(); ok {
		_spec.SetField(adminloginlog.FieldBrowserName, field.TypeString, value)
	}
	if alluo.mutation.BrowserNameCleared() {
		_spec.ClearField(adminloginlog.FieldBrowserName, field.TypeString)
	}
	if value, ok := alluo.mutation.BrowserVersion(); ok {
		_spec.SetField(adminloginlog.FieldBrowserVersion, field.TypeString, value)
	}
	if alluo.mutation.BrowserVersionCleared() {
		_spec.ClearField(adminloginlog.FieldBrowserVersion, field.TypeString)
	}
	if value, ok := alluo.mutation.ClientID(); ok {
		_spec.SetField(adminloginlog.FieldClientID, field.TypeString, value)
	}
	if alluo.mutation.ClientIDCleared() {
		_spec.ClearField(adminloginlog.FieldClientID, field.TypeString)
	}
	if value, ok := alluo.mutation.ClientName(); ok {
		_spec.SetField(adminloginlog.FieldClientName, field.TypeString, value)
	}
	if alluo.mutation.ClientNameCleared() {
		_spec.ClearField(adminloginlog.FieldClientName, field.TypeString)
	}
	if value, ok := alluo.mutation.OsName(); ok {
		_spec.SetField(adminloginlog.FieldOsName, field.TypeString, value)
	}
	if alluo.mutation.OsNameCleared() {
		_spec.ClearField(adminloginlog.FieldOsName, field.TypeString)
	}
	if value, ok := alluo.mutation.OsVersion(); ok {
		_spec.SetField(adminloginlog.FieldOsVersion, field.TypeString, value)
	}
	if alluo.mutation.OsVersionCleared() {
		_spec.ClearField(adminloginlog.FieldOsVersion, field.TypeString)
	}
	if value, ok := alluo.mutation.UserID(); ok {
		_spec.SetField(adminloginlog.FieldUserID, field.TypeUint32, value)
	}
	if value, ok := alluo.mutation.AddedUserID(); ok {
		_spec.AddField(adminloginlog.FieldUserID, field.TypeUint32, value)
	}
	if alluo.mutation.UserIDCleared() {
		_spec.ClearField(adminloginlog.FieldUserID, field.TypeUint32)
	}
	if value, ok := alluo.mutation.Username(); ok {
		_spec.SetField(adminloginlog.FieldUsername, field.TypeString, value)
	}
	if alluo.mutation.UsernameCleared() {
		_spec.ClearField(adminloginlog.FieldUsername, field.TypeString)
	}
	if value, ok := alluo.mutation.StatusCode(); ok {
		_spec.SetField(adminloginlog.FieldStatusCode, field.TypeInt32, value)
	}
	if value, ok := alluo.mutation.AddedStatusCode(); ok {
		_spec.AddField(adminloginlog.FieldStatusCode, field.TypeInt32, value)
	}
	if alluo.mutation.StatusCodeCleared() {
		_spec.ClearField(adminloginlog.FieldStatusCode, field.TypeInt32)
	}
	if value, ok := alluo.mutation.Success(); ok {
		_spec.SetField(adminloginlog.FieldSuccess, field.TypeBool, value)
	}
	if alluo.mutation.SuccessCleared() {
		_spec.ClearField(adminloginlog.FieldSuccess, field.TypeBool)
	}
	if value, ok := alluo.mutation.Reason(); ok {
		_spec.SetField(adminloginlog.FieldReason, field.TypeString, value)
	}
	if alluo.mutation.ReasonCleared() {
		_spec.ClearField(adminloginlog.FieldReason, field.TypeString)
	}
	if value, ok := alluo.mutation.Location(); ok {
		_spec.SetField(adminloginlog.FieldLocation, field.TypeString, value)
	}
	if alluo.mutation.LocationCleared() {
		_spec.ClearField(adminloginlog.FieldLocation, field.TypeString)
	}
	_spec.AddModifiers(alluo.modifiers...)
	_node = &AdminLoginLog{config: alluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, alluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{adminloginlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	alluo.mutation.done = true
	return _node, nil
}
