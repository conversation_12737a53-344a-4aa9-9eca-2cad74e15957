// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminoperationlog"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminOperationLogUpdate is the builder for updating AdminOperationLog entities.
type AdminOperationLogUpdate struct {
	config
	hooks     []Hook
	mutation  *AdminOperationLogMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AdminOperationLogUpdate builder.
func (aolu *AdminOperationLogUpdate) Where(ps ...predicate.AdminOperationLog) *AdminOperationLogUpdate {
	aolu.mutation.Where(ps...)
	return aolu
}

// SetRequestID sets the "request_id" field.
func (aolu *AdminOperationLogUpdate) SetRequestID(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetRequestID(s)
	return aolu
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableRequestID(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetRequestID(*s)
	}
	return aolu
}

// ClearRequestID clears the value of the "request_id" field.
func (aolu *AdminOperationLogUpdate) ClearRequestID() *AdminOperationLogUpdate {
	aolu.mutation.ClearRequestID()
	return aolu
}

// SetMethod sets the "method" field.
func (aolu *AdminOperationLogUpdate) SetMethod(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetMethod(s)
	return aolu
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableMethod(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetMethod(*s)
	}
	return aolu
}

// ClearMethod clears the value of the "method" field.
func (aolu *AdminOperationLogUpdate) ClearMethod() *AdminOperationLogUpdate {
	aolu.mutation.ClearMethod()
	return aolu
}

// SetOperation sets the "operation" field.
func (aolu *AdminOperationLogUpdate) SetOperation(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetOperation(s)
	return aolu
}

// SetNillableOperation sets the "operation" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableOperation(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetOperation(*s)
	}
	return aolu
}

// ClearOperation clears the value of the "operation" field.
func (aolu *AdminOperationLogUpdate) ClearOperation() *AdminOperationLogUpdate {
	aolu.mutation.ClearOperation()
	return aolu
}

// SetPath sets the "path" field.
func (aolu *AdminOperationLogUpdate) SetPath(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetPath(s)
	return aolu
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillablePath(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetPath(*s)
	}
	return aolu
}

// ClearPath clears the value of the "path" field.
func (aolu *AdminOperationLogUpdate) ClearPath() *AdminOperationLogUpdate {
	aolu.mutation.ClearPath()
	return aolu
}

// SetReferer sets the "referer" field.
func (aolu *AdminOperationLogUpdate) SetReferer(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetReferer(s)
	return aolu
}

// SetNillableReferer sets the "referer" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableReferer(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetReferer(*s)
	}
	return aolu
}

// ClearReferer clears the value of the "referer" field.
func (aolu *AdminOperationLogUpdate) ClearReferer() *AdminOperationLogUpdate {
	aolu.mutation.ClearReferer()
	return aolu
}

// SetRequestURI sets the "request_uri" field.
func (aolu *AdminOperationLogUpdate) SetRequestURI(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetRequestURI(s)
	return aolu
}

// SetNillableRequestURI sets the "request_uri" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableRequestURI(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetRequestURI(*s)
	}
	return aolu
}

// ClearRequestURI clears the value of the "request_uri" field.
func (aolu *AdminOperationLogUpdate) ClearRequestURI() *AdminOperationLogUpdate {
	aolu.mutation.ClearRequestURI()
	return aolu
}

// SetRequestBody sets the "request_body" field.
func (aolu *AdminOperationLogUpdate) SetRequestBody(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetRequestBody(s)
	return aolu
}

// SetNillableRequestBody sets the "request_body" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableRequestBody(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetRequestBody(*s)
	}
	return aolu
}

// ClearRequestBody clears the value of the "request_body" field.
func (aolu *AdminOperationLogUpdate) ClearRequestBody() *AdminOperationLogUpdate {
	aolu.mutation.ClearRequestBody()
	return aolu
}

// SetRequestHeader sets the "request_header" field.
func (aolu *AdminOperationLogUpdate) SetRequestHeader(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetRequestHeader(s)
	return aolu
}

// SetNillableRequestHeader sets the "request_header" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableRequestHeader(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetRequestHeader(*s)
	}
	return aolu
}

// ClearRequestHeader clears the value of the "request_header" field.
func (aolu *AdminOperationLogUpdate) ClearRequestHeader() *AdminOperationLogUpdate {
	aolu.mutation.ClearRequestHeader()
	return aolu
}

// SetResponse sets the "response" field.
func (aolu *AdminOperationLogUpdate) SetResponse(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetResponse(s)
	return aolu
}

// SetNillableResponse sets the "response" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableResponse(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetResponse(*s)
	}
	return aolu
}

// ClearResponse clears the value of the "response" field.
func (aolu *AdminOperationLogUpdate) ClearResponse() *AdminOperationLogUpdate {
	aolu.mutation.ClearResponse()
	return aolu
}

// SetCostTime sets the "cost_time" field.
func (aolu *AdminOperationLogUpdate) SetCostTime(f float64) *AdminOperationLogUpdate {
	aolu.mutation.ResetCostTime()
	aolu.mutation.SetCostTime(f)
	return aolu
}

// SetNillableCostTime sets the "cost_time" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableCostTime(f *float64) *AdminOperationLogUpdate {
	if f != nil {
		aolu.SetCostTime(*f)
	}
	return aolu
}

// AddCostTime adds f to the "cost_time" field.
func (aolu *AdminOperationLogUpdate) AddCostTime(f float64) *AdminOperationLogUpdate {
	aolu.mutation.AddCostTime(f)
	return aolu
}

// ClearCostTime clears the value of the "cost_time" field.
func (aolu *AdminOperationLogUpdate) ClearCostTime() *AdminOperationLogUpdate {
	aolu.mutation.ClearCostTime()
	return aolu
}

// SetUserID sets the "user_id" field.
func (aolu *AdminOperationLogUpdate) SetUserID(u uint32) *AdminOperationLogUpdate {
	aolu.mutation.ResetUserID()
	aolu.mutation.SetUserID(u)
	return aolu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableUserID(u *uint32) *AdminOperationLogUpdate {
	if u != nil {
		aolu.SetUserID(*u)
	}
	return aolu
}

// AddUserID adds u to the "user_id" field.
func (aolu *AdminOperationLogUpdate) AddUserID(u int32) *AdminOperationLogUpdate {
	aolu.mutation.AddUserID(u)
	return aolu
}

// ClearUserID clears the value of the "user_id" field.
func (aolu *AdminOperationLogUpdate) ClearUserID() *AdminOperationLogUpdate {
	aolu.mutation.ClearUserID()
	return aolu
}

// SetUsername sets the "username" field.
func (aolu *AdminOperationLogUpdate) SetUsername(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetUsername(s)
	return aolu
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableUsername(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetUsername(*s)
	}
	return aolu
}

// ClearUsername clears the value of the "username" field.
func (aolu *AdminOperationLogUpdate) ClearUsername() *AdminOperationLogUpdate {
	aolu.mutation.ClearUsername()
	return aolu
}

// SetClientIP sets the "client_ip" field.
func (aolu *AdminOperationLogUpdate) SetClientIP(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetClientIP(s)
	return aolu
}

// SetNillableClientIP sets the "client_ip" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableClientIP(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetClientIP(*s)
	}
	return aolu
}

// ClearClientIP clears the value of the "client_ip" field.
func (aolu *AdminOperationLogUpdate) ClearClientIP() *AdminOperationLogUpdate {
	aolu.mutation.ClearClientIP()
	return aolu
}

// SetStatusCode sets the "status_code" field.
func (aolu *AdminOperationLogUpdate) SetStatusCode(i int32) *AdminOperationLogUpdate {
	aolu.mutation.ResetStatusCode()
	aolu.mutation.SetStatusCode(i)
	return aolu
}

// SetNillableStatusCode sets the "status_code" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableStatusCode(i *int32) *AdminOperationLogUpdate {
	if i != nil {
		aolu.SetStatusCode(*i)
	}
	return aolu
}

// AddStatusCode adds i to the "status_code" field.
func (aolu *AdminOperationLogUpdate) AddStatusCode(i int32) *AdminOperationLogUpdate {
	aolu.mutation.AddStatusCode(i)
	return aolu
}

// ClearStatusCode clears the value of the "status_code" field.
func (aolu *AdminOperationLogUpdate) ClearStatusCode() *AdminOperationLogUpdate {
	aolu.mutation.ClearStatusCode()
	return aolu
}

// SetReason sets the "reason" field.
func (aolu *AdminOperationLogUpdate) SetReason(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetReason(s)
	return aolu
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableReason(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetReason(*s)
	}
	return aolu
}

// ClearReason clears the value of the "reason" field.
func (aolu *AdminOperationLogUpdate) ClearReason() *AdminOperationLogUpdate {
	aolu.mutation.ClearReason()
	return aolu
}

// SetSuccess sets the "success" field.
func (aolu *AdminOperationLogUpdate) SetSuccess(b bool) *AdminOperationLogUpdate {
	aolu.mutation.SetSuccess(b)
	return aolu
}

// SetNillableSuccess sets the "success" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableSuccess(b *bool) *AdminOperationLogUpdate {
	if b != nil {
		aolu.SetSuccess(*b)
	}
	return aolu
}

// ClearSuccess clears the value of the "success" field.
func (aolu *AdminOperationLogUpdate) ClearSuccess() *AdminOperationLogUpdate {
	aolu.mutation.ClearSuccess()
	return aolu
}

// SetLocation sets the "location" field.
func (aolu *AdminOperationLogUpdate) SetLocation(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetLocation(s)
	return aolu
}

// SetNillableLocation sets the "location" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableLocation(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetLocation(*s)
	}
	return aolu
}

// ClearLocation clears the value of the "location" field.
func (aolu *AdminOperationLogUpdate) ClearLocation() *AdminOperationLogUpdate {
	aolu.mutation.ClearLocation()
	return aolu
}

// SetUserAgent sets the "user_agent" field.
func (aolu *AdminOperationLogUpdate) SetUserAgent(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetUserAgent(s)
	return aolu
}

// SetNillableUserAgent sets the "user_agent" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableUserAgent(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetUserAgent(*s)
	}
	return aolu
}

// ClearUserAgent clears the value of the "user_agent" field.
func (aolu *AdminOperationLogUpdate) ClearUserAgent() *AdminOperationLogUpdate {
	aolu.mutation.ClearUserAgent()
	return aolu
}

// SetBrowserName sets the "browser_name" field.
func (aolu *AdminOperationLogUpdate) SetBrowserName(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetBrowserName(s)
	return aolu
}

// SetNillableBrowserName sets the "browser_name" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableBrowserName(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetBrowserName(*s)
	}
	return aolu
}

// ClearBrowserName clears the value of the "browser_name" field.
func (aolu *AdminOperationLogUpdate) ClearBrowserName() *AdminOperationLogUpdate {
	aolu.mutation.ClearBrowserName()
	return aolu
}

// SetBrowserVersion sets the "browser_version" field.
func (aolu *AdminOperationLogUpdate) SetBrowserVersion(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetBrowserVersion(s)
	return aolu
}

// SetNillableBrowserVersion sets the "browser_version" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableBrowserVersion(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetBrowserVersion(*s)
	}
	return aolu
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (aolu *AdminOperationLogUpdate) ClearBrowserVersion() *AdminOperationLogUpdate {
	aolu.mutation.ClearBrowserVersion()
	return aolu
}

// SetClientID sets the "client_id" field.
func (aolu *AdminOperationLogUpdate) SetClientID(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetClientID(s)
	return aolu
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableClientID(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetClientID(*s)
	}
	return aolu
}

// ClearClientID clears the value of the "client_id" field.
func (aolu *AdminOperationLogUpdate) ClearClientID() *AdminOperationLogUpdate {
	aolu.mutation.ClearClientID()
	return aolu
}

// SetClientName sets the "client_name" field.
func (aolu *AdminOperationLogUpdate) SetClientName(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetClientName(s)
	return aolu
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableClientName(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetClientName(*s)
	}
	return aolu
}

// ClearClientName clears the value of the "client_name" field.
func (aolu *AdminOperationLogUpdate) ClearClientName() *AdminOperationLogUpdate {
	aolu.mutation.ClearClientName()
	return aolu
}

// SetOsName sets the "os_name" field.
func (aolu *AdminOperationLogUpdate) SetOsName(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetOsName(s)
	return aolu
}

// SetNillableOsName sets the "os_name" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableOsName(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetOsName(*s)
	}
	return aolu
}

// ClearOsName clears the value of the "os_name" field.
func (aolu *AdminOperationLogUpdate) ClearOsName() *AdminOperationLogUpdate {
	aolu.mutation.ClearOsName()
	return aolu
}

// SetOsVersion sets the "os_version" field.
func (aolu *AdminOperationLogUpdate) SetOsVersion(s string) *AdminOperationLogUpdate {
	aolu.mutation.SetOsVersion(s)
	return aolu
}

// SetNillableOsVersion sets the "os_version" field if the given value is not nil.
func (aolu *AdminOperationLogUpdate) SetNillableOsVersion(s *string) *AdminOperationLogUpdate {
	if s != nil {
		aolu.SetOsVersion(*s)
	}
	return aolu
}

// ClearOsVersion clears the value of the "os_version" field.
func (aolu *AdminOperationLogUpdate) ClearOsVersion() *AdminOperationLogUpdate {
	aolu.mutation.ClearOsVersion()
	return aolu
}

// Mutation returns the AdminOperationLogMutation object of the builder.
func (aolu *AdminOperationLogUpdate) Mutation() *AdminOperationLogMutation {
	return aolu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aolu *AdminOperationLogUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, aolu.sqlSave, aolu.mutation, aolu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aolu *AdminOperationLogUpdate) SaveX(ctx context.Context) int {
	affected, err := aolu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aolu *AdminOperationLogUpdate) Exec(ctx context.Context) error {
	_, err := aolu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aolu *AdminOperationLogUpdate) ExecX(ctx context.Context) {
	if err := aolu.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aolu *AdminOperationLogUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AdminOperationLogUpdate {
	aolu.modifiers = append(aolu.modifiers, modifiers...)
	return aolu
}

func (aolu *AdminOperationLogUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(adminoperationlog.Table, adminoperationlog.Columns, sqlgraph.NewFieldSpec(adminoperationlog.FieldID, field.TypeUint32))
	if ps := aolu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if aolu.mutation.CreateTimeCleared() {
		_spec.ClearField(adminoperationlog.FieldCreateTime, field.TypeTime)
	}
	if value, ok := aolu.mutation.RequestID(); ok {
		_spec.SetField(adminoperationlog.FieldRequestID, field.TypeString, value)
	}
	if aolu.mutation.RequestIDCleared() {
		_spec.ClearField(adminoperationlog.FieldRequestID, field.TypeString)
	}
	if value, ok := aolu.mutation.Method(); ok {
		_spec.SetField(adminoperationlog.FieldMethod, field.TypeString, value)
	}
	if aolu.mutation.MethodCleared() {
		_spec.ClearField(adminoperationlog.FieldMethod, field.TypeString)
	}
	if value, ok := aolu.mutation.Operation(); ok {
		_spec.SetField(adminoperationlog.FieldOperation, field.TypeString, value)
	}
	if aolu.mutation.OperationCleared() {
		_spec.ClearField(adminoperationlog.FieldOperation, field.TypeString)
	}
	if value, ok := aolu.mutation.Path(); ok {
		_spec.SetField(adminoperationlog.FieldPath, field.TypeString, value)
	}
	if aolu.mutation.PathCleared() {
		_spec.ClearField(adminoperationlog.FieldPath, field.TypeString)
	}
	if value, ok := aolu.mutation.Referer(); ok {
		_spec.SetField(adminoperationlog.FieldReferer, field.TypeString, value)
	}
	if aolu.mutation.RefererCleared() {
		_spec.ClearField(adminoperationlog.FieldReferer, field.TypeString)
	}
	if value, ok := aolu.mutation.RequestURI(); ok {
		_spec.SetField(adminoperationlog.FieldRequestURI, field.TypeString, value)
	}
	if aolu.mutation.RequestURICleared() {
		_spec.ClearField(adminoperationlog.FieldRequestURI, field.TypeString)
	}
	if value, ok := aolu.mutation.RequestBody(); ok {
		_spec.SetField(adminoperationlog.FieldRequestBody, field.TypeString, value)
	}
	if aolu.mutation.RequestBodyCleared() {
		_spec.ClearField(adminoperationlog.FieldRequestBody, field.TypeString)
	}
	if value, ok := aolu.mutation.RequestHeader(); ok {
		_spec.SetField(adminoperationlog.FieldRequestHeader, field.TypeString, value)
	}
	if aolu.mutation.RequestHeaderCleared() {
		_spec.ClearField(adminoperationlog.FieldRequestHeader, field.TypeString)
	}
	if value, ok := aolu.mutation.Response(); ok {
		_spec.SetField(adminoperationlog.FieldResponse, field.TypeString, value)
	}
	if aolu.mutation.ResponseCleared() {
		_spec.ClearField(adminoperationlog.FieldResponse, field.TypeString)
	}
	if value, ok := aolu.mutation.CostTime(); ok {
		_spec.SetField(adminoperationlog.FieldCostTime, field.TypeFloat64, value)
	}
	if value, ok := aolu.mutation.AddedCostTime(); ok {
		_spec.AddField(adminoperationlog.FieldCostTime, field.TypeFloat64, value)
	}
	if aolu.mutation.CostTimeCleared() {
		_spec.ClearField(adminoperationlog.FieldCostTime, field.TypeFloat64)
	}
	if value, ok := aolu.mutation.UserID(); ok {
		_spec.SetField(adminoperationlog.FieldUserID, field.TypeUint32, value)
	}
	if value, ok := aolu.mutation.AddedUserID(); ok {
		_spec.AddField(adminoperationlog.FieldUserID, field.TypeUint32, value)
	}
	if aolu.mutation.UserIDCleared() {
		_spec.ClearField(adminoperationlog.FieldUserID, field.TypeUint32)
	}
	if value, ok := aolu.mutation.Username(); ok {
		_spec.SetField(adminoperationlog.FieldUsername, field.TypeString, value)
	}
	if aolu.mutation.UsernameCleared() {
		_spec.ClearField(adminoperationlog.FieldUsername, field.TypeString)
	}
	if value, ok := aolu.mutation.ClientIP(); ok {
		_spec.SetField(adminoperationlog.FieldClientIP, field.TypeString, value)
	}
	if aolu.mutation.ClientIPCleared() {
		_spec.ClearField(adminoperationlog.FieldClientIP, field.TypeString)
	}
	if value, ok := aolu.mutation.StatusCode(); ok {
		_spec.SetField(adminoperationlog.FieldStatusCode, field.TypeInt32, value)
	}
	if value, ok := aolu.mutation.AddedStatusCode(); ok {
		_spec.AddField(adminoperationlog.FieldStatusCode, field.TypeInt32, value)
	}
	if aolu.mutation.StatusCodeCleared() {
		_spec.ClearField(adminoperationlog.FieldStatusCode, field.TypeInt32)
	}
	if value, ok := aolu.mutation.Reason(); ok {
		_spec.SetField(adminoperationlog.FieldReason, field.TypeString, value)
	}
	if aolu.mutation.ReasonCleared() {
		_spec.ClearField(adminoperationlog.FieldReason, field.TypeString)
	}
	if value, ok := aolu.mutation.Success(); ok {
		_spec.SetField(adminoperationlog.FieldSuccess, field.TypeBool, value)
	}
	if aolu.mutation.SuccessCleared() {
		_spec.ClearField(adminoperationlog.FieldSuccess, field.TypeBool)
	}
	if value, ok := aolu.mutation.Location(); ok {
		_spec.SetField(adminoperationlog.FieldLocation, field.TypeString, value)
	}
	if aolu.mutation.LocationCleared() {
		_spec.ClearField(adminoperationlog.FieldLocation, field.TypeString)
	}
	if value, ok := aolu.mutation.UserAgent(); ok {
		_spec.SetField(adminoperationlog.FieldUserAgent, field.TypeString, value)
	}
	if aolu.mutation.UserAgentCleared() {
		_spec.ClearField(adminoperationlog.FieldUserAgent, field.TypeString)
	}
	if value, ok := aolu.mutation.BrowserName(); ok {
		_spec.SetField(adminoperationlog.FieldBrowserName, field.TypeString, value)
	}
	if aolu.mutation.BrowserNameCleared() {
		_spec.ClearField(adminoperationlog.FieldBrowserName, field.TypeString)
	}
	if value, ok := aolu.mutation.BrowserVersion(); ok {
		_spec.SetField(adminoperationlog.FieldBrowserVersion, field.TypeString, value)
	}
	if aolu.mutation.BrowserVersionCleared() {
		_spec.ClearField(adminoperationlog.FieldBrowserVersion, field.TypeString)
	}
	if value, ok := aolu.mutation.ClientID(); ok {
		_spec.SetField(adminoperationlog.FieldClientID, field.TypeString, value)
	}
	if aolu.mutation.ClientIDCleared() {
		_spec.ClearField(adminoperationlog.FieldClientID, field.TypeString)
	}
	if value, ok := aolu.mutation.ClientName(); ok {
		_spec.SetField(adminoperationlog.FieldClientName, field.TypeString, value)
	}
	if aolu.mutation.ClientNameCleared() {
		_spec.ClearField(adminoperationlog.FieldClientName, field.TypeString)
	}
	if value, ok := aolu.mutation.OsName(); ok {
		_spec.SetField(adminoperationlog.FieldOsName, field.TypeString, value)
	}
	if aolu.mutation.OsNameCleared() {
		_spec.ClearField(adminoperationlog.FieldOsName, field.TypeString)
	}
	if value, ok := aolu.mutation.OsVersion(); ok {
		_spec.SetField(adminoperationlog.FieldOsVersion, field.TypeString, value)
	}
	if aolu.mutation.OsVersionCleared() {
		_spec.ClearField(adminoperationlog.FieldOsVersion, field.TypeString)
	}
	_spec.AddModifiers(aolu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, aolu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{adminoperationlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aolu.mutation.done = true
	return n, nil
}

// AdminOperationLogUpdateOne is the builder for updating a single AdminOperationLog entity.
type AdminOperationLogUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AdminOperationLogMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetRequestID sets the "request_id" field.
func (aoluo *AdminOperationLogUpdateOne) SetRequestID(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetRequestID(s)
	return aoluo
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableRequestID(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetRequestID(*s)
	}
	return aoluo
}

// ClearRequestID clears the value of the "request_id" field.
func (aoluo *AdminOperationLogUpdateOne) ClearRequestID() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearRequestID()
	return aoluo
}

// SetMethod sets the "method" field.
func (aoluo *AdminOperationLogUpdateOne) SetMethod(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetMethod(s)
	return aoluo
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableMethod(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetMethod(*s)
	}
	return aoluo
}

// ClearMethod clears the value of the "method" field.
func (aoluo *AdminOperationLogUpdateOne) ClearMethod() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearMethod()
	return aoluo
}

// SetOperation sets the "operation" field.
func (aoluo *AdminOperationLogUpdateOne) SetOperation(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetOperation(s)
	return aoluo
}

// SetNillableOperation sets the "operation" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableOperation(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetOperation(*s)
	}
	return aoluo
}

// ClearOperation clears the value of the "operation" field.
func (aoluo *AdminOperationLogUpdateOne) ClearOperation() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearOperation()
	return aoluo
}

// SetPath sets the "path" field.
func (aoluo *AdminOperationLogUpdateOne) SetPath(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetPath(s)
	return aoluo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillablePath(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetPath(*s)
	}
	return aoluo
}

// ClearPath clears the value of the "path" field.
func (aoluo *AdminOperationLogUpdateOne) ClearPath() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearPath()
	return aoluo
}

// SetReferer sets the "referer" field.
func (aoluo *AdminOperationLogUpdateOne) SetReferer(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetReferer(s)
	return aoluo
}

// SetNillableReferer sets the "referer" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableReferer(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetReferer(*s)
	}
	return aoluo
}

// ClearReferer clears the value of the "referer" field.
func (aoluo *AdminOperationLogUpdateOne) ClearReferer() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearReferer()
	return aoluo
}

// SetRequestURI sets the "request_uri" field.
func (aoluo *AdminOperationLogUpdateOne) SetRequestURI(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetRequestURI(s)
	return aoluo
}

// SetNillableRequestURI sets the "request_uri" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableRequestURI(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetRequestURI(*s)
	}
	return aoluo
}

// ClearRequestURI clears the value of the "request_uri" field.
func (aoluo *AdminOperationLogUpdateOne) ClearRequestURI() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearRequestURI()
	return aoluo
}

// SetRequestBody sets the "request_body" field.
func (aoluo *AdminOperationLogUpdateOne) SetRequestBody(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetRequestBody(s)
	return aoluo
}

// SetNillableRequestBody sets the "request_body" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableRequestBody(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetRequestBody(*s)
	}
	return aoluo
}

// ClearRequestBody clears the value of the "request_body" field.
func (aoluo *AdminOperationLogUpdateOne) ClearRequestBody() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearRequestBody()
	return aoluo
}

// SetRequestHeader sets the "request_header" field.
func (aoluo *AdminOperationLogUpdateOne) SetRequestHeader(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetRequestHeader(s)
	return aoluo
}

// SetNillableRequestHeader sets the "request_header" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableRequestHeader(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetRequestHeader(*s)
	}
	return aoluo
}

// ClearRequestHeader clears the value of the "request_header" field.
func (aoluo *AdminOperationLogUpdateOne) ClearRequestHeader() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearRequestHeader()
	return aoluo
}

// SetResponse sets the "response" field.
func (aoluo *AdminOperationLogUpdateOne) SetResponse(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetResponse(s)
	return aoluo
}

// SetNillableResponse sets the "response" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableResponse(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetResponse(*s)
	}
	return aoluo
}

// ClearResponse clears the value of the "response" field.
func (aoluo *AdminOperationLogUpdateOne) ClearResponse() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearResponse()
	return aoluo
}

// SetCostTime sets the "cost_time" field.
func (aoluo *AdminOperationLogUpdateOne) SetCostTime(f float64) *AdminOperationLogUpdateOne {
	aoluo.mutation.ResetCostTime()
	aoluo.mutation.SetCostTime(f)
	return aoluo
}

// SetNillableCostTime sets the "cost_time" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableCostTime(f *float64) *AdminOperationLogUpdateOne {
	if f != nil {
		aoluo.SetCostTime(*f)
	}
	return aoluo
}

// AddCostTime adds f to the "cost_time" field.
func (aoluo *AdminOperationLogUpdateOne) AddCostTime(f float64) *AdminOperationLogUpdateOne {
	aoluo.mutation.AddCostTime(f)
	return aoluo
}

// ClearCostTime clears the value of the "cost_time" field.
func (aoluo *AdminOperationLogUpdateOne) ClearCostTime() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearCostTime()
	return aoluo
}

// SetUserID sets the "user_id" field.
func (aoluo *AdminOperationLogUpdateOne) SetUserID(u uint32) *AdminOperationLogUpdateOne {
	aoluo.mutation.ResetUserID()
	aoluo.mutation.SetUserID(u)
	return aoluo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableUserID(u *uint32) *AdminOperationLogUpdateOne {
	if u != nil {
		aoluo.SetUserID(*u)
	}
	return aoluo
}

// AddUserID adds u to the "user_id" field.
func (aoluo *AdminOperationLogUpdateOne) AddUserID(u int32) *AdminOperationLogUpdateOne {
	aoluo.mutation.AddUserID(u)
	return aoluo
}

// ClearUserID clears the value of the "user_id" field.
func (aoluo *AdminOperationLogUpdateOne) ClearUserID() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearUserID()
	return aoluo
}

// SetUsername sets the "username" field.
func (aoluo *AdminOperationLogUpdateOne) SetUsername(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetUsername(s)
	return aoluo
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableUsername(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetUsername(*s)
	}
	return aoluo
}

// ClearUsername clears the value of the "username" field.
func (aoluo *AdminOperationLogUpdateOne) ClearUsername() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearUsername()
	return aoluo
}

// SetClientIP sets the "client_ip" field.
func (aoluo *AdminOperationLogUpdateOne) SetClientIP(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetClientIP(s)
	return aoluo
}

// SetNillableClientIP sets the "client_ip" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableClientIP(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetClientIP(*s)
	}
	return aoluo
}

// ClearClientIP clears the value of the "client_ip" field.
func (aoluo *AdminOperationLogUpdateOne) ClearClientIP() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearClientIP()
	return aoluo
}

// SetStatusCode sets the "status_code" field.
func (aoluo *AdminOperationLogUpdateOne) SetStatusCode(i int32) *AdminOperationLogUpdateOne {
	aoluo.mutation.ResetStatusCode()
	aoluo.mutation.SetStatusCode(i)
	return aoluo
}

// SetNillableStatusCode sets the "status_code" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableStatusCode(i *int32) *AdminOperationLogUpdateOne {
	if i != nil {
		aoluo.SetStatusCode(*i)
	}
	return aoluo
}

// AddStatusCode adds i to the "status_code" field.
func (aoluo *AdminOperationLogUpdateOne) AddStatusCode(i int32) *AdminOperationLogUpdateOne {
	aoluo.mutation.AddStatusCode(i)
	return aoluo
}

// ClearStatusCode clears the value of the "status_code" field.
func (aoluo *AdminOperationLogUpdateOne) ClearStatusCode() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearStatusCode()
	return aoluo
}

// SetReason sets the "reason" field.
func (aoluo *AdminOperationLogUpdateOne) SetReason(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetReason(s)
	return aoluo
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableReason(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetReason(*s)
	}
	return aoluo
}

// ClearReason clears the value of the "reason" field.
func (aoluo *AdminOperationLogUpdateOne) ClearReason() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearReason()
	return aoluo
}

// SetSuccess sets the "success" field.
func (aoluo *AdminOperationLogUpdateOne) SetSuccess(b bool) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetSuccess(b)
	return aoluo
}

// SetNillableSuccess sets the "success" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableSuccess(b *bool) *AdminOperationLogUpdateOne {
	if b != nil {
		aoluo.SetSuccess(*b)
	}
	return aoluo
}

// ClearSuccess clears the value of the "success" field.
func (aoluo *AdminOperationLogUpdateOne) ClearSuccess() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearSuccess()
	return aoluo
}

// SetLocation sets the "location" field.
func (aoluo *AdminOperationLogUpdateOne) SetLocation(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetLocation(s)
	return aoluo
}

// SetNillableLocation sets the "location" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableLocation(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetLocation(*s)
	}
	return aoluo
}

// ClearLocation clears the value of the "location" field.
func (aoluo *AdminOperationLogUpdateOne) ClearLocation() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearLocation()
	return aoluo
}

// SetUserAgent sets the "user_agent" field.
func (aoluo *AdminOperationLogUpdateOne) SetUserAgent(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetUserAgent(s)
	return aoluo
}

// SetNillableUserAgent sets the "user_agent" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableUserAgent(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetUserAgent(*s)
	}
	return aoluo
}

// ClearUserAgent clears the value of the "user_agent" field.
func (aoluo *AdminOperationLogUpdateOne) ClearUserAgent() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearUserAgent()
	return aoluo
}

// SetBrowserName sets the "browser_name" field.
func (aoluo *AdminOperationLogUpdateOne) SetBrowserName(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetBrowserName(s)
	return aoluo
}

// SetNillableBrowserName sets the "browser_name" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableBrowserName(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetBrowserName(*s)
	}
	return aoluo
}

// ClearBrowserName clears the value of the "browser_name" field.
func (aoluo *AdminOperationLogUpdateOne) ClearBrowserName() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearBrowserName()
	return aoluo
}

// SetBrowserVersion sets the "browser_version" field.
func (aoluo *AdminOperationLogUpdateOne) SetBrowserVersion(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetBrowserVersion(s)
	return aoluo
}

// SetNillableBrowserVersion sets the "browser_version" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableBrowserVersion(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetBrowserVersion(*s)
	}
	return aoluo
}

// ClearBrowserVersion clears the value of the "browser_version" field.
func (aoluo *AdminOperationLogUpdateOne) ClearBrowserVersion() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearBrowserVersion()
	return aoluo
}

// SetClientID sets the "client_id" field.
func (aoluo *AdminOperationLogUpdateOne) SetClientID(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetClientID(s)
	return aoluo
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableClientID(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetClientID(*s)
	}
	return aoluo
}

// ClearClientID clears the value of the "client_id" field.
func (aoluo *AdminOperationLogUpdateOne) ClearClientID() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearClientID()
	return aoluo
}

// SetClientName sets the "client_name" field.
func (aoluo *AdminOperationLogUpdateOne) SetClientName(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetClientName(s)
	return aoluo
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableClientName(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetClientName(*s)
	}
	return aoluo
}

// ClearClientName clears the value of the "client_name" field.
func (aoluo *AdminOperationLogUpdateOne) ClearClientName() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearClientName()
	return aoluo
}

// SetOsName sets the "os_name" field.
func (aoluo *AdminOperationLogUpdateOne) SetOsName(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetOsName(s)
	return aoluo
}

// SetNillableOsName sets the "os_name" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableOsName(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetOsName(*s)
	}
	return aoluo
}

// ClearOsName clears the value of the "os_name" field.
func (aoluo *AdminOperationLogUpdateOne) ClearOsName() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearOsName()
	return aoluo
}

// SetOsVersion sets the "os_version" field.
func (aoluo *AdminOperationLogUpdateOne) SetOsVersion(s string) *AdminOperationLogUpdateOne {
	aoluo.mutation.SetOsVersion(s)
	return aoluo
}

// SetNillableOsVersion sets the "os_version" field if the given value is not nil.
func (aoluo *AdminOperationLogUpdateOne) SetNillableOsVersion(s *string) *AdminOperationLogUpdateOne {
	if s != nil {
		aoluo.SetOsVersion(*s)
	}
	return aoluo
}

// ClearOsVersion clears the value of the "os_version" field.
func (aoluo *AdminOperationLogUpdateOne) ClearOsVersion() *AdminOperationLogUpdateOne {
	aoluo.mutation.ClearOsVersion()
	return aoluo
}

// Mutation returns the AdminOperationLogMutation object of the builder.
func (aoluo *AdminOperationLogUpdateOne) Mutation() *AdminOperationLogMutation {
	return aoluo.mutation
}

// Where appends a list predicates to the AdminOperationLogUpdate builder.
func (aoluo *AdminOperationLogUpdateOne) Where(ps ...predicate.AdminOperationLog) *AdminOperationLogUpdateOne {
	aoluo.mutation.Where(ps...)
	return aoluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aoluo *AdminOperationLogUpdateOne) Select(field string, fields ...string) *AdminOperationLogUpdateOne {
	aoluo.fields = append([]string{field}, fields...)
	return aoluo
}

// Save executes the query and returns the updated AdminOperationLog entity.
func (aoluo *AdminOperationLogUpdateOne) Save(ctx context.Context) (*AdminOperationLog, error) {
	return withHooks(ctx, aoluo.sqlSave, aoluo.mutation, aoluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aoluo *AdminOperationLogUpdateOne) SaveX(ctx context.Context) *AdminOperationLog {
	node, err := aoluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aoluo *AdminOperationLogUpdateOne) Exec(ctx context.Context) error {
	_, err := aoluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aoluo *AdminOperationLogUpdateOne) ExecX(ctx context.Context) {
	if err := aoluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aoluo *AdminOperationLogUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AdminOperationLogUpdateOne {
	aoluo.modifiers = append(aoluo.modifiers, modifiers...)
	return aoluo
}

func (aoluo *AdminOperationLogUpdateOne) sqlSave(ctx context.Context) (_node *AdminOperationLog, err error) {
	_spec := sqlgraph.NewUpdateSpec(adminoperationlog.Table, adminoperationlog.Columns, sqlgraph.NewFieldSpec(adminoperationlog.FieldID, field.TypeUint32))
	id, ok := aoluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AdminOperationLog.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aoluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, adminoperationlog.FieldID)
		for _, f := range fields {
			if !adminoperationlog.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != adminoperationlog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aoluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if aoluo.mutation.CreateTimeCleared() {
		_spec.ClearField(adminoperationlog.FieldCreateTime, field.TypeTime)
	}
	if value, ok := aoluo.mutation.RequestID(); ok {
		_spec.SetField(adminoperationlog.FieldRequestID, field.TypeString, value)
	}
	if aoluo.mutation.RequestIDCleared() {
		_spec.ClearField(adminoperationlog.FieldRequestID, field.TypeString)
	}
	if value, ok := aoluo.mutation.Method(); ok {
		_spec.SetField(adminoperationlog.FieldMethod, field.TypeString, value)
	}
	if aoluo.mutation.MethodCleared() {
		_spec.ClearField(adminoperationlog.FieldMethod, field.TypeString)
	}
	if value, ok := aoluo.mutation.Operation(); ok {
		_spec.SetField(adminoperationlog.FieldOperation, field.TypeString, value)
	}
	if aoluo.mutation.OperationCleared() {
		_spec.ClearField(adminoperationlog.FieldOperation, field.TypeString)
	}
	if value, ok := aoluo.mutation.Path(); ok {
		_spec.SetField(adminoperationlog.FieldPath, field.TypeString, value)
	}
	if aoluo.mutation.PathCleared() {
		_spec.ClearField(adminoperationlog.FieldPath, field.TypeString)
	}
	if value, ok := aoluo.mutation.Referer(); ok {
		_spec.SetField(adminoperationlog.FieldReferer, field.TypeString, value)
	}
	if aoluo.mutation.RefererCleared() {
		_spec.ClearField(adminoperationlog.FieldReferer, field.TypeString)
	}
	if value, ok := aoluo.mutation.RequestURI(); ok {
		_spec.SetField(adminoperationlog.FieldRequestURI, field.TypeString, value)
	}
	if aoluo.mutation.RequestURICleared() {
		_spec.ClearField(adminoperationlog.FieldRequestURI, field.TypeString)
	}
	if value, ok := aoluo.mutation.RequestBody(); ok {
		_spec.SetField(adminoperationlog.FieldRequestBody, field.TypeString, value)
	}
	if aoluo.mutation.RequestBodyCleared() {
		_spec.ClearField(adminoperationlog.FieldRequestBody, field.TypeString)
	}
	if value, ok := aoluo.mutation.RequestHeader(); ok {
		_spec.SetField(adminoperationlog.FieldRequestHeader, field.TypeString, value)
	}
	if aoluo.mutation.RequestHeaderCleared() {
		_spec.ClearField(adminoperationlog.FieldRequestHeader, field.TypeString)
	}
	if value, ok := aoluo.mutation.Response(); ok {
		_spec.SetField(adminoperationlog.FieldResponse, field.TypeString, value)
	}
	if aoluo.mutation.ResponseCleared() {
		_spec.ClearField(adminoperationlog.FieldResponse, field.TypeString)
	}
	if value, ok := aoluo.mutation.CostTime(); ok {
		_spec.SetField(adminoperationlog.FieldCostTime, field.TypeFloat64, value)
	}
	if value, ok := aoluo.mutation.AddedCostTime(); ok {
		_spec.AddField(adminoperationlog.FieldCostTime, field.TypeFloat64, value)
	}
	if aoluo.mutation.CostTimeCleared() {
		_spec.ClearField(adminoperationlog.FieldCostTime, field.TypeFloat64)
	}
	if value, ok := aoluo.mutation.UserID(); ok {
		_spec.SetField(adminoperationlog.FieldUserID, field.TypeUint32, value)
	}
	if value, ok := aoluo.mutation.AddedUserID(); ok {
		_spec.AddField(adminoperationlog.FieldUserID, field.TypeUint32, value)
	}
	if aoluo.mutation.UserIDCleared() {
		_spec.ClearField(adminoperationlog.FieldUserID, field.TypeUint32)
	}
	if value, ok := aoluo.mutation.Username(); ok {
		_spec.SetField(adminoperationlog.FieldUsername, field.TypeString, value)
	}
	if aoluo.mutation.UsernameCleared() {
		_spec.ClearField(adminoperationlog.FieldUsername, field.TypeString)
	}
	if value, ok := aoluo.mutation.ClientIP(); ok {
		_spec.SetField(adminoperationlog.FieldClientIP, field.TypeString, value)
	}
	if aoluo.mutation.ClientIPCleared() {
		_spec.ClearField(adminoperationlog.FieldClientIP, field.TypeString)
	}
	if value, ok := aoluo.mutation.StatusCode(); ok {
		_spec.SetField(adminoperationlog.FieldStatusCode, field.TypeInt32, value)
	}
	if value, ok := aoluo.mutation.AddedStatusCode(); ok {
		_spec.AddField(adminoperationlog.FieldStatusCode, field.TypeInt32, value)
	}
	if aoluo.mutation.StatusCodeCleared() {
		_spec.ClearField(adminoperationlog.FieldStatusCode, field.TypeInt32)
	}
	if value, ok := aoluo.mutation.Reason(); ok {
		_spec.SetField(adminoperationlog.FieldReason, field.TypeString, value)
	}
	if aoluo.mutation.ReasonCleared() {
		_spec.ClearField(adminoperationlog.FieldReason, field.TypeString)
	}
	if value, ok := aoluo.mutation.Success(); ok {
		_spec.SetField(adminoperationlog.FieldSuccess, field.TypeBool, value)
	}
	if aoluo.mutation.SuccessCleared() {
		_spec.ClearField(adminoperationlog.FieldSuccess, field.TypeBool)
	}
	if value, ok := aoluo.mutation.Location(); ok {
		_spec.SetField(adminoperationlog.FieldLocation, field.TypeString, value)
	}
	if aoluo.mutation.LocationCleared() {
		_spec.ClearField(adminoperationlog.FieldLocation, field.TypeString)
	}
	if value, ok := aoluo.mutation.UserAgent(); ok {
		_spec.SetField(adminoperationlog.FieldUserAgent, field.TypeString, value)
	}
	if aoluo.mutation.UserAgentCleared() {
		_spec.ClearField(adminoperationlog.FieldUserAgent, field.TypeString)
	}
	if value, ok := aoluo.mutation.BrowserName(); ok {
		_spec.SetField(adminoperationlog.FieldBrowserName, field.TypeString, value)
	}
	if aoluo.mutation.BrowserNameCleared() {
		_spec.ClearField(adminoperationlog.FieldBrowserName, field.TypeString)
	}
	if value, ok := aoluo.mutation.BrowserVersion(); ok {
		_spec.SetField(adminoperationlog.FieldBrowserVersion, field.TypeString, value)
	}
	if aoluo.mutation.BrowserVersionCleared() {
		_spec.ClearField(adminoperationlog.FieldBrowserVersion, field.TypeString)
	}
	if value, ok := aoluo.mutation.ClientID(); ok {
		_spec.SetField(adminoperationlog.FieldClientID, field.TypeString, value)
	}
	if aoluo.mutation.ClientIDCleared() {
		_spec.ClearField(adminoperationlog.FieldClientID, field.TypeString)
	}
	if value, ok := aoluo.mutation.ClientName(); ok {
		_spec.SetField(adminoperationlog.FieldClientName, field.TypeString, value)
	}
	if aoluo.mutation.ClientNameCleared() {
		_spec.ClearField(adminoperationlog.FieldClientName, field.TypeString)
	}
	if value, ok := aoluo.mutation.OsName(); ok {
		_spec.SetField(adminoperationlog.FieldOsName, field.TypeString, value)
	}
	if aoluo.mutation.OsNameCleared() {
		_spec.ClearField(adminoperationlog.FieldOsName, field.TypeString)
	}
	if value, ok := aoluo.mutation.OsVersion(); ok {
		_spec.SetField(adminoperationlog.FieldOsVersion, field.TypeString, value)
	}
	if aoluo.mutation.OsVersionCleared() {
		_spec.ClearField(adminoperationlog.FieldOsVersion, field.TypeString)
	}
	_spec.AddModifiers(aoluo.modifiers...)
	_node = &AdminOperationLog{config: aoluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aoluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{adminoperationlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aoluo.mutation.done = true
	return _node, nil
}
