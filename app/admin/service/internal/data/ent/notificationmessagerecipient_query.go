// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagerecipient"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageRecipientQuery is the builder for querying NotificationMessageRecipient entities.
type NotificationMessageRecipientQuery struct {
	config
	ctx        *QueryContext
	order      []notificationmessagerecipient.OrderOption
	inters     []Interceptor
	predicates []predicate.NotificationMessageRecipient
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NotificationMessageRecipientQuery builder.
func (nmrq *NotificationMessageRecipientQuery) Where(ps ...predicate.NotificationMessageRecipient) *NotificationMessageRecipientQuery {
	nmrq.predicates = append(nmrq.predicates, ps...)
	return nmrq
}

// Limit the number of records to be returned by this query.
func (nmrq *NotificationMessageRecipientQuery) Limit(limit int) *NotificationMessageRecipientQuery {
	nmrq.ctx.Limit = &limit
	return nmrq
}

// Offset to start from.
func (nmrq *NotificationMessageRecipientQuery) Offset(offset int) *NotificationMessageRecipientQuery {
	nmrq.ctx.Offset = &offset
	return nmrq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (nmrq *NotificationMessageRecipientQuery) Unique(unique bool) *NotificationMessageRecipientQuery {
	nmrq.ctx.Unique = &unique
	return nmrq
}

// Order specifies how the records should be ordered.
func (nmrq *NotificationMessageRecipientQuery) Order(o ...notificationmessagerecipient.OrderOption) *NotificationMessageRecipientQuery {
	nmrq.order = append(nmrq.order, o...)
	return nmrq
}

// First returns the first NotificationMessageRecipient entity from the query.
// Returns a *NotFoundError when no NotificationMessageRecipient was found.
func (nmrq *NotificationMessageRecipientQuery) First(ctx context.Context) (*NotificationMessageRecipient, error) {
	nodes, err := nmrq.Limit(1).All(setContextOp(ctx, nmrq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{notificationmessagerecipient.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) FirstX(ctx context.Context) *NotificationMessageRecipient {
	node, err := nmrq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first NotificationMessageRecipient ID from the query.
// Returns a *NotFoundError when no NotificationMessageRecipient ID was found.
func (nmrq *NotificationMessageRecipientQuery) FirstID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = nmrq.Limit(1).IDs(setContextOp(ctx, nmrq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{notificationmessagerecipient.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) FirstIDX(ctx context.Context) uint32 {
	id, err := nmrq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single NotificationMessageRecipient entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one NotificationMessageRecipient entity is found.
// Returns a *NotFoundError when no NotificationMessageRecipient entities are found.
func (nmrq *NotificationMessageRecipientQuery) Only(ctx context.Context) (*NotificationMessageRecipient, error) {
	nodes, err := nmrq.Limit(2).All(setContextOp(ctx, nmrq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{notificationmessagerecipient.Label}
	default:
		return nil, &NotSingularError{notificationmessagerecipient.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) OnlyX(ctx context.Context) *NotificationMessageRecipient {
	node, err := nmrq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only NotificationMessageRecipient ID in the query.
// Returns a *NotSingularError when more than one NotificationMessageRecipient ID is found.
// Returns a *NotFoundError when no entities are found.
func (nmrq *NotificationMessageRecipientQuery) OnlyID(ctx context.Context) (id uint32, err error) {
	var ids []uint32
	if ids, err = nmrq.Limit(2).IDs(setContextOp(ctx, nmrq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{notificationmessagerecipient.Label}
	default:
		err = &NotSingularError{notificationmessagerecipient.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) OnlyIDX(ctx context.Context) uint32 {
	id, err := nmrq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of NotificationMessageRecipients.
func (nmrq *NotificationMessageRecipientQuery) All(ctx context.Context) ([]*NotificationMessageRecipient, error) {
	ctx = setContextOp(ctx, nmrq.ctx, ent.OpQueryAll)
	if err := nmrq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*NotificationMessageRecipient, *NotificationMessageRecipientQuery]()
	return withInterceptors[[]*NotificationMessageRecipient](ctx, nmrq, qr, nmrq.inters)
}

// AllX is like All, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) AllX(ctx context.Context) []*NotificationMessageRecipient {
	nodes, err := nmrq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of NotificationMessageRecipient IDs.
func (nmrq *NotificationMessageRecipientQuery) IDs(ctx context.Context) (ids []uint32, err error) {
	if nmrq.ctx.Unique == nil && nmrq.path != nil {
		nmrq.Unique(true)
	}
	ctx = setContextOp(ctx, nmrq.ctx, ent.OpQueryIDs)
	if err = nmrq.Select(notificationmessagerecipient.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) IDsX(ctx context.Context) []uint32 {
	ids, err := nmrq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (nmrq *NotificationMessageRecipientQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, nmrq.ctx, ent.OpQueryCount)
	if err := nmrq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, nmrq, querierCount[*NotificationMessageRecipientQuery](), nmrq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) CountX(ctx context.Context) int {
	count, err := nmrq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (nmrq *NotificationMessageRecipientQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, nmrq.ctx, ent.OpQueryExist)
	switch _, err := nmrq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (nmrq *NotificationMessageRecipientQuery) ExistX(ctx context.Context) bool {
	exist, err := nmrq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NotificationMessageRecipientQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (nmrq *NotificationMessageRecipientQuery) Clone() *NotificationMessageRecipientQuery {
	if nmrq == nil {
		return nil
	}
	return &NotificationMessageRecipientQuery{
		config:     nmrq.config,
		ctx:        nmrq.ctx.Clone(),
		order:      append([]notificationmessagerecipient.OrderOption{}, nmrq.order...),
		inters:     append([]Interceptor{}, nmrq.inters...),
		predicates: append([]predicate.NotificationMessageRecipient{}, nmrq.predicates...),
		// clone intermediate query.
		sql:       nmrq.sql.Clone(),
		path:      nmrq.path,
		modifiers: append([]func(*sql.Selector){}, nmrq.modifiers...),
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.NotificationMessageRecipient.Query().
//		GroupBy(notificationmessagerecipient.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (nmrq *NotificationMessageRecipientQuery) GroupBy(field string, fields ...string) *NotificationMessageRecipientGroupBy {
	nmrq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NotificationMessageRecipientGroupBy{build: nmrq}
	grbuild.flds = &nmrq.ctx.Fields
	grbuild.label = notificationmessagerecipient.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.NotificationMessageRecipient.Query().
//		Select(notificationmessagerecipient.FieldCreateTime).
//		Scan(ctx, &v)
func (nmrq *NotificationMessageRecipientQuery) Select(fields ...string) *NotificationMessageRecipientSelect {
	nmrq.ctx.Fields = append(nmrq.ctx.Fields, fields...)
	sbuild := &NotificationMessageRecipientSelect{NotificationMessageRecipientQuery: nmrq}
	sbuild.label = notificationmessagerecipient.Label
	sbuild.flds, sbuild.scan = &nmrq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NotificationMessageRecipientSelect configured with the given aggregations.
func (nmrq *NotificationMessageRecipientQuery) Aggregate(fns ...AggregateFunc) *NotificationMessageRecipientSelect {
	return nmrq.Select().Aggregate(fns...)
}

func (nmrq *NotificationMessageRecipientQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range nmrq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, nmrq); err != nil {
				return err
			}
		}
	}
	for _, f := range nmrq.ctx.Fields {
		if !notificationmessagerecipient.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if nmrq.path != nil {
		prev, err := nmrq.path(ctx)
		if err != nil {
			return err
		}
		nmrq.sql = prev
	}
	return nil
}

func (nmrq *NotificationMessageRecipientQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*NotificationMessageRecipient, error) {
	var (
		nodes = []*NotificationMessageRecipient{}
		_spec = nmrq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*NotificationMessageRecipient).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &NotificationMessageRecipient{config: nmrq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(nmrq.modifiers) > 0 {
		_spec.Modifiers = nmrq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, nmrq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (nmrq *NotificationMessageRecipientQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := nmrq.querySpec()
	if len(nmrq.modifiers) > 0 {
		_spec.Modifiers = nmrq.modifiers
	}
	_spec.Node.Columns = nmrq.ctx.Fields
	if len(nmrq.ctx.Fields) > 0 {
		_spec.Unique = nmrq.ctx.Unique != nil && *nmrq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, nmrq.driver, _spec)
}

func (nmrq *NotificationMessageRecipientQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(notificationmessagerecipient.Table, notificationmessagerecipient.Columns, sqlgraph.NewFieldSpec(notificationmessagerecipient.FieldID, field.TypeUint32))
	_spec.From = nmrq.sql
	if unique := nmrq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if nmrq.path != nil {
		_spec.Unique = true
	}
	if fields := nmrq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationmessagerecipient.FieldID)
		for i := range fields {
			if fields[i] != notificationmessagerecipient.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := nmrq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := nmrq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := nmrq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := nmrq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (nmrq *NotificationMessageRecipientQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(nmrq.driver.Dialect())
	t1 := builder.Table(notificationmessagerecipient.Table)
	columns := nmrq.ctx.Fields
	if len(columns) == 0 {
		columns = notificationmessagerecipient.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if nmrq.sql != nil {
		selector = nmrq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if nmrq.ctx.Unique != nil && *nmrq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range nmrq.modifiers {
		m(selector)
	}
	for _, p := range nmrq.predicates {
		p(selector)
	}
	for _, p := range nmrq.order {
		p(selector)
	}
	if offset := nmrq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := nmrq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (nmrq *NotificationMessageRecipientQuery) ForUpdate(opts ...sql.LockOption) *NotificationMessageRecipientQuery {
	if nmrq.driver.Dialect() == dialect.Postgres {
		nmrq.Unique(false)
	}
	nmrq.modifiers = append(nmrq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return nmrq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (nmrq *NotificationMessageRecipientQuery) ForShare(opts ...sql.LockOption) *NotificationMessageRecipientQuery {
	if nmrq.driver.Dialect() == dialect.Postgres {
		nmrq.Unique(false)
	}
	nmrq.modifiers = append(nmrq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return nmrq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (nmrq *NotificationMessageRecipientQuery) Modify(modifiers ...func(s *sql.Selector)) *NotificationMessageRecipientSelect {
	nmrq.modifiers = append(nmrq.modifiers, modifiers...)
	return nmrq.Select()
}

// NotificationMessageRecipientGroupBy is the group-by builder for NotificationMessageRecipient entities.
type NotificationMessageRecipientGroupBy struct {
	selector
	build *NotificationMessageRecipientQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (nmrgb *NotificationMessageRecipientGroupBy) Aggregate(fns ...AggregateFunc) *NotificationMessageRecipientGroupBy {
	nmrgb.fns = append(nmrgb.fns, fns...)
	return nmrgb
}

// Scan applies the selector query and scans the result into the given value.
func (nmrgb *NotificationMessageRecipientGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nmrgb.build.ctx, ent.OpQueryGroupBy)
	if err := nmrgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationMessageRecipientQuery, *NotificationMessageRecipientGroupBy](ctx, nmrgb.build, nmrgb, nmrgb.build.inters, v)
}

func (nmrgb *NotificationMessageRecipientGroupBy) sqlScan(ctx context.Context, root *NotificationMessageRecipientQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(nmrgb.fns))
	for _, fn := range nmrgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*nmrgb.flds)+len(nmrgb.fns))
		for _, f := range *nmrgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*nmrgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nmrgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NotificationMessageRecipientSelect is the builder for selecting fields of NotificationMessageRecipient entities.
type NotificationMessageRecipientSelect struct {
	*NotificationMessageRecipientQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (nmrs *NotificationMessageRecipientSelect) Aggregate(fns ...AggregateFunc) *NotificationMessageRecipientSelect {
	nmrs.fns = append(nmrs.fns, fns...)
	return nmrs
}

// Scan applies the selector query and scans the result into the given value.
func (nmrs *NotificationMessageRecipientSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nmrs.ctx, ent.OpQuerySelect)
	if err := nmrs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationMessageRecipientQuery, *NotificationMessageRecipientSelect](ctx, nmrs.NotificationMessageRecipientQuery, nmrs, nmrs.inters, v)
}

func (nmrs *NotificationMessageRecipientSelect) sqlScan(ctx context.Context, root *NotificationMessageRecipientQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(nmrs.fns))
	for _, fn := range nmrs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*nmrs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nmrs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (nmrs *NotificationMessageRecipientSelect) Modify(modifiers ...func(s *sql.Selector)) *NotificationMessageRecipientSelect {
	nmrs.modifiers = append(nmrs.modifiers, modifiers...)
	return nmrs
}
