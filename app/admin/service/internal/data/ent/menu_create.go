// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	servicev1 "kratos-admin/api/gen/go/admin/service/v1"
	"kratos-admin/app/admin/service/internal/data/ent/menu"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MenuCreate is the builder for creating a Menu entity.
type MenuCreate struct {
	config
	mutation *MenuMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetStatus sets the "status" field.
func (mc *MenuCreate) SetStatus(m menu.Status) *MenuCreate {
	mc.mutation.SetStatus(m)
	return mc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (mc *MenuCreate) SetNillableStatus(m *menu.Status) *MenuCreate {
	if m != nil {
		mc.SetStatus(*m)
	}
	return mc
}

// SetCreateTime sets the "create_time" field.
func (mc *MenuCreate) SetCreateTime(t time.Time) *MenuCreate {
	mc.mutation.SetCreateTime(t)
	return mc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (mc *MenuCreate) SetNillableCreateTime(t *time.Time) *MenuCreate {
	if t != nil {
		mc.SetCreateTime(*t)
	}
	return mc
}

// SetUpdateTime sets the "update_time" field.
func (mc *MenuCreate) SetUpdateTime(t time.Time) *MenuCreate {
	mc.mutation.SetUpdateTime(t)
	return mc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (mc *MenuCreate) SetNillableUpdateTime(t *time.Time) *MenuCreate {
	if t != nil {
		mc.SetUpdateTime(*t)
	}
	return mc
}

// SetDeleteTime sets the "delete_time" field.
func (mc *MenuCreate) SetDeleteTime(t time.Time) *MenuCreate {
	mc.mutation.SetDeleteTime(t)
	return mc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (mc *MenuCreate) SetNillableDeleteTime(t *time.Time) *MenuCreate {
	if t != nil {
		mc.SetDeleteTime(*t)
	}
	return mc
}

// SetCreateBy sets the "create_by" field.
func (mc *MenuCreate) SetCreateBy(u uint32) *MenuCreate {
	mc.mutation.SetCreateBy(u)
	return mc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (mc *MenuCreate) SetNillableCreateBy(u *uint32) *MenuCreate {
	if u != nil {
		mc.SetCreateBy(*u)
	}
	return mc
}

// SetUpdateBy sets the "update_by" field.
func (mc *MenuCreate) SetUpdateBy(u uint32) *MenuCreate {
	mc.mutation.SetUpdateBy(u)
	return mc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (mc *MenuCreate) SetNillableUpdateBy(u *uint32) *MenuCreate {
	if u != nil {
		mc.SetUpdateBy(*u)
	}
	return mc
}

// SetRemark sets the "remark" field.
func (mc *MenuCreate) SetRemark(s string) *MenuCreate {
	mc.mutation.SetRemark(s)
	return mc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (mc *MenuCreate) SetNillableRemark(s *string) *MenuCreate {
	if s != nil {
		mc.SetRemark(*s)
	}
	return mc
}

// SetParentID sets the "parent_id" field.
func (mc *MenuCreate) SetParentID(i int32) *MenuCreate {
	mc.mutation.SetParentID(i)
	return mc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (mc *MenuCreate) SetNillableParentID(i *int32) *MenuCreate {
	if i != nil {
		mc.SetParentID(*i)
	}
	return mc
}

// SetType sets the "type" field.
func (mc *MenuCreate) SetType(m menu.Type) *MenuCreate {
	mc.mutation.SetType(m)
	return mc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (mc *MenuCreate) SetNillableType(m *menu.Type) *MenuCreate {
	if m != nil {
		mc.SetType(*m)
	}
	return mc
}

// SetPath sets the "path" field.
func (mc *MenuCreate) SetPath(s string) *MenuCreate {
	mc.mutation.SetPath(s)
	return mc
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (mc *MenuCreate) SetNillablePath(s *string) *MenuCreate {
	if s != nil {
		mc.SetPath(*s)
	}
	return mc
}

// SetRedirect sets the "redirect" field.
func (mc *MenuCreate) SetRedirect(s string) *MenuCreate {
	mc.mutation.SetRedirect(s)
	return mc
}

// SetNillableRedirect sets the "redirect" field if the given value is not nil.
func (mc *MenuCreate) SetNillableRedirect(s *string) *MenuCreate {
	if s != nil {
		mc.SetRedirect(*s)
	}
	return mc
}

// SetAlias sets the "alias" field.
func (mc *MenuCreate) SetAlias(s string) *MenuCreate {
	mc.mutation.SetAlias(s)
	return mc
}

// SetNillableAlias sets the "alias" field if the given value is not nil.
func (mc *MenuCreate) SetNillableAlias(s *string) *MenuCreate {
	if s != nil {
		mc.SetAlias(*s)
	}
	return mc
}

// SetName sets the "name" field.
func (mc *MenuCreate) SetName(s string) *MenuCreate {
	mc.mutation.SetName(s)
	return mc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (mc *MenuCreate) SetNillableName(s *string) *MenuCreate {
	if s != nil {
		mc.SetName(*s)
	}
	return mc
}

// SetComponent sets the "component" field.
func (mc *MenuCreate) SetComponent(s string) *MenuCreate {
	mc.mutation.SetComponent(s)
	return mc
}

// SetNillableComponent sets the "component" field if the given value is not nil.
func (mc *MenuCreate) SetNillableComponent(s *string) *MenuCreate {
	if s != nil {
		mc.SetComponent(*s)
	}
	return mc
}

// SetMeta sets the "meta" field.
func (mc *MenuCreate) SetMeta(sm *servicev1.RouteMeta) *MenuCreate {
	mc.mutation.SetMeta(sm)
	return mc
}

// SetID sets the "id" field.
func (mc *MenuCreate) SetID(i int32) *MenuCreate {
	mc.mutation.SetID(i)
	return mc
}

// SetParent sets the "parent" edge to the Menu entity.
func (mc *MenuCreate) SetParent(m *Menu) *MenuCreate {
	return mc.SetParentID(m.ID)
}

// AddChildIDs adds the "children" edge to the Menu entity by IDs.
func (mc *MenuCreate) AddChildIDs(ids ...int32) *MenuCreate {
	mc.mutation.AddChildIDs(ids...)
	return mc
}

// AddChildren adds the "children" edges to the Menu entity.
func (mc *MenuCreate) AddChildren(m ...*Menu) *MenuCreate {
	ids := make([]int32, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return mc.AddChildIDs(ids...)
}

// Mutation returns the MenuMutation object of the builder.
func (mc *MenuCreate) Mutation() *MenuMutation {
	return mc.mutation
}

// Save creates the Menu in the database.
func (mc *MenuCreate) Save(ctx context.Context) (*Menu, error) {
	mc.defaults()
	return withHooks(ctx, mc.sqlSave, mc.mutation, mc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (mc *MenuCreate) SaveX(ctx context.Context) *Menu {
	v, err := mc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mc *MenuCreate) Exec(ctx context.Context) error {
	_, err := mc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mc *MenuCreate) ExecX(ctx context.Context) {
	if err := mc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mc *MenuCreate) defaults() {
	if _, ok := mc.mutation.Status(); !ok {
		v := menu.DefaultStatus
		mc.mutation.SetStatus(v)
	}
	if _, ok := mc.mutation.Remark(); !ok {
		v := menu.DefaultRemark
		mc.mutation.SetRemark(v)
	}
	if _, ok := mc.mutation.GetType(); !ok {
		v := menu.DefaultType
		mc.mutation.SetType(v)
	}
	if _, ok := mc.mutation.Path(); !ok {
		v := menu.DefaultPath
		mc.mutation.SetPath(v)
	}
	if _, ok := mc.mutation.Component(); !ok {
		v := menu.DefaultComponent
		mc.mutation.SetComponent(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mc *MenuCreate) check() error {
	if v, ok := mc.mutation.Status(); ok {
		if err := menu.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Menu.status": %w`, err)}
		}
	}
	if v, ok := mc.mutation.GetType(); ok {
		if err := menu.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Menu.type": %w`, err)}
		}
	}
	if v, ok := mc.mutation.Meta(); ok {
		if err := v.Validate(); err != nil {
			return &ValidationError{Name: "meta", err: fmt.Errorf(`ent: validator failed for field "Menu.meta": %w`, err)}
		}
	}
	if v, ok := mc.mutation.ID(); ok {
		if err := menu.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Menu.id": %w`, err)}
		}
	}
	return nil
}

func (mc *MenuCreate) sqlSave(ctx context.Context) (*Menu, error) {
	if err := mc.check(); err != nil {
		return nil, err
	}
	_node, _spec := mc.createSpec()
	if err := sqlgraph.CreateNode(ctx, mc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int32(id)
	}
	mc.mutation.id = &_node.ID
	mc.mutation.done = true
	return _node, nil
}

func (mc *MenuCreate) createSpec() (*Menu, *sqlgraph.CreateSpec) {
	var (
		_node = &Menu{config: mc.config}
		_spec = sqlgraph.NewCreateSpec(menu.Table, sqlgraph.NewFieldSpec(menu.FieldID, field.TypeInt32))
	)
	_spec.OnConflict = mc.conflict
	if id, ok := mc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := mc.mutation.Status(); ok {
		_spec.SetField(menu.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := mc.mutation.CreateTime(); ok {
		_spec.SetField(menu.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := mc.mutation.UpdateTime(); ok {
		_spec.SetField(menu.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := mc.mutation.DeleteTime(); ok {
		_spec.SetField(menu.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := mc.mutation.CreateBy(); ok {
		_spec.SetField(menu.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := mc.mutation.UpdateBy(); ok {
		_spec.SetField(menu.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := mc.mutation.Remark(); ok {
		_spec.SetField(menu.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := mc.mutation.GetType(); ok {
		_spec.SetField(menu.FieldType, field.TypeEnum, value)
		_node.Type = &value
	}
	if value, ok := mc.mutation.Path(); ok {
		_spec.SetField(menu.FieldPath, field.TypeString, value)
		_node.Path = &value
	}
	if value, ok := mc.mutation.Redirect(); ok {
		_spec.SetField(menu.FieldRedirect, field.TypeString, value)
		_node.Redirect = &value
	}
	if value, ok := mc.mutation.Alias(); ok {
		_spec.SetField(menu.FieldAlias, field.TypeString, value)
		_node.Alias = &value
	}
	if value, ok := mc.mutation.Name(); ok {
		_spec.SetField(menu.FieldName, field.TypeString, value)
		_node.Name = &value
	}
	if value, ok := mc.mutation.Component(); ok {
		_spec.SetField(menu.FieldComponent, field.TypeString, value)
		_node.Component = &value
	}
	if value, ok := mc.mutation.Meta(); ok {
		_spec.SetField(menu.FieldMeta, field.TypeJSON, value)
		_node.Meta = value
	}
	if nodes := mc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeInt32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := mc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeInt32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Menu.Create().
//		SetStatus(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MenuUpsert) {
//			SetStatus(v+v).
//		}).
//		Exec(ctx)
func (mc *MenuCreate) OnConflict(opts ...sql.ConflictOption) *MenuUpsertOne {
	mc.conflict = opts
	return &MenuUpsertOne{
		create: mc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (mc *MenuCreate) OnConflictColumns(columns ...string) *MenuUpsertOne {
	mc.conflict = append(mc.conflict, sql.ConflictColumns(columns...))
	return &MenuUpsertOne{
		create: mc,
	}
}

type (
	// MenuUpsertOne is the builder for "upsert"-ing
	//  one Menu node.
	MenuUpsertOne struct {
		create *MenuCreate
	}

	// MenuUpsert is the "OnConflict" setter.
	MenuUpsert struct {
		*sql.UpdateSet
	}
)

// SetStatus sets the "status" field.
func (u *MenuUpsert) SetStatus(v menu.Status) *MenuUpsert {
	u.Set(menu.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *MenuUpsert) UpdateStatus() *MenuUpsert {
	u.SetExcluded(menu.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *MenuUpsert) ClearStatus() *MenuUpsert {
	u.SetNull(menu.FieldStatus)
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *MenuUpsert) SetUpdateTime(v time.Time) *MenuUpsert {
	u.Set(menu.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *MenuUpsert) UpdateUpdateTime() *MenuUpsert {
	u.SetExcluded(menu.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *MenuUpsert) ClearUpdateTime() *MenuUpsert {
	u.SetNull(menu.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *MenuUpsert) SetDeleteTime(v time.Time) *MenuUpsert {
	u.Set(menu.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *MenuUpsert) UpdateDeleteTime() *MenuUpsert {
	u.SetExcluded(menu.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *MenuUpsert) ClearDeleteTime() *MenuUpsert {
	u.SetNull(menu.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *MenuUpsert) SetCreateBy(v uint32) *MenuUpsert {
	u.Set(menu.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *MenuUpsert) UpdateCreateBy() *MenuUpsert {
	u.SetExcluded(menu.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *MenuUpsert) AddCreateBy(v uint32) *MenuUpsert {
	u.Add(menu.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *MenuUpsert) ClearCreateBy() *MenuUpsert {
	u.SetNull(menu.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *MenuUpsert) SetUpdateBy(v uint32) *MenuUpsert {
	u.Set(menu.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *MenuUpsert) UpdateUpdateBy() *MenuUpsert {
	u.SetExcluded(menu.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *MenuUpsert) AddUpdateBy(v uint32) *MenuUpsert {
	u.Add(menu.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *MenuUpsert) ClearUpdateBy() *MenuUpsert {
	u.SetNull(menu.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *MenuUpsert) SetRemark(v string) *MenuUpsert {
	u.Set(menu.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *MenuUpsert) UpdateRemark() *MenuUpsert {
	u.SetExcluded(menu.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *MenuUpsert) ClearRemark() *MenuUpsert {
	u.SetNull(menu.FieldRemark)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *MenuUpsert) SetParentID(v int32) *MenuUpsert {
	u.Set(menu.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *MenuUpsert) UpdateParentID() *MenuUpsert {
	u.SetExcluded(menu.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *MenuUpsert) ClearParentID() *MenuUpsert {
	u.SetNull(menu.FieldParentID)
	return u
}

// SetType sets the "type" field.
func (u *MenuUpsert) SetType(v menu.Type) *MenuUpsert {
	u.Set(menu.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *MenuUpsert) UpdateType() *MenuUpsert {
	u.SetExcluded(menu.FieldType)
	return u
}

// ClearType clears the value of the "type" field.
func (u *MenuUpsert) ClearType() *MenuUpsert {
	u.SetNull(menu.FieldType)
	return u
}

// SetPath sets the "path" field.
func (u *MenuUpsert) SetPath(v string) *MenuUpsert {
	u.Set(menu.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *MenuUpsert) UpdatePath() *MenuUpsert {
	u.SetExcluded(menu.FieldPath)
	return u
}

// ClearPath clears the value of the "path" field.
func (u *MenuUpsert) ClearPath() *MenuUpsert {
	u.SetNull(menu.FieldPath)
	return u
}

// SetRedirect sets the "redirect" field.
func (u *MenuUpsert) SetRedirect(v string) *MenuUpsert {
	u.Set(menu.FieldRedirect, v)
	return u
}

// UpdateRedirect sets the "redirect" field to the value that was provided on create.
func (u *MenuUpsert) UpdateRedirect() *MenuUpsert {
	u.SetExcluded(menu.FieldRedirect)
	return u
}

// ClearRedirect clears the value of the "redirect" field.
func (u *MenuUpsert) ClearRedirect() *MenuUpsert {
	u.SetNull(menu.FieldRedirect)
	return u
}

// SetAlias sets the "alias" field.
func (u *MenuUpsert) SetAlias(v string) *MenuUpsert {
	u.Set(menu.FieldAlias, v)
	return u
}

// UpdateAlias sets the "alias" field to the value that was provided on create.
func (u *MenuUpsert) UpdateAlias() *MenuUpsert {
	u.SetExcluded(menu.FieldAlias)
	return u
}

// ClearAlias clears the value of the "alias" field.
func (u *MenuUpsert) ClearAlias() *MenuUpsert {
	u.SetNull(menu.FieldAlias)
	return u
}

// SetName sets the "name" field.
func (u *MenuUpsert) SetName(v string) *MenuUpsert {
	u.Set(menu.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MenuUpsert) UpdateName() *MenuUpsert {
	u.SetExcluded(menu.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *MenuUpsert) ClearName() *MenuUpsert {
	u.SetNull(menu.FieldName)
	return u
}

// SetComponent sets the "component" field.
func (u *MenuUpsert) SetComponent(v string) *MenuUpsert {
	u.Set(menu.FieldComponent, v)
	return u
}

// UpdateComponent sets the "component" field to the value that was provided on create.
func (u *MenuUpsert) UpdateComponent() *MenuUpsert {
	u.SetExcluded(menu.FieldComponent)
	return u
}

// ClearComponent clears the value of the "component" field.
func (u *MenuUpsert) ClearComponent() *MenuUpsert {
	u.SetNull(menu.FieldComponent)
	return u
}

// SetMeta sets the "meta" field.
func (u *MenuUpsert) SetMeta(v *servicev1.RouteMeta) *MenuUpsert {
	u.Set(menu.FieldMeta, v)
	return u
}

// UpdateMeta sets the "meta" field to the value that was provided on create.
func (u *MenuUpsert) UpdateMeta() *MenuUpsert {
	u.SetExcluded(menu.FieldMeta)
	return u
}

// ClearMeta clears the value of the "meta" field.
func (u *MenuUpsert) ClearMeta() *MenuUpsert {
	u.SetNull(menu.FieldMeta)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(menu.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MenuUpsertOne) UpdateNewValues() *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(menu.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(menu.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Menu.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *MenuUpsertOne) Ignore() *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MenuUpsertOne) DoNothing() *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MenuCreate.OnConflict
// documentation for more info.
func (u *MenuUpsertOne) Update(set func(*MenuUpsert)) *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MenuUpsert{UpdateSet: update})
	}))
	return u
}

// SetStatus sets the "status" field.
func (u *MenuUpsertOne) SetStatus(v menu.Status) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateStatus() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *MenuUpsertOne) ClearStatus() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearStatus()
	})
}

// SetUpdateTime sets the "update_time" field.
func (u *MenuUpsertOne) SetUpdateTime(v time.Time) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateUpdateTime() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *MenuUpsertOne) ClearUpdateTime() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *MenuUpsertOne) SetDeleteTime(v time.Time) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateDeleteTime() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *MenuUpsertOne) ClearDeleteTime() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *MenuUpsertOne) SetCreateBy(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *MenuUpsertOne) AddCreateBy(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateCreateBy() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *MenuUpsertOne) ClearCreateBy() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *MenuUpsertOne) SetUpdateBy(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *MenuUpsertOne) AddUpdateBy(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateUpdateBy() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *MenuUpsertOne) ClearUpdateBy() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *MenuUpsertOne) SetRemark(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateRemark() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *MenuUpsertOne) ClearRemark() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRemark()
	})
}

// SetParentID sets the "parent_id" field.
func (u *MenuUpsertOne) SetParentID(v int32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateParentID() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *MenuUpsertOne) ClearParentID() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearParentID()
	})
}

// SetType sets the "type" field.
func (u *MenuUpsertOne) SetType(v menu.Type) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateType() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateType()
	})
}

// ClearType clears the value of the "type" field.
func (u *MenuUpsertOne) ClearType() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearType()
	})
}

// SetPath sets the "path" field.
func (u *MenuUpsertOne) SetPath(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdatePath() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdatePath()
	})
}

// ClearPath clears the value of the "path" field.
func (u *MenuUpsertOne) ClearPath() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearPath()
	})
}

// SetRedirect sets the "redirect" field.
func (u *MenuUpsertOne) SetRedirect(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetRedirect(v)
	})
}

// UpdateRedirect sets the "redirect" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateRedirect() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRedirect()
	})
}

// ClearRedirect clears the value of the "redirect" field.
func (u *MenuUpsertOne) ClearRedirect() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRedirect()
	})
}

// SetAlias sets the "alias" field.
func (u *MenuUpsertOne) SetAlias(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetAlias(v)
	})
}

// UpdateAlias sets the "alias" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateAlias() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateAlias()
	})
}

// ClearAlias clears the value of the "alias" field.
func (u *MenuUpsertOne) ClearAlias() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearAlias()
	})
}

// SetName sets the "name" field.
func (u *MenuUpsertOne) SetName(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateName() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *MenuUpsertOne) ClearName() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearName()
	})
}

// SetComponent sets the "component" field.
func (u *MenuUpsertOne) SetComponent(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetComponent(v)
	})
}

// UpdateComponent sets the "component" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateComponent() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateComponent()
	})
}

// ClearComponent clears the value of the "component" field.
func (u *MenuUpsertOne) ClearComponent() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearComponent()
	})
}

// SetMeta sets the "meta" field.
func (u *MenuUpsertOne) SetMeta(v *servicev1.RouteMeta) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetMeta(v)
	})
}

// UpdateMeta sets the "meta" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateMeta() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateMeta()
	})
}

// ClearMeta clears the value of the "meta" field.
func (u *MenuUpsertOne) ClearMeta() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearMeta()
	})
}

// Exec executes the query.
func (u *MenuUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for MenuCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MenuUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *MenuUpsertOne) ID(ctx context.Context) (id int32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *MenuUpsertOne) IDX(ctx context.Context) int32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// MenuCreateBulk is the builder for creating many Menu entities in bulk.
type MenuCreateBulk struct {
	config
	err      error
	builders []*MenuCreate
	conflict []sql.ConflictOption
}

// Save creates the Menu entities in the database.
func (mcb *MenuCreateBulk) Save(ctx context.Context) ([]*Menu, error) {
	if mcb.err != nil {
		return nil, mcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mcb.builders))
	nodes := make([]*Menu, len(mcb.builders))
	mutators := make([]Mutator, len(mcb.builders))
	for i := range mcb.builders {
		func(i int, root context.Context) {
			builder := mcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MenuMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = mcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mcb *MenuCreateBulk) SaveX(ctx context.Context) []*Menu {
	v, err := mcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mcb *MenuCreateBulk) Exec(ctx context.Context) error {
	_, err := mcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mcb *MenuCreateBulk) ExecX(ctx context.Context) {
	if err := mcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Menu.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MenuUpsert) {
//			SetStatus(v+v).
//		}).
//		Exec(ctx)
func (mcb *MenuCreateBulk) OnConflict(opts ...sql.ConflictOption) *MenuUpsertBulk {
	mcb.conflict = opts
	return &MenuUpsertBulk{
		create: mcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (mcb *MenuCreateBulk) OnConflictColumns(columns ...string) *MenuUpsertBulk {
	mcb.conflict = append(mcb.conflict, sql.ConflictColumns(columns...))
	return &MenuUpsertBulk{
		create: mcb,
	}
}

// MenuUpsertBulk is the builder for "upsert"-ing
// a bulk of Menu nodes.
type MenuUpsertBulk struct {
	create *MenuCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(menu.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MenuUpsertBulk) UpdateNewValues() *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(menu.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(menu.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *MenuUpsertBulk) Ignore() *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MenuUpsertBulk) DoNothing() *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MenuCreateBulk.OnConflict
// documentation for more info.
func (u *MenuUpsertBulk) Update(set func(*MenuUpsert)) *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MenuUpsert{UpdateSet: update})
	}))
	return u
}

// SetStatus sets the "status" field.
func (u *MenuUpsertBulk) SetStatus(v menu.Status) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateStatus() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *MenuUpsertBulk) ClearStatus() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearStatus()
	})
}

// SetUpdateTime sets the "update_time" field.
func (u *MenuUpsertBulk) SetUpdateTime(v time.Time) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateUpdateTime() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *MenuUpsertBulk) ClearUpdateTime() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *MenuUpsertBulk) SetDeleteTime(v time.Time) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateDeleteTime() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *MenuUpsertBulk) ClearDeleteTime() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *MenuUpsertBulk) SetCreateBy(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *MenuUpsertBulk) AddCreateBy(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateCreateBy() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *MenuUpsertBulk) ClearCreateBy() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *MenuUpsertBulk) SetUpdateBy(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *MenuUpsertBulk) AddUpdateBy(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateUpdateBy() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *MenuUpsertBulk) ClearUpdateBy() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *MenuUpsertBulk) SetRemark(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateRemark() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *MenuUpsertBulk) ClearRemark() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRemark()
	})
}

// SetParentID sets the "parent_id" field.
func (u *MenuUpsertBulk) SetParentID(v int32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateParentID() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *MenuUpsertBulk) ClearParentID() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearParentID()
	})
}

// SetType sets the "type" field.
func (u *MenuUpsertBulk) SetType(v menu.Type) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateType() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateType()
	})
}

// ClearType clears the value of the "type" field.
func (u *MenuUpsertBulk) ClearType() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearType()
	})
}

// SetPath sets the "path" field.
func (u *MenuUpsertBulk) SetPath(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdatePath() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdatePath()
	})
}

// ClearPath clears the value of the "path" field.
func (u *MenuUpsertBulk) ClearPath() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearPath()
	})
}

// SetRedirect sets the "redirect" field.
func (u *MenuUpsertBulk) SetRedirect(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetRedirect(v)
	})
}

// UpdateRedirect sets the "redirect" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateRedirect() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRedirect()
	})
}

// ClearRedirect clears the value of the "redirect" field.
func (u *MenuUpsertBulk) ClearRedirect() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRedirect()
	})
}

// SetAlias sets the "alias" field.
func (u *MenuUpsertBulk) SetAlias(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetAlias(v)
	})
}

// UpdateAlias sets the "alias" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateAlias() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateAlias()
	})
}

// ClearAlias clears the value of the "alias" field.
func (u *MenuUpsertBulk) ClearAlias() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearAlias()
	})
}

// SetName sets the "name" field.
func (u *MenuUpsertBulk) SetName(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateName() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *MenuUpsertBulk) ClearName() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearName()
	})
}

// SetComponent sets the "component" field.
func (u *MenuUpsertBulk) SetComponent(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetComponent(v)
	})
}

// UpdateComponent sets the "component" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateComponent() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateComponent()
	})
}

// ClearComponent clears the value of the "component" field.
func (u *MenuUpsertBulk) ClearComponent() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearComponent()
	})
}

// SetMeta sets the "meta" field.
func (u *MenuUpsertBulk) SetMeta(v *servicev1.RouteMeta) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetMeta(v)
	})
}

// UpdateMeta sets the "meta" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateMeta() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateMeta()
	})
}

// ClearMeta clears the value of the "meta" field.
func (u *MenuUpsertBulk) ClearMeta() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearMeta()
	})
}

// Exec executes the query.
func (u *MenuUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the MenuCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for MenuCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MenuUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
