// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginrestriction"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminLoginRestrictionDelete is the builder for deleting a AdminLoginRestriction entity.
type AdminLoginRestrictionDelete struct {
	config
	hooks    []Hook
	mutation *AdminLoginRestrictionMutation
}

// Where appends a list predicates to the AdminLoginRestrictionDelete builder.
func (alrd *AdminLoginRestrictionDelete) Where(ps ...predicate.AdminLoginRestriction) *AdminLoginRestrictionDelete {
	alrd.mutation.Where(ps...)
	return alrd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (alrd *AdminLoginRestrictionDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, alrd.sqlExec, alrd.mutation, alrd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (alrd *AdminLoginRestrictionDelete) ExecX(ctx context.Context) int {
	n, err := alrd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (alrd *AdminLoginRestrictionDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(adminloginrestriction.Table, sqlgraph.NewFieldSpec(adminloginrestriction.FieldID, field.TypeUint32))
	if ps := alrd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, alrd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	alrd.mutation.done = true
	return affected, err
}

// AdminLoginRestrictionDeleteOne is the builder for deleting a single AdminLoginRestriction entity.
type AdminLoginRestrictionDeleteOne struct {
	alrd *AdminLoginRestrictionDelete
}

// Where appends a list predicates to the AdminLoginRestrictionDelete builder.
func (alrdo *AdminLoginRestrictionDeleteOne) Where(ps ...predicate.AdminLoginRestriction) *AdminLoginRestrictionDeleteOne {
	alrdo.alrd.mutation.Where(ps...)
	return alrdo
}

// Exec executes the deletion query.
func (alrdo *AdminLoginRestrictionDeleteOne) Exec(ctx context.Context) error {
	n, err := alrdo.alrd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{adminloginrestriction.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (alrdo *AdminLoginRestrictionDeleteOne) ExecX(ctx context.Context) {
	if err := alrdo.Exec(ctx); err != nil {
		panic(err)
	}
}
