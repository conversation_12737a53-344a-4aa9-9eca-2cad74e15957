// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	servicev1 "kratos-admin/api/gen/go/admin/service/v1"
	"kratos-admin/app/admin/service/internal/data/ent/task"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TaskCreate is the builder for creating a Task entity.
type TaskCreate struct {
	config
	mutation *TaskMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (tc *TaskCreate) SetCreateTime(t time.Time) *TaskCreate {
	tc.mutation.SetCreateTime(t)
	return tc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (tc *TaskCreate) SetNillableCreateTime(t *time.Time) *TaskCreate {
	if t != nil {
		tc.SetCreateTime(*t)
	}
	return tc
}

// SetUpdateTime sets the "update_time" field.
func (tc *TaskCreate) SetUpdateTime(t time.Time) *TaskCreate {
	tc.mutation.SetUpdateTime(t)
	return tc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (tc *TaskCreate) SetNillableUpdateTime(t *time.Time) *TaskCreate {
	if t != nil {
		tc.SetUpdateTime(*t)
	}
	return tc
}

// SetDeleteTime sets the "delete_time" field.
func (tc *TaskCreate) SetDeleteTime(t time.Time) *TaskCreate {
	tc.mutation.SetDeleteTime(t)
	return tc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (tc *TaskCreate) SetNillableDeleteTime(t *time.Time) *TaskCreate {
	if t != nil {
		tc.SetDeleteTime(*t)
	}
	return tc
}

// SetCreateBy sets the "create_by" field.
func (tc *TaskCreate) SetCreateBy(u uint32) *TaskCreate {
	tc.mutation.SetCreateBy(u)
	return tc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (tc *TaskCreate) SetNillableCreateBy(u *uint32) *TaskCreate {
	if u != nil {
		tc.SetCreateBy(*u)
	}
	return tc
}

// SetUpdateBy sets the "update_by" field.
func (tc *TaskCreate) SetUpdateBy(u uint32) *TaskCreate {
	tc.mutation.SetUpdateBy(u)
	return tc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (tc *TaskCreate) SetNillableUpdateBy(u *uint32) *TaskCreate {
	if u != nil {
		tc.SetUpdateBy(*u)
	}
	return tc
}

// SetRemark sets the "remark" field.
func (tc *TaskCreate) SetRemark(s string) *TaskCreate {
	tc.mutation.SetRemark(s)
	return tc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (tc *TaskCreate) SetNillableRemark(s *string) *TaskCreate {
	if s != nil {
		tc.SetRemark(*s)
	}
	return tc
}

// SetTenantID sets the "tenant_id" field.
func (tc *TaskCreate) SetTenantID(u uint32) *TaskCreate {
	tc.mutation.SetTenantID(u)
	return tc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (tc *TaskCreate) SetNillableTenantID(u *uint32) *TaskCreate {
	if u != nil {
		tc.SetTenantID(*u)
	}
	return tc
}

// SetType sets the "type" field.
func (tc *TaskCreate) SetType(t task.Type) *TaskCreate {
	tc.mutation.SetType(t)
	return tc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tc *TaskCreate) SetNillableType(t *task.Type) *TaskCreate {
	if t != nil {
		tc.SetType(*t)
	}
	return tc
}

// SetTypeName sets the "type_name" field.
func (tc *TaskCreate) SetTypeName(s string) *TaskCreate {
	tc.mutation.SetTypeName(s)
	return tc
}

// SetNillableTypeName sets the "type_name" field if the given value is not nil.
func (tc *TaskCreate) SetNillableTypeName(s *string) *TaskCreate {
	if s != nil {
		tc.SetTypeName(*s)
	}
	return tc
}

// SetTaskPayload sets the "task_payload" field.
func (tc *TaskCreate) SetTaskPayload(s string) *TaskCreate {
	tc.mutation.SetTaskPayload(s)
	return tc
}

// SetNillableTaskPayload sets the "task_payload" field if the given value is not nil.
func (tc *TaskCreate) SetNillableTaskPayload(s *string) *TaskCreate {
	if s != nil {
		tc.SetTaskPayload(*s)
	}
	return tc
}

// SetCronSpec sets the "cron_spec" field.
func (tc *TaskCreate) SetCronSpec(s string) *TaskCreate {
	tc.mutation.SetCronSpec(s)
	return tc
}

// SetNillableCronSpec sets the "cron_spec" field if the given value is not nil.
func (tc *TaskCreate) SetNillableCronSpec(s *string) *TaskCreate {
	if s != nil {
		tc.SetCronSpec(*s)
	}
	return tc
}

// SetTaskOptions sets the "task_options" field.
func (tc *TaskCreate) SetTaskOptions(so *servicev1.TaskOption) *TaskCreate {
	tc.mutation.SetTaskOptions(so)
	return tc
}

// SetEnable sets the "enable" field.
func (tc *TaskCreate) SetEnable(b bool) *TaskCreate {
	tc.mutation.SetEnable(b)
	return tc
}

// SetNillableEnable sets the "enable" field if the given value is not nil.
func (tc *TaskCreate) SetNillableEnable(b *bool) *TaskCreate {
	if b != nil {
		tc.SetEnable(*b)
	}
	return tc
}

// SetID sets the "id" field.
func (tc *TaskCreate) SetID(u uint32) *TaskCreate {
	tc.mutation.SetID(u)
	return tc
}

// Mutation returns the TaskMutation object of the builder.
func (tc *TaskCreate) Mutation() *TaskMutation {
	return tc.mutation
}

// Save creates the Task in the database.
func (tc *TaskCreate) Save(ctx context.Context) (*Task, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TaskCreate) SaveX(ctx context.Context) *Task {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TaskCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TaskCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TaskCreate) defaults() {
	if _, ok := tc.mutation.Remark(); !ok {
		v := task.DefaultRemark
		tc.mutation.SetRemark(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TaskCreate) check() error {
	if v, ok := tc.mutation.TenantID(); ok {
		if err := task.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "Task.tenant_id": %w`, err)}
		}
	}
	if v, ok := tc.mutation.GetType(); ok {
		if err := task.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Task.type": %w`, err)}
		}
	}
	if v, ok := tc.mutation.TaskOptions(); ok {
		if err := v.Validate(); err != nil {
			return &ValidationError{Name: "task_options", err: fmt.Errorf(`ent: validator failed for field "Task.task_options": %w`, err)}
		}
	}
	if v, ok := tc.mutation.ID(); ok {
		if err := task.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Task.id": %w`, err)}
		}
	}
	return nil
}

func (tc *TaskCreate) sqlSave(ctx context.Context) (*Task, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TaskCreate) createSpec() (*Task, *sqlgraph.CreateSpec) {
	var (
		_node = &Task{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(task.Table, sqlgraph.NewFieldSpec(task.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = tc.conflict
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.CreateTime(); ok {
		_spec.SetField(task.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := tc.mutation.UpdateTime(); ok {
		_spec.SetField(task.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := tc.mutation.DeleteTime(); ok {
		_spec.SetField(task.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := tc.mutation.CreateBy(); ok {
		_spec.SetField(task.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := tc.mutation.UpdateBy(); ok {
		_spec.SetField(task.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := tc.mutation.Remark(); ok {
		_spec.SetField(task.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := tc.mutation.TenantID(); ok {
		_spec.SetField(task.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := tc.mutation.GetType(); ok {
		_spec.SetField(task.FieldType, field.TypeEnum, value)
		_node.Type = &value
	}
	if value, ok := tc.mutation.TypeName(); ok {
		_spec.SetField(task.FieldTypeName, field.TypeString, value)
		_node.TypeName = &value
	}
	if value, ok := tc.mutation.TaskPayload(); ok {
		_spec.SetField(task.FieldTaskPayload, field.TypeString, value)
		_node.TaskPayload = &value
	}
	if value, ok := tc.mutation.CronSpec(); ok {
		_spec.SetField(task.FieldCronSpec, field.TypeString, value)
		_node.CronSpec = &value
	}
	if value, ok := tc.mutation.TaskOptions(); ok {
		_spec.SetField(task.FieldTaskOptions, field.TypeJSON, value)
		_node.TaskOptions = value
	}
	if value, ok := tc.mutation.Enable(); ok {
		_spec.SetField(task.FieldEnable, field.TypeBool, value)
		_node.Enable = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Task.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TaskUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (tc *TaskCreate) OnConflict(opts ...sql.ConflictOption) *TaskUpsertOne {
	tc.conflict = opts
	return &TaskUpsertOne{
		create: tc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Task.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tc *TaskCreate) OnConflictColumns(columns ...string) *TaskUpsertOne {
	tc.conflict = append(tc.conflict, sql.ConflictColumns(columns...))
	return &TaskUpsertOne{
		create: tc,
	}
}

type (
	// TaskUpsertOne is the builder for "upsert"-ing
	//  one Task node.
	TaskUpsertOne struct {
		create *TaskCreate
	}

	// TaskUpsert is the "OnConflict" setter.
	TaskUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *TaskUpsert) SetUpdateTime(v time.Time) *TaskUpsert {
	u.Set(task.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *TaskUpsert) UpdateUpdateTime() *TaskUpsert {
	u.SetExcluded(task.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *TaskUpsert) ClearUpdateTime() *TaskUpsert {
	u.SetNull(task.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *TaskUpsert) SetDeleteTime(v time.Time) *TaskUpsert {
	u.Set(task.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *TaskUpsert) UpdateDeleteTime() *TaskUpsert {
	u.SetExcluded(task.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *TaskUpsert) ClearDeleteTime() *TaskUpsert {
	u.SetNull(task.FieldDeleteTime)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *TaskUpsert) SetCreateBy(v uint32) *TaskUpsert {
	u.Set(task.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *TaskUpsert) UpdateCreateBy() *TaskUpsert {
	u.SetExcluded(task.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *TaskUpsert) AddCreateBy(v uint32) *TaskUpsert {
	u.Add(task.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *TaskUpsert) ClearCreateBy() *TaskUpsert {
	u.SetNull(task.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *TaskUpsert) SetUpdateBy(v uint32) *TaskUpsert {
	u.Set(task.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *TaskUpsert) UpdateUpdateBy() *TaskUpsert {
	u.SetExcluded(task.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *TaskUpsert) AddUpdateBy(v uint32) *TaskUpsert {
	u.Add(task.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *TaskUpsert) ClearUpdateBy() *TaskUpsert {
	u.SetNull(task.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *TaskUpsert) SetRemark(v string) *TaskUpsert {
	u.Set(task.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *TaskUpsert) UpdateRemark() *TaskUpsert {
	u.SetExcluded(task.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *TaskUpsert) ClearRemark() *TaskUpsert {
	u.SetNull(task.FieldRemark)
	return u
}

// SetType sets the "type" field.
func (u *TaskUpsert) SetType(v task.Type) *TaskUpsert {
	u.Set(task.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *TaskUpsert) UpdateType() *TaskUpsert {
	u.SetExcluded(task.FieldType)
	return u
}

// ClearType clears the value of the "type" field.
func (u *TaskUpsert) ClearType() *TaskUpsert {
	u.SetNull(task.FieldType)
	return u
}

// SetTypeName sets the "type_name" field.
func (u *TaskUpsert) SetTypeName(v string) *TaskUpsert {
	u.Set(task.FieldTypeName, v)
	return u
}

// UpdateTypeName sets the "type_name" field to the value that was provided on create.
func (u *TaskUpsert) UpdateTypeName() *TaskUpsert {
	u.SetExcluded(task.FieldTypeName)
	return u
}

// ClearTypeName clears the value of the "type_name" field.
func (u *TaskUpsert) ClearTypeName() *TaskUpsert {
	u.SetNull(task.FieldTypeName)
	return u
}

// SetTaskPayload sets the "task_payload" field.
func (u *TaskUpsert) SetTaskPayload(v string) *TaskUpsert {
	u.Set(task.FieldTaskPayload, v)
	return u
}

// UpdateTaskPayload sets the "task_payload" field to the value that was provided on create.
func (u *TaskUpsert) UpdateTaskPayload() *TaskUpsert {
	u.SetExcluded(task.FieldTaskPayload)
	return u
}

// ClearTaskPayload clears the value of the "task_payload" field.
func (u *TaskUpsert) ClearTaskPayload() *TaskUpsert {
	u.SetNull(task.FieldTaskPayload)
	return u
}

// SetCronSpec sets the "cron_spec" field.
func (u *TaskUpsert) SetCronSpec(v string) *TaskUpsert {
	u.Set(task.FieldCronSpec, v)
	return u
}

// UpdateCronSpec sets the "cron_spec" field to the value that was provided on create.
func (u *TaskUpsert) UpdateCronSpec() *TaskUpsert {
	u.SetExcluded(task.FieldCronSpec)
	return u
}

// ClearCronSpec clears the value of the "cron_spec" field.
func (u *TaskUpsert) ClearCronSpec() *TaskUpsert {
	u.SetNull(task.FieldCronSpec)
	return u
}

// SetTaskOptions sets the "task_options" field.
func (u *TaskUpsert) SetTaskOptions(v *servicev1.TaskOption) *TaskUpsert {
	u.Set(task.FieldTaskOptions, v)
	return u
}

// UpdateTaskOptions sets the "task_options" field to the value that was provided on create.
func (u *TaskUpsert) UpdateTaskOptions() *TaskUpsert {
	u.SetExcluded(task.FieldTaskOptions)
	return u
}

// ClearTaskOptions clears the value of the "task_options" field.
func (u *TaskUpsert) ClearTaskOptions() *TaskUpsert {
	u.SetNull(task.FieldTaskOptions)
	return u
}

// SetEnable sets the "enable" field.
func (u *TaskUpsert) SetEnable(v bool) *TaskUpsert {
	u.Set(task.FieldEnable, v)
	return u
}

// UpdateEnable sets the "enable" field to the value that was provided on create.
func (u *TaskUpsert) UpdateEnable() *TaskUpsert {
	u.SetExcluded(task.FieldEnable)
	return u
}

// ClearEnable clears the value of the "enable" field.
func (u *TaskUpsert) ClearEnable() *TaskUpsert {
	u.SetNull(task.FieldEnable)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Task.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(task.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TaskUpsertOne) UpdateNewValues() *TaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(task.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(task.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(task.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Task.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TaskUpsertOne) Ignore() *TaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TaskUpsertOne) DoNothing() *TaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TaskCreate.OnConflict
// documentation for more info.
func (u *TaskUpsertOne) Update(set func(*TaskUpsert)) *TaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TaskUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *TaskUpsertOne) SetUpdateTime(v time.Time) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateUpdateTime() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *TaskUpsertOne) ClearUpdateTime() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *TaskUpsertOne) SetDeleteTime(v time.Time) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateDeleteTime() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *TaskUpsertOne) ClearDeleteTime() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *TaskUpsertOne) SetCreateBy(v uint32) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *TaskUpsertOne) AddCreateBy(v uint32) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateCreateBy() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *TaskUpsertOne) ClearCreateBy() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *TaskUpsertOne) SetUpdateBy(v uint32) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *TaskUpsertOne) AddUpdateBy(v uint32) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateUpdateBy() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *TaskUpsertOne) ClearUpdateBy() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *TaskUpsertOne) SetRemark(v string) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateRemark() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *TaskUpsertOne) ClearRemark() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearRemark()
	})
}

// SetType sets the "type" field.
func (u *TaskUpsertOne) SetType(v task.Type) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateType() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateType()
	})
}

// ClearType clears the value of the "type" field.
func (u *TaskUpsertOne) ClearType() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearType()
	})
}

// SetTypeName sets the "type_name" field.
func (u *TaskUpsertOne) SetTypeName(v string) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetTypeName(v)
	})
}

// UpdateTypeName sets the "type_name" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateTypeName() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateTypeName()
	})
}

// ClearTypeName clears the value of the "type_name" field.
func (u *TaskUpsertOne) ClearTypeName() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearTypeName()
	})
}

// SetTaskPayload sets the "task_payload" field.
func (u *TaskUpsertOne) SetTaskPayload(v string) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetTaskPayload(v)
	})
}

// UpdateTaskPayload sets the "task_payload" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateTaskPayload() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateTaskPayload()
	})
}

// ClearTaskPayload clears the value of the "task_payload" field.
func (u *TaskUpsertOne) ClearTaskPayload() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearTaskPayload()
	})
}

// SetCronSpec sets the "cron_spec" field.
func (u *TaskUpsertOne) SetCronSpec(v string) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetCronSpec(v)
	})
}

// UpdateCronSpec sets the "cron_spec" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateCronSpec() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateCronSpec()
	})
}

// ClearCronSpec clears the value of the "cron_spec" field.
func (u *TaskUpsertOne) ClearCronSpec() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearCronSpec()
	})
}

// SetTaskOptions sets the "task_options" field.
func (u *TaskUpsertOne) SetTaskOptions(v *servicev1.TaskOption) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetTaskOptions(v)
	})
}

// UpdateTaskOptions sets the "task_options" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateTaskOptions() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateTaskOptions()
	})
}

// ClearTaskOptions clears the value of the "task_options" field.
func (u *TaskUpsertOne) ClearTaskOptions() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearTaskOptions()
	})
}

// SetEnable sets the "enable" field.
func (u *TaskUpsertOne) SetEnable(v bool) *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.SetEnable(v)
	})
}

// UpdateEnable sets the "enable" field to the value that was provided on create.
func (u *TaskUpsertOne) UpdateEnable() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateEnable()
	})
}

// ClearEnable clears the value of the "enable" field.
func (u *TaskUpsertOne) ClearEnable() *TaskUpsertOne {
	return u.Update(func(s *TaskUpsert) {
		s.ClearEnable()
	})
}

// Exec executes the query.
func (u *TaskUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TaskCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TaskUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TaskUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TaskUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TaskCreateBulk is the builder for creating many Task entities in bulk.
type TaskCreateBulk struct {
	config
	err      error
	builders []*TaskCreate
	conflict []sql.ConflictOption
}

// Save creates the Task entities in the database.
func (tcb *TaskCreateBulk) Save(ctx context.Context) ([]*Task, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Task, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TaskMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TaskCreateBulk) SaveX(ctx context.Context) []*Task {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TaskCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TaskCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Task.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TaskUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (tcb *TaskCreateBulk) OnConflict(opts ...sql.ConflictOption) *TaskUpsertBulk {
	tcb.conflict = opts
	return &TaskUpsertBulk{
		create: tcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Task.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tcb *TaskCreateBulk) OnConflictColumns(columns ...string) *TaskUpsertBulk {
	tcb.conflict = append(tcb.conflict, sql.ConflictColumns(columns...))
	return &TaskUpsertBulk{
		create: tcb,
	}
}

// TaskUpsertBulk is the builder for "upsert"-ing
// a bulk of Task nodes.
type TaskUpsertBulk struct {
	create *TaskCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Task.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(task.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TaskUpsertBulk) UpdateNewValues() *TaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(task.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(task.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(task.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Task.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TaskUpsertBulk) Ignore() *TaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TaskUpsertBulk) DoNothing() *TaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TaskCreateBulk.OnConflict
// documentation for more info.
func (u *TaskUpsertBulk) Update(set func(*TaskUpsert)) *TaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TaskUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *TaskUpsertBulk) SetUpdateTime(v time.Time) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateUpdateTime() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *TaskUpsertBulk) ClearUpdateTime() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *TaskUpsertBulk) SetDeleteTime(v time.Time) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateDeleteTime() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *TaskUpsertBulk) ClearDeleteTime() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearDeleteTime()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *TaskUpsertBulk) SetCreateBy(v uint32) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *TaskUpsertBulk) AddCreateBy(v uint32) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateCreateBy() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *TaskUpsertBulk) ClearCreateBy() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *TaskUpsertBulk) SetUpdateBy(v uint32) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *TaskUpsertBulk) AddUpdateBy(v uint32) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateUpdateBy() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *TaskUpsertBulk) ClearUpdateBy() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *TaskUpsertBulk) SetRemark(v string) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateRemark() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *TaskUpsertBulk) ClearRemark() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearRemark()
	})
}

// SetType sets the "type" field.
func (u *TaskUpsertBulk) SetType(v task.Type) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateType() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateType()
	})
}

// ClearType clears the value of the "type" field.
func (u *TaskUpsertBulk) ClearType() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearType()
	})
}

// SetTypeName sets the "type_name" field.
func (u *TaskUpsertBulk) SetTypeName(v string) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetTypeName(v)
	})
}

// UpdateTypeName sets the "type_name" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateTypeName() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateTypeName()
	})
}

// ClearTypeName clears the value of the "type_name" field.
func (u *TaskUpsertBulk) ClearTypeName() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearTypeName()
	})
}

// SetTaskPayload sets the "task_payload" field.
func (u *TaskUpsertBulk) SetTaskPayload(v string) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetTaskPayload(v)
	})
}

// UpdateTaskPayload sets the "task_payload" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateTaskPayload() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateTaskPayload()
	})
}

// ClearTaskPayload clears the value of the "task_payload" field.
func (u *TaskUpsertBulk) ClearTaskPayload() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearTaskPayload()
	})
}

// SetCronSpec sets the "cron_spec" field.
func (u *TaskUpsertBulk) SetCronSpec(v string) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetCronSpec(v)
	})
}

// UpdateCronSpec sets the "cron_spec" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateCronSpec() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateCronSpec()
	})
}

// ClearCronSpec clears the value of the "cron_spec" field.
func (u *TaskUpsertBulk) ClearCronSpec() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearCronSpec()
	})
}

// SetTaskOptions sets the "task_options" field.
func (u *TaskUpsertBulk) SetTaskOptions(v *servicev1.TaskOption) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetTaskOptions(v)
	})
}

// UpdateTaskOptions sets the "task_options" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateTaskOptions() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateTaskOptions()
	})
}

// ClearTaskOptions clears the value of the "task_options" field.
func (u *TaskUpsertBulk) ClearTaskOptions() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearTaskOptions()
	})
}

// SetEnable sets the "enable" field.
func (u *TaskUpsertBulk) SetEnable(v bool) *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.SetEnable(v)
	})
}

// UpdateEnable sets the "enable" field to the value that was provided on create.
func (u *TaskUpsertBulk) UpdateEnable() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.UpdateEnable()
	})
}

// ClearEnable clears the value of the "enable" field.
func (u *TaskUpsertBulk) ClearEnable() *TaskUpsertBulk {
	return u.Update(func(s *TaskUpsert) {
		s.ClearEnable()
	})
}

// Exec executes the query.
func (u *TaskUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TaskCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TaskCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TaskUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
