// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/adminloginlog"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 后台登录日志表
type AdminLoginLog struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 登录IP地址
	LoginIP *string `json:"login_ip,omitempty"`
	// 登录MAC地址
	LoginMAC *string `json:"login_mac,omitempty"`
	// 登录时间
	LoginTime *time.Time `json:"login_time,omitempty"`
	// 浏览器的用户代理信息
	UserAgent *string `json:"user_agent,omitempty"`
	// 浏览器名称
	BrowserName *string `json:"browser_name,omitempty"`
	// 浏览器版本
	BrowserVersion *string `json:"browser_version,omitempty"`
	// 客户端ID
	ClientID *string `json:"client_id,omitempty"`
	// 客户端名称
	ClientName *string `json:"client_name,omitempty"`
	// 操作系统名称
	OsName *string `json:"os_name,omitempty"`
	// 操作系统版本
	OsVersion *string `json:"os_version,omitempty"`
	// 操作者用户ID
	UserID *uint32 `json:"user_id,omitempty"`
	// 操作者账号名
	Username *string `json:"username,omitempty"`
	// 状态码
	StatusCode *int32 `json:"status_code,omitempty"`
	// 操作成功
	Success *bool `json:"success,omitempty"`
	// 登录失败原因
	Reason *string `json:"reason,omitempty"`
	// 登录地理位置
	Location     *string `json:"location,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AdminLoginLog) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case adminloginlog.FieldSuccess:
			values[i] = new(sql.NullBool)
		case adminloginlog.FieldID, adminloginlog.FieldUserID, adminloginlog.FieldStatusCode:
			values[i] = new(sql.NullInt64)
		case adminloginlog.FieldLoginIP, adminloginlog.FieldLoginMAC, adminloginlog.FieldUserAgent, adminloginlog.FieldBrowserName, adminloginlog.FieldBrowserVersion, adminloginlog.FieldClientID, adminloginlog.FieldClientName, adminloginlog.FieldOsName, adminloginlog.FieldOsVersion, adminloginlog.FieldUsername, adminloginlog.FieldReason, adminloginlog.FieldLocation:
			values[i] = new(sql.NullString)
		case adminloginlog.FieldCreateTime, adminloginlog.FieldLoginTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AdminLoginLog fields.
func (all *AdminLoginLog) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case adminloginlog.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			all.ID = uint32(value.Int64)
		case adminloginlog.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				all.CreateTime = new(time.Time)
				*all.CreateTime = value.Time
			}
		case adminloginlog.FieldLoginIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field login_ip", values[i])
			} else if value.Valid {
				all.LoginIP = new(string)
				*all.LoginIP = value.String
			}
		case adminloginlog.FieldLoginMAC:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field login_mac", values[i])
			} else if value.Valid {
				all.LoginMAC = new(string)
				*all.LoginMAC = value.String
			}
		case adminloginlog.FieldLoginTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field login_time", values[i])
			} else if value.Valid {
				all.LoginTime = new(time.Time)
				*all.LoginTime = value.Time
			}
		case adminloginlog.FieldUserAgent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_agent", values[i])
			} else if value.Valid {
				all.UserAgent = new(string)
				*all.UserAgent = value.String
			}
		case adminloginlog.FieldBrowserName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field browser_name", values[i])
			} else if value.Valid {
				all.BrowserName = new(string)
				*all.BrowserName = value.String
			}
		case adminloginlog.FieldBrowserVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field browser_version", values[i])
			} else if value.Valid {
				all.BrowserVersion = new(string)
				*all.BrowserVersion = value.String
			}
		case adminloginlog.FieldClientID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_id", values[i])
			} else if value.Valid {
				all.ClientID = new(string)
				*all.ClientID = value.String
			}
		case adminloginlog.FieldClientName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_name", values[i])
			} else if value.Valid {
				all.ClientName = new(string)
				*all.ClientName = value.String
			}
		case adminloginlog.FieldOsName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field os_name", values[i])
			} else if value.Valid {
				all.OsName = new(string)
				*all.OsName = value.String
			}
		case adminloginlog.FieldOsVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field os_version", values[i])
			} else if value.Valid {
				all.OsVersion = new(string)
				*all.OsVersion = value.String
			}
		case adminloginlog.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				all.UserID = new(uint32)
				*all.UserID = uint32(value.Int64)
			}
		case adminloginlog.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				all.Username = new(string)
				*all.Username = value.String
			}
		case adminloginlog.FieldStatusCode:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status_code", values[i])
			} else if value.Valid {
				all.StatusCode = new(int32)
				*all.StatusCode = int32(value.Int64)
			}
		case adminloginlog.FieldSuccess:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field success", values[i])
			} else if value.Valid {
				all.Success = new(bool)
				*all.Success = value.Bool
			}
		case adminloginlog.FieldReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reason", values[i])
			} else if value.Valid {
				all.Reason = new(string)
				*all.Reason = value.String
			}
		case adminloginlog.FieldLocation:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field location", values[i])
			} else if value.Valid {
				all.Location = new(string)
				*all.Location = value.String
			}
		default:
			all.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AdminLoginLog.
// This includes values selected through modifiers, order, etc.
func (all *AdminLoginLog) Value(name string) (ent.Value, error) {
	return all.selectValues.Get(name)
}

// Update returns a builder for updating this AdminLoginLog.
// Note that you need to call AdminLoginLog.Unwrap() before calling this method if this AdminLoginLog
// was returned from a transaction, and the transaction was committed or rolled back.
func (all *AdminLoginLog) Update() *AdminLoginLogUpdateOne {
	return NewAdminLoginLogClient(all.config).UpdateOne(all)
}

// Unwrap unwraps the AdminLoginLog entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (all *AdminLoginLog) Unwrap() *AdminLoginLog {
	_tx, ok := all.config.driver.(*txDriver)
	if !ok {
		panic("ent: AdminLoginLog is not a transactional entity")
	}
	all.config.driver = _tx.drv
	return all
}

// String implements the fmt.Stringer.
func (all *AdminLoginLog) String() string {
	var builder strings.Builder
	builder.WriteString("AdminLoginLog(")
	builder.WriteString(fmt.Sprintf("id=%v, ", all.ID))
	if v := all.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := all.LoginIP; v != nil {
		builder.WriteString("login_ip=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.LoginMAC; v != nil {
		builder.WriteString("login_mac=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.LoginTime; v != nil {
		builder.WriteString("login_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := all.UserAgent; v != nil {
		builder.WriteString("user_agent=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.BrowserName; v != nil {
		builder.WriteString("browser_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.BrowserVersion; v != nil {
		builder.WriteString("browser_version=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.ClientID; v != nil {
		builder.WriteString("client_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.ClientName; v != nil {
		builder.WriteString("client_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.OsName; v != nil {
		builder.WriteString("os_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.OsVersion; v != nil {
		builder.WriteString("os_version=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.UserID; v != nil {
		builder.WriteString("user_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := all.Username; v != nil {
		builder.WriteString("username=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.StatusCode; v != nil {
		builder.WriteString("status_code=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := all.Success; v != nil {
		builder.WriteString("success=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := all.Reason; v != nil {
		builder.WriteString("reason=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := all.Location; v != nil {
		builder.WriteString("location=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// AdminLoginLogs is a parsable slice of AdminLoginLog.
type AdminLoginLogs []*AdminLoginLog
