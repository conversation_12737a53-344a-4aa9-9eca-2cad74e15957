// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagerecipient"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotificationMessageRecipientUpdate is the builder for updating NotificationMessageRecipient entities.
type NotificationMessageRecipientUpdate struct {
	config
	hooks     []Hook
	mutation  *NotificationMessageRecipientMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the NotificationMessageRecipientUpdate builder.
func (nmru *NotificationMessageRecipientUpdate) Where(ps ...predicate.NotificationMessageRecipient) *NotificationMessageRecipientUpdate {
	nmru.mutation.Where(ps...)
	return nmru
}

// SetUpdateTime sets the "update_time" field.
func (nmru *NotificationMessageRecipientUpdate) SetUpdateTime(t time.Time) *NotificationMessageRecipientUpdate {
	nmru.mutation.SetUpdateTime(t)
	return nmru
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmru *NotificationMessageRecipientUpdate) SetNillableUpdateTime(t *time.Time) *NotificationMessageRecipientUpdate {
	if t != nil {
		nmru.SetUpdateTime(*t)
	}
	return nmru
}

// ClearUpdateTime clears the value of the "update_time" field.
func (nmru *NotificationMessageRecipientUpdate) ClearUpdateTime() *NotificationMessageRecipientUpdate {
	nmru.mutation.ClearUpdateTime()
	return nmru
}

// SetDeleteTime sets the "delete_time" field.
func (nmru *NotificationMessageRecipientUpdate) SetDeleteTime(t time.Time) *NotificationMessageRecipientUpdate {
	nmru.mutation.SetDeleteTime(t)
	return nmru
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmru *NotificationMessageRecipientUpdate) SetNillableDeleteTime(t *time.Time) *NotificationMessageRecipientUpdate {
	if t != nil {
		nmru.SetDeleteTime(*t)
	}
	return nmru
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (nmru *NotificationMessageRecipientUpdate) ClearDeleteTime() *NotificationMessageRecipientUpdate {
	nmru.mutation.ClearDeleteTime()
	return nmru
}

// SetMessageID sets the "message_id" field.
func (nmru *NotificationMessageRecipientUpdate) SetMessageID(u uint32) *NotificationMessageRecipientUpdate {
	nmru.mutation.ResetMessageID()
	nmru.mutation.SetMessageID(u)
	return nmru
}

// SetNillableMessageID sets the "message_id" field if the given value is not nil.
func (nmru *NotificationMessageRecipientUpdate) SetNillableMessageID(u *uint32) *NotificationMessageRecipientUpdate {
	if u != nil {
		nmru.SetMessageID(*u)
	}
	return nmru
}

// AddMessageID adds u to the "message_id" field.
func (nmru *NotificationMessageRecipientUpdate) AddMessageID(u int32) *NotificationMessageRecipientUpdate {
	nmru.mutation.AddMessageID(u)
	return nmru
}

// ClearMessageID clears the value of the "message_id" field.
func (nmru *NotificationMessageRecipientUpdate) ClearMessageID() *NotificationMessageRecipientUpdate {
	nmru.mutation.ClearMessageID()
	return nmru
}

// SetRecipientID sets the "recipient_id" field.
func (nmru *NotificationMessageRecipientUpdate) SetRecipientID(u uint32) *NotificationMessageRecipientUpdate {
	nmru.mutation.ResetRecipientID()
	nmru.mutation.SetRecipientID(u)
	return nmru
}

// SetNillableRecipientID sets the "recipient_id" field if the given value is not nil.
func (nmru *NotificationMessageRecipientUpdate) SetNillableRecipientID(u *uint32) *NotificationMessageRecipientUpdate {
	if u != nil {
		nmru.SetRecipientID(*u)
	}
	return nmru
}

// AddRecipientID adds u to the "recipient_id" field.
func (nmru *NotificationMessageRecipientUpdate) AddRecipientID(u int32) *NotificationMessageRecipientUpdate {
	nmru.mutation.AddRecipientID(u)
	return nmru
}

// ClearRecipientID clears the value of the "recipient_id" field.
func (nmru *NotificationMessageRecipientUpdate) ClearRecipientID() *NotificationMessageRecipientUpdate {
	nmru.mutation.ClearRecipientID()
	return nmru
}

// SetStatus sets the "status" field.
func (nmru *NotificationMessageRecipientUpdate) SetStatus(n notificationmessagerecipient.Status) *NotificationMessageRecipientUpdate {
	nmru.mutation.SetStatus(n)
	return nmru
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nmru *NotificationMessageRecipientUpdate) SetNillableStatus(n *notificationmessagerecipient.Status) *NotificationMessageRecipientUpdate {
	if n != nil {
		nmru.SetStatus(*n)
	}
	return nmru
}

// ClearStatus clears the value of the "status" field.
func (nmru *NotificationMessageRecipientUpdate) ClearStatus() *NotificationMessageRecipientUpdate {
	nmru.mutation.ClearStatus()
	return nmru
}

// Mutation returns the NotificationMessageRecipientMutation object of the builder.
func (nmru *NotificationMessageRecipientUpdate) Mutation() *NotificationMessageRecipientMutation {
	return nmru.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (nmru *NotificationMessageRecipientUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, nmru.sqlSave, nmru.mutation, nmru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nmru *NotificationMessageRecipientUpdate) SaveX(ctx context.Context) int {
	affected, err := nmru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (nmru *NotificationMessageRecipientUpdate) Exec(ctx context.Context) error {
	_, err := nmru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmru *NotificationMessageRecipientUpdate) ExecX(ctx context.Context) {
	if err := nmru.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmru *NotificationMessageRecipientUpdate) check() error {
	if v, ok := nmru.mutation.Status(); ok {
		if err := notificationmessagerecipient.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageRecipient.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (nmru *NotificationMessageRecipientUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *NotificationMessageRecipientUpdate {
	nmru.modifiers = append(nmru.modifiers, modifiers...)
	return nmru
}

func (nmru *NotificationMessageRecipientUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := nmru.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationmessagerecipient.Table, notificationmessagerecipient.Columns, sqlgraph.NewFieldSpec(notificationmessagerecipient.FieldID, field.TypeUint32))
	if ps := nmru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if nmru.mutation.CreateTimeCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldCreateTime, field.TypeTime)
	}
	if value, ok := nmru.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldUpdateTime, field.TypeTime, value)
	}
	if nmru.mutation.UpdateTimeCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := nmru.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldDeleteTime, field.TypeTime, value)
	}
	if nmru.mutation.DeleteTimeCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldDeleteTime, field.TypeTime)
	}
	if nmru.mutation.TenantIDCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldTenantID, field.TypeUint32)
	}
	if value, ok := nmru.mutation.MessageID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldMessageID, field.TypeUint32, value)
	}
	if value, ok := nmru.mutation.AddedMessageID(); ok {
		_spec.AddField(notificationmessagerecipient.FieldMessageID, field.TypeUint32, value)
	}
	if nmru.mutation.MessageIDCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldMessageID, field.TypeUint32)
	}
	if value, ok := nmru.mutation.RecipientID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32, value)
	}
	if value, ok := nmru.mutation.AddedRecipientID(); ok {
		_spec.AddField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32, value)
	}
	if nmru.mutation.RecipientIDCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32)
	}
	if value, ok := nmru.mutation.Status(); ok {
		_spec.SetField(notificationmessagerecipient.FieldStatus, field.TypeEnum, value)
	}
	if nmru.mutation.StatusCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldStatus, field.TypeEnum)
	}
	_spec.AddModifiers(nmru.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, nmru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationmessagerecipient.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	nmru.mutation.done = true
	return n, nil
}

// NotificationMessageRecipientUpdateOne is the builder for updating a single NotificationMessageRecipient entity.
type NotificationMessageRecipientUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *NotificationMessageRecipientMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (nmruo *NotificationMessageRecipientUpdateOne) SetUpdateTime(t time.Time) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.SetUpdateTime(t)
	return nmruo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (nmruo *NotificationMessageRecipientUpdateOne) SetNillableUpdateTime(t *time.Time) *NotificationMessageRecipientUpdateOne {
	if t != nil {
		nmruo.SetUpdateTime(*t)
	}
	return nmruo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (nmruo *NotificationMessageRecipientUpdateOne) ClearUpdateTime() *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ClearUpdateTime()
	return nmruo
}

// SetDeleteTime sets the "delete_time" field.
func (nmruo *NotificationMessageRecipientUpdateOne) SetDeleteTime(t time.Time) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.SetDeleteTime(t)
	return nmruo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (nmruo *NotificationMessageRecipientUpdateOne) SetNillableDeleteTime(t *time.Time) *NotificationMessageRecipientUpdateOne {
	if t != nil {
		nmruo.SetDeleteTime(*t)
	}
	return nmruo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (nmruo *NotificationMessageRecipientUpdateOne) ClearDeleteTime() *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ClearDeleteTime()
	return nmruo
}

// SetMessageID sets the "message_id" field.
func (nmruo *NotificationMessageRecipientUpdateOne) SetMessageID(u uint32) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ResetMessageID()
	nmruo.mutation.SetMessageID(u)
	return nmruo
}

// SetNillableMessageID sets the "message_id" field if the given value is not nil.
func (nmruo *NotificationMessageRecipientUpdateOne) SetNillableMessageID(u *uint32) *NotificationMessageRecipientUpdateOne {
	if u != nil {
		nmruo.SetMessageID(*u)
	}
	return nmruo
}

// AddMessageID adds u to the "message_id" field.
func (nmruo *NotificationMessageRecipientUpdateOne) AddMessageID(u int32) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.AddMessageID(u)
	return nmruo
}

// ClearMessageID clears the value of the "message_id" field.
func (nmruo *NotificationMessageRecipientUpdateOne) ClearMessageID() *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ClearMessageID()
	return nmruo
}

// SetRecipientID sets the "recipient_id" field.
func (nmruo *NotificationMessageRecipientUpdateOne) SetRecipientID(u uint32) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ResetRecipientID()
	nmruo.mutation.SetRecipientID(u)
	return nmruo
}

// SetNillableRecipientID sets the "recipient_id" field if the given value is not nil.
func (nmruo *NotificationMessageRecipientUpdateOne) SetNillableRecipientID(u *uint32) *NotificationMessageRecipientUpdateOne {
	if u != nil {
		nmruo.SetRecipientID(*u)
	}
	return nmruo
}

// AddRecipientID adds u to the "recipient_id" field.
func (nmruo *NotificationMessageRecipientUpdateOne) AddRecipientID(u int32) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.AddRecipientID(u)
	return nmruo
}

// ClearRecipientID clears the value of the "recipient_id" field.
func (nmruo *NotificationMessageRecipientUpdateOne) ClearRecipientID() *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ClearRecipientID()
	return nmruo
}

// SetStatus sets the "status" field.
func (nmruo *NotificationMessageRecipientUpdateOne) SetStatus(n notificationmessagerecipient.Status) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.SetStatus(n)
	return nmruo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nmruo *NotificationMessageRecipientUpdateOne) SetNillableStatus(n *notificationmessagerecipient.Status) *NotificationMessageRecipientUpdateOne {
	if n != nil {
		nmruo.SetStatus(*n)
	}
	return nmruo
}

// ClearStatus clears the value of the "status" field.
func (nmruo *NotificationMessageRecipientUpdateOne) ClearStatus() *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.ClearStatus()
	return nmruo
}

// Mutation returns the NotificationMessageRecipientMutation object of the builder.
func (nmruo *NotificationMessageRecipientUpdateOne) Mutation() *NotificationMessageRecipientMutation {
	return nmruo.mutation
}

// Where appends a list predicates to the NotificationMessageRecipientUpdate builder.
func (nmruo *NotificationMessageRecipientUpdateOne) Where(ps ...predicate.NotificationMessageRecipient) *NotificationMessageRecipientUpdateOne {
	nmruo.mutation.Where(ps...)
	return nmruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (nmruo *NotificationMessageRecipientUpdateOne) Select(field string, fields ...string) *NotificationMessageRecipientUpdateOne {
	nmruo.fields = append([]string{field}, fields...)
	return nmruo
}

// Save executes the query and returns the updated NotificationMessageRecipient entity.
func (nmruo *NotificationMessageRecipientUpdateOne) Save(ctx context.Context) (*NotificationMessageRecipient, error) {
	return withHooks(ctx, nmruo.sqlSave, nmruo.mutation, nmruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nmruo *NotificationMessageRecipientUpdateOne) SaveX(ctx context.Context) *NotificationMessageRecipient {
	node, err := nmruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (nmruo *NotificationMessageRecipientUpdateOne) Exec(ctx context.Context) error {
	_, err := nmruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nmruo *NotificationMessageRecipientUpdateOne) ExecX(ctx context.Context) {
	if err := nmruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nmruo *NotificationMessageRecipientUpdateOne) check() error {
	if v, ok := nmruo.mutation.Status(); ok {
		if err := notificationmessagerecipient.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "NotificationMessageRecipient.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (nmruo *NotificationMessageRecipientUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *NotificationMessageRecipientUpdateOne {
	nmruo.modifiers = append(nmruo.modifiers, modifiers...)
	return nmruo
}

func (nmruo *NotificationMessageRecipientUpdateOne) sqlSave(ctx context.Context) (_node *NotificationMessageRecipient, err error) {
	if err := nmruo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationmessagerecipient.Table, notificationmessagerecipient.Columns, sqlgraph.NewFieldSpec(notificationmessagerecipient.FieldID, field.TypeUint32))
	id, ok := nmruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "NotificationMessageRecipient.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := nmruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationmessagerecipient.FieldID)
		for _, f := range fields {
			if !notificationmessagerecipient.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != notificationmessagerecipient.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := nmruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if nmruo.mutation.CreateTimeCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldCreateTime, field.TypeTime)
	}
	if value, ok := nmruo.mutation.UpdateTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldUpdateTime, field.TypeTime, value)
	}
	if nmruo.mutation.UpdateTimeCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := nmruo.mutation.DeleteTime(); ok {
		_spec.SetField(notificationmessagerecipient.FieldDeleteTime, field.TypeTime, value)
	}
	if nmruo.mutation.DeleteTimeCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldDeleteTime, field.TypeTime)
	}
	if nmruo.mutation.TenantIDCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldTenantID, field.TypeUint32)
	}
	if value, ok := nmruo.mutation.MessageID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldMessageID, field.TypeUint32, value)
	}
	if value, ok := nmruo.mutation.AddedMessageID(); ok {
		_spec.AddField(notificationmessagerecipient.FieldMessageID, field.TypeUint32, value)
	}
	if nmruo.mutation.MessageIDCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldMessageID, field.TypeUint32)
	}
	if value, ok := nmruo.mutation.RecipientID(); ok {
		_spec.SetField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32, value)
	}
	if value, ok := nmruo.mutation.AddedRecipientID(); ok {
		_spec.AddField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32, value)
	}
	if nmruo.mutation.RecipientIDCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldRecipientID, field.TypeUint32)
	}
	if value, ok := nmruo.mutation.Status(); ok {
		_spec.SetField(notificationmessagerecipient.FieldStatus, field.TypeEnum, value)
	}
	if nmruo.mutation.StatusCleared() {
		_spec.ClearField(notificationmessagerecipient.FieldStatus, field.TypeEnum)
	}
	_spec.AddModifiers(nmruo.modifiers...)
	_node = &NotificationMessageRecipient{config: nmruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, nmruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationmessagerecipient.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	nmruo.mutation.done = true
	return _node, nil
}
