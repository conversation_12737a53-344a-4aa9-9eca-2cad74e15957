// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/dict"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DictCreate is the builder for creating a Dict entity.
type DictCreate struct {
	config
	mutation *DictMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (dc *DictCreate) SetCreateTime(t time.Time) *DictCreate {
	dc.mutation.SetCreateTime(t)
	return dc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (dc *DictCreate) SetNillableCreateTime(t *time.Time) *DictCreate {
	if t != nil {
		dc.SetCreateTime(*t)
	}
	return dc
}

// SetUpdateTime sets the "update_time" field.
func (dc *DictCreate) SetUpdateTime(t time.Time) *DictCreate {
	dc.mutation.SetUpdateTime(t)
	return dc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (dc *DictCreate) SetNillableUpdateTime(t *time.Time) *DictCreate {
	if t != nil {
		dc.SetUpdateTime(*t)
	}
	return dc
}

// SetDeleteTime sets the "delete_time" field.
func (dc *DictCreate) SetDeleteTime(t time.Time) *DictCreate {
	dc.mutation.SetDeleteTime(t)
	return dc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (dc *DictCreate) SetNillableDeleteTime(t *time.Time) *DictCreate {
	if t != nil {
		dc.SetDeleteTime(*t)
	}
	return dc
}

// SetStatus sets the "status" field.
func (dc *DictCreate) SetStatus(d dict.Status) *DictCreate {
	dc.mutation.SetStatus(d)
	return dc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (dc *DictCreate) SetNillableStatus(d *dict.Status) *DictCreate {
	if d != nil {
		dc.SetStatus(*d)
	}
	return dc
}

// SetCreateBy sets the "create_by" field.
func (dc *DictCreate) SetCreateBy(u uint32) *DictCreate {
	dc.mutation.SetCreateBy(u)
	return dc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (dc *DictCreate) SetNillableCreateBy(u *uint32) *DictCreate {
	if u != nil {
		dc.SetCreateBy(*u)
	}
	return dc
}

// SetUpdateBy sets the "update_by" field.
func (dc *DictCreate) SetUpdateBy(u uint32) *DictCreate {
	dc.mutation.SetUpdateBy(u)
	return dc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (dc *DictCreate) SetNillableUpdateBy(u *uint32) *DictCreate {
	if u != nil {
		dc.SetUpdateBy(*u)
	}
	return dc
}

// SetRemark sets the "remark" field.
func (dc *DictCreate) SetRemark(s string) *DictCreate {
	dc.mutation.SetRemark(s)
	return dc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (dc *DictCreate) SetNillableRemark(s *string) *DictCreate {
	if s != nil {
		dc.SetRemark(*s)
	}
	return dc
}

// SetTenantID sets the "tenant_id" field.
func (dc *DictCreate) SetTenantID(u uint32) *DictCreate {
	dc.mutation.SetTenantID(u)
	return dc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (dc *DictCreate) SetNillableTenantID(u *uint32) *DictCreate {
	if u != nil {
		dc.SetTenantID(*u)
	}
	return dc
}

// SetKey sets the "key" field.
func (dc *DictCreate) SetKey(s string) *DictCreate {
	dc.mutation.SetKey(s)
	return dc
}

// SetNillableKey sets the "key" field if the given value is not nil.
func (dc *DictCreate) SetNillableKey(s *string) *DictCreate {
	if s != nil {
		dc.SetKey(*s)
	}
	return dc
}

// SetCategory sets the "category" field.
func (dc *DictCreate) SetCategory(s string) *DictCreate {
	dc.mutation.SetCategory(s)
	return dc
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (dc *DictCreate) SetNillableCategory(s *string) *DictCreate {
	if s != nil {
		dc.SetCategory(*s)
	}
	return dc
}

// SetCategoryDesc sets the "category_desc" field.
func (dc *DictCreate) SetCategoryDesc(s string) *DictCreate {
	dc.mutation.SetCategoryDesc(s)
	return dc
}

// SetNillableCategoryDesc sets the "category_desc" field if the given value is not nil.
func (dc *DictCreate) SetNillableCategoryDesc(s *string) *DictCreate {
	if s != nil {
		dc.SetCategoryDesc(*s)
	}
	return dc
}

// SetValue sets the "value" field.
func (dc *DictCreate) SetValue(s string) *DictCreate {
	dc.mutation.SetValue(s)
	return dc
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (dc *DictCreate) SetNillableValue(s *string) *DictCreate {
	if s != nil {
		dc.SetValue(*s)
	}
	return dc
}

// SetValueDesc sets the "value_desc" field.
func (dc *DictCreate) SetValueDesc(s string) *DictCreate {
	dc.mutation.SetValueDesc(s)
	return dc
}

// SetNillableValueDesc sets the "value_desc" field if the given value is not nil.
func (dc *DictCreate) SetNillableValueDesc(s *string) *DictCreate {
	if s != nil {
		dc.SetValueDesc(*s)
	}
	return dc
}

// SetValueDataType sets the "value_data_type" field.
func (dc *DictCreate) SetValueDataType(s string) *DictCreate {
	dc.mutation.SetValueDataType(s)
	return dc
}

// SetNillableValueDataType sets the "value_data_type" field if the given value is not nil.
func (dc *DictCreate) SetNillableValueDataType(s *string) *DictCreate {
	if s != nil {
		dc.SetValueDataType(*s)
	}
	return dc
}

// SetSortID sets the "sort_id" field.
func (dc *DictCreate) SetSortID(i int32) *DictCreate {
	dc.mutation.SetSortID(i)
	return dc
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (dc *DictCreate) SetNillableSortID(i *int32) *DictCreate {
	if i != nil {
		dc.SetSortID(*i)
	}
	return dc
}

// SetID sets the "id" field.
func (dc *DictCreate) SetID(u uint32) *DictCreate {
	dc.mutation.SetID(u)
	return dc
}

// Mutation returns the DictMutation object of the builder.
func (dc *DictCreate) Mutation() *DictMutation {
	return dc.mutation
}

// Save creates the Dict in the database.
func (dc *DictCreate) Save(ctx context.Context) (*Dict, error) {
	dc.defaults()
	return withHooks(ctx, dc.sqlSave, dc.mutation, dc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dc *DictCreate) SaveX(ctx context.Context) *Dict {
	v, err := dc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dc *DictCreate) Exec(ctx context.Context) error {
	_, err := dc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dc *DictCreate) ExecX(ctx context.Context) {
	if err := dc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dc *DictCreate) defaults() {
	if _, ok := dc.mutation.Status(); !ok {
		v := dict.DefaultStatus
		dc.mutation.SetStatus(v)
	}
	if _, ok := dc.mutation.Remark(); !ok {
		v := dict.DefaultRemark
		dc.mutation.SetRemark(v)
	}
	if _, ok := dc.mutation.SortID(); !ok {
		v := dict.DefaultSortID
		dc.mutation.SetSortID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dc *DictCreate) check() error {
	if v, ok := dc.mutation.Status(); ok {
		if err := dict.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Dict.status": %w`, err)}
		}
	}
	if v, ok := dc.mutation.TenantID(); ok {
		if err := dict.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "Dict.tenant_id": %w`, err)}
		}
	}
	if v, ok := dc.mutation.ID(); ok {
		if err := dict.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Dict.id": %w`, err)}
		}
	}
	return nil
}

func (dc *DictCreate) sqlSave(ctx context.Context) (*Dict, error) {
	if err := dc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	dc.mutation.id = &_node.ID
	dc.mutation.done = true
	return _node, nil
}

func (dc *DictCreate) createSpec() (*Dict, *sqlgraph.CreateSpec) {
	var (
		_node = &Dict{config: dc.config}
		_spec = sqlgraph.NewCreateSpec(dict.Table, sqlgraph.NewFieldSpec(dict.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = dc.conflict
	if id, ok := dc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := dc.mutation.CreateTime(); ok {
		_spec.SetField(dict.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := dc.mutation.UpdateTime(); ok {
		_spec.SetField(dict.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := dc.mutation.DeleteTime(); ok {
		_spec.SetField(dict.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := dc.mutation.Status(); ok {
		_spec.SetField(dict.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := dc.mutation.CreateBy(); ok {
		_spec.SetField(dict.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := dc.mutation.UpdateBy(); ok {
		_spec.SetField(dict.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := dc.mutation.Remark(); ok {
		_spec.SetField(dict.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := dc.mutation.TenantID(); ok {
		_spec.SetField(dict.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := dc.mutation.Key(); ok {
		_spec.SetField(dict.FieldKey, field.TypeString, value)
		_node.Key = &value
	}
	if value, ok := dc.mutation.Category(); ok {
		_spec.SetField(dict.FieldCategory, field.TypeString, value)
		_node.Category = &value
	}
	if value, ok := dc.mutation.CategoryDesc(); ok {
		_spec.SetField(dict.FieldCategoryDesc, field.TypeString, value)
		_node.CategoryDesc = &value
	}
	if value, ok := dc.mutation.Value(); ok {
		_spec.SetField(dict.FieldValue, field.TypeString, value)
		_node.Value = &value
	}
	if value, ok := dc.mutation.ValueDesc(); ok {
		_spec.SetField(dict.FieldValueDesc, field.TypeString, value)
		_node.ValueDesc = &value
	}
	if value, ok := dc.mutation.ValueDataType(); ok {
		_spec.SetField(dict.FieldValueDataType, field.TypeString, value)
		_node.ValueDataType = &value
	}
	if value, ok := dc.mutation.SortID(); ok {
		_spec.SetField(dict.FieldSortID, field.TypeInt32, value)
		_node.SortID = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Dict.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DictUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (dc *DictCreate) OnConflict(opts ...sql.ConflictOption) *DictUpsertOne {
	dc.conflict = opts
	return &DictUpsertOne{
		create: dc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Dict.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (dc *DictCreate) OnConflictColumns(columns ...string) *DictUpsertOne {
	dc.conflict = append(dc.conflict, sql.ConflictColumns(columns...))
	return &DictUpsertOne{
		create: dc,
	}
}

type (
	// DictUpsertOne is the builder for "upsert"-ing
	//  one Dict node.
	DictUpsertOne struct {
		create *DictCreate
	}

	// DictUpsert is the "OnConflict" setter.
	DictUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *DictUpsert) SetUpdateTime(v time.Time) *DictUpsert {
	u.Set(dict.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *DictUpsert) UpdateUpdateTime() *DictUpsert {
	u.SetExcluded(dict.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *DictUpsert) ClearUpdateTime() *DictUpsert {
	u.SetNull(dict.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *DictUpsert) SetDeleteTime(v time.Time) *DictUpsert {
	u.Set(dict.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *DictUpsert) UpdateDeleteTime() *DictUpsert {
	u.SetExcluded(dict.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *DictUpsert) ClearDeleteTime() *DictUpsert {
	u.SetNull(dict.FieldDeleteTime)
	return u
}

// SetStatus sets the "status" field.
func (u *DictUpsert) SetStatus(v dict.Status) *DictUpsert {
	u.Set(dict.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DictUpsert) UpdateStatus() *DictUpsert {
	u.SetExcluded(dict.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *DictUpsert) ClearStatus() *DictUpsert {
	u.SetNull(dict.FieldStatus)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *DictUpsert) SetCreateBy(v uint32) *DictUpsert {
	u.Set(dict.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *DictUpsert) UpdateCreateBy() *DictUpsert {
	u.SetExcluded(dict.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *DictUpsert) AddCreateBy(v uint32) *DictUpsert {
	u.Add(dict.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *DictUpsert) ClearCreateBy() *DictUpsert {
	u.SetNull(dict.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *DictUpsert) SetUpdateBy(v uint32) *DictUpsert {
	u.Set(dict.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *DictUpsert) UpdateUpdateBy() *DictUpsert {
	u.SetExcluded(dict.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *DictUpsert) AddUpdateBy(v uint32) *DictUpsert {
	u.Add(dict.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *DictUpsert) ClearUpdateBy() *DictUpsert {
	u.SetNull(dict.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *DictUpsert) SetRemark(v string) *DictUpsert {
	u.Set(dict.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *DictUpsert) UpdateRemark() *DictUpsert {
	u.SetExcluded(dict.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *DictUpsert) ClearRemark() *DictUpsert {
	u.SetNull(dict.FieldRemark)
	return u
}

// SetKey sets the "key" field.
func (u *DictUpsert) SetKey(v string) *DictUpsert {
	u.Set(dict.FieldKey, v)
	return u
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *DictUpsert) UpdateKey() *DictUpsert {
	u.SetExcluded(dict.FieldKey)
	return u
}

// ClearKey clears the value of the "key" field.
func (u *DictUpsert) ClearKey() *DictUpsert {
	u.SetNull(dict.FieldKey)
	return u
}

// SetCategory sets the "category" field.
func (u *DictUpsert) SetCategory(v string) *DictUpsert {
	u.Set(dict.FieldCategory, v)
	return u
}

// UpdateCategory sets the "category" field to the value that was provided on create.
func (u *DictUpsert) UpdateCategory() *DictUpsert {
	u.SetExcluded(dict.FieldCategory)
	return u
}

// ClearCategory clears the value of the "category" field.
func (u *DictUpsert) ClearCategory() *DictUpsert {
	u.SetNull(dict.FieldCategory)
	return u
}

// SetCategoryDesc sets the "category_desc" field.
func (u *DictUpsert) SetCategoryDesc(v string) *DictUpsert {
	u.Set(dict.FieldCategoryDesc, v)
	return u
}

// UpdateCategoryDesc sets the "category_desc" field to the value that was provided on create.
func (u *DictUpsert) UpdateCategoryDesc() *DictUpsert {
	u.SetExcluded(dict.FieldCategoryDesc)
	return u
}

// ClearCategoryDesc clears the value of the "category_desc" field.
func (u *DictUpsert) ClearCategoryDesc() *DictUpsert {
	u.SetNull(dict.FieldCategoryDesc)
	return u
}

// SetValue sets the "value" field.
func (u *DictUpsert) SetValue(v string) *DictUpsert {
	u.Set(dict.FieldValue, v)
	return u
}

// UpdateValue sets the "value" field to the value that was provided on create.
func (u *DictUpsert) UpdateValue() *DictUpsert {
	u.SetExcluded(dict.FieldValue)
	return u
}

// ClearValue clears the value of the "value" field.
func (u *DictUpsert) ClearValue() *DictUpsert {
	u.SetNull(dict.FieldValue)
	return u
}

// SetValueDesc sets the "value_desc" field.
func (u *DictUpsert) SetValueDesc(v string) *DictUpsert {
	u.Set(dict.FieldValueDesc, v)
	return u
}

// UpdateValueDesc sets the "value_desc" field to the value that was provided on create.
func (u *DictUpsert) UpdateValueDesc() *DictUpsert {
	u.SetExcluded(dict.FieldValueDesc)
	return u
}

// ClearValueDesc clears the value of the "value_desc" field.
func (u *DictUpsert) ClearValueDesc() *DictUpsert {
	u.SetNull(dict.FieldValueDesc)
	return u
}

// SetValueDataType sets the "value_data_type" field.
func (u *DictUpsert) SetValueDataType(v string) *DictUpsert {
	u.Set(dict.FieldValueDataType, v)
	return u
}

// UpdateValueDataType sets the "value_data_type" field to the value that was provided on create.
func (u *DictUpsert) UpdateValueDataType() *DictUpsert {
	u.SetExcluded(dict.FieldValueDataType)
	return u
}

// ClearValueDataType clears the value of the "value_data_type" field.
func (u *DictUpsert) ClearValueDataType() *DictUpsert {
	u.SetNull(dict.FieldValueDataType)
	return u
}

// SetSortID sets the "sort_id" field.
func (u *DictUpsert) SetSortID(v int32) *DictUpsert {
	u.Set(dict.FieldSortID, v)
	return u
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *DictUpsert) UpdateSortID() *DictUpsert {
	u.SetExcluded(dict.FieldSortID)
	return u
}

// AddSortID adds v to the "sort_id" field.
func (u *DictUpsert) AddSortID(v int32) *DictUpsert {
	u.Add(dict.FieldSortID, v)
	return u
}

// ClearSortID clears the value of the "sort_id" field.
func (u *DictUpsert) ClearSortID() *DictUpsert {
	u.SetNull(dict.FieldSortID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Dict.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(dict.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DictUpsertOne) UpdateNewValues() *DictUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(dict.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(dict.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(dict.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Dict.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DictUpsertOne) Ignore() *DictUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DictUpsertOne) DoNothing() *DictUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DictCreate.OnConflict
// documentation for more info.
func (u *DictUpsertOne) Update(set func(*DictUpsert)) *DictUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DictUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *DictUpsertOne) SetUpdateTime(v time.Time) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateUpdateTime() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *DictUpsertOne) ClearUpdateTime() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *DictUpsertOne) SetDeleteTime(v time.Time) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateDeleteTime() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *DictUpsertOne) ClearDeleteTime() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *DictUpsertOne) SetStatus(v dict.Status) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateStatus() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *DictUpsertOne) ClearStatus() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *DictUpsertOne) SetCreateBy(v uint32) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *DictUpsertOne) AddCreateBy(v uint32) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateCreateBy() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *DictUpsertOne) ClearCreateBy() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *DictUpsertOne) SetUpdateBy(v uint32) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *DictUpsertOne) AddUpdateBy(v uint32) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateUpdateBy() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *DictUpsertOne) ClearUpdateBy() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *DictUpsertOne) SetRemark(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateRemark() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *DictUpsertOne) ClearRemark() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearRemark()
	})
}

// SetKey sets the "key" field.
func (u *DictUpsertOne) SetKey(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetKey(v)
	})
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateKey() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateKey()
	})
}

// ClearKey clears the value of the "key" field.
func (u *DictUpsertOne) ClearKey() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearKey()
	})
}

// SetCategory sets the "category" field.
func (u *DictUpsertOne) SetCategory(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetCategory(v)
	})
}

// UpdateCategory sets the "category" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateCategory() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateCategory()
	})
}

// ClearCategory clears the value of the "category" field.
func (u *DictUpsertOne) ClearCategory() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearCategory()
	})
}

// SetCategoryDesc sets the "category_desc" field.
func (u *DictUpsertOne) SetCategoryDesc(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetCategoryDesc(v)
	})
}

// UpdateCategoryDesc sets the "category_desc" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateCategoryDesc() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateCategoryDesc()
	})
}

// ClearCategoryDesc clears the value of the "category_desc" field.
func (u *DictUpsertOne) ClearCategoryDesc() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearCategoryDesc()
	})
}

// SetValue sets the "value" field.
func (u *DictUpsertOne) SetValue(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetValue(v)
	})
}

// UpdateValue sets the "value" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateValue() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateValue()
	})
}

// ClearValue clears the value of the "value" field.
func (u *DictUpsertOne) ClearValue() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearValue()
	})
}

// SetValueDesc sets the "value_desc" field.
func (u *DictUpsertOne) SetValueDesc(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetValueDesc(v)
	})
}

// UpdateValueDesc sets the "value_desc" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateValueDesc() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateValueDesc()
	})
}

// ClearValueDesc clears the value of the "value_desc" field.
func (u *DictUpsertOne) ClearValueDesc() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearValueDesc()
	})
}

// SetValueDataType sets the "value_data_type" field.
func (u *DictUpsertOne) SetValueDataType(v string) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetValueDataType(v)
	})
}

// UpdateValueDataType sets the "value_data_type" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateValueDataType() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateValueDataType()
	})
}

// ClearValueDataType clears the value of the "value_data_type" field.
func (u *DictUpsertOne) ClearValueDataType() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearValueDataType()
	})
}

// SetSortID sets the "sort_id" field.
func (u *DictUpsertOne) SetSortID(v int32) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *DictUpsertOne) AddSortID(v int32) *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *DictUpsertOne) UpdateSortID() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *DictUpsertOne) ClearSortID() *DictUpsertOne {
	return u.Update(func(s *DictUpsert) {
		s.ClearSortID()
	})
}

// Exec executes the query.
func (u *DictUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DictCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DictUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DictUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DictUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DictCreateBulk is the builder for creating many Dict entities in bulk.
type DictCreateBulk struct {
	config
	err      error
	builders []*DictCreate
	conflict []sql.ConflictOption
}

// Save creates the Dict entities in the database.
func (dcb *DictCreateBulk) Save(ctx context.Context) ([]*Dict, error) {
	if dcb.err != nil {
		return nil, dcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dcb.builders))
	nodes := make([]*Dict, len(dcb.builders))
	mutators := make([]Mutator, len(dcb.builders))
	for i := range dcb.builders {
		func(i int, root context.Context) {
			builder := dcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DictMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = dcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dcb *DictCreateBulk) SaveX(ctx context.Context) []*Dict {
	v, err := dcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dcb *DictCreateBulk) Exec(ctx context.Context) error {
	_, err := dcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dcb *DictCreateBulk) ExecX(ctx context.Context) {
	if err := dcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Dict.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DictUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (dcb *DictCreateBulk) OnConflict(opts ...sql.ConflictOption) *DictUpsertBulk {
	dcb.conflict = opts
	return &DictUpsertBulk{
		create: dcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Dict.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (dcb *DictCreateBulk) OnConflictColumns(columns ...string) *DictUpsertBulk {
	dcb.conflict = append(dcb.conflict, sql.ConflictColumns(columns...))
	return &DictUpsertBulk{
		create: dcb,
	}
}

// DictUpsertBulk is the builder for "upsert"-ing
// a bulk of Dict nodes.
type DictUpsertBulk struct {
	create *DictCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Dict.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(dict.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DictUpsertBulk) UpdateNewValues() *DictUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(dict.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(dict.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(dict.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Dict.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DictUpsertBulk) Ignore() *DictUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DictUpsertBulk) DoNothing() *DictUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DictCreateBulk.OnConflict
// documentation for more info.
func (u *DictUpsertBulk) Update(set func(*DictUpsert)) *DictUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DictUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *DictUpsertBulk) SetUpdateTime(v time.Time) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateUpdateTime() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *DictUpsertBulk) ClearUpdateTime() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *DictUpsertBulk) SetDeleteTime(v time.Time) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateDeleteTime() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *DictUpsertBulk) ClearDeleteTime() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *DictUpsertBulk) SetStatus(v dict.Status) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateStatus() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *DictUpsertBulk) ClearStatus() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *DictUpsertBulk) SetCreateBy(v uint32) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *DictUpsertBulk) AddCreateBy(v uint32) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateCreateBy() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *DictUpsertBulk) ClearCreateBy() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *DictUpsertBulk) SetUpdateBy(v uint32) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *DictUpsertBulk) AddUpdateBy(v uint32) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateUpdateBy() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *DictUpsertBulk) ClearUpdateBy() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *DictUpsertBulk) SetRemark(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateRemark() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *DictUpsertBulk) ClearRemark() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearRemark()
	})
}

// SetKey sets the "key" field.
func (u *DictUpsertBulk) SetKey(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetKey(v)
	})
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateKey() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateKey()
	})
}

// ClearKey clears the value of the "key" field.
func (u *DictUpsertBulk) ClearKey() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearKey()
	})
}

// SetCategory sets the "category" field.
func (u *DictUpsertBulk) SetCategory(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetCategory(v)
	})
}

// UpdateCategory sets the "category" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateCategory() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateCategory()
	})
}

// ClearCategory clears the value of the "category" field.
func (u *DictUpsertBulk) ClearCategory() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearCategory()
	})
}

// SetCategoryDesc sets the "category_desc" field.
func (u *DictUpsertBulk) SetCategoryDesc(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetCategoryDesc(v)
	})
}

// UpdateCategoryDesc sets the "category_desc" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateCategoryDesc() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateCategoryDesc()
	})
}

// ClearCategoryDesc clears the value of the "category_desc" field.
func (u *DictUpsertBulk) ClearCategoryDesc() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearCategoryDesc()
	})
}

// SetValue sets the "value" field.
func (u *DictUpsertBulk) SetValue(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetValue(v)
	})
}

// UpdateValue sets the "value" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateValue() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateValue()
	})
}

// ClearValue clears the value of the "value" field.
func (u *DictUpsertBulk) ClearValue() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearValue()
	})
}

// SetValueDesc sets the "value_desc" field.
func (u *DictUpsertBulk) SetValueDesc(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetValueDesc(v)
	})
}

// UpdateValueDesc sets the "value_desc" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateValueDesc() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateValueDesc()
	})
}

// ClearValueDesc clears the value of the "value_desc" field.
func (u *DictUpsertBulk) ClearValueDesc() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearValueDesc()
	})
}

// SetValueDataType sets the "value_data_type" field.
func (u *DictUpsertBulk) SetValueDataType(v string) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetValueDataType(v)
	})
}

// UpdateValueDataType sets the "value_data_type" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateValueDataType() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateValueDataType()
	})
}

// ClearValueDataType clears the value of the "value_data_type" field.
func (u *DictUpsertBulk) ClearValueDataType() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearValueDataType()
	})
}

// SetSortID sets the "sort_id" field.
func (u *DictUpsertBulk) SetSortID(v int32) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *DictUpsertBulk) AddSortID(v int32) *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *DictUpsertBulk) UpdateSortID() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *DictUpsertBulk) ClearSortID() *DictUpsertBulk {
	return u.Update(func(s *DictUpsert) {
		s.ClearSortID()
	})
}

// Exec executes the query.
func (u *DictUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DictCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DictCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DictUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
