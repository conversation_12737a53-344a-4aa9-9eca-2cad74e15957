// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessagecategory"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 站内信通知消息分类表
type NotificationMessageCategory struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 创建者ID
	CreateBy *uint32 `json:"create_by,omitempty"`
	// 更新者ID
	UpdateBy *uint32 `json:"update_by,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 租户ID
	TenantID *uint32 `json:"tenant_id,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 编码
	Code *string `json:"code,omitempty"`
	// 排序编号
	SortID *int32 `json:"sort_id,omitempty"`
	// 是否启用
	Enable *bool `json:"enable,omitempty"`
	// 父节点ID
	ParentID *uint32 `json:"parent_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the NotificationMessageCategoryQuery when eager-loading is set.
	Edges        NotificationMessageCategoryEdges `json:"edges"`
	selectValues sql.SelectValues
}

// NotificationMessageCategoryEdges holds the relations/edges for other nodes in the graph.
type NotificationMessageCategoryEdges struct {
	// Parent holds the value of the parent edge.
	Parent *NotificationMessageCategory `json:"parent,omitempty"`
	// Children holds the value of the children edge.
	Children []*NotificationMessageCategory `json:"children,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// ParentOrErr returns the Parent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e NotificationMessageCategoryEdges) ParentOrErr() (*NotificationMessageCategory, error) {
	if e.Parent != nil {
		return e.Parent, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: notificationmessagecategory.Label}
	}
	return nil, &NotLoadedError{edge: "parent"}
}

// ChildrenOrErr returns the Children value or an error if the edge
// was not loaded in eager-loading.
func (e NotificationMessageCategoryEdges) ChildrenOrErr() ([]*NotificationMessageCategory, error) {
	if e.loadedTypes[1] {
		return e.Children, nil
	}
	return nil, &NotLoadedError{edge: "children"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*NotificationMessageCategory) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case notificationmessagecategory.FieldEnable:
			values[i] = new(sql.NullBool)
		case notificationmessagecategory.FieldID, notificationmessagecategory.FieldCreateBy, notificationmessagecategory.FieldUpdateBy, notificationmessagecategory.FieldTenantID, notificationmessagecategory.FieldSortID, notificationmessagecategory.FieldParentID:
			values[i] = new(sql.NullInt64)
		case notificationmessagecategory.FieldRemark, notificationmessagecategory.FieldName, notificationmessagecategory.FieldCode:
			values[i] = new(sql.NullString)
		case notificationmessagecategory.FieldCreateTime, notificationmessagecategory.FieldUpdateTime, notificationmessagecategory.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the NotificationMessageCategory fields.
func (nmc *NotificationMessageCategory) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case notificationmessagecategory.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			nmc.ID = uint32(value.Int64)
		case notificationmessagecategory.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				nmc.CreateTime = new(time.Time)
				*nmc.CreateTime = value.Time
			}
		case notificationmessagecategory.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				nmc.UpdateTime = new(time.Time)
				*nmc.UpdateTime = value.Time
			}
		case notificationmessagecategory.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				nmc.DeleteTime = new(time.Time)
				*nmc.DeleteTime = value.Time
			}
		case notificationmessagecategory.FieldCreateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_by", values[i])
			} else if value.Valid {
				nmc.CreateBy = new(uint32)
				*nmc.CreateBy = uint32(value.Int64)
			}
		case notificationmessagecategory.FieldUpdateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field update_by", values[i])
			} else if value.Valid {
				nmc.UpdateBy = new(uint32)
				*nmc.UpdateBy = uint32(value.Int64)
			}
		case notificationmessagecategory.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				nmc.Remark = new(string)
				*nmc.Remark = value.String
			}
		case notificationmessagecategory.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				nmc.TenantID = new(uint32)
				*nmc.TenantID = uint32(value.Int64)
			}
		case notificationmessagecategory.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				nmc.Name = new(string)
				*nmc.Name = value.String
			}
		case notificationmessagecategory.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				nmc.Code = new(string)
				*nmc.Code = value.String
			}
		case notificationmessagecategory.FieldSortID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort_id", values[i])
			} else if value.Valid {
				nmc.SortID = new(int32)
				*nmc.SortID = int32(value.Int64)
			}
		case notificationmessagecategory.FieldEnable:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enable", values[i])
			} else if value.Valid {
				nmc.Enable = new(bool)
				*nmc.Enable = value.Bool
			}
		case notificationmessagecategory.FieldParentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field parent_id", values[i])
			} else if value.Valid {
				nmc.ParentID = new(uint32)
				*nmc.ParentID = uint32(value.Int64)
			}
		default:
			nmc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the NotificationMessageCategory.
// This includes values selected through modifiers, order, etc.
func (nmc *NotificationMessageCategory) Value(name string) (ent.Value, error) {
	return nmc.selectValues.Get(name)
}

// QueryParent queries the "parent" edge of the NotificationMessageCategory entity.
func (nmc *NotificationMessageCategory) QueryParent() *NotificationMessageCategoryQuery {
	return NewNotificationMessageCategoryClient(nmc.config).QueryParent(nmc)
}

// QueryChildren queries the "children" edge of the NotificationMessageCategory entity.
func (nmc *NotificationMessageCategory) QueryChildren() *NotificationMessageCategoryQuery {
	return NewNotificationMessageCategoryClient(nmc.config).QueryChildren(nmc)
}

// Update returns a builder for updating this NotificationMessageCategory.
// Note that you need to call NotificationMessageCategory.Unwrap() before calling this method if this NotificationMessageCategory
// was returned from a transaction, and the transaction was committed or rolled back.
func (nmc *NotificationMessageCategory) Update() *NotificationMessageCategoryUpdateOne {
	return NewNotificationMessageCategoryClient(nmc.config).UpdateOne(nmc)
}

// Unwrap unwraps the NotificationMessageCategory entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (nmc *NotificationMessageCategory) Unwrap() *NotificationMessageCategory {
	_tx, ok := nmc.config.driver.(*txDriver)
	if !ok {
		panic("ent: NotificationMessageCategory is not a transactional entity")
	}
	nmc.config.driver = _tx.drv
	return nmc
}

// String implements the fmt.Stringer.
func (nmc *NotificationMessageCategory) String() string {
	var builder strings.Builder
	builder.WriteString("NotificationMessageCategory(")
	builder.WriteString(fmt.Sprintf("id=%v, ", nmc.ID))
	if v := nmc.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := nmc.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := nmc.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := nmc.CreateBy; v != nil {
		builder.WriteString("create_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nmc.UpdateBy; v != nil {
		builder.WriteString("update_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nmc.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := nmc.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nmc.Name; v != nil {
		builder.WriteString("name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := nmc.Code; v != nil {
		builder.WriteString("code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := nmc.SortID; v != nil {
		builder.WriteString("sort_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nmc.Enable; v != nil {
		builder.WriteString("enable=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nmc.ParentID; v != nil {
		builder.WriteString("parent_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// NotificationMessageCategories is a parsable slice of NotificationMessageCategory.
type NotificationMessageCategories []*NotificationMessageCategory
