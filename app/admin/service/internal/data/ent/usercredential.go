// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/usercredential"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 用户认证信息表
type UserCredential struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 租户ID
	TenantID *uint32 `json:"tenant_id,omitempty"`
	// 关联主表的用户ID
	UserID *uint32 `json:"user_id,omitempty"`
	// 认证方式类型
	IdentityType *usercredential.IdentityType `json:"identity_type,omitempty"`
	// 身份唯一标识符
	Identifier *string `json:"identifier,omitempty"`
	// 凭证类型
	CredentialType *usercredential.CredentialType `json:"credential_type,omitempty"`
	// 凭证
	Credential *string `json:"credential,omitempty"`
	// 是否主认证方式
	IsPrimary *bool `json:"is_primary,omitempty"`
	// 凭证状态
	Status *usercredential.Status `json:"status,omitempty"`
	// 扩展信息
	ExtraInfo *string `json:"extra_info,omitempty"`
	// 激活账号用的令牌
	ActivateToken *string `json:"activate_token,omitempty"`
	// 重置密码用的令牌
	ResetToken   *string `json:"reset_token,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*UserCredential) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case usercredential.FieldIsPrimary:
			values[i] = new(sql.NullBool)
		case usercredential.FieldID, usercredential.FieldTenantID, usercredential.FieldUserID:
			values[i] = new(sql.NullInt64)
		case usercredential.FieldIdentityType, usercredential.FieldIdentifier, usercredential.FieldCredentialType, usercredential.FieldCredential, usercredential.FieldStatus, usercredential.FieldExtraInfo, usercredential.FieldActivateToken, usercredential.FieldResetToken:
			values[i] = new(sql.NullString)
		case usercredential.FieldCreateTime, usercredential.FieldUpdateTime, usercredential.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the UserCredential fields.
func (uc *UserCredential) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case usercredential.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			uc.ID = uint32(value.Int64)
		case usercredential.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				uc.CreateTime = new(time.Time)
				*uc.CreateTime = value.Time
			}
		case usercredential.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				uc.UpdateTime = new(time.Time)
				*uc.UpdateTime = value.Time
			}
		case usercredential.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				uc.DeleteTime = new(time.Time)
				*uc.DeleteTime = value.Time
			}
		case usercredential.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				uc.TenantID = new(uint32)
				*uc.TenantID = uint32(value.Int64)
			}
		case usercredential.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				uc.UserID = new(uint32)
				*uc.UserID = uint32(value.Int64)
			}
		case usercredential.FieldIdentityType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field identity_type", values[i])
			} else if value.Valid {
				uc.IdentityType = new(usercredential.IdentityType)
				*uc.IdentityType = usercredential.IdentityType(value.String)
			}
		case usercredential.FieldIdentifier:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field identifier", values[i])
			} else if value.Valid {
				uc.Identifier = new(string)
				*uc.Identifier = value.String
			}
		case usercredential.FieldCredentialType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field credential_type", values[i])
			} else if value.Valid {
				uc.CredentialType = new(usercredential.CredentialType)
				*uc.CredentialType = usercredential.CredentialType(value.String)
			}
		case usercredential.FieldCredential:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field credential", values[i])
			} else if value.Valid {
				uc.Credential = new(string)
				*uc.Credential = value.String
			}
		case usercredential.FieldIsPrimary:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_primary", values[i])
			} else if value.Valid {
				uc.IsPrimary = new(bool)
				*uc.IsPrimary = value.Bool
			}
		case usercredential.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				uc.Status = new(usercredential.Status)
				*uc.Status = usercredential.Status(value.String)
			}
		case usercredential.FieldExtraInfo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field extra_info", values[i])
			} else if value.Valid {
				uc.ExtraInfo = new(string)
				*uc.ExtraInfo = value.String
			}
		case usercredential.FieldActivateToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field activate_token", values[i])
			} else if value.Valid {
				uc.ActivateToken = new(string)
				*uc.ActivateToken = value.String
			}
		case usercredential.FieldResetToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reset_token", values[i])
			} else if value.Valid {
				uc.ResetToken = new(string)
				*uc.ResetToken = value.String
			}
		default:
			uc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the UserCredential.
// This includes values selected through modifiers, order, etc.
func (uc *UserCredential) Value(name string) (ent.Value, error) {
	return uc.selectValues.Get(name)
}

// Update returns a builder for updating this UserCredential.
// Note that you need to call UserCredential.Unwrap() before calling this method if this UserCredential
// was returned from a transaction, and the transaction was committed or rolled back.
func (uc *UserCredential) Update() *UserCredentialUpdateOne {
	return NewUserCredentialClient(uc.config).UpdateOne(uc)
}

// Unwrap unwraps the UserCredential entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (uc *UserCredential) Unwrap() *UserCredential {
	_tx, ok := uc.config.driver.(*txDriver)
	if !ok {
		panic("ent: UserCredential is not a transactional entity")
	}
	uc.config.driver = _tx.drv
	return uc
}

// String implements the fmt.Stringer.
func (uc *UserCredential) String() string {
	var builder strings.Builder
	builder.WriteString("UserCredential(")
	builder.WriteString(fmt.Sprintf("id=%v, ", uc.ID))
	if v := uc.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := uc.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := uc.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := uc.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := uc.UserID; v != nil {
		builder.WriteString("user_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := uc.IdentityType; v != nil {
		builder.WriteString("identity_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := uc.Identifier; v != nil {
		builder.WriteString("identifier=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := uc.CredentialType; v != nil {
		builder.WriteString("credential_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := uc.Credential; v != nil {
		builder.WriteString("credential=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := uc.IsPrimary; v != nil {
		builder.WriteString("is_primary=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := uc.Status; v != nil {
		builder.WriteString("status=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := uc.ExtraInfo; v != nil {
		builder.WriteString("extra_info=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := uc.ActivateToken; v != nil {
		builder.WriteString("activate_token=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := uc.ResetToken; v != nil {
		builder.WriteString("reset_token=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// UserCredentials is a parsable slice of UserCredential.
type UserCredentials []*UserCredential
