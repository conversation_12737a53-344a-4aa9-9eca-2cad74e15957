// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/file"
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FileUpdate is the builder for updating File entities.
type FileUpdate struct {
	config
	hooks     []Hook
	mutation  *FileMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the FileUpdate builder.
func (fu *FileUpdate) Where(ps ...predicate.File) *FileUpdate {
	fu.mutation.Where(ps...)
	return fu
}

// SetUpdateTime sets the "update_time" field.
func (fu *FileUpdate) SetUpdateTime(t time.Time) *FileUpdate {
	fu.mutation.SetUpdateTime(t)
	return fu
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (fu *FileUpdate) SetNillableUpdateTime(t *time.Time) *FileUpdate {
	if t != nil {
		fu.SetUpdateTime(*t)
	}
	return fu
}

// ClearUpdateTime clears the value of the "update_time" field.
func (fu *FileUpdate) ClearUpdateTime() *FileUpdate {
	fu.mutation.ClearUpdateTime()
	return fu
}

// SetDeleteTime sets the "delete_time" field.
func (fu *FileUpdate) SetDeleteTime(t time.Time) *FileUpdate {
	fu.mutation.SetDeleteTime(t)
	return fu
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (fu *FileUpdate) SetNillableDeleteTime(t *time.Time) *FileUpdate {
	if t != nil {
		fu.SetDeleteTime(*t)
	}
	return fu
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (fu *FileUpdate) ClearDeleteTime() *FileUpdate {
	fu.mutation.ClearDeleteTime()
	return fu
}

// SetCreateBy sets the "create_by" field.
func (fu *FileUpdate) SetCreateBy(u uint32) *FileUpdate {
	fu.mutation.ResetCreateBy()
	fu.mutation.SetCreateBy(u)
	return fu
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (fu *FileUpdate) SetNillableCreateBy(u *uint32) *FileUpdate {
	if u != nil {
		fu.SetCreateBy(*u)
	}
	return fu
}

// AddCreateBy adds u to the "create_by" field.
func (fu *FileUpdate) AddCreateBy(u int32) *FileUpdate {
	fu.mutation.AddCreateBy(u)
	return fu
}

// ClearCreateBy clears the value of the "create_by" field.
func (fu *FileUpdate) ClearCreateBy() *FileUpdate {
	fu.mutation.ClearCreateBy()
	return fu
}

// SetRemark sets the "remark" field.
func (fu *FileUpdate) SetRemark(s string) *FileUpdate {
	fu.mutation.SetRemark(s)
	return fu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (fu *FileUpdate) SetNillableRemark(s *string) *FileUpdate {
	if s != nil {
		fu.SetRemark(*s)
	}
	return fu
}

// ClearRemark clears the value of the "remark" field.
func (fu *FileUpdate) ClearRemark() *FileUpdate {
	fu.mutation.ClearRemark()
	return fu
}

// SetProvider sets the "provider" field.
func (fu *FileUpdate) SetProvider(f file.Provider) *FileUpdate {
	fu.mutation.SetProvider(f)
	return fu
}

// SetNillableProvider sets the "provider" field if the given value is not nil.
func (fu *FileUpdate) SetNillableProvider(f *file.Provider) *FileUpdate {
	if f != nil {
		fu.SetProvider(*f)
	}
	return fu
}

// ClearProvider clears the value of the "provider" field.
func (fu *FileUpdate) ClearProvider() *FileUpdate {
	fu.mutation.ClearProvider()
	return fu
}

// SetBucketName sets the "bucket_name" field.
func (fu *FileUpdate) SetBucketName(s string) *FileUpdate {
	fu.mutation.SetBucketName(s)
	return fu
}

// SetNillableBucketName sets the "bucket_name" field if the given value is not nil.
func (fu *FileUpdate) SetNillableBucketName(s *string) *FileUpdate {
	if s != nil {
		fu.SetBucketName(*s)
	}
	return fu
}

// ClearBucketName clears the value of the "bucket_name" field.
func (fu *FileUpdate) ClearBucketName() *FileUpdate {
	fu.mutation.ClearBucketName()
	return fu
}

// SetFileDirectory sets the "file_directory" field.
func (fu *FileUpdate) SetFileDirectory(s string) *FileUpdate {
	fu.mutation.SetFileDirectory(s)
	return fu
}

// SetNillableFileDirectory sets the "file_directory" field if the given value is not nil.
func (fu *FileUpdate) SetNillableFileDirectory(s *string) *FileUpdate {
	if s != nil {
		fu.SetFileDirectory(*s)
	}
	return fu
}

// ClearFileDirectory clears the value of the "file_directory" field.
func (fu *FileUpdate) ClearFileDirectory() *FileUpdate {
	fu.mutation.ClearFileDirectory()
	return fu
}

// SetFileGUID sets the "file_guid" field.
func (fu *FileUpdate) SetFileGUID(s string) *FileUpdate {
	fu.mutation.SetFileGUID(s)
	return fu
}

// SetNillableFileGUID sets the "file_guid" field if the given value is not nil.
func (fu *FileUpdate) SetNillableFileGUID(s *string) *FileUpdate {
	if s != nil {
		fu.SetFileGUID(*s)
	}
	return fu
}

// ClearFileGUID clears the value of the "file_guid" field.
func (fu *FileUpdate) ClearFileGUID() *FileUpdate {
	fu.mutation.ClearFileGUID()
	return fu
}

// SetSaveFileName sets the "save_file_name" field.
func (fu *FileUpdate) SetSaveFileName(s string) *FileUpdate {
	fu.mutation.SetSaveFileName(s)
	return fu
}

// SetNillableSaveFileName sets the "save_file_name" field if the given value is not nil.
func (fu *FileUpdate) SetNillableSaveFileName(s *string) *FileUpdate {
	if s != nil {
		fu.SetSaveFileName(*s)
	}
	return fu
}

// ClearSaveFileName clears the value of the "save_file_name" field.
func (fu *FileUpdate) ClearSaveFileName() *FileUpdate {
	fu.mutation.ClearSaveFileName()
	return fu
}

// SetFileName sets the "file_name" field.
func (fu *FileUpdate) SetFileName(s string) *FileUpdate {
	fu.mutation.SetFileName(s)
	return fu
}

// SetNillableFileName sets the "file_name" field if the given value is not nil.
func (fu *FileUpdate) SetNillableFileName(s *string) *FileUpdate {
	if s != nil {
		fu.SetFileName(*s)
	}
	return fu
}

// ClearFileName clears the value of the "file_name" field.
func (fu *FileUpdate) ClearFileName() *FileUpdate {
	fu.mutation.ClearFileName()
	return fu
}

// SetExtension sets the "extension" field.
func (fu *FileUpdate) SetExtension(s string) *FileUpdate {
	fu.mutation.SetExtension(s)
	return fu
}

// SetNillableExtension sets the "extension" field if the given value is not nil.
func (fu *FileUpdate) SetNillableExtension(s *string) *FileUpdate {
	if s != nil {
		fu.SetExtension(*s)
	}
	return fu
}

// ClearExtension clears the value of the "extension" field.
func (fu *FileUpdate) ClearExtension() *FileUpdate {
	fu.mutation.ClearExtension()
	return fu
}

// SetSize sets the "size" field.
func (fu *FileUpdate) SetSize(u uint64) *FileUpdate {
	fu.mutation.ResetSize()
	fu.mutation.SetSize(u)
	return fu
}

// SetNillableSize sets the "size" field if the given value is not nil.
func (fu *FileUpdate) SetNillableSize(u *uint64) *FileUpdate {
	if u != nil {
		fu.SetSize(*u)
	}
	return fu
}

// AddSize adds u to the "size" field.
func (fu *FileUpdate) AddSize(u int64) *FileUpdate {
	fu.mutation.AddSize(u)
	return fu
}

// ClearSize clears the value of the "size" field.
func (fu *FileUpdate) ClearSize() *FileUpdate {
	fu.mutation.ClearSize()
	return fu
}

// SetSizeFormat sets the "size_format" field.
func (fu *FileUpdate) SetSizeFormat(s string) *FileUpdate {
	fu.mutation.SetSizeFormat(s)
	return fu
}

// SetNillableSizeFormat sets the "size_format" field if the given value is not nil.
func (fu *FileUpdate) SetNillableSizeFormat(s *string) *FileUpdate {
	if s != nil {
		fu.SetSizeFormat(*s)
	}
	return fu
}

// ClearSizeFormat clears the value of the "size_format" field.
func (fu *FileUpdate) ClearSizeFormat() *FileUpdate {
	fu.mutation.ClearSizeFormat()
	return fu
}

// SetLinkURL sets the "link_url" field.
func (fu *FileUpdate) SetLinkURL(s string) *FileUpdate {
	fu.mutation.SetLinkURL(s)
	return fu
}

// SetNillableLinkURL sets the "link_url" field if the given value is not nil.
func (fu *FileUpdate) SetNillableLinkURL(s *string) *FileUpdate {
	if s != nil {
		fu.SetLinkURL(*s)
	}
	return fu
}

// ClearLinkURL clears the value of the "link_url" field.
func (fu *FileUpdate) ClearLinkURL() *FileUpdate {
	fu.mutation.ClearLinkURL()
	return fu
}

// SetMd5 sets the "md5" field.
func (fu *FileUpdate) SetMd5(s string) *FileUpdate {
	fu.mutation.SetMd5(s)
	return fu
}

// SetNillableMd5 sets the "md5" field if the given value is not nil.
func (fu *FileUpdate) SetNillableMd5(s *string) *FileUpdate {
	if s != nil {
		fu.SetMd5(*s)
	}
	return fu
}

// ClearMd5 clears the value of the "md5" field.
func (fu *FileUpdate) ClearMd5() *FileUpdate {
	fu.mutation.ClearMd5()
	return fu
}

// Mutation returns the FileMutation object of the builder.
func (fu *FileUpdate) Mutation() *FileMutation {
	return fu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (fu *FileUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, fu.sqlSave, fu.mutation, fu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fu *FileUpdate) SaveX(ctx context.Context) int {
	affected, err := fu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (fu *FileUpdate) Exec(ctx context.Context) error {
	_, err := fu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fu *FileUpdate) ExecX(ctx context.Context) {
	if err := fu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fu *FileUpdate) check() error {
	if v, ok := fu.mutation.Provider(); ok {
		if err := file.ProviderValidator(v); err != nil {
			return &ValidationError{Name: "provider", err: fmt.Errorf(`ent: validator failed for field "File.provider": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (fu *FileUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *FileUpdate {
	fu.modifiers = append(fu.modifiers, modifiers...)
	return fu
}

func (fu *FileUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := fu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(file.Table, file.Columns, sqlgraph.NewFieldSpec(file.FieldID, field.TypeUint32))
	if ps := fu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if fu.mutation.CreateTimeCleared() {
		_spec.ClearField(file.FieldCreateTime, field.TypeTime)
	}
	if value, ok := fu.mutation.UpdateTime(); ok {
		_spec.SetField(file.FieldUpdateTime, field.TypeTime, value)
	}
	if fu.mutation.UpdateTimeCleared() {
		_spec.ClearField(file.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := fu.mutation.DeleteTime(); ok {
		_spec.SetField(file.FieldDeleteTime, field.TypeTime, value)
	}
	if fu.mutation.DeleteTimeCleared() {
		_spec.ClearField(file.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := fu.mutation.CreateBy(); ok {
		_spec.SetField(file.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := fu.mutation.AddedCreateBy(); ok {
		_spec.AddField(file.FieldCreateBy, field.TypeUint32, value)
	}
	if fu.mutation.CreateByCleared() {
		_spec.ClearField(file.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := fu.mutation.Remark(); ok {
		_spec.SetField(file.FieldRemark, field.TypeString, value)
	}
	if fu.mutation.RemarkCleared() {
		_spec.ClearField(file.FieldRemark, field.TypeString)
	}
	if fu.mutation.TenantIDCleared() {
		_spec.ClearField(file.FieldTenantID, field.TypeUint32)
	}
	if value, ok := fu.mutation.Provider(); ok {
		_spec.SetField(file.FieldProvider, field.TypeEnum, value)
	}
	if fu.mutation.ProviderCleared() {
		_spec.ClearField(file.FieldProvider, field.TypeEnum)
	}
	if value, ok := fu.mutation.BucketName(); ok {
		_spec.SetField(file.FieldBucketName, field.TypeString, value)
	}
	if fu.mutation.BucketNameCleared() {
		_spec.ClearField(file.FieldBucketName, field.TypeString)
	}
	if value, ok := fu.mutation.FileDirectory(); ok {
		_spec.SetField(file.FieldFileDirectory, field.TypeString, value)
	}
	if fu.mutation.FileDirectoryCleared() {
		_spec.ClearField(file.FieldFileDirectory, field.TypeString)
	}
	if value, ok := fu.mutation.FileGUID(); ok {
		_spec.SetField(file.FieldFileGUID, field.TypeString, value)
	}
	if fu.mutation.FileGUIDCleared() {
		_spec.ClearField(file.FieldFileGUID, field.TypeString)
	}
	if value, ok := fu.mutation.SaveFileName(); ok {
		_spec.SetField(file.FieldSaveFileName, field.TypeString, value)
	}
	if fu.mutation.SaveFileNameCleared() {
		_spec.ClearField(file.FieldSaveFileName, field.TypeString)
	}
	if value, ok := fu.mutation.FileName(); ok {
		_spec.SetField(file.FieldFileName, field.TypeString, value)
	}
	if fu.mutation.FileNameCleared() {
		_spec.ClearField(file.FieldFileName, field.TypeString)
	}
	if value, ok := fu.mutation.Extension(); ok {
		_spec.SetField(file.FieldExtension, field.TypeString, value)
	}
	if fu.mutation.ExtensionCleared() {
		_spec.ClearField(file.FieldExtension, field.TypeString)
	}
	if value, ok := fu.mutation.Size(); ok {
		_spec.SetField(file.FieldSize, field.TypeUint64, value)
	}
	if value, ok := fu.mutation.AddedSize(); ok {
		_spec.AddField(file.FieldSize, field.TypeUint64, value)
	}
	if fu.mutation.SizeCleared() {
		_spec.ClearField(file.FieldSize, field.TypeUint64)
	}
	if value, ok := fu.mutation.SizeFormat(); ok {
		_spec.SetField(file.FieldSizeFormat, field.TypeString, value)
	}
	if fu.mutation.SizeFormatCleared() {
		_spec.ClearField(file.FieldSizeFormat, field.TypeString)
	}
	if value, ok := fu.mutation.LinkURL(); ok {
		_spec.SetField(file.FieldLinkURL, field.TypeString, value)
	}
	if fu.mutation.LinkURLCleared() {
		_spec.ClearField(file.FieldLinkURL, field.TypeString)
	}
	if value, ok := fu.mutation.Md5(); ok {
		_spec.SetField(file.FieldMd5, field.TypeString, value)
	}
	if fu.mutation.Md5Cleared() {
		_spec.ClearField(file.FieldMd5, field.TypeString)
	}
	_spec.AddModifiers(fu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, fu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{file.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	fu.mutation.done = true
	return n, nil
}

// FileUpdateOne is the builder for updating a single File entity.
type FileUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *FileMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdateTime sets the "update_time" field.
func (fuo *FileUpdateOne) SetUpdateTime(t time.Time) *FileUpdateOne {
	fuo.mutation.SetUpdateTime(t)
	return fuo
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableUpdateTime(t *time.Time) *FileUpdateOne {
	if t != nil {
		fuo.SetUpdateTime(*t)
	}
	return fuo
}

// ClearUpdateTime clears the value of the "update_time" field.
func (fuo *FileUpdateOne) ClearUpdateTime() *FileUpdateOne {
	fuo.mutation.ClearUpdateTime()
	return fuo
}

// SetDeleteTime sets the "delete_time" field.
func (fuo *FileUpdateOne) SetDeleteTime(t time.Time) *FileUpdateOne {
	fuo.mutation.SetDeleteTime(t)
	return fuo
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableDeleteTime(t *time.Time) *FileUpdateOne {
	if t != nil {
		fuo.SetDeleteTime(*t)
	}
	return fuo
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (fuo *FileUpdateOne) ClearDeleteTime() *FileUpdateOne {
	fuo.mutation.ClearDeleteTime()
	return fuo
}

// SetCreateBy sets the "create_by" field.
func (fuo *FileUpdateOne) SetCreateBy(u uint32) *FileUpdateOne {
	fuo.mutation.ResetCreateBy()
	fuo.mutation.SetCreateBy(u)
	return fuo
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableCreateBy(u *uint32) *FileUpdateOne {
	if u != nil {
		fuo.SetCreateBy(*u)
	}
	return fuo
}

// AddCreateBy adds u to the "create_by" field.
func (fuo *FileUpdateOne) AddCreateBy(u int32) *FileUpdateOne {
	fuo.mutation.AddCreateBy(u)
	return fuo
}

// ClearCreateBy clears the value of the "create_by" field.
func (fuo *FileUpdateOne) ClearCreateBy() *FileUpdateOne {
	fuo.mutation.ClearCreateBy()
	return fuo
}

// SetRemark sets the "remark" field.
func (fuo *FileUpdateOne) SetRemark(s string) *FileUpdateOne {
	fuo.mutation.SetRemark(s)
	return fuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableRemark(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetRemark(*s)
	}
	return fuo
}

// ClearRemark clears the value of the "remark" field.
func (fuo *FileUpdateOne) ClearRemark() *FileUpdateOne {
	fuo.mutation.ClearRemark()
	return fuo
}

// SetProvider sets the "provider" field.
func (fuo *FileUpdateOne) SetProvider(f file.Provider) *FileUpdateOne {
	fuo.mutation.SetProvider(f)
	return fuo
}

// SetNillableProvider sets the "provider" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableProvider(f *file.Provider) *FileUpdateOne {
	if f != nil {
		fuo.SetProvider(*f)
	}
	return fuo
}

// ClearProvider clears the value of the "provider" field.
func (fuo *FileUpdateOne) ClearProvider() *FileUpdateOne {
	fuo.mutation.ClearProvider()
	return fuo
}

// SetBucketName sets the "bucket_name" field.
func (fuo *FileUpdateOne) SetBucketName(s string) *FileUpdateOne {
	fuo.mutation.SetBucketName(s)
	return fuo
}

// SetNillableBucketName sets the "bucket_name" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableBucketName(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetBucketName(*s)
	}
	return fuo
}

// ClearBucketName clears the value of the "bucket_name" field.
func (fuo *FileUpdateOne) ClearBucketName() *FileUpdateOne {
	fuo.mutation.ClearBucketName()
	return fuo
}

// SetFileDirectory sets the "file_directory" field.
func (fuo *FileUpdateOne) SetFileDirectory(s string) *FileUpdateOne {
	fuo.mutation.SetFileDirectory(s)
	return fuo
}

// SetNillableFileDirectory sets the "file_directory" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableFileDirectory(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetFileDirectory(*s)
	}
	return fuo
}

// ClearFileDirectory clears the value of the "file_directory" field.
func (fuo *FileUpdateOne) ClearFileDirectory() *FileUpdateOne {
	fuo.mutation.ClearFileDirectory()
	return fuo
}

// SetFileGUID sets the "file_guid" field.
func (fuo *FileUpdateOne) SetFileGUID(s string) *FileUpdateOne {
	fuo.mutation.SetFileGUID(s)
	return fuo
}

// SetNillableFileGUID sets the "file_guid" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableFileGUID(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetFileGUID(*s)
	}
	return fuo
}

// ClearFileGUID clears the value of the "file_guid" field.
func (fuo *FileUpdateOne) ClearFileGUID() *FileUpdateOne {
	fuo.mutation.ClearFileGUID()
	return fuo
}

// SetSaveFileName sets the "save_file_name" field.
func (fuo *FileUpdateOne) SetSaveFileName(s string) *FileUpdateOne {
	fuo.mutation.SetSaveFileName(s)
	return fuo
}

// SetNillableSaveFileName sets the "save_file_name" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableSaveFileName(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetSaveFileName(*s)
	}
	return fuo
}

// ClearSaveFileName clears the value of the "save_file_name" field.
func (fuo *FileUpdateOne) ClearSaveFileName() *FileUpdateOne {
	fuo.mutation.ClearSaveFileName()
	return fuo
}

// SetFileName sets the "file_name" field.
func (fuo *FileUpdateOne) SetFileName(s string) *FileUpdateOne {
	fuo.mutation.SetFileName(s)
	return fuo
}

// SetNillableFileName sets the "file_name" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableFileName(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetFileName(*s)
	}
	return fuo
}

// ClearFileName clears the value of the "file_name" field.
func (fuo *FileUpdateOne) ClearFileName() *FileUpdateOne {
	fuo.mutation.ClearFileName()
	return fuo
}

// SetExtension sets the "extension" field.
func (fuo *FileUpdateOne) SetExtension(s string) *FileUpdateOne {
	fuo.mutation.SetExtension(s)
	return fuo
}

// SetNillableExtension sets the "extension" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableExtension(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetExtension(*s)
	}
	return fuo
}

// ClearExtension clears the value of the "extension" field.
func (fuo *FileUpdateOne) ClearExtension() *FileUpdateOne {
	fuo.mutation.ClearExtension()
	return fuo
}

// SetSize sets the "size" field.
func (fuo *FileUpdateOne) SetSize(u uint64) *FileUpdateOne {
	fuo.mutation.ResetSize()
	fuo.mutation.SetSize(u)
	return fuo
}

// SetNillableSize sets the "size" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableSize(u *uint64) *FileUpdateOne {
	if u != nil {
		fuo.SetSize(*u)
	}
	return fuo
}

// AddSize adds u to the "size" field.
func (fuo *FileUpdateOne) AddSize(u int64) *FileUpdateOne {
	fuo.mutation.AddSize(u)
	return fuo
}

// ClearSize clears the value of the "size" field.
func (fuo *FileUpdateOne) ClearSize() *FileUpdateOne {
	fuo.mutation.ClearSize()
	return fuo
}

// SetSizeFormat sets the "size_format" field.
func (fuo *FileUpdateOne) SetSizeFormat(s string) *FileUpdateOne {
	fuo.mutation.SetSizeFormat(s)
	return fuo
}

// SetNillableSizeFormat sets the "size_format" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableSizeFormat(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetSizeFormat(*s)
	}
	return fuo
}

// ClearSizeFormat clears the value of the "size_format" field.
func (fuo *FileUpdateOne) ClearSizeFormat() *FileUpdateOne {
	fuo.mutation.ClearSizeFormat()
	return fuo
}

// SetLinkURL sets the "link_url" field.
func (fuo *FileUpdateOne) SetLinkURL(s string) *FileUpdateOne {
	fuo.mutation.SetLinkURL(s)
	return fuo
}

// SetNillableLinkURL sets the "link_url" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableLinkURL(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetLinkURL(*s)
	}
	return fuo
}

// ClearLinkURL clears the value of the "link_url" field.
func (fuo *FileUpdateOne) ClearLinkURL() *FileUpdateOne {
	fuo.mutation.ClearLinkURL()
	return fuo
}

// SetMd5 sets the "md5" field.
func (fuo *FileUpdateOne) SetMd5(s string) *FileUpdateOne {
	fuo.mutation.SetMd5(s)
	return fuo
}

// SetNillableMd5 sets the "md5" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableMd5(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetMd5(*s)
	}
	return fuo
}

// ClearMd5 clears the value of the "md5" field.
func (fuo *FileUpdateOne) ClearMd5() *FileUpdateOne {
	fuo.mutation.ClearMd5()
	return fuo
}

// Mutation returns the FileMutation object of the builder.
func (fuo *FileUpdateOne) Mutation() *FileMutation {
	return fuo.mutation
}

// Where appends a list predicates to the FileUpdate builder.
func (fuo *FileUpdateOne) Where(ps ...predicate.File) *FileUpdateOne {
	fuo.mutation.Where(ps...)
	return fuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (fuo *FileUpdateOne) Select(field string, fields ...string) *FileUpdateOne {
	fuo.fields = append([]string{field}, fields...)
	return fuo
}

// Save executes the query and returns the updated File entity.
func (fuo *FileUpdateOne) Save(ctx context.Context) (*File, error) {
	return withHooks(ctx, fuo.sqlSave, fuo.mutation, fuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fuo *FileUpdateOne) SaveX(ctx context.Context) *File {
	node, err := fuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (fuo *FileUpdateOne) Exec(ctx context.Context) error {
	_, err := fuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fuo *FileUpdateOne) ExecX(ctx context.Context) {
	if err := fuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fuo *FileUpdateOne) check() error {
	if v, ok := fuo.mutation.Provider(); ok {
		if err := file.ProviderValidator(v); err != nil {
			return &ValidationError{Name: "provider", err: fmt.Errorf(`ent: validator failed for field "File.provider": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (fuo *FileUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *FileUpdateOne {
	fuo.modifiers = append(fuo.modifiers, modifiers...)
	return fuo
}

func (fuo *FileUpdateOne) sqlSave(ctx context.Context) (_node *File, err error) {
	if err := fuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(file.Table, file.Columns, sqlgraph.NewFieldSpec(file.FieldID, field.TypeUint32))
	id, ok := fuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "File.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := fuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, file.FieldID)
		for _, f := range fields {
			if !file.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != file.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := fuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if fuo.mutation.CreateTimeCleared() {
		_spec.ClearField(file.FieldCreateTime, field.TypeTime)
	}
	if value, ok := fuo.mutation.UpdateTime(); ok {
		_spec.SetField(file.FieldUpdateTime, field.TypeTime, value)
	}
	if fuo.mutation.UpdateTimeCleared() {
		_spec.ClearField(file.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := fuo.mutation.DeleteTime(); ok {
		_spec.SetField(file.FieldDeleteTime, field.TypeTime, value)
	}
	if fuo.mutation.DeleteTimeCleared() {
		_spec.ClearField(file.FieldDeleteTime, field.TypeTime)
	}
	if value, ok := fuo.mutation.CreateBy(); ok {
		_spec.SetField(file.FieldCreateBy, field.TypeUint32, value)
	}
	if value, ok := fuo.mutation.AddedCreateBy(); ok {
		_spec.AddField(file.FieldCreateBy, field.TypeUint32, value)
	}
	if fuo.mutation.CreateByCleared() {
		_spec.ClearField(file.FieldCreateBy, field.TypeUint32)
	}
	if value, ok := fuo.mutation.Remark(); ok {
		_spec.SetField(file.FieldRemark, field.TypeString, value)
	}
	if fuo.mutation.RemarkCleared() {
		_spec.ClearField(file.FieldRemark, field.TypeString)
	}
	if fuo.mutation.TenantIDCleared() {
		_spec.ClearField(file.FieldTenantID, field.TypeUint32)
	}
	if value, ok := fuo.mutation.Provider(); ok {
		_spec.SetField(file.FieldProvider, field.TypeEnum, value)
	}
	if fuo.mutation.ProviderCleared() {
		_spec.ClearField(file.FieldProvider, field.TypeEnum)
	}
	if value, ok := fuo.mutation.BucketName(); ok {
		_spec.SetField(file.FieldBucketName, field.TypeString, value)
	}
	if fuo.mutation.BucketNameCleared() {
		_spec.ClearField(file.FieldBucketName, field.TypeString)
	}
	if value, ok := fuo.mutation.FileDirectory(); ok {
		_spec.SetField(file.FieldFileDirectory, field.TypeString, value)
	}
	if fuo.mutation.FileDirectoryCleared() {
		_spec.ClearField(file.FieldFileDirectory, field.TypeString)
	}
	if value, ok := fuo.mutation.FileGUID(); ok {
		_spec.SetField(file.FieldFileGUID, field.TypeString, value)
	}
	if fuo.mutation.FileGUIDCleared() {
		_spec.ClearField(file.FieldFileGUID, field.TypeString)
	}
	if value, ok := fuo.mutation.SaveFileName(); ok {
		_spec.SetField(file.FieldSaveFileName, field.TypeString, value)
	}
	if fuo.mutation.SaveFileNameCleared() {
		_spec.ClearField(file.FieldSaveFileName, field.TypeString)
	}
	if value, ok := fuo.mutation.FileName(); ok {
		_spec.SetField(file.FieldFileName, field.TypeString, value)
	}
	if fuo.mutation.FileNameCleared() {
		_spec.ClearField(file.FieldFileName, field.TypeString)
	}
	if value, ok := fuo.mutation.Extension(); ok {
		_spec.SetField(file.FieldExtension, field.TypeString, value)
	}
	if fuo.mutation.ExtensionCleared() {
		_spec.ClearField(file.FieldExtension, field.TypeString)
	}
	if value, ok := fuo.mutation.Size(); ok {
		_spec.SetField(file.FieldSize, field.TypeUint64, value)
	}
	if value, ok := fuo.mutation.AddedSize(); ok {
		_spec.AddField(file.FieldSize, field.TypeUint64, value)
	}
	if fuo.mutation.SizeCleared() {
		_spec.ClearField(file.FieldSize, field.TypeUint64)
	}
	if value, ok := fuo.mutation.SizeFormat(); ok {
		_spec.SetField(file.FieldSizeFormat, field.TypeString, value)
	}
	if fuo.mutation.SizeFormatCleared() {
		_spec.ClearField(file.FieldSizeFormat, field.TypeString)
	}
	if value, ok := fuo.mutation.LinkURL(); ok {
		_spec.SetField(file.FieldLinkURL, field.TypeString, value)
	}
	if fuo.mutation.LinkURLCleared() {
		_spec.ClearField(file.FieldLinkURL, field.TypeString)
	}
	if value, ok := fuo.mutation.Md5(); ok {
		_spec.SetField(file.FieldMd5, field.TypeString, value)
	}
	if fuo.mutation.Md5Cleared() {
		_spec.ClearField(file.FieldMd5, field.TypeString)
	}
	_spec.AddModifiers(fuo.modifiers...)
	_node = &File{config: fuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, fuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{file.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	fuo.mutation.done = true
	return _node, nil
}
