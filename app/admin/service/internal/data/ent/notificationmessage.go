// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/notificationmessage"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 站内信通知消息表
type NotificationMessage struct {
	config `json:"-"`
	// ID of the ent.
	// id
	ID uint32 `json:"id,omitempty"`
	// 创建时间
	CreateTime *time.Time `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *time.Time `json:"delete_time,omitempty"`
	// 创建者ID
	CreateBy *uint32 `json:"create_by,omitempty"`
	// 更新者ID
	UpdateBy *uint32 `json:"update_by,omitempty"`
	// 租户ID
	TenantID *uint32 `json:"tenant_id,omitempty"`
	// 主题
	Subject *string `json:"subject,omitempty"`
	// 内容
	Content *string `json:"content,omitempty"`
	// 分类ID
	CategoryID *uint32 `json:"category_id,omitempty"`
	// 消息状态
	Status       *notificationmessage.Status `json:"status,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*NotificationMessage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case notificationmessage.FieldID, notificationmessage.FieldCreateBy, notificationmessage.FieldUpdateBy, notificationmessage.FieldTenantID, notificationmessage.FieldCategoryID:
			values[i] = new(sql.NullInt64)
		case notificationmessage.FieldSubject, notificationmessage.FieldContent, notificationmessage.FieldStatus:
			values[i] = new(sql.NullString)
		case notificationmessage.FieldCreateTime, notificationmessage.FieldUpdateTime, notificationmessage.FieldDeleteTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the NotificationMessage fields.
func (nm *NotificationMessage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case notificationmessage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			nm.ID = uint32(value.Int64)
		case notificationmessage.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				nm.CreateTime = new(time.Time)
				*nm.CreateTime = value.Time
			}
		case notificationmessage.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				nm.UpdateTime = new(time.Time)
				*nm.UpdateTime = value.Time
			}
		case notificationmessage.FieldDeleteTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delete_time", values[i])
			} else if value.Valid {
				nm.DeleteTime = new(time.Time)
				*nm.DeleteTime = value.Time
			}
		case notificationmessage.FieldCreateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_by", values[i])
			} else if value.Valid {
				nm.CreateBy = new(uint32)
				*nm.CreateBy = uint32(value.Int64)
			}
		case notificationmessage.FieldUpdateBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field update_by", values[i])
			} else if value.Valid {
				nm.UpdateBy = new(uint32)
				*nm.UpdateBy = uint32(value.Int64)
			}
		case notificationmessage.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				nm.TenantID = new(uint32)
				*nm.TenantID = uint32(value.Int64)
			}
		case notificationmessage.FieldSubject:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field subject", values[i])
			} else if value.Valid {
				nm.Subject = new(string)
				*nm.Subject = value.String
			}
		case notificationmessage.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				nm.Content = new(string)
				*nm.Content = value.String
			}
		case notificationmessage.FieldCategoryID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field category_id", values[i])
			} else if value.Valid {
				nm.CategoryID = new(uint32)
				*nm.CategoryID = uint32(value.Int64)
			}
		case notificationmessage.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				nm.Status = new(notificationmessage.Status)
				*nm.Status = notificationmessage.Status(value.String)
			}
		default:
			nm.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the NotificationMessage.
// This includes values selected through modifiers, order, etc.
func (nm *NotificationMessage) Value(name string) (ent.Value, error) {
	return nm.selectValues.Get(name)
}

// Update returns a builder for updating this NotificationMessage.
// Note that you need to call NotificationMessage.Unwrap() before calling this method if this NotificationMessage
// was returned from a transaction, and the transaction was committed or rolled back.
func (nm *NotificationMessage) Update() *NotificationMessageUpdateOne {
	return NewNotificationMessageClient(nm.config).UpdateOne(nm)
}

// Unwrap unwraps the NotificationMessage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (nm *NotificationMessage) Unwrap() *NotificationMessage {
	_tx, ok := nm.config.driver.(*txDriver)
	if !ok {
		panic("ent: NotificationMessage is not a transactional entity")
	}
	nm.config.driver = _tx.drv
	return nm
}

// String implements the fmt.Stringer.
func (nm *NotificationMessage) String() string {
	var builder strings.Builder
	builder.WriteString("NotificationMessage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", nm.ID))
	if v := nm.CreateTime; v != nil {
		builder.WriteString("create_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := nm.UpdateTime; v != nil {
		builder.WriteString("update_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := nm.DeleteTime; v != nil {
		builder.WriteString("delete_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := nm.CreateBy; v != nil {
		builder.WriteString("create_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nm.UpdateBy; v != nil {
		builder.WriteString("update_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nm.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nm.Subject; v != nil {
		builder.WriteString("subject=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := nm.Content; v != nil {
		builder.WriteString("content=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := nm.CategoryID; v != nil {
		builder.WriteString("category_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := nm.Status; v != nil {
		builder.WriteString("status=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// NotificationMessages is a parsable slice of NotificationMessage.
type NotificationMessages []*NotificationMessage
