// Code generated by ent, DO NOT EDIT.

package user

import (
	"kratos-admin/app/admin/service/internal/data/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldID, id))
}

// CreateBy applies equality check predicate on the "create_by" field. It's identical to CreateByEQ.
func CreateBy(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreateBy, v))
}

// UpdateBy applies equality check predicate on the "update_by" field. It's identical to UpdateByEQ.
func UpdateBy(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdateBy, v))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdateTime, v))
}

// DeleteTime applies equality check predicate on the "delete_time" field. It's identical to DeleteTimeEQ.
func DeleteTime(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeleteTime, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRemark, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTenantID, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUsername, v))
}

// Nickname applies equality check predicate on the "nickname" field. It's identical to NicknameEQ.
func Nickname(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldNickname, v))
}

// Realname applies equality check predicate on the "realname" field. It's identical to RealnameEQ.
func Realname(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRealname, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// Mobile applies equality check predicate on the "mobile" field. It's identical to MobileEQ.
func Mobile(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldMobile, v))
}

// Telephone applies equality check predicate on the "telephone" field. It's identical to TelephoneEQ.
func Telephone(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTelephone, v))
}

// Avatar applies equality check predicate on the "avatar" field. It's identical to AvatarEQ.
func Avatar(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAvatar, v))
}

// Address applies equality check predicate on the "address" field. It's identical to AddressEQ.
func Address(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAddress, v))
}

// Region applies equality check predicate on the "region" field. It's identical to RegionEQ.
func Region(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRegion, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDescription, v))
}

// LastLoginTime applies equality check predicate on the "last_login_time" field. It's identical to LastLoginTimeEQ.
func LastLoginTime(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldLastLoginTime, v))
}

// LastLoginIP applies equality check predicate on the "last_login_ip" field. It's identical to LastLoginIPEQ.
func LastLoginIP(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldLastLoginIP, v))
}

// OrgID applies equality check predicate on the "org_id" field. It's identical to OrgIDEQ.
func OrgID(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldOrgID, v))
}

// PositionID applies equality check predicate on the "position_id" field. It's identical to PositionIDEQ.
func PositionID(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPositionID, v))
}

// WorkID applies equality check predicate on the "work_id" field. It's identical to WorkIDEQ.
func WorkID(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldWorkID, v))
}

// CreateByEQ applies the EQ predicate on the "create_by" field.
func CreateByEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreateBy, v))
}

// CreateByNEQ applies the NEQ predicate on the "create_by" field.
func CreateByNEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldCreateBy, v))
}

// CreateByIn applies the In predicate on the "create_by" field.
func CreateByIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldCreateBy, vs...))
}

// CreateByNotIn applies the NotIn predicate on the "create_by" field.
func CreateByNotIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldCreateBy, vs...))
}

// CreateByGT applies the GT predicate on the "create_by" field.
func CreateByGT(v uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldCreateBy, v))
}

// CreateByGTE applies the GTE predicate on the "create_by" field.
func CreateByGTE(v uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldCreateBy, v))
}

// CreateByLT applies the LT predicate on the "create_by" field.
func CreateByLT(v uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldCreateBy, v))
}

// CreateByLTE applies the LTE predicate on the "create_by" field.
func CreateByLTE(v uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldCreateBy, v))
}

// CreateByIsNil applies the IsNil predicate on the "create_by" field.
func CreateByIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldCreateBy))
}

// CreateByNotNil applies the NotNil predicate on the "create_by" field.
func CreateByNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldCreateBy))
}

// UpdateByEQ applies the EQ predicate on the "update_by" field.
func UpdateByEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdateBy, v))
}

// UpdateByNEQ applies the NEQ predicate on the "update_by" field.
func UpdateByNEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUpdateBy, v))
}

// UpdateByIn applies the In predicate on the "update_by" field.
func UpdateByIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldUpdateBy, vs...))
}

// UpdateByNotIn applies the NotIn predicate on the "update_by" field.
func UpdateByNotIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUpdateBy, vs...))
}

// UpdateByGT applies the GT predicate on the "update_by" field.
func UpdateByGT(v uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldUpdateBy, v))
}

// UpdateByGTE applies the GTE predicate on the "update_by" field.
func UpdateByGTE(v uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUpdateBy, v))
}

// UpdateByLT applies the LT predicate on the "update_by" field.
func UpdateByLT(v uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldUpdateBy, v))
}

// UpdateByLTE applies the LTE predicate on the "update_by" field.
func UpdateByLTE(v uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUpdateBy, v))
}

// UpdateByIsNil applies the IsNil predicate on the "update_by" field.
func UpdateByIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldUpdateBy))
}

// UpdateByNotNil applies the NotNil predicate on the "update_by" field.
func UpdateByNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldUpdateBy))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldUpdateTime))
}

// DeleteTimeEQ applies the EQ predicate on the "delete_time" field.
func DeleteTimeEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeleteTime, v))
}

// DeleteTimeNEQ applies the NEQ predicate on the "delete_time" field.
func DeleteTimeNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDeleteTime, v))
}

// DeleteTimeIn applies the In predicate on the "delete_time" field.
func DeleteTimeIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldDeleteTime, vs...))
}

// DeleteTimeNotIn applies the NotIn predicate on the "delete_time" field.
func DeleteTimeNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDeleteTime, vs...))
}

// DeleteTimeGT applies the GT predicate on the "delete_time" field.
func DeleteTimeGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldDeleteTime, v))
}

// DeleteTimeGTE applies the GTE predicate on the "delete_time" field.
func DeleteTimeGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDeleteTime, v))
}

// DeleteTimeLT applies the LT predicate on the "delete_time" field.
func DeleteTimeLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldDeleteTime, v))
}

// DeleteTimeLTE applies the LTE predicate on the "delete_time" field.
func DeleteTimeLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDeleteTime, v))
}

// DeleteTimeIsNil applies the IsNil predicate on the "delete_time" field.
func DeleteTimeIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDeleteTime))
}

// DeleteTimeNotNil applies the NotNil predicate on the "delete_time" field.
func DeleteTimeNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDeleteTime))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldRemark, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.User {
	return predicate.User(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.User {
	return predicate.User(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldStatus))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldTenantID))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameIsNil applies the IsNil predicate on the "username" field.
func UsernameIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldUsername))
}

// UsernameNotNil applies the NotNil predicate on the "username" field.
func UsernameNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldUsername))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldUsername, v))
}

// NicknameEQ applies the EQ predicate on the "nickname" field.
func NicknameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldNickname, v))
}

// NicknameNEQ applies the NEQ predicate on the "nickname" field.
func NicknameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldNickname, v))
}

// NicknameIn applies the In predicate on the "nickname" field.
func NicknameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldNickname, vs...))
}

// NicknameNotIn applies the NotIn predicate on the "nickname" field.
func NicknameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldNickname, vs...))
}

// NicknameGT applies the GT predicate on the "nickname" field.
func NicknameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldNickname, v))
}

// NicknameGTE applies the GTE predicate on the "nickname" field.
func NicknameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldNickname, v))
}

// NicknameLT applies the LT predicate on the "nickname" field.
func NicknameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldNickname, v))
}

// NicknameLTE applies the LTE predicate on the "nickname" field.
func NicknameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldNickname, v))
}

// NicknameContains applies the Contains predicate on the "nickname" field.
func NicknameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldNickname, v))
}

// NicknameHasPrefix applies the HasPrefix predicate on the "nickname" field.
func NicknameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldNickname, v))
}

// NicknameHasSuffix applies the HasSuffix predicate on the "nickname" field.
func NicknameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldNickname, v))
}

// NicknameIsNil applies the IsNil predicate on the "nickname" field.
func NicknameIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldNickname))
}

// NicknameNotNil applies the NotNil predicate on the "nickname" field.
func NicknameNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldNickname))
}

// NicknameEqualFold applies the EqualFold predicate on the "nickname" field.
func NicknameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldNickname, v))
}

// NicknameContainsFold applies the ContainsFold predicate on the "nickname" field.
func NicknameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldNickname, v))
}

// RealnameEQ applies the EQ predicate on the "realname" field.
func RealnameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRealname, v))
}

// RealnameNEQ applies the NEQ predicate on the "realname" field.
func RealnameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldRealname, v))
}

// RealnameIn applies the In predicate on the "realname" field.
func RealnameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldRealname, vs...))
}

// RealnameNotIn applies the NotIn predicate on the "realname" field.
func RealnameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldRealname, vs...))
}

// RealnameGT applies the GT predicate on the "realname" field.
func RealnameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldRealname, v))
}

// RealnameGTE applies the GTE predicate on the "realname" field.
func RealnameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldRealname, v))
}

// RealnameLT applies the LT predicate on the "realname" field.
func RealnameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldRealname, v))
}

// RealnameLTE applies the LTE predicate on the "realname" field.
func RealnameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldRealname, v))
}

// RealnameContains applies the Contains predicate on the "realname" field.
func RealnameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldRealname, v))
}

// RealnameHasPrefix applies the HasPrefix predicate on the "realname" field.
func RealnameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldRealname, v))
}

// RealnameHasSuffix applies the HasSuffix predicate on the "realname" field.
func RealnameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldRealname, v))
}

// RealnameIsNil applies the IsNil predicate on the "realname" field.
func RealnameIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldRealname))
}

// RealnameNotNil applies the NotNil predicate on the "realname" field.
func RealnameNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldRealname))
}

// RealnameEqualFold applies the EqualFold predicate on the "realname" field.
func RealnameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldRealname, v))
}

// RealnameContainsFold applies the ContainsFold predicate on the "realname" field.
func RealnameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldRealname, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailIsNil applies the IsNil predicate on the "email" field.
func EmailIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldEmail))
}

// EmailNotNil applies the NotNil predicate on the "email" field.
func EmailNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldEmail))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldEmail, v))
}

// MobileEQ applies the EQ predicate on the "mobile" field.
func MobileEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldMobile, v))
}

// MobileNEQ applies the NEQ predicate on the "mobile" field.
func MobileNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldMobile, v))
}

// MobileIn applies the In predicate on the "mobile" field.
func MobileIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldMobile, vs...))
}

// MobileNotIn applies the NotIn predicate on the "mobile" field.
func MobileNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldMobile, vs...))
}

// MobileGT applies the GT predicate on the "mobile" field.
func MobileGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldMobile, v))
}

// MobileGTE applies the GTE predicate on the "mobile" field.
func MobileGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldMobile, v))
}

// MobileLT applies the LT predicate on the "mobile" field.
func MobileLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldMobile, v))
}

// MobileLTE applies the LTE predicate on the "mobile" field.
func MobileLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldMobile, v))
}

// MobileContains applies the Contains predicate on the "mobile" field.
func MobileContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldMobile, v))
}

// MobileHasPrefix applies the HasPrefix predicate on the "mobile" field.
func MobileHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldMobile, v))
}

// MobileHasSuffix applies the HasSuffix predicate on the "mobile" field.
func MobileHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldMobile, v))
}

// MobileIsNil applies the IsNil predicate on the "mobile" field.
func MobileIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldMobile))
}

// MobileNotNil applies the NotNil predicate on the "mobile" field.
func MobileNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldMobile))
}

// MobileEqualFold applies the EqualFold predicate on the "mobile" field.
func MobileEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldMobile, v))
}

// MobileContainsFold applies the ContainsFold predicate on the "mobile" field.
func MobileContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldMobile, v))
}

// TelephoneEQ applies the EQ predicate on the "telephone" field.
func TelephoneEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTelephone, v))
}

// TelephoneNEQ applies the NEQ predicate on the "telephone" field.
func TelephoneNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldTelephone, v))
}

// TelephoneIn applies the In predicate on the "telephone" field.
func TelephoneIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldTelephone, vs...))
}

// TelephoneNotIn applies the NotIn predicate on the "telephone" field.
func TelephoneNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldTelephone, vs...))
}

// TelephoneGT applies the GT predicate on the "telephone" field.
func TelephoneGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldTelephone, v))
}

// TelephoneGTE applies the GTE predicate on the "telephone" field.
func TelephoneGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldTelephone, v))
}

// TelephoneLT applies the LT predicate on the "telephone" field.
func TelephoneLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldTelephone, v))
}

// TelephoneLTE applies the LTE predicate on the "telephone" field.
func TelephoneLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldTelephone, v))
}

// TelephoneContains applies the Contains predicate on the "telephone" field.
func TelephoneContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldTelephone, v))
}

// TelephoneHasPrefix applies the HasPrefix predicate on the "telephone" field.
func TelephoneHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldTelephone, v))
}

// TelephoneHasSuffix applies the HasSuffix predicate on the "telephone" field.
func TelephoneHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldTelephone, v))
}

// TelephoneIsNil applies the IsNil predicate on the "telephone" field.
func TelephoneIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldTelephone))
}

// TelephoneNotNil applies the NotNil predicate on the "telephone" field.
func TelephoneNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldTelephone))
}

// TelephoneEqualFold applies the EqualFold predicate on the "telephone" field.
func TelephoneEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldTelephone, v))
}

// TelephoneContainsFold applies the ContainsFold predicate on the "telephone" field.
func TelephoneContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldTelephone, v))
}

// AvatarEQ applies the EQ predicate on the "avatar" field.
func AvatarEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAvatar, v))
}

// AvatarNEQ applies the NEQ predicate on the "avatar" field.
func AvatarNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAvatar, v))
}

// AvatarIn applies the In predicate on the "avatar" field.
func AvatarIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldAvatar, vs...))
}

// AvatarNotIn applies the NotIn predicate on the "avatar" field.
func AvatarNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAvatar, vs...))
}

// AvatarGT applies the GT predicate on the "avatar" field.
func AvatarGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldAvatar, v))
}

// AvatarGTE applies the GTE predicate on the "avatar" field.
func AvatarGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldAvatar, v))
}

// AvatarLT applies the LT predicate on the "avatar" field.
func AvatarLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldAvatar, v))
}

// AvatarLTE applies the LTE predicate on the "avatar" field.
func AvatarLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldAvatar, v))
}

// AvatarContains applies the Contains predicate on the "avatar" field.
func AvatarContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldAvatar, v))
}

// AvatarHasPrefix applies the HasPrefix predicate on the "avatar" field.
func AvatarHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldAvatar, v))
}

// AvatarHasSuffix applies the HasSuffix predicate on the "avatar" field.
func AvatarHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldAvatar, v))
}

// AvatarIsNil applies the IsNil predicate on the "avatar" field.
func AvatarIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAvatar))
}

// AvatarNotNil applies the NotNil predicate on the "avatar" field.
func AvatarNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAvatar))
}

// AvatarEqualFold applies the EqualFold predicate on the "avatar" field.
func AvatarEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldAvatar, v))
}

// AvatarContainsFold applies the ContainsFold predicate on the "avatar" field.
func AvatarContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldAvatar, v))
}

// AddressEQ applies the EQ predicate on the "address" field.
func AddressEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAddress, v))
}

// AddressNEQ applies the NEQ predicate on the "address" field.
func AddressNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAddress, v))
}

// AddressIn applies the In predicate on the "address" field.
func AddressIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldAddress, vs...))
}

// AddressNotIn applies the NotIn predicate on the "address" field.
func AddressNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAddress, vs...))
}

// AddressGT applies the GT predicate on the "address" field.
func AddressGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldAddress, v))
}

// AddressGTE applies the GTE predicate on the "address" field.
func AddressGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldAddress, v))
}

// AddressLT applies the LT predicate on the "address" field.
func AddressLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldAddress, v))
}

// AddressLTE applies the LTE predicate on the "address" field.
func AddressLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldAddress, v))
}

// AddressContains applies the Contains predicate on the "address" field.
func AddressContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldAddress, v))
}

// AddressHasPrefix applies the HasPrefix predicate on the "address" field.
func AddressHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldAddress, v))
}

// AddressHasSuffix applies the HasSuffix predicate on the "address" field.
func AddressHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldAddress, v))
}

// AddressIsNil applies the IsNil predicate on the "address" field.
func AddressIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAddress))
}

// AddressNotNil applies the NotNil predicate on the "address" field.
func AddressNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAddress))
}

// AddressEqualFold applies the EqualFold predicate on the "address" field.
func AddressEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldAddress, v))
}

// AddressContainsFold applies the ContainsFold predicate on the "address" field.
func AddressContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldAddress, v))
}

// RegionEQ applies the EQ predicate on the "region" field.
func RegionEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRegion, v))
}

// RegionNEQ applies the NEQ predicate on the "region" field.
func RegionNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldRegion, v))
}

// RegionIn applies the In predicate on the "region" field.
func RegionIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldRegion, vs...))
}

// RegionNotIn applies the NotIn predicate on the "region" field.
func RegionNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldRegion, vs...))
}

// RegionGT applies the GT predicate on the "region" field.
func RegionGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldRegion, v))
}

// RegionGTE applies the GTE predicate on the "region" field.
func RegionGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldRegion, v))
}

// RegionLT applies the LT predicate on the "region" field.
func RegionLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldRegion, v))
}

// RegionLTE applies the LTE predicate on the "region" field.
func RegionLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldRegion, v))
}

// RegionContains applies the Contains predicate on the "region" field.
func RegionContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldRegion, v))
}

// RegionHasPrefix applies the HasPrefix predicate on the "region" field.
func RegionHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldRegion, v))
}

// RegionHasSuffix applies the HasSuffix predicate on the "region" field.
func RegionHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldRegion, v))
}

// RegionIsNil applies the IsNil predicate on the "region" field.
func RegionIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldRegion))
}

// RegionNotNil applies the NotNil predicate on the "region" field.
func RegionNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldRegion))
}

// RegionEqualFold applies the EqualFold predicate on the "region" field.
func RegionEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldRegion, v))
}

// RegionContainsFold applies the ContainsFold predicate on the "region" field.
func RegionContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldRegion, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldDescription, v))
}

// GenderEQ applies the EQ predicate on the "gender" field.
func GenderEQ(v Gender) predicate.User {
	return predicate.User(sql.FieldEQ(FieldGender, v))
}

// GenderNEQ applies the NEQ predicate on the "gender" field.
func GenderNEQ(v Gender) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldGender, v))
}

// GenderIn applies the In predicate on the "gender" field.
func GenderIn(vs ...Gender) predicate.User {
	return predicate.User(sql.FieldIn(FieldGender, vs...))
}

// GenderNotIn applies the NotIn predicate on the "gender" field.
func GenderNotIn(vs ...Gender) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldGender, vs...))
}

// GenderIsNil applies the IsNil predicate on the "gender" field.
func GenderIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldGender))
}

// GenderNotNil applies the NotNil predicate on the "gender" field.
func GenderNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldGender))
}

// AuthorityEQ applies the EQ predicate on the "authority" field.
func AuthorityEQ(v Authority) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAuthority, v))
}

// AuthorityNEQ applies the NEQ predicate on the "authority" field.
func AuthorityNEQ(v Authority) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAuthority, v))
}

// AuthorityIn applies the In predicate on the "authority" field.
func AuthorityIn(vs ...Authority) predicate.User {
	return predicate.User(sql.FieldIn(FieldAuthority, vs...))
}

// AuthorityNotIn applies the NotIn predicate on the "authority" field.
func AuthorityNotIn(vs ...Authority) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAuthority, vs...))
}

// AuthorityIsNil applies the IsNil predicate on the "authority" field.
func AuthorityIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAuthority))
}

// AuthorityNotNil applies the NotNil predicate on the "authority" field.
func AuthorityNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAuthority))
}

// LastLoginTimeEQ applies the EQ predicate on the "last_login_time" field.
func LastLoginTimeEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldLastLoginTime, v))
}

// LastLoginTimeNEQ applies the NEQ predicate on the "last_login_time" field.
func LastLoginTimeNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldLastLoginTime, v))
}

// LastLoginTimeIn applies the In predicate on the "last_login_time" field.
func LastLoginTimeIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldLastLoginTime, vs...))
}

// LastLoginTimeNotIn applies the NotIn predicate on the "last_login_time" field.
func LastLoginTimeNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldLastLoginTime, vs...))
}

// LastLoginTimeGT applies the GT predicate on the "last_login_time" field.
func LastLoginTimeGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldLastLoginTime, v))
}

// LastLoginTimeGTE applies the GTE predicate on the "last_login_time" field.
func LastLoginTimeGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldLastLoginTime, v))
}

// LastLoginTimeLT applies the LT predicate on the "last_login_time" field.
func LastLoginTimeLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldLastLoginTime, v))
}

// LastLoginTimeLTE applies the LTE predicate on the "last_login_time" field.
func LastLoginTimeLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldLastLoginTime, v))
}

// LastLoginTimeIsNil applies the IsNil predicate on the "last_login_time" field.
func LastLoginTimeIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldLastLoginTime))
}

// LastLoginTimeNotNil applies the NotNil predicate on the "last_login_time" field.
func LastLoginTimeNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldLastLoginTime))
}

// LastLoginIPEQ applies the EQ predicate on the "last_login_ip" field.
func LastLoginIPEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldLastLoginIP, v))
}

// LastLoginIPNEQ applies the NEQ predicate on the "last_login_ip" field.
func LastLoginIPNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldLastLoginIP, v))
}

// LastLoginIPIn applies the In predicate on the "last_login_ip" field.
func LastLoginIPIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldLastLoginIP, vs...))
}

// LastLoginIPNotIn applies the NotIn predicate on the "last_login_ip" field.
func LastLoginIPNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldLastLoginIP, vs...))
}

// LastLoginIPGT applies the GT predicate on the "last_login_ip" field.
func LastLoginIPGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldLastLoginIP, v))
}

// LastLoginIPGTE applies the GTE predicate on the "last_login_ip" field.
func LastLoginIPGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldLastLoginIP, v))
}

// LastLoginIPLT applies the LT predicate on the "last_login_ip" field.
func LastLoginIPLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldLastLoginIP, v))
}

// LastLoginIPLTE applies the LTE predicate on the "last_login_ip" field.
func LastLoginIPLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldLastLoginIP, v))
}

// LastLoginIPContains applies the Contains predicate on the "last_login_ip" field.
func LastLoginIPContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldLastLoginIP, v))
}

// LastLoginIPHasPrefix applies the HasPrefix predicate on the "last_login_ip" field.
func LastLoginIPHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldLastLoginIP, v))
}

// LastLoginIPHasSuffix applies the HasSuffix predicate on the "last_login_ip" field.
func LastLoginIPHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldLastLoginIP, v))
}

// LastLoginIPIsNil applies the IsNil predicate on the "last_login_ip" field.
func LastLoginIPIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldLastLoginIP))
}

// LastLoginIPNotNil applies the NotNil predicate on the "last_login_ip" field.
func LastLoginIPNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldLastLoginIP))
}

// LastLoginIPEqualFold applies the EqualFold predicate on the "last_login_ip" field.
func LastLoginIPEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldLastLoginIP, v))
}

// LastLoginIPContainsFold applies the ContainsFold predicate on the "last_login_ip" field.
func LastLoginIPContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldLastLoginIP, v))
}

// OrgIDEQ applies the EQ predicate on the "org_id" field.
func OrgIDEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldOrgID, v))
}

// OrgIDNEQ applies the NEQ predicate on the "org_id" field.
func OrgIDNEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldOrgID, v))
}

// OrgIDIn applies the In predicate on the "org_id" field.
func OrgIDIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldOrgID, vs...))
}

// OrgIDNotIn applies the NotIn predicate on the "org_id" field.
func OrgIDNotIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldOrgID, vs...))
}

// OrgIDGT applies the GT predicate on the "org_id" field.
func OrgIDGT(v uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldOrgID, v))
}

// OrgIDGTE applies the GTE predicate on the "org_id" field.
func OrgIDGTE(v uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldOrgID, v))
}

// OrgIDLT applies the LT predicate on the "org_id" field.
func OrgIDLT(v uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldOrgID, v))
}

// OrgIDLTE applies the LTE predicate on the "org_id" field.
func OrgIDLTE(v uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldOrgID, v))
}

// OrgIDIsNil applies the IsNil predicate on the "org_id" field.
func OrgIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldOrgID))
}

// OrgIDNotNil applies the NotNil predicate on the "org_id" field.
func OrgIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldOrgID))
}

// PositionIDEQ applies the EQ predicate on the "position_id" field.
func PositionIDEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPositionID, v))
}

// PositionIDNEQ applies the NEQ predicate on the "position_id" field.
func PositionIDNEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPositionID, v))
}

// PositionIDIn applies the In predicate on the "position_id" field.
func PositionIDIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldPositionID, vs...))
}

// PositionIDNotIn applies the NotIn predicate on the "position_id" field.
func PositionIDNotIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPositionID, vs...))
}

// PositionIDGT applies the GT predicate on the "position_id" field.
func PositionIDGT(v uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldPositionID, v))
}

// PositionIDGTE applies the GTE predicate on the "position_id" field.
func PositionIDGTE(v uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPositionID, v))
}

// PositionIDLT applies the LT predicate on the "position_id" field.
func PositionIDLT(v uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldPositionID, v))
}

// PositionIDLTE applies the LTE predicate on the "position_id" field.
func PositionIDLTE(v uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPositionID, v))
}

// PositionIDIsNil applies the IsNil predicate on the "position_id" field.
func PositionIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldPositionID))
}

// PositionIDNotNil applies the NotNil predicate on the "position_id" field.
func PositionIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldPositionID))
}

// WorkIDEQ applies the EQ predicate on the "work_id" field.
func WorkIDEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldEQ(FieldWorkID, v))
}

// WorkIDNEQ applies the NEQ predicate on the "work_id" field.
func WorkIDNEQ(v uint32) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldWorkID, v))
}

// WorkIDIn applies the In predicate on the "work_id" field.
func WorkIDIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldIn(FieldWorkID, vs...))
}

// WorkIDNotIn applies the NotIn predicate on the "work_id" field.
func WorkIDNotIn(vs ...uint32) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldWorkID, vs...))
}

// WorkIDGT applies the GT predicate on the "work_id" field.
func WorkIDGT(v uint32) predicate.User {
	return predicate.User(sql.FieldGT(FieldWorkID, v))
}

// WorkIDGTE applies the GTE predicate on the "work_id" field.
func WorkIDGTE(v uint32) predicate.User {
	return predicate.User(sql.FieldGTE(FieldWorkID, v))
}

// WorkIDLT applies the LT predicate on the "work_id" field.
func WorkIDLT(v uint32) predicate.User {
	return predicate.User(sql.FieldLT(FieldWorkID, v))
}

// WorkIDLTE applies the LTE predicate on the "work_id" field.
func WorkIDLTE(v uint32) predicate.User {
	return predicate.User(sql.FieldLTE(FieldWorkID, v))
}

// WorkIDIsNil applies the IsNil predicate on the "work_id" field.
func WorkIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldWorkID))
}

// WorkIDNotNil applies the NotNil predicate on the "work_id" field.
func WorkIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldWorkID))
}

// RolesIsNil applies the IsNil predicate on the "roles" field.
func RolesIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldRoles))
}

// RolesNotNil applies the NotNil predicate on the "roles" field.
func RolesNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldRoles))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.User) predicate.User {
	return predicate.User(sql.NotPredicates(p))
}
