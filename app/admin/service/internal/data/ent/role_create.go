// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"kratos-admin/app/admin/service/internal/data/ent/role"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// RoleCreate is the builder for creating a Role entity.
type RoleCreate struct {
	config
	mutation *RoleMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (rc *RoleCreate) SetCreateTime(t time.Time) *RoleCreate {
	rc.mutation.SetCreateTime(t)
	return rc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (rc *RoleCreate) SetNillableCreateTime(t *time.Time) *RoleCreate {
	if t != nil {
		rc.SetCreateTime(*t)
	}
	return rc
}

// SetUpdateTime sets the "update_time" field.
func (rc *RoleCreate) SetUpdateTime(t time.Time) *RoleCreate {
	rc.mutation.SetUpdateTime(t)
	return rc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (rc *RoleCreate) SetNillableUpdateTime(t *time.Time) *RoleCreate {
	if t != nil {
		rc.SetUpdateTime(*t)
	}
	return rc
}

// SetDeleteTime sets the "delete_time" field.
func (rc *RoleCreate) SetDeleteTime(t time.Time) *RoleCreate {
	rc.mutation.SetDeleteTime(t)
	return rc
}

// SetNillableDeleteTime sets the "delete_time" field if the given value is not nil.
func (rc *RoleCreate) SetNillableDeleteTime(t *time.Time) *RoleCreate {
	if t != nil {
		rc.SetDeleteTime(*t)
	}
	return rc
}

// SetStatus sets the "status" field.
func (rc *RoleCreate) SetStatus(r role.Status) *RoleCreate {
	rc.mutation.SetStatus(r)
	return rc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (rc *RoleCreate) SetNillableStatus(r *role.Status) *RoleCreate {
	if r != nil {
		rc.SetStatus(*r)
	}
	return rc
}

// SetCreateBy sets the "create_by" field.
func (rc *RoleCreate) SetCreateBy(u uint32) *RoleCreate {
	rc.mutation.SetCreateBy(u)
	return rc
}

// SetNillableCreateBy sets the "create_by" field if the given value is not nil.
func (rc *RoleCreate) SetNillableCreateBy(u *uint32) *RoleCreate {
	if u != nil {
		rc.SetCreateBy(*u)
	}
	return rc
}

// SetUpdateBy sets the "update_by" field.
func (rc *RoleCreate) SetUpdateBy(u uint32) *RoleCreate {
	rc.mutation.SetUpdateBy(u)
	return rc
}

// SetNillableUpdateBy sets the "update_by" field if the given value is not nil.
func (rc *RoleCreate) SetNillableUpdateBy(u *uint32) *RoleCreate {
	if u != nil {
		rc.SetUpdateBy(*u)
	}
	return rc
}

// SetRemark sets the "remark" field.
func (rc *RoleCreate) SetRemark(s string) *RoleCreate {
	rc.mutation.SetRemark(s)
	return rc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (rc *RoleCreate) SetNillableRemark(s *string) *RoleCreate {
	if s != nil {
		rc.SetRemark(*s)
	}
	return rc
}

// SetTenantID sets the "tenant_id" field.
func (rc *RoleCreate) SetTenantID(u uint32) *RoleCreate {
	rc.mutation.SetTenantID(u)
	return rc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (rc *RoleCreate) SetNillableTenantID(u *uint32) *RoleCreate {
	if u != nil {
		rc.SetTenantID(*u)
	}
	return rc
}

// SetName sets the "name" field.
func (rc *RoleCreate) SetName(s string) *RoleCreate {
	rc.mutation.SetName(s)
	return rc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (rc *RoleCreate) SetNillableName(s *string) *RoleCreate {
	if s != nil {
		rc.SetName(*s)
	}
	return rc
}

// SetCode sets the "code" field.
func (rc *RoleCreate) SetCode(s string) *RoleCreate {
	rc.mutation.SetCode(s)
	return rc
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (rc *RoleCreate) SetNillableCode(s *string) *RoleCreate {
	if s != nil {
		rc.SetCode(*s)
	}
	return rc
}

// SetParentID sets the "parent_id" field.
func (rc *RoleCreate) SetParentID(u uint32) *RoleCreate {
	rc.mutation.SetParentID(u)
	return rc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (rc *RoleCreate) SetNillableParentID(u *uint32) *RoleCreate {
	if u != nil {
		rc.SetParentID(*u)
	}
	return rc
}

// SetSortID sets the "sort_id" field.
func (rc *RoleCreate) SetSortID(i int32) *RoleCreate {
	rc.mutation.SetSortID(i)
	return rc
}

// SetNillableSortID sets the "sort_id" field if the given value is not nil.
func (rc *RoleCreate) SetNillableSortID(i *int32) *RoleCreate {
	if i != nil {
		rc.SetSortID(*i)
	}
	return rc
}

// SetMenus sets the "menus" field.
func (rc *RoleCreate) SetMenus(u []uint32) *RoleCreate {
	rc.mutation.SetMenus(u)
	return rc
}

// SetApis sets the "apis" field.
func (rc *RoleCreate) SetApis(u []uint32) *RoleCreate {
	rc.mutation.SetApis(u)
	return rc
}

// SetID sets the "id" field.
func (rc *RoleCreate) SetID(u uint32) *RoleCreate {
	rc.mutation.SetID(u)
	return rc
}

// SetParent sets the "parent" edge to the Role entity.
func (rc *RoleCreate) SetParent(r *Role) *RoleCreate {
	return rc.SetParentID(r.ID)
}

// AddChildIDs adds the "children" edge to the Role entity by IDs.
func (rc *RoleCreate) AddChildIDs(ids ...uint32) *RoleCreate {
	rc.mutation.AddChildIDs(ids...)
	return rc
}

// AddChildren adds the "children" edges to the Role entity.
func (rc *RoleCreate) AddChildren(r ...*Role) *RoleCreate {
	ids := make([]uint32, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return rc.AddChildIDs(ids...)
}

// Mutation returns the RoleMutation object of the builder.
func (rc *RoleCreate) Mutation() *RoleMutation {
	return rc.mutation
}

// Save creates the Role in the database.
func (rc *RoleCreate) Save(ctx context.Context) (*Role, error) {
	rc.defaults()
	return withHooks(ctx, rc.sqlSave, rc.mutation, rc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (rc *RoleCreate) SaveX(ctx context.Context) *Role {
	v, err := rc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (rc *RoleCreate) Exec(ctx context.Context) error {
	_, err := rc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (rc *RoleCreate) ExecX(ctx context.Context) {
	if err := rc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (rc *RoleCreate) defaults() {
	if _, ok := rc.mutation.Status(); !ok {
		v := role.DefaultStatus
		rc.mutation.SetStatus(v)
	}
	if _, ok := rc.mutation.Remark(); !ok {
		v := role.DefaultRemark
		rc.mutation.SetRemark(v)
	}
	if _, ok := rc.mutation.Code(); !ok {
		v := role.DefaultCode
		rc.mutation.SetCode(v)
	}
	if _, ok := rc.mutation.SortID(); !ok {
		v := role.DefaultSortID
		rc.mutation.SetSortID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (rc *RoleCreate) check() error {
	if v, ok := rc.mutation.Status(); ok {
		if err := role.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Role.status": %w`, err)}
		}
	}
	if v, ok := rc.mutation.TenantID(); ok {
		if err := role.TenantIDValidator(v); err != nil {
			return &ValidationError{Name: "tenant_id", err: fmt.Errorf(`ent: validator failed for field "Role.tenant_id": %w`, err)}
		}
	}
	if v, ok := rc.mutation.Name(); ok {
		if err := role.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Role.name": %w`, err)}
		}
	}
	if v, ok := rc.mutation.Code(); ok {
		if err := role.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Role.code": %w`, err)}
		}
	}
	if v, ok := rc.mutation.ID(); ok {
		if err := role.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Role.id": %w`, err)}
		}
	}
	return nil
}

func (rc *RoleCreate) sqlSave(ctx context.Context) (*Role, error) {
	if err := rc.check(); err != nil {
		return nil, err
	}
	_node, _spec := rc.createSpec()
	if err := sqlgraph.CreateNode(ctx, rc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint32(id)
	}
	rc.mutation.id = &_node.ID
	rc.mutation.done = true
	return _node, nil
}

func (rc *RoleCreate) createSpec() (*Role, *sqlgraph.CreateSpec) {
	var (
		_node = &Role{config: rc.config}
		_spec = sqlgraph.NewCreateSpec(role.Table, sqlgraph.NewFieldSpec(role.FieldID, field.TypeUint32))
	)
	_spec.OnConflict = rc.conflict
	if id, ok := rc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := rc.mutation.CreateTime(); ok {
		_spec.SetField(role.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = &value
	}
	if value, ok := rc.mutation.UpdateTime(); ok {
		_spec.SetField(role.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = &value
	}
	if value, ok := rc.mutation.DeleteTime(); ok {
		_spec.SetField(role.FieldDeleteTime, field.TypeTime, value)
		_node.DeleteTime = &value
	}
	if value, ok := rc.mutation.Status(); ok {
		_spec.SetField(role.FieldStatus, field.TypeEnum, value)
		_node.Status = &value
	}
	if value, ok := rc.mutation.CreateBy(); ok {
		_spec.SetField(role.FieldCreateBy, field.TypeUint32, value)
		_node.CreateBy = &value
	}
	if value, ok := rc.mutation.UpdateBy(); ok {
		_spec.SetField(role.FieldUpdateBy, field.TypeUint32, value)
		_node.UpdateBy = &value
	}
	if value, ok := rc.mutation.Remark(); ok {
		_spec.SetField(role.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := rc.mutation.TenantID(); ok {
		_spec.SetField(role.FieldTenantID, field.TypeUint32, value)
		_node.TenantID = &value
	}
	if value, ok := rc.mutation.Name(); ok {
		_spec.SetField(role.FieldName, field.TypeString, value)
		_node.Name = &value
	}
	if value, ok := rc.mutation.Code(); ok {
		_spec.SetField(role.FieldCode, field.TypeString, value)
		_node.Code = &value
	}
	if value, ok := rc.mutation.SortID(); ok {
		_spec.SetField(role.FieldSortID, field.TypeInt32, value)
		_node.SortID = &value
	}
	if value, ok := rc.mutation.Menus(); ok {
		_spec.SetField(role.FieldMenus, field.TypeJSON, value)
		_node.Menus = value
	}
	if value, ok := rc.mutation.Apis(); ok {
		_spec.SetField(role.FieldApis, field.TypeJSON, value)
		_node.Apis = value
	}
	if nodes := rc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeUint32),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Role.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RoleUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (rc *RoleCreate) OnConflict(opts ...sql.ConflictOption) *RoleUpsertOne {
	rc.conflict = opts
	return &RoleUpsertOne{
		create: rc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (rc *RoleCreate) OnConflictColumns(columns ...string) *RoleUpsertOne {
	rc.conflict = append(rc.conflict, sql.ConflictColumns(columns...))
	return &RoleUpsertOne{
		create: rc,
	}
}

type (
	// RoleUpsertOne is the builder for "upsert"-ing
	//  one Role node.
	RoleUpsertOne struct {
		create *RoleCreate
	}

	// RoleUpsert is the "OnConflict" setter.
	RoleUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *RoleUpsert) SetUpdateTime(v time.Time) *RoleUpsert {
	u.Set(role.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *RoleUpsert) UpdateUpdateTime() *RoleUpsert {
	u.SetExcluded(role.FieldUpdateTime)
	return u
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *RoleUpsert) ClearUpdateTime() *RoleUpsert {
	u.SetNull(role.FieldUpdateTime)
	return u
}

// SetDeleteTime sets the "delete_time" field.
func (u *RoleUpsert) SetDeleteTime(v time.Time) *RoleUpsert {
	u.Set(role.FieldDeleteTime, v)
	return u
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *RoleUpsert) UpdateDeleteTime() *RoleUpsert {
	u.SetExcluded(role.FieldDeleteTime)
	return u
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *RoleUpsert) ClearDeleteTime() *RoleUpsert {
	u.SetNull(role.FieldDeleteTime)
	return u
}

// SetStatus sets the "status" field.
func (u *RoleUpsert) SetStatus(v role.Status) *RoleUpsert {
	u.Set(role.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *RoleUpsert) UpdateStatus() *RoleUpsert {
	u.SetExcluded(role.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *RoleUpsert) ClearStatus() *RoleUpsert {
	u.SetNull(role.FieldStatus)
	return u
}

// SetCreateBy sets the "create_by" field.
func (u *RoleUpsert) SetCreateBy(v uint32) *RoleUpsert {
	u.Set(role.FieldCreateBy, v)
	return u
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *RoleUpsert) UpdateCreateBy() *RoleUpsert {
	u.SetExcluded(role.FieldCreateBy)
	return u
}

// AddCreateBy adds v to the "create_by" field.
func (u *RoleUpsert) AddCreateBy(v uint32) *RoleUpsert {
	u.Add(role.FieldCreateBy, v)
	return u
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *RoleUpsert) ClearCreateBy() *RoleUpsert {
	u.SetNull(role.FieldCreateBy)
	return u
}

// SetUpdateBy sets the "update_by" field.
func (u *RoleUpsert) SetUpdateBy(v uint32) *RoleUpsert {
	u.Set(role.FieldUpdateBy, v)
	return u
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *RoleUpsert) UpdateUpdateBy() *RoleUpsert {
	u.SetExcluded(role.FieldUpdateBy)
	return u
}

// AddUpdateBy adds v to the "update_by" field.
func (u *RoleUpsert) AddUpdateBy(v uint32) *RoleUpsert {
	u.Add(role.FieldUpdateBy, v)
	return u
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *RoleUpsert) ClearUpdateBy() *RoleUpsert {
	u.SetNull(role.FieldUpdateBy)
	return u
}

// SetRemark sets the "remark" field.
func (u *RoleUpsert) SetRemark(v string) *RoleUpsert {
	u.Set(role.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *RoleUpsert) UpdateRemark() *RoleUpsert {
	u.SetExcluded(role.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *RoleUpsert) ClearRemark() *RoleUpsert {
	u.SetNull(role.FieldRemark)
	return u
}

// SetName sets the "name" field.
func (u *RoleUpsert) SetName(v string) *RoleUpsert {
	u.Set(role.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *RoleUpsert) UpdateName() *RoleUpsert {
	u.SetExcluded(role.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *RoleUpsert) ClearName() *RoleUpsert {
	u.SetNull(role.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *RoleUpsert) SetCode(v string) *RoleUpsert {
	u.Set(role.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *RoleUpsert) UpdateCode() *RoleUpsert {
	u.SetExcluded(role.FieldCode)
	return u
}

// ClearCode clears the value of the "code" field.
func (u *RoleUpsert) ClearCode() *RoleUpsert {
	u.SetNull(role.FieldCode)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *RoleUpsert) SetParentID(v uint32) *RoleUpsert {
	u.Set(role.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *RoleUpsert) UpdateParentID() *RoleUpsert {
	u.SetExcluded(role.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *RoleUpsert) ClearParentID() *RoleUpsert {
	u.SetNull(role.FieldParentID)
	return u
}

// SetSortID sets the "sort_id" field.
func (u *RoleUpsert) SetSortID(v int32) *RoleUpsert {
	u.Set(role.FieldSortID, v)
	return u
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *RoleUpsert) UpdateSortID() *RoleUpsert {
	u.SetExcluded(role.FieldSortID)
	return u
}

// AddSortID adds v to the "sort_id" field.
func (u *RoleUpsert) AddSortID(v int32) *RoleUpsert {
	u.Add(role.FieldSortID, v)
	return u
}

// ClearSortID clears the value of the "sort_id" field.
func (u *RoleUpsert) ClearSortID() *RoleUpsert {
	u.SetNull(role.FieldSortID)
	return u
}

// SetMenus sets the "menus" field.
func (u *RoleUpsert) SetMenus(v []uint32) *RoleUpsert {
	u.Set(role.FieldMenus, v)
	return u
}

// UpdateMenus sets the "menus" field to the value that was provided on create.
func (u *RoleUpsert) UpdateMenus() *RoleUpsert {
	u.SetExcluded(role.FieldMenus)
	return u
}

// ClearMenus clears the value of the "menus" field.
func (u *RoleUpsert) ClearMenus() *RoleUpsert {
	u.SetNull(role.FieldMenus)
	return u
}

// SetApis sets the "apis" field.
func (u *RoleUpsert) SetApis(v []uint32) *RoleUpsert {
	u.Set(role.FieldApis, v)
	return u
}

// UpdateApis sets the "apis" field to the value that was provided on create.
func (u *RoleUpsert) UpdateApis() *RoleUpsert {
	u.SetExcluded(role.FieldApis)
	return u
}

// ClearApis clears the value of the "apis" field.
func (u *RoleUpsert) ClearApis() *RoleUpsert {
	u.SetNull(role.FieldApis)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(role.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RoleUpsertOne) UpdateNewValues() *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(role.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(role.FieldCreateTime)
		}
		if _, exists := u.create.mutation.TenantID(); exists {
			s.SetIgnore(role.FieldTenantID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Role.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *RoleUpsertOne) Ignore() *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RoleUpsertOne) DoNothing() *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RoleCreate.OnConflict
// documentation for more info.
func (u *RoleUpsertOne) Update(set func(*RoleUpsert)) *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RoleUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *RoleUpsertOne) SetUpdateTime(v time.Time) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateUpdateTime() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *RoleUpsertOne) ClearUpdateTime() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *RoleUpsertOne) SetDeleteTime(v time.Time) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateDeleteTime() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *RoleUpsertOne) ClearDeleteTime() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *RoleUpsertOne) SetStatus(v role.Status) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateStatus() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *RoleUpsertOne) ClearStatus() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *RoleUpsertOne) SetCreateBy(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *RoleUpsertOne) AddCreateBy(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateCreateBy() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *RoleUpsertOne) ClearCreateBy() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *RoleUpsertOne) SetUpdateBy(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *RoleUpsertOne) AddUpdateBy(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateUpdateBy() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *RoleUpsertOne) ClearUpdateBy() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *RoleUpsertOne) SetRemark(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateRemark() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *RoleUpsertOne) ClearRemark() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *RoleUpsertOne) SetName(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateName() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *RoleUpsertOne) ClearName() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *RoleUpsertOne) SetCode(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateCode() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateCode()
	})
}

// ClearCode clears the value of the "code" field.
func (u *RoleUpsertOne) ClearCode() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearCode()
	})
}

// SetParentID sets the "parent_id" field.
func (u *RoleUpsertOne) SetParentID(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateParentID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *RoleUpsertOne) ClearParentID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearParentID()
	})
}

// SetSortID sets the "sort_id" field.
func (u *RoleUpsertOne) SetSortID(v int32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *RoleUpsertOne) AddSortID(v int32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateSortID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *RoleUpsertOne) ClearSortID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearSortID()
	})
}

// SetMenus sets the "menus" field.
func (u *RoleUpsertOne) SetMenus(v []uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetMenus(v)
	})
}

// UpdateMenus sets the "menus" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateMenus() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateMenus()
	})
}

// ClearMenus clears the value of the "menus" field.
func (u *RoleUpsertOne) ClearMenus() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearMenus()
	})
}

// SetApis sets the "apis" field.
func (u *RoleUpsertOne) SetApis(v []uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetApis(v)
	})
}

// UpdateApis sets the "apis" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateApis() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateApis()
	})
}

// ClearApis clears the value of the "apis" field.
func (u *RoleUpsertOne) ClearApis() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearApis()
	})
}

// Exec executes the query.
func (u *RoleUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RoleCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RoleUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *RoleUpsertOne) ID(ctx context.Context) (id uint32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *RoleUpsertOne) IDX(ctx context.Context) uint32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// RoleCreateBulk is the builder for creating many Role entities in bulk.
type RoleCreateBulk struct {
	config
	err      error
	builders []*RoleCreate
	conflict []sql.ConflictOption
}

// Save creates the Role entities in the database.
func (rcb *RoleCreateBulk) Save(ctx context.Context) ([]*Role, error) {
	if rcb.err != nil {
		return nil, rcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(rcb.builders))
	nodes := make([]*Role, len(rcb.builders))
	mutators := make([]Mutator, len(rcb.builders))
	for i := range rcb.builders {
		func(i int, root context.Context) {
			builder := rcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*RoleMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, rcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = rcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, rcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, rcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (rcb *RoleCreateBulk) SaveX(ctx context.Context) []*Role {
	v, err := rcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (rcb *RoleCreateBulk) Exec(ctx context.Context) error {
	_, err := rcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (rcb *RoleCreateBulk) ExecX(ctx context.Context) {
	if err := rcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Role.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RoleUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (rcb *RoleCreateBulk) OnConflict(opts ...sql.ConflictOption) *RoleUpsertBulk {
	rcb.conflict = opts
	return &RoleUpsertBulk{
		create: rcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (rcb *RoleCreateBulk) OnConflictColumns(columns ...string) *RoleUpsertBulk {
	rcb.conflict = append(rcb.conflict, sql.ConflictColumns(columns...))
	return &RoleUpsertBulk{
		create: rcb,
	}
}

// RoleUpsertBulk is the builder for "upsert"-ing
// a bulk of Role nodes.
type RoleUpsertBulk struct {
	create *RoleCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(role.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RoleUpsertBulk) UpdateNewValues() *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(role.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(role.FieldCreateTime)
			}
			if _, exists := b.mutation.TenantID(); exists {
				s.SetIgnore(role.FieldTenantID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *RoleUpsertBulk) Ignore() *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RoleUpsertBulk) DoNothing() *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RoleCreateBulk.OnConflict
// documentation for more info.
func (u *RoleUpsertBulk) Update(set func(*RoleUpsert)) *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RoleUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *RoleUpsertBulk) SetUpdateTime(v time.Time) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateUpdateTime() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateUpdateTime()
	})
}

// ClearUpdateTime clears the value of the "update_time" field.
func (u *RoleUpsertBulk) ClearUpdateTime() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearUpdateTime()
	})
}

// SetDeleteTime sets the "delete_time" field.
func (u *RoleUpsertBulk) SetDeleteTime(v time.Time) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetDeleteTime(v)
	})
}

// UpdateDeleteTime sets the "delete_time" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateDeleteTime() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateDeleteTime()
	})
}

// ClearDeleteTime clears the value of the "delete_time" field.
func (u *RoleUpsertBulk) ClearDeleteTime() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearDeleteTime()
	})
}

// SetStatus sets the "status" field.
func (u *RoleUpsertBulk) SetStatus(v role.Status) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateStatus() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *RoleUpsertBulk) ClearStatus() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearStatus()
	})
}

// SetCreateBy sets the "create_by" field.
func (u *RoleUpsertBulk) SetCreateBy(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetCreateBy(v)
	})
}

// AddCreateBy adds v to the "create_by" field.
func (u *RoleUpsertBulk) AddCreateBy(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.AddCreateBy(v)
	})
}

// UpdateCreateBy sets the "create_by" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateCreateBy() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateCreateBy()
	})
}

// ClearCreateBy clears the value of the "create_by" field.
func (u *RoleUpsertBulk) ClearCreateBy() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearCreateBy()
	})
}

// SetUpdateBy sets the "update_by" field.
func (u *RoleUpsertBulk) SetUpdateBy(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetUpdateBy(v)
	})
}

// AddUpdateBy adds v to the "update_by" field.
func (u *RoleUpsertBulk) AddUpdateBy(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.AddUpdateBy(v)
	})
}

// UpdateUpdateBy sets the "update_by" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateUpdateBy() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateUpdateBy()
	})
}

// ClearUpdateBy clears the value of the "update_by" field.
func (u *RoleUpsertBulk) ClearUpdateBy() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearUpdateBy()
	})
}

// SetRemark sets the "remark" field.
func (u *RoleUpsertBulk) SetRemark(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateRemark() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *RoleUpsertBulk) ClearRemark() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearRemark()
	})
}

// SetName sets the "name" field.
func (u *RoleUpsertBulk) SetName(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateName() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *RoleUpsertBulk) ClearName() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *RoleUpsertBulk) SetCode(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateCode() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateCode()
	})
}

// ClearCode clears the value of the "code" field.
func (u *RoleUpsertBulk) ClearCode() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearCode()
	})
}

// SetParentID sets the "parent_id" field.
func (u *RoleUpsertBulk) SetParentID(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateParentID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *RoleUpsertBulk) ClearParentID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearParentID()
	})
}

// SetSortID sets the "sort_id" field.
func (u *RoleUpsertBulk) SetSortID(v int32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetSortID(v)
	})
}

// AddSortID adds v to the "sort_id" field.
func (u *RoleUpsertBulk) AddSortID(v int32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.AddSortID(v)
	})
}

// UpdateSortID sets the "sort_id" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateSortID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateSortID()
	})
}

// ClearSortID clears the value of the "sort_id" field.
func (u *RoleUpsertBulk) ClearSortID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearSortID()
	})
}

// SetMenus sets the "menus" field.
func (u *RoleUpsertBulk) SetMenus(v []uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetMenus(v)
	})
}

// UpdateMenus sets the "menus" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateMenus() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateMenus()
	})
}

// ClearMenus clears the value of the "menus" field.
func (u *RoleUpsertBulk) ClearMenus() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearMenus()
	})
}

// SetApis sets the "apis" field.
func (u *RoleUpsertBulk) SetApis(v []uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetApis(v)
	})
}

// UpdateApis sets the "apis" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateApis() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateApis()
	})
}

// ClearApis clears the value of the "apis" field.
func (u *RoleUpsertBulk) ClearApis() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearApis()
	})
}

// Exec executes the query.
func (u *RoleUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the RoleCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RoleCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RoleUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
