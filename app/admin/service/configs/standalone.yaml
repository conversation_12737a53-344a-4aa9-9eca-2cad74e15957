# 独立模式配置 - 不依赖任何外部中间件
data:
  database:
    driver: "sqlite3"
    # SQLite 数据库文件路径
    source: "file:./data/kratos_admin.db?cache=shared&_fk=1"
    migrate: true
    debug: true
    enable_trace: false
    enable_metrics: false
    max_idle_connections: 25
    max_open_connections: 25
    connection_max_lifetime: 300s

  # 禁用 Redis 配置
  # redis:
  #   addr: "localhost:6379"

server:
  rest:
    addr: 0.0.0.0:7788
    timeout: 30s
    middleware:
      auth:
        access_token_key_prefix: "access_token:"
        refresh_token_key_prefix: "refresh_token:"
        access_token_expires: 7200s
        refresh_token_expires: 604800s
        signing_method: "HS256"
        signing_key: "kratos_admin"
        issuer: "kratos_admin"
      cors:
        allow_origins:
          - "*"
        allow_methods:
          - "GET"
          - "POST"
          - "PUT"
          - "DELETE"
          - "OPTIONS"
        allow_headers:
          - "*"
        allow_credentials: true
        max_age: 86400s
      logging:
        enable: true
      tracing:
        enable: false
      metrics:
        enable: false

  # 禁用 Asynq 服务器
  # asynq:
  #   uri: "redis://localhost:6379/0"

  # 禁用 SSE 服务器
  # sse:
  #   addr: 0.0.0.0:7789

# 禁用链路追踪
trace:
  endpoint: ""
  batcher: ""
  sampler: 0.0
  env: "standalone"
  insecure: true

# 禁用服务注册 - 完全注释掉 registry 配置
# registry:
#   type: ""

# 禁用 OSS 配置
# oss:
#   minio:
