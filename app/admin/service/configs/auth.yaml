authn:
  type: "jwt" # jwt, oidc, preshared_key

  jwt:
    method: "HS256" # HS256, <PERSON><PERSON>384, <PERSON><PERSON>512, <PERSON>256, <PERSON>384, <PERSON>512, <PERSON><PERSON>256, <PERSON><PERSON>384, <PERSON><PERSON>512, Ed25519
    key: "some_api_key"

  oidc:
    issuer_url: "https://example.com"
    audience: "your_audience"
    method: "HS256"

  preshared_key:
    valid_keys:
      - "key1"
      - "key2"

authz:
  type: "opa" # casbin, opa, zanzibar, noop

  casbin:

  opa:

  zanzibar:
    type: "keto" # keto, open_fga

    keto:
      write_url: "http://keto:4466"
      read_url: "http://keto:4466"
      use_grpc: true

    open_fga:
      api_url: "http://openfga:8080"
      store_id: "your_store_id"
      token: "your_token"
