registry:
  type: "etcd"  # Options: etcd, consul, zookeeper, nacos, kubernetes, eureka, polaris, servicecomb

  consul:
    address: "127.0.0.1:8500"
    scheme: "http"
    heartbeat: false
    health_check: false
    health_check_interval: 5

  etcd:
    endpoints:
      - "127.0.0.1:2379"

  zookeeper:
    endpoints:
      - "zookeeper:2181"
    timeout: 10s

  nacos:
    address: "127.0.0.1"
    port: 8848
    namespace_id: "public"
    log_level: "../../configs/cache"
    cache_dir: "../../configs/log"
    log_dir: "debug"
    update_thread_num: 20
    timeout: 10s
    beat_interval: 5s
    not_load_cache_at_start: true
    update_cache_when_empty: true

  kubernetes:

  eureka:
    endpoints:
      - "eureka:18761"
    heartbeat_interval: 10s
    refresh_interval: 10s
    path:

  polaris:
    address: polaris
    port:
    instance_count:
    namespace:
    service:
    token:

  servicecomb:
    endpoints:
      - "servicecomb:30100"
