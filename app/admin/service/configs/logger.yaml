logger:
  type: std # Options: std, file, fluent, zap, logrus, aliyun, tencent

  fluent:
    endpoint: "tcp://localhost:24224"

  zap:
    level: "debug"
    filename: "./logs/info.log"
    max_size: 1
    max_age: 30
    max_backups: 5

  logrus:
    level: "debug"
    formatter: "text"
    timestamp_format: "2006-01-02 15:04:05"
    disable_colors: false
    disable_timestamp: false

  aliyun:
    endpoint: ""
    project: ""
    access_key: "<access_key>"
    access_secret: "<access_secret>"

  tencent:
    endpoint: ""
    topic_id:
    access_key: "<access_key>"
    access_secret: "<access_secret>"
