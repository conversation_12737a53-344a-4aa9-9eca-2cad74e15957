openapi: 3.0.3
info:
  title: GoofishApi API
  description: 闲管家虚拟货源标准接口 - goofish API 路由配置，字段与 Apifox 文档保持一致
  version: 0.0.1
paths:
  /api/open/callback/virtual/goods/notify/{token}:
    post:
      tags:
      - GoofishApi
      description: 商品回调通知 - 注意：实际回调地址在订阅通知时传入
      operationId: GoofishApi_GoodsCallback
      parameters:
      - name: token
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoodsCallbackItem'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GoodsCallbackResponse'
  /api/open/callback/virtual/order/notify/{token}:
    post:
      tags:
      - GoofishApi
      description: 订单回调通知 - 注意：实际回调地址在创建订单时传入
      operationId: GoofishApi_OrderCallback
      parameters:
      - name: token
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderCallbackRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCallbackResponse'
  /goofish/goods/change/subscribe/list:
    post:
      tags:
      - GoofishApi
      description: 查询商品订阅列表 - 查询已订阅商品变更通知的商品列表
      operationId: GoofishApi_GetGoodsChangeSubscribeList
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoodsChangeSubscribeListRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GoodsChangeSubscribeListResponse'
  /goofish/goods/change/subscribe:
    post:
      tags:
      - GoofishApi
      description: 订阅商品变更通知 - 订阅货源商品价格、库存、状态变更通知
      operationId: GoofishApi_GoodsChangeSubscribe
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoodsChangeSubscribeRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GoodsChangeSubscribeResponse'
  /goofish/goods/change/unsubscribe:
    post:
      tags:
      - GoofishApi
      description: 取消商品变更通知 - 取消订阅货源商品价格、库存、状态变更通知
      operationId: GoofishApi_GoodsChangeUnsubscribe
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoodsChangeUnsubscribeRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GoodsChangeUnsubscribeResponse'
  /goofish/goods/detail:
    post:
      tags:
      - GoofishApi
      description: 查询商品详情
      operationId: GoofishApi_GetGoodsDetail
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoodsDetailRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GoodsDetailResponse'
  /goofish/goods/list:
    post:
      tags:
      - GoofishApi
      description: 查询商品列表
      operationId: GoofishApi_GetGoodsList
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoodsListRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GoodsListResponse'
  /goofish/open/info:
    post:
      tags:
      - GoofishApi
      description: 查询平台信息
      operationId: GoofishApi_GetPlatformInfo
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlatformInfoRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlatformInfoResponse'
  /goofish/order/detail:
    post:
      tags:
      - GoofishApi
      description: 查询订单详情
      operationId: GoofishApi_GetOrderDetail
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderDetailRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetailResponse'
  /goofish/order/purchase/create:
    post:
      tags:
      - GoofishApi
      description: 创建卡密订单
      operationId: GoofishApi_CreateCardOrder
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCardOrderRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCardOrderResponse'
  /goofish/order/recharge/create:
    post:
      tags:
      - GoofishApi
      description: 创建直充订单
      operationId: GoofishApi_CreateRechargeOrder
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRechargeOrderRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRechargeOrderResponse'
  /goofish/user/info:
    post:
      tags:
      - GoofishApi
      description: 查询商户信息
      operationId: GoofishApi_GetUserInfo
      parameters:
      - name: mchId
        in: query
        description: 货源平台商户ID（AppKey）
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        required: true
        schema:
          type: integer
          format: int64
        description: 当前时间戳（单位秒，5分钟内有效）
      - name: sign
        in: query
        required: true
        schema:
          type: string
        description: 签名MD5值（参考签名说明）
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInfoRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfoResponse'
components:
  schemas:
    BizContent:
      type: object
      properties:
        account:
          type: string
        gameName:
          type: string
        gameRole:
          type: string
        gameArea:
          type: string
        gameServer:
          type: string
        buyerIp:
          type: string
        buyerArea:
          type: string
      description: 订单相关
    CardItem:
      type: object
      properties:
        cardNo:
          type: string
        cardPwd:
          type: string
    CardOrderData:
      type: object
      properties:
        orderNo:
          type: string
        outOrderNo:
          type: string
        orderStatus:
          type: integer
          format: int32
        orderAmount:
          type: string
        orderTime:
          type: string
        endTime:
          type: string
        cardItems:
          type: array
          items:
            $ref: '#/components/schemas/CardItem'
        remark:
          type: string
    CreateCardOrderRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        orderNo:
          type: string
          description: Body 参数
        goodsNo:
          type: string
        buyQuantity:
          type: integer
          format: int32
        maxAmount:
          type: string
        notifyUrl:
          type: string
        bizOrderNo:
          type: string
      description: 创建卡密订单
    CreateCardOrderResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/CardOrderData'
    CreateRechargeOrderRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        orderNo:
          type: string
          description: Body 参数
        goodsNo:
          type: string
        bizContent:
          $ref: '#/components/schemas/BizContent'
        buyQuantity:
          type: integer
          format: int32
        maxAmount:
          type: string
        notifyUrl:
          type: string
        bizOrderNo:
          type: string
      description: 创建直充订单
    CreateRechargeOrderResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/RechargeOrderData'
    GoodsCallbackItem:
      type: object
      properties:
        goodsNo:
          type: string
        goodsType:
          type: integer
          format: int32
        price:
          type: string
        stock:
          type: integer
          format: int32
        status:
          type: integer
          format: int32
        changeTime:
          type: string
      description: 商品回调通知
    GoodsCallbackResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
    GoodsChangeSubscribeListData:
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/GoodsChangeSubscribeListItem'
        count:
          type: integer
          format: int32
    GoodsChangeSubscribeListItem:
      type: object
      properties:
        goodsType:
          type: integer
          format: int32
        goodsNo:
          type: string
        subscribeTime:
          type: string
        token:
          type: string
        notifyUrl:
          type: string
    GoodsChangeSubscribeListRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        goodsType:
          type: integer
          description: Body 参数
          format: int32
        goodsNo:
          type: string
        pageNo:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
      description: 查询商品订阅列表 - 完整请求
    GoodsChangeSubscribeListResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/GoodsChangeSubscribeListData'
    GoodsChangeSubscribeRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        goodsType:
          type: integer
          description: Body 参数
          format: int32
        goodsNo:
          type: string
        token:
          type: string
        notifyUrl:
          type: string
      description: 订阅商品变更通知
    GoodsChangeSubscribeResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
    GoodsChangeUnsubscribeRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        goodsType:
          type: integer
          description: Body 参数
          format: int32
        goodsNo:
          type: string
        token:
          type: string
      description: 取消商品变更通知
    GoodsChangeUnsubscribeResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
    GoodsDetail:
      type: object
      properties:
        goodsNo:
          type: string
        goodsType:
          type: integer
          format: int32
        goodsName:
          type: string
        price:
          type: string
        stock:
          type: integer
          format: int32
        status:
          type: integer
          format: int32
        updateTime:
          type: string
        template:
          type: array
          items:
            $ref: '#/components/schemas/GoodsTemplate'
      description: 商品详情
    GoodsDetailRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        goodsType:
          type: integer
          description: Body 参数
          format: int32
        goodsNo:
          type: string
      description: 查询商品详情
    GoodsDetailResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/GoodsDetail'
    GoodsListData:
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/GoodsDetail'
        count:
          type: integer
          format: int32
    GoodsListRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        keyword:
          type: string
          description: Body 参数
        goodsType:
          type: integer
          format: int32
        pageNo:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
      description: 查询商品列表
    GoodsListResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/GoodsListData'
    GoodsTemplate:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        desc:
          type: string
        check:
          type: integer
          format: int32
      description: 商品详情模板
    OrderCallbackRequest:
      type: object
      properties:
        token:
          type: string
        orderType:
          type: integer
          format: int32
        orderNo:
          type: string
        outOrderNo:
          type: string
        orderStatus:
          type: integer
          format: int32
        endTime:
          type: string
        cardItems:
          type: array
          items:
            $ref: '#/components/schemas/CardItem'
        remark:
          type: string
      description: 订单回调通知
    OrderCallbackResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
    OrderDetailData:
      type: object
      properties:
        orderType:
          type: integer
          format: int32
        orderNo:
          type: string
        outOrderNo:
          type: string
        orderStatus:
          type: integer
          format: int32
        orderAmount:
          type: string
        goodsNo:
          type: string
        goodsName:
          type: string
        buyQuantity:
          type: integer
          format: int32
        orderTime:
          type: string
        endTime:
          type: string
        bizContent:
          $ref: '#/components/schemas/BizContent'
        cardItems:
          type: array
          items:
            $ref: '#/components/schemas/CardItem'
        remark:
          type: string
    OrderDetailRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
        orderType:
          type: integer
          description: Body 参数
          format: int32
        orderNo:
          type: string
        outOrderNo:
          type: string
      description: 查询订单详情
    OrderDetailResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/OrderDetailData'
    PlatformInfoData:
      type: object
      properties:
        appId:
          type: string
    PlatformInfoRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
      description: 查询平台信息
    PlatformInfoResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/PlatformInfoData'
    RechargeOrderData:
      type: object
      properties:
        orderNo:
          type: string
        outOrderNo:
          type: string
        orderStatus:
          type: integer
          format: int32
        orderAmount:
          type: string
        goodsName:
          type: string
        orderTime:
          type: string
        endTime:
          type: string
        remark:
          type: string
    UserInfoData:
      type: object
      properties:
        balance:
          type: string
    UserInfoRequest:
      type: object
      properties:
        mchId:
          type: string
          description: Query 参数
        timestamp:
          type: string
        sign:
          type: string
      description: 查询商户信息
    UserInfoResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        msg:
          type: string
        data:
          $ref: '#/components/schemas/UserInfoData'
tags:
- name: GoofishApi
