package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport/http"

	"github.com/tx7do/kratos-bootstrap/bootstrap"
	"github.com/tx7do/kratos-transport/transport/asynq"
	"github.com/tx7do/kratos-transport/transport/sse"

	"github.com/tx7do/go-utils/trans"

	"kratos-admin/pkg/service"
)

var version string

// go build -ldflags "-X main.version=x.y.z"

func newApp(
	lg log.Logger,
	re registry.Registrar,
	hs *http.Server,
	as *asynq.Server,
	ss *sse.Server,
) *kratos.App {
	// 过滤掉 nil 服务器，只添加非 nil 的服务器
	var servers []kratos.Option

	if re != nil {
		servers = append(servers, kratos.Registrar(re))
	}

	if hs != nil {
		servers = append(servers, kratos.Server(hs))
	}
	if as != nil {
		servers = append(servers, kratos.Server(as))
	}
	if ss != nil {
		servers = append(servers, kratos.Server(ss))
	}

	return kratos.New(servers...)
}

func main() {
	bootstrap.Bootstrap(initApp, trans.Ptr(service.AdminService), trans.Ptr(version))
}
