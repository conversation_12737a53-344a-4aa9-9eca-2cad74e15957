// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/tx7do/kratos-bootstrap/api/gen/go/conf/v1"
	"kratos-admin/app/admin/service/internal/data"
	"kratos-admin/app/admin/service/internal/server"
	"kratos-admin/app/admin/service/internal/service"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(logger log.Logger, registrar registry.Registrar, bootstrap *v1.Bootstrap) (*kratos.App, func(), error) {
	authenticator := data.NewAuthenticator(bootstrap)
	entClient := data.NewEntClient(bootstrap, logger)
	client := data.NewRedisClient(bootstrap, logger)
	dataData, cleanup, err := data.NewData(logger, entClient, client)
	if err != nil {
		return nil, nil, err
	}
	roleRepo := data.NewRoleRepo(dataData, logger)
	apiResourceRepo := data.NewApiResourceRepo(dataData, logger)
	authorizer := data.NewAuthorizer(logger, bootstrap, roleRepo, apiResourceRepo)
	adminOperationLogRepo := data.NewAdminOperationLogRepo(dataData, logger)
	adminLoginLogRepo := data.NewAdminLoginLogRepo(dataData, logger)
	userRepo := data.NewUserRepo(logger, dataData)
	crypto := data.NewPasswordCrypto()
	userCredentialRepo := data.NewUserCredentialRepo(logger, dataData, crypto)
	tenantRepo := data.NewTenantRepo(dataData, logger)
	userTokenCacheRepo := data.NewUserTokenRepo(logger, client, authenticator, bootstrap)
	authenticationService := service.NewAuthenticationService(logger, userRepo, userCredentialRepo, tenantRepo, roleRepo, userTokenCacheRepo, authenticator)
	userService := service.NewUserService(logger, userRepo, roleRepo, userCredentialRepo)
	menuRepo := data.NewMenuRepo(dataData, logger)
	menuService := service.NewMenuService(logger, menuRepo)
	routerService := service.NewRouterService(logger, menuRepo, roleRepo, userRepo)
	organizationRepo := data.NewOrganizationRepo(dataData, logger)
	organizationService := service.NewOrganizationService(logger, organizationRepo)
	roleService := service.NewRoleService(logger, roleRepo)
	positionRepo := data.NewPositionRepo(dataData, logger)
	positionService := service.NewPositionService(logger, positionRepo)
	dictRepo := data.NewDictRepo(dataData, logger)
	dictService := service.NewDictService(logger, dictRepo)
	departmentRepo := data.NewDepartmentRepo(dataData, logger)
	departmentService := service.NewDepartmentService(logger, departmentRepo)
	adminLoginLogService := service.NewAdminLoginLogService(logger, adminLoginLogRepo)
	adminOperationLogService := service.NewAdminOperationLogService(logger, adminOperationLogRepo, apiResourceRepo)
	minIOClient := data.NewMinIoClient(bootstrap, logger)
	ossService := service.NewOssService(logger, minIOClient)
	uEditorService := service.NewUEditorService(logger, minIOClient)
	fileRepo := data.NewFileRepo(dataData, logger)
	fileService := service.NewFileService(logger, fileRepo)
	tenantService := service.NewTenantService(logger, tenantRepo)
	taskRepo := data.NewTaskRepo(dataData, logger)
	taskService := service.NewTaskService(logger, taskRepo, userRepo)
	notificationMessageRepo := data.NewNotificationMessageRepo(dataData, logger)
	notificationMessageService := service.NewNotificationMessageService(logger, notificationMessageRepo)
	notificationMessageCategoryRepo := data.NewNotificationMessageCategoryRepo(dataData, logger)
	notificationMessageCategoryService := service.NewNotificationMessageCategoryService(logger, notificationMessageCategoryRepo)
	notificationMessageRecipientRepo := data.NewNotificationMessageRecipientRepo(dataData, logger)
	notificationMessageRecipientService := service.NewNotificationMessageRecipientService(logger, notificationMessageRecipientRepo)
	privateMessageRepo := data.NewPrivateMessageRepo(dataData, logger)
	privateMessageService := service.NewPrivateMessageService(logger, privateMessageRepo)
	adminLoginRestrictionRepo := data.NewAdminLoginRestrictionRepo(dataData, logger)
	adminLoginRestrictionService := service.NewAdminLoginRestrictionService(logger, adminLoginRestrictionRepo)
	userProfileService := service.NewUserProfileService(logger, userRepo, userTokenCacheRepo, roleRepo)
	apiResourceService := service.NewApiResourceService(logger, apiResourceRepo)
	goofishApiService := service.NewGoofishApiService(logger)
	httpServer := server.NewRESTServer(bootstrap, logger, authenticator, authorizer, adminOperationLogRepo, adminLoginLogRepo, authenticationService, userService, menuService, routerService, organizationService, roleService, positionService, dictService, departmentService, adminLoginLogService, adminOperationLogService, ossService, uEditorService, fileService, tenantService, taskService, notificationMessageService, notificationMessageCategoryService, notificationMessageRecipientService, privateMessageService, adminLoginRestrictionService, userProfileService, apiResourceService, goofishApiService)
	asynqServer := server.NewAsynqServer(bootstrap, logger, taskService)
	sseServer := server.NewSseServer(bootstrap, logger)
	app := newApp(logger, registrar, httpServer, asynqServer, sseServer)
	return app, func() {
		cleanup()
	}, nil
}
