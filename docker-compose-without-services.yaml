networks:
  app-tier:
    driver: bridge

services:
  postgres:
    image: docker.io/timescale/timescaledb:latest-pg15
    restart: always
    ports:
      - "5432:5432"
    networks:
      - app-tier
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*Abcd123456
      - POSTGRES_DB=kratos_admin

  redis:
    image: docker.io/bitnami/redis:latest
    restart: always
    ports:
      - "6379:6379"
    networks:
      - app-tier
    environment:
      - ALLOW_EMPTY_PASSWORD=no
      - REDIS_PASSWORD=*Abcd123456
      - REDIS_AOF_ENABLED=no
      - REDIS_IO_THREADS_DO_READS=yes
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL,CONFIG
    volumes:
      - /root/app/redis/data:/bitnami/redis/data

  minio:
    image: docker.io/minio/minio:latest
    restart: always
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - app-tier
    environment:
      - MINIO_ROOT_USER=root
      - MINIO_ROOT_PASSWORD=*Abcd123456
      - MINIO_DEFAULT_BUCKETS=images
      - MINIO_FORCE_NEW_KEYS=yes
      - BITNAMI_DEBUG=true
    volumes:
      - /root/app/minio/data:/data
    command: server /data --console-address ':9001'

#  consul:
#    image: docker.io/bitnami/consul:latest
#    restart: always
#    ports:
#      - '8300:8300'
#      - '8301:8301'
#      - '8301:8301/udp'
#      - '8500:8500'
#      - '8600:8600'
#      - '8600:8600/udp'
#    networks:
#      - app-tier
#    environment:
#      - CONSUL_BIND_INTERFACE='eth0'
#      - CONSUL_AGENT_MODE=server
#      - CONSUL_ENABLE_UI=true
#      - CONSUL_BOOTSTRAP_EXPECT=1
#      - CONSUL_CLIENT_LAN_ADDRESS=0.0.0.0
#      - CONSUL_BIND_ADDR=0.0.0.0
#      - CONSUL_DISABLE_KEYRING_FILE=true
#      - CONSUL_NODE_NAME=consul

  etcd:
    image: docker.io/bitnami/etcd:latest
    restart: always
    ports:
      - "2379:2379"
      - "2380:2380"
    networks:
      - app-tier
    environment:
      - ETCDCTL_API=3
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ADVERTISE_CLIENT_URLS=http://0.0.0.0:2379

  jaeger:
    image: docker.io/jaegertracing/all-in-one:latest
    restart: always
    ports:
      - "6831:6831/udp"
      - "5778:5778"
      - "4317:4317"
      - "4318:4318"
      - "16686:16686"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
    networks:
      - app-tier
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_OTLP_ENABLED=true
