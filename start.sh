#!/bin/bash

# 启动脚本 - 确保 serverless 环境中的数据库文件和目录正确设置

echo "Starting goofish-api in serverless environment..."

# 显示当前用户和环境信息
echo "Current user: $(whoami)"
echo "Current working directory: $(pwd)"
echo "Environment variables:"
env | grep -E "(PATH|HOME|USER|PWD)" | sort

# 检查并创建数据目录
if [ ! -d "/code/data" ]; then
    echo "Creating data directory..."
    mkdir -p /code/data
    if [ $? -eq 0 ]; then
        echo "Data directory created successfully"
    else
        echo "Failed to create data directory"
        exit 1
    fi
else
    echo "Data directory already exists"
fi

# 检查数据库文件是否存在
if [ ! -f "/code/data/kratos_admin.db" ]; then
    echo "Database file not found, it will be created during migration..."
    # 尝试创建一个空的数据库文件来测试权限
    touch /code/data/kratos_admin.db
    if [ $? -eq 0 ]; then
        echo "Successfully created test database file"
        rm /code/data/kratos_admin.db
    else
        echo "Failed to create test database file - permission issue"
        exit 1
    fi
else
    echo "Database file already exists"
fi

# 设置目录权限
chmod 755 /code/data
echo "Set directory permissions to 755"

# 如果数据库文件存在，设置文件权限
if [ -f "/code/data/kratos_admin.db" ]; then
    chmod 644 /code/data/kratos_admin.db
    echo "Database file permissions set to 644"
fi

# 显示当前目录结构和权限
echo "Current directory structure:"
ls -la /code/
echo "Data directory contents:"
ls -la /code/data/
echo "Config directory contents:"
ls -la /code/configs/

# 检查可执行文件
echo "Checking main executable:"
ls -la /code/main
file /code/main

# 启动应用
echo "Starting application..."
exec /code/main -conf /code/configs
