-- 默认的超级管理员，默认账号：admin，密码：admin
TRUNCATE TABLE kratos_admin.public.users RESTART IDENTITY;
INSERT INTO kratos_admin.public.users (username, nickname, email, authority, roles)
VALUES ('admin', 'admin', '<EMAIL>', 'SYS_ADMIN', '["super"]');
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));

TRUNCATE TABLE user_credentials RESTART IDENTITY;
INSERT INTO user_credentials (user_id, identity_type, identifier, credential_type, credential, status, is_primary,
                              create_time)
VALUES (1, 'USERNAME', 'admin', 'PASSWORD_HASH', '$2a$10$yajZDX20Y40FkG0Bu4N19eXNqRizez/S9fK63.JxGkfLq.RoNKR/a',
        'ENABLED', true, now()),
       (1, 'EMAIL', '<EMAIL>', 'PASSWORD_HASH', '$2a$10$yajZDX20Y40FkG0Bu4N19eXNqRizez/S9fK63.JxGkfLq.RoNKR/a',
        'ENABLED', true, now())
;
SELECT setval('user_credentials_id_seq', (SELECT MAX(id) FROM user_credentials));

-- 默认的角色
TRUNCATE TABLE kratos_admin.public.sys_roles RESTART IDENTITY;
INSERT INTO kratos_admin.public.sys_roles(id, parent_id, create_by, sort_id, name, code, status, remark, menus, apis,
                                          create_time)
VALUES (1, null, 0, 1, '超级管理员', 'super', 'ON', '超级管理员拥有对系统的最高权限',
        '[1, 2, 10, 11, 12, 13, 14, 20, 21, 22, 15, 16, 17, 18, 23, 24, 25, 26, 27, 30, 31, 32]', '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102]', now()),
       (2, null, 0, 2, '管理员', 'admin', 'ON', '系统管理员拥有对整个系统的管理权限',
        '[1, 2, 6, 7, 8, 9, 10, 11, 12, 13, 14]', '[]', now()),
       (3, null, 0, 3, '普通用户', 'user', 'ON', '普通用户没有管理权限，只有设备和APP的使用权限', '[]', '[]', now()),
       (4, null, 0, 4, '游客', 'guest', 'ON', '游客只有非常有限的数据读取权限', '[]', '[]', now());
SELECT setval('sys_roles_id_seq', (SELECT MAX(id) FROM sys_roles));

-- 后台目录
TRUNCATE TABLE kratos_admin.public.sys_menus RESTART IDENTITY;
INSERT INTO kratos_admin.public.sys_menus(id, parent_id, type, name, path, redirect, component, status, create_time,
                                          meta)
VALUES (1, null, 'FOLDER', 'Dashboard', '/', null, 'BasicLayout', 'ON', now(),
        '{"order":-1, "title":"page.dashboard.title", "icon":"lucide:layout-dashboard", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (2, 1, 'MENU', 'Analytics', '/analytics', null, 'dashboard/analytics/index.vue', 'ON', now(),
        '{"order":-1, "title":"page.dashboard.analytics", "icon":"lucide:area-chart", "affixTab": true, "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),

       (10, null, 'FOLDER', 'TenantManagement', '/tenant', null, 'BasicLayout', 'ON', now(),
        '{"order":2001, "title":"menu.tenant.moduleName", "icon":"lucide:earth", "keepAlive":true, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (11, 10, 'MENU', 'TenantMemberManagement', 'members', null, 'app/tenant/tenant/index.vue', 'ON', now(),
        '{"order":1, "title":"menu.tenant.member", "icon":"lucide:book-user", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),

       (20, null, 'FOLDER', 'OrganizationalPersonnelManagement', '/opm', null, 'BasicLayout', 'ON', now(),
        '{"order":2002, "title":"menu.opm.moduleName", "icon":"lucide:shield-check", "keepAlive":true, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (21, 20, 'MENU', 'UserManagement', 'users', null, 'app/opm/users/index.vue', 'ON', now(),
        '{"order":1, "title":"menu.opm.user", "icon":"lucide:users", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (22, 20, 'MENU', 'UserDetail', 'users/detail/:id', null, 'app/opm/users/detail/index.vue', 'ON', now(),
        '{"order":2, "title":"menu.opm.userDetail", "icon":"", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":true, "hideInTab":false}'),
       (25, 20, 'MENU', 'OrganizationManagement', 'organizations', null, 'app/opm/org/index.vue', 'ON', now(),
        '{"order":3, "title":"menu.opm.org", "icon":"lucide:building-2", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (26, 20, 'MENU', 'DepartmentManagement', 'departments', null, 'app/opm/dept/index.vue', 'ON', now(),
        '{"order":4, "title":"menu.opm.dept", "icon":"lucide:network", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (27, 20, 'MENU', 'PositionManagement', 'positions', null, 'app/opm/position/index.vue', 'ON', now(),
        '{"order":5, "title":"menu.opm.position", "icon":"lucide:id-card", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),

       (30, null, 'FOLDER', 'PermissionManagement', '/permission', null, 'BasicLayout', 'ON', now(),
        '{"order":2003, "title":"menu.permission.moduleName", "icon":"lucide:shield-check", "keepAlive":true, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (31, 30, 'MENU', 'RoleManagement', 'roles', null, 'app/permission/role/index.vue', 'ON', now(),
        '{"order":1, "title":"menu.permission.role", "icon":"lucide:user-round-cog", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (32, 30, 'MENU', 'MenuManagement', 'menus', null, 'app/permission/menu/index.vue', 'ON', now(),
        '{"order":2, "title":"menu.permission.menu", "icon":"lucide:layout-list", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),

       (40, null, 'FOLDER', 'LogAuditManagement', '/log', null, 'BasicLayout', 'ON', now(),
        '{"order":2002, "title":"menu.log.moduleName", "icon":"lucide:logs", "keepAlive":true, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (41, 40, 'MENU', 'AdminLoginLog', 'login', null, 'app/log/admin_login_log/index.vue', 'ON', now(),
        '{"order":1, "title":"menu.log.adminLoginLog", "icon":"lucide:file-symlink", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (42, 40, 'MENU', 'AdminOperationLog', 'operation', null, 'app/log/admin_operation_log/index.vue', 'ON', now(),
        '{"order":2, "title":"menu.log.adminOperationLog", "icon":"lucide:file-sliders", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),

       (50, null, 'FOLDER', 'System', '/system', null, 'BasicLayout', 'ON', now(),
        '{"order":2004, "title":"menu.system.moduleName", "icon":"lucide:settings", "keepAlive":true, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (51, 50, 'MENU', 'DictManagement', 'dict', null, 'app/system/dict/index.vue', 'ON', now(),
        '{"order":1, "title":"menu.system.dict", "icon":"lucide:library-big", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (52, 50, 'MENU', 'FileManagement', 'files', null, 'app/system/files/index.vue', 'ON', now(),
        '{"order":2, "title":"menu.system.file", "icon":"lucide:file", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (53, 50, 'MENU', 'TaskManagement', 'tasks', null, 'app/system/task/index.vue', 'ON', now(),
        '{"order":3, "title":"menu.system.task", "icon":"lucide:calendar-clock", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (54, 50, 'MENU', 'APIResourceManagement', 'apis', null, 'app/system/api_resource/index.vue', 'ON', now(),
        '{"order":4, "title":"menu.system.apiResource", "icon":"lucide:route", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (55, 50, 'MENU', 'NotificationMessageManagement', 'notifications', null,
        'app/system/notification_message/index.vue', 'ON', now(),
        '{"order":5, "title":"menu.system.notificationMessage", "icon":"lucide:bell", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (56, 50, 'MENU', 'NotificationMessageCategoryManagement', 'notification_categories', null,
        'app/system/notification_message_category/index.vue', 'ON', now(),
        '{"order":6, "title":"menu.system.notificationMessageCategory", "icon":"lucide:bell-dot", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}'),
       (57, 50, 'MENU', 'PrivateMessageManagement', 'private_messages', null, 'app/system/private_message/index.vue',
        'ON', now(),
        '{"order":7, "title":"menu.system.privateMessage", "icon":"lucide:message-circle-more", "keepAlive":false, "hideInBreadcrumb":false, "hideInMenu":false, "hideInTab":false}');
SELECT setval('sys_menus_id_seq', (SELECT MAX(id) FROM sys_menus));

-- API资源表数据
TRUNCATE TABLE kratos_admin.public.sys_api_resources RESTART IDENTITY;
INSERT INTO kratos_admin.public.sys_api_resources (id, create_time, update_time, create_by, update_by, delete_time, name, service, description, code, path, method)
VALUES (1, '2025-06-16 04:05:27.786576+00', null, null, null, null, '更新API资源', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_Update', '/admin/v1/api-resources/{data.id}', 'PUT'),
       (2, '2025-06-16 04:05:27.788342+00', null, null, null, null, '登录', 'AuthenticationService', '用户后台登录认证服务', 'AuthenticationService_Login', '/admin/v1/login', 'POST'),
       (3, '2025-06-16 04:05:27.789528+00', null, null, null, null, '控制调度任务', 'TaskService', '调度任务管理服务', 'TaskService_ControlTask', '/admin/v1/tasks:control', 'POST'),
       (4, '2025-06-16 04:05:27.791119+00', null, null, null, null, '查询通知消息分类列表', 'NotificationMessageCategoryService', '通知消息分类管理服务', 'NotificationMessageCategoryService_List', '/admin/v1/notifications:categories', 'GET'),
       (5, '2025-06-16 04:05:27.792292+00', null, null, null, null, '创建通知消息分类', 'NotificationMessageCategoryService', '通知消息分类管理服务', 'NotificationMessageCategoryService_Create', '/admin/v1/notifications:categories', 'POST'),
       (6, '2025-06-16 04:05:27.793446+00', null, null, null, null, 'UEditor API', 'UEditorService', 'UEditor后端服务', 'UEditorService_UEditorAPI', '/admin/v1/ueditor', 'GET'),
       (7, '2025-06-16 04:05:27.794657+00', null, null, null, null, '上传文件', 'UEditorService', 'UEditor后端服务', 'UEditorService_UploadFile', '/admin/v1/ueditor', 'POST'),
       (8, '2025-06-16 04:05:27.795816+00', null, null, null, null, '删除用户', 'UserService', '用户管理服务', 'UserService_Delete', '/admin/v1/users/{id}', 'DELETE'),
       (9, '2025-06-16 04:05:27.797016+00', null, null, null, null, '获取用户数据', 'UserService', '用户管理服务', 'UserService_Get', '/admin/v1/users/{id}', 'GET'),
       (10, '2025-06-16 04:05:27.798348+00', null, null, null, null, '查询后台操作日志详情', 'AdminOperationLogService', '后台操作日志管理服务', 'AdminOperationLogService_Get', '/admin/v1/admin_operation_logs/{id}', 'GET'),
       (11, '2025-06-16 04:05:27.799627+00', null, null, null, null, '删除部门', 'DepartmentService', '部门管理服务', 'DepartmentService_Delete', '/admin/v1/departments/{id}', 'DELETE'),
       (12, '2025-06-16 04:05:27.800994+00', null, null, null, null, '查询部门详情', 'DepartmentService', '部门管理服务', 'DepartmentService_Get', '/admin/v1/departments/{id}', 'GET'),
       (13, '2025-06-16 04:05:27.802289+00', null, null, null, null, '查询通知消息列表', 'NotificationMessageService', '通知消息管理服务', 'NotificationMessageService_List', '/admin/v1/notifications', 'GET'),
       (14, '2025-06-16 04:05:27.803563+00', null, null, null, null, '创建通知消息', 'NotificationMessageService', '通知消息管理服务', 'NotificationMessageService_Create', '/admin/v1/notifications', 'POST'),
       (15, '2025-06-16 04:05:27.804881+00', null, null, null, null, '查询通知消息详情', 'NotificationMessageService', '通知消息管理服务', 'NotificationMessageService_Get', '/admin/v1/notifications/{id}', 'GET'),
       (16, '2025-06-16 04:05:27.806202+00', null, null, null, null, '删除通知消息', 'NotificationMessageService', '通知消息管理服务', 'NotificationMessageService_Delete', '/admin/v1/notifications/{id}', 'DELETE'),
       (17, '2025-06-16 04:05:27.807471+00', null, null, null, null, '查询菜单列表', 'MenuService', '后台菜单管理服务', 'MenuService_List', '/admin/v1/menus', 'GET'),
       (18, '2025-06-16 04:05:27.808787+00', null, null, null, null, '创建菜单', 'MenuService', '后台菜单管理服务', 'MenuService_Create', '/admin/v1/menus', 'POST'),
       (19, '2025-06-16 04:05:27.810086+00', null, null, null, null, '更新调度任务', 'TaskService', '调度任务管理服务', 'TaskService_Update', '/admin/v1/tasks/{data.id}', 'PUT'),
       (20, '2025-06-16 04:05:27.811351+00', null, null, null, null, '修改用户密码', 'AuthenticationService', '用户后台登录认证服务', 'AuthenticationService_ChangePassword', '/admin/v1/change_password', 'POST'),
       (21, '2025-06-16 04:05:27.812648+00', null, null, null, null, '删除组织', 'OrganizationService', '组织管理服务', 'OrganizationService_Delete', '/admin/v1/organizations/{id}', 'DELETE'),
       (22, '2025-06-16 04:05:27.813905+00', null, null, null, null, '查询组织详情', 'OrganizationService', '组织管理服务', 'OrganizationService_Get', '/admin/v1/organizations/{id}', 'GET'),
       (23, '2025-06-16 04:05:27.815156+00', null, null, null, null, '查询部门列表', 'DepartmentService', '部门管理服务', 'DepartmentService_List', '/admin/v1/departments', 'GET'),
       (24, '2025-06-16 04:05:27.816583+00', null, null, null, null, '创建部门', 'DepartmentService', '部门管理服务', 'DepartmentService_Create', '/admin/v1/departments', 'POST'),
       (25, '2025-06-16 04:05:27.817828+00', null, null, null, null, '更新用户资料', 'UserProfileService', '用户个人资料服务', 'UserProfileService_UpdateUser', '/admin/v1/me', 'PUT'),
       (26, '2025-06-16 04:05:27.819234+00', null, null, null, null, '获取用户资料', 'UserProfileService', '用户个人资料服务', 'UserProfileService_GetUser', '/admin/v1/me', 'GET'),
       (27, '2025-06-16 04:05:27.820528+00', null, null, null, null, '查询后台操作日志列表', 'AdminOperationLogService', '后台操作日志管理服务', 'AdminOperationLogService_List', '/admin/v1/admin_operation_logs', 'GET'),
       (28, '2025-06-16 04:05:27.821717+00', null, null, null, null, '更新字典', 'DictService', '字典管理服务', 'DictService_Update', '/admin/v1/dict/{data.id}', 'PUT'),
       (29, '2025-06-16 04:05:27.823037+00', null, null, null, null, '更新角色', 'RoleService', '角色管理服务', 'RoleService_Update', '/admin/v1/roles/{data.id}', 'PUT'),
       (30, '2025-06-16 04:05:27.824301+00', null, null, null, null, '更新部门', 'DepartmentService', '部门管理服务', 'DepartmentService_Update', '/admin/v1/departments/{data.id}', 'PUT'),
       (31, '2025-06-16 04:05:27.825654+00', null, null, null, null, '删除文件', 'FileService', '文件管理服务', 'FileService_Delete', '/admin/v1/files/{id}', 'DELETE'),
       (32, '2025-06-16 04:05:27.826925+00', null, null, null, null, '查询文件详情', 'FileService', '文件管理服务', 'FileService_Get', '/admin/v1/files/{id}', 'GET'),
       (33, '2025-06-16 04:05:27.828231+00', null, null, null, null, '查询通知消息接收者列表', 'NotificationMessageRecipientService', '通知消息接收者管理服务', 'NotificationMessageRecipientService_List', '/admin/v1/notifications:recipients', 'GET'),
       (34, '2025-06-16 04:05:27.829546+00', null, null, null, null, '创建通知消息接收者', 'NotificationMessageRecipientService', '通知消息接收者管理服务', 'NotificationMessageRecipientService_Create', '/admin/v1/notifications:recipients', 'POST'),
       (35, '2025-06-16 04:05:27.830803+00', null, null, null, null, '更新后台登录限制', 'AdminLoginRestrictionService', '后台登录限制管理服务', 'AdminLoginRestrictionService_Update', '/admin/v1/login-restrictions/{data.id}', 'PUT'),
       (36, '2025-06-16 04:05:27.832016+00', null, null, null, null, '创建租户', 'TenantService', '租户管理服务', 'TenantService_Create', '/admin/v1/tenants', 'POST'),
       (37, '2025-06-16 04:05:27.833228+00', null, null, null, null, '获取租户列表', 'TenantService', '租户管理服务', 'TenantService_List', '/admin/v1/tenants', 'GET'),
       (38, '2025-06-16 04:05:27.834444+00', null, null, null, null, '查询后台登录日志列表', 'AdminLoginLogService', '后台登录日志管理服务', 'AdminLoginLogService_List', '/admin/v1/admin_login_logs', 'GET'),
       (39, '2025-06-16 04:05:27.835713+00', null, null, null, null, '删除菜单', 'MenuService', '后台菜单管理服务', 'MenuService_Delete', '/admin/v1/menus/{id}', 'DELETE'),
       (40, '2025-06-16 04:05:27.837175+00', null, null, null, null, '查询菜单详情', 'MenuService', '后台菜单管理服务', 'MenuService_Get', '/admin/v1/menus/{id}', 'GET'),
       (41, '2025-06-16 04:05:27.838520+00', null, null, null, null, '查询职位列表', 'PositionService', '职位管理服务', 'PositionService_List', '/admin/v1/positions', 'GET'),
       (42, '2025-06-16 04:05:27.839737+00', null, null, null, null, '创建职位', 'PositionService', '职位管理服务', 'PositionService_Create', '/admin/v1/positions', 'POST'),
       (43, '2025-06-16 04:05:27.840961+00', null, null, null, null, '更新组织', 'OrganizationService', '组织管理服务', 'OrganizationService_Update', '/admin/v1/organizations/{data.id}', 'PUT'),
       (44, '2025-06-16 04:05:27.842293+00', null, null, null, null, '获取对象存储（OSS）上传用的预签名链接', 'OssService', 'OSS服务', 'OssService_OssUploadUrl', '/admin/v1/file:upload-url', 'POST'),
       (45, '2025-06-16 04:05:27.843653+00', null, null, null, null, '获取用户列表', 'UserService', '用户管理服务', 'UserService_List', '/admin/v1/users', 'GET'),
       (46, '2025-06-16 04:05:27.845092+00', null, null, null, null, '创建用户', 'UserService', '用户管理服务', 'UserService_Create', '/admin/v1/users', 'POST'),
       (93, '2025-06-16 04:05:27.906230+00', null, null, null, null, '删除调度任务', 'TaskService', '调度任务管理服务', 'TaskService_Delete', '/admin/v1/tasks/{id}', 'DELETE'),
       (47, '2025-06-16 04:05:27.846377+00', null, null, null, null, '删除通知消息接收者', 'NotificationMessageRecipientService', '通知消息接收者管理服务', 'NotificationMessageRecipientService_Delete', '/admin/v1/notifications:recipients/{id}', 'DELETE'),
       (48, '2025-06-16 04:05:27.847872+00', null, null, null, null, '查询通知消息接收者详情', 'NotificationMessageRecipientService', '通知消息接收者管理服务', 'NotificationMessageRecipientService_Get', '/admin/v1/notifications:recipients/{id}', 'GET'),
       (49, '2025-06-16 04:05:27.849225+00', null, null, null, null, 'POST方法上传文件', 'OssService', 'OSS服务', 'OssService_PostUploadFile', '/admin/v1/file:upload', 'POST'),
       (50, '2025-06-16 04:05:27.850588+00', null, null, null, null, 'PUT方法上传文件', 'OssService', 'OSS服务', 'OssService_PutUploadFile', '/admin/v1/file:upload', 'PUT'),
       (51, '2025-06-16 04:05:27.851907+00', null, null, null, null, '删除通知消息分类', 'NotificationMessageCategoryService', '通知消息分类管理服务', 'NotificationMessageCategoryService_Delete', '/admin/v1/notifications:categories/{id}', 'DELETE'),
       (52, '2025-06-16 04:05:27.853163+00', null, null, null, null, '查询通知消息分类详情', 'NotificationMessageCategoryService', '通知消息分类管理服务', 'NotificationMessageCategoryService_Get', '/admin/v1/notifications:categories/{id}', 'GET'),
       (53, '2025-06-16 04:05:27.854430+00', null, null, null, null, '更新菜单', 'MenuService', '后台菜单管理服务', 'MenuService_Update', '/admin/v1/menus/{data.id}', 'PUT'),
       (54, '2025-06-16 04:05:27.855710+00', null, null, null, null, '登出', 'AuthenticationService', '用户后台登录认证服务', 'AuthenticationService_Logout', '/admin/v1/logout', 'POST'),
       (55, '2025-06-16 04:05:27.856956+00', null, null, null, null, '重启所有的调度任务', 'TaskService', '调度任务管理服务', 'TaskService_RestartAllTask', '/admin/v1/tasks:restart', 'POST'),
       (56, '2025-06-16 04:05:27.858318+00', null, null, null, null, '创建API资源', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_Create', '/admin/v1/api-resources', 'POST'),
       (57, '2025-06-16 04:05:27.859642+00', null, null, null, null, '查询API资源列表', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_List', '/admin/v1/api-resources', 'GET'),
       (58, '2025-06-16 04:05:27.860866+00', null, null, null, null, '查询组织列表', 'OrganizationService', '组织管理服务', 'OrganizationService_List', '/admin/v1/organizations', 'GET'),
       (59, '2025-06-16 04:05:27.862208+00', null, null, null, null, '创建组织', 'OrganizationService', '组织管理服务', 'OrganizationService_Create', '/admin/v1/organizations', 'POST'),
       (60, '2025-06-16 04:05:27.863459+00', null, null, null, null, '查询私信消息列表', 'PrivateMessageService', '私信消息管理服务', 'PrivateMessageService_List', '/admin/v1/private_messages', 'GET'),
       (61, '2025-06-16 04:05:27.864673+00', null, null, null, null, '创建私信消息', 'PrivateMessageService', '私信消息管理服务', 'PrivateMessageService_Create', '/admin/v1/private_messages', 'POST'),
       (62, '2025-06-16 04:05:27.866034+00', null, null, null, null, '查询后台登录日志详情', 'AdminLoginLogService', '后台登录日志管理服务', 'AdminLoginLogService_Get', '/admin/v1/admin_login_logs/{id}', 'GET'),
       (63, '2025-06-16 04:05:27.867244+00', null, null, null, null, '删除API资源', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_Delete', '/admin/v1/api-resources/{id}', 'DELETE'),
       (64, '2025-06-16 04:05:27.868401+00', null, null, null, null, '查询API资源详情', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_Get', '/admin/v1/api-resources/{id}', 'GET'),
       (65, '2025-06-16 04:05:27.869602+00', null, null, null, null, '删除角色', 'RoleService', '角色管理服务', 'RoleService_Delete', '/admin/v1/roles/{id}', 'DELETE'),
       (66, '2025-06-16 04:05:27.870969+00', null, null, null, null, '查询角色详情', 'RoleService', '角色管理服务', 'RoleService_Get', '/admin/v1/roles/{id}', 'GET'),
       (67, '2025-06-16 04:05:27.872362+00', null, null, null, null, '查询权限码列表', 'RouterService', '网站后台动态路由服务', 'RouterService_ListPermissionCode', '/admin/v1/perm-codes', 'GET'),
       (68, '2025-06-16 04:05:27.873716+00', null, null, null, null, '查询角色列表', 'RoleService', '角色管理服务', 'RoleService_List', '/admin/v1/roles', 'GET'),
       (69, '2025-06-16 04:05:27.875002+00', null, null, null, null, '创建角色', 'RoleService', '角色管理服务', 'RoleService_Create', '/admin/v1/roles', 'POST'),
       (70, '2025-06-16 04:05:27.876290+00', null, null, null, null, '删除职位', 'PositionService', '职位管理服务', 'PositionService_Delete', '/admin/v1/positions/{id}', 'DELETE'),
       (71, '2025-06-16 04:05:27.877518+00', null, null, null, null, '查询职位详情', 'PositionService', '职位管理服务', 'PositionService_Get', '/admin/v1/positions/{id}', 'GET'),
       (72, '2025-06-16 04:05:27.878825+00', null, null, null, null, '查询后台登录限制列表', 'AdminLoginRestrictionService', '后台登录限制管理服务', 'AdminLoginRestrictionService_List', '/admin/v1/login-restrictions', 'GET'),
       (73, '2025-06-16 04:05:27.880061+00', null, null, null, null, '创建后台登录限制', 'AdminLoginRestrictionService', '后台登录限制管理服务', 'AdminLoginRestrictionService_Create', '/admin/v1/login-restrictions', 'POST'),
       (74, '2025-06-16 04:05:27.881353+00', null, null, null, null, '刷新认证令牌', 'AuthenticationService', '用户后台登录认证服务', 'AuthenticationService_RefreshToken', '/admin/v1/refresh_token', 'POST'),
       (75, '2025-06-16 04:05:27.883515+00', null, null, null, null, '更新通知消息', 'NotificationMessageService', '通知消息管理服务', 'NotificationMessageService_Update', '/admin/v1/notifications/{data.id}', 'PUT'),
       (76, '2025-06-16 04:05:27.884734+00', null, null, null, null, '更新职位', 'PositionService', '职位管理服务', 'PositionService_Update', '/admin/v1/positions/{data.id}', 'PUT'),
       (77, '2025-06-16 04:05:27.886016+00', null, null, null, null, '查询调度任务列表', 'TaskService', '调度任务管理服务', 'TaskService_List', '/admin/v1/tasks', 'GET'),
       (78, '2025-06-16 04:05:27.887253+00', null, null, null, null, '创建调度任务', 'TaskService', '调度任务管理服务', 'TaskService_Create', '/admin/v1/tasks', 'POST'),
       (79, '2025-06-16 04:05:27.888507+00', null, null, null, null, '同步API资源', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_SyncApiResources', '/admin/v1/api-resources/sync', 'POST'),
       (80, '2025-06-16 04:05:27.889829+00', null, null, null, null, '更新租户', 'TenantService', '租户管理服务', 'TenantService_Update', '/admin/v1/tenants/{data.id}', 'PUT'),
       (81, '2025-06-16 04:05:27.891045+00', null, null, null, null, '查询文件列表', 'FileService', '文件管理服务', 'FileService_List', '/admin/v1/files', 'GET'),
       (82, '2025-06-16 04:05:27.892229+00', null, null, null, null, '创建文件', 'FileService', '文件管理服务', 'FileService_Create', '/admin/v1/files', 'POST'),
       (83, '2025-06-16 04:05:27.893676+00', null, null, null, null, '删除租户', 'TenantService', '租户管理服务', 'TenantService_Delete', '/admin/v1/tenants/{id}', 'DELETE'),
       (84, '2025-06-16 04:05:27.895012+00', null, null, null, null, '获取租户数据', 'TenantService', '租户管理服务', 'TenantService_Get', '/admin/v1/tenants/{id}', 'GET'),
       (85, '2025-06-16 04:05:27.896312+00', null, null, null, null, '更新通知消息接收者', 'NotificationMessageRecipientService', '通知消息接收者管理服务', 'NotificationMessageRecipientService_Update', '/admin/v1/notifications:recipients/{data.id}', 'PUT'),
       (86, '2025-06-16 04:05:27.897540+00', null, null, null, null, '停止所有的调度任务', 'TaskService', '调度任务管理服务', 'TaskService_StopAllTask', '/admin/v1/tasks:stop', 'POST'),
       (87, '2025-06-16 04:05:27.898773+00', null, null, null, null, '删除私信消息', 'PrivateMessageService', '私信消息管理服务', 'PrivateMessageService_Delete', '/admin/v1/private_messages/{id}', 'DELETE'),
       (88, '2025-06-16 04:05:27.899954+00', null, null, null, null, '查询私信消息详情', 'PrivateMessageService', '私信消息管理服务', 'PrivateMessageService_Get', '/admin/v1/private_messages/{id}', 'GET'),
       (89, '2025-06-16 04:05:27.901241+00', null, null, null, null, '查询路由列表', 'RouterService', '网站后台动态路由服务', 'RouterService_ListRoute', '/admin/v1/routes', 'GET'),
       (90, '2025-06-16 04:05:27.902479+00', null, null, null, null, '删除字典', 'DictService', '字典管理服务', 'DictService_Delete', '/admin/v1/dict/{id}', 'DELETE'),
       (91, '2025-06-16 04:05:27.903830+00', null, null, null, null, '查询字典详情', 'DictService', '字典管理服务', 'DictService_Get', '/admin/v1/dict/{id}', 'GET'),
       (92, '2025-06-16 04:05:27.905041+00', null, null, null, null, '更新通知消息分类', 'NotificationMessageCategoryService', '通知消息分类管理服务', 'NotificationMessageCategoryService_Update', '/admin/v1/notifications:categories/{data.id}', 'PUT'),
       (94, '2025-06-16 04:05:27.907450+00', null, null, null, null, '查询调度任务详情', 'TaskService', '调度任务管理服务', 'TaskService_Get', '/admin/v1/tasks/{id}', 'GET'),
       (95, '2025-06-16 04:05:27.908825+00', null, null, null, null, '删除后台登录限制', 'AdminLoginRestrictionService', '后台登录限制管理服务', 'AdminLoginRestrictionService_Delete', '/admin/v1/login-restrictions/{id}', 'DELETE'),
       (96, '2025-06-16 04:05:27.910018+00', null, null, null, null, '查询后台登录限制详情', 'AdminLoginRestrictionService', '后台登录限制管理服务', 'AdminLoginRestrictionService_Get', '/admin/v1/login-restrictions/{id}', 'GET'),
       (97, '2025-06-16 04:05:27.911250+00', null, null, null, null, '查询路由数据', 'ApiResourceService', 'API资源管理服务', 'ApiResourceService_GetWalkRouteData', '/admin/v1/api-resources/walk-route', 'GET'),
       (98, '2025-06-16 04:05:27.912576+00', null, null, null, null, '更新用户', 'UserService', '用户管理服务', 'UserService_Update', '/admin/v1/users/{data.id}', 'PUT'),
       (99, '2025-06-16 04:05:27.913933+00', null, null, null, null, '更新私信消息', 'PrivateMessageService', '私信消息管理服务', 'PrivateMessageService_Update', '/admin/v1/private_messages/{data.id}', 'PUT'),
       (100, '2025-06-16 04:05:27.915165+00', null, null, null, null, '查询字典列表', 'DictService', '字典管理服务', 'DictService_List', '/admin/v1/dict', 'GET'),
       (101, '2025-06-16 04:05:27.916423+00', null, null, null, null, '创建字典', 'DictService', '字典管理服务', 'DictService_Create', '/admin/v1/dict', 'POST'),
       (102, '2025-06-16 04:05:27.917577+00', null, null, null, null, '更新文件', 'FileService', '文件管理服务', 'FileService_Update', '/admin/v1/files/{data.id}', 'PUT');
