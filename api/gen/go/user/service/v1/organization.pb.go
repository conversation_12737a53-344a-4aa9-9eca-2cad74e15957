// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/organization.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 组织
type Organization struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                    // 组织ID
	Name          *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`                                 // 组织名称
	SortId        *int32                 `protobuf:"varint,3,opt,name=sort_id,json=sortId,proto3,oneof" json:"sort_id,omitempty"`              // 排序编号
	Status        *string                `protobuf:"bytes,4,opt,name=status,proto3,oneof" json:"status,omitempty"`                             // 状态
	Remark        *string                `protobuf:"bytes,5,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                             // 备注
	ParentId      *uint32                `protobuf:"varint,50,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`       // 父节点ID
	Children      []*Organization        `protobuf:"bytes,51,rep,name=children,proto3" json:"children,omitempty"`                              // 子节点树
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`      // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`      // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"` // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"` // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"` // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Organization) Reset() {
	*x = Organization{}
	mi := &file_user_service_v1_organization_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Organization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Organization) ProtoMessage() {}

func (x *Organization) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Organization.ProtoReflect.Descriptor instead.
func (*Organization) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{0}
}

func (x *Organization) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Organization) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Organization) GetSortId() int32 {
	if x != nil && x.SortId != nil {
		return *x.SortId
	}
	return 0
}

func (x *Organization) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *Organization) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Organization) GetParentId() uint32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *Organization) GetChildren() []*Organization {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Organization) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Organization) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Organization) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Organization) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Organization) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 组织列表 - 答复
type ListOrganizationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Organization        `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrganizationResponse) Reset() {
	*x = ListOrganizationResponse{}
	mi := &file_user_service_v1_organization_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrganizationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrganizationResponse) ProtoMessage() {}

func (x *ListOrganizationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrganizationResponse.ProtoReflect.Descriptor instead.
func (*ListOrganizationResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{1}
}

func (x *ListOrganizationResponse) GetItems() []*Organization {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListOrganizationResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 组织数据 - 请求
type GetOrganizationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrganizationRequest) Reset() {
	*x = GetOrganizationRequest{}
	mi := &file_user_service_v1_organization_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrganizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrganizationRequest) ProtoMessage() {}

func (x *GetOrganizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrganizationRequest.ProtoReflect.Descriptor instead.
func (*GetOrganizationRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{2}
}

func (x *GetOrganizationRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建组织 - 请求
type CreateOrganizationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Organization          `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrganizationRequest) Reset() {
	*x = CreateOrganizationRequest{}
	mi := &file_user_service_v1_organization_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrganizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrganizationRequest) ProtoMessage() {}

func (x *CreateOrganizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrganizationRequest.ProtoReflect.Descriptor instead.
func (*CreateOrganizationRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{3}
}

func (x *CreateOrganizationRequest) GetData() *Organization {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新组织 - 请求
type UpdateOrganizationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Organization          `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrganizationRequest) Reset() {
	*x = UpdateOrganizationRequest{}
	mi := &file_user_service_v1_organization_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrganizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrganizationRequest) ProtoMessage() {}

func (x *UpdateOrganizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrganizationRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrganizationRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateOrganizationRequest) GetData() *Organization {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateOrganizationRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateOrganizationRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除组织 - 请求
type DeleteOrganizationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteOrganizationRequest) Reset() {
	*x = DeleteOrganizationRequest{}
	mi := &file_user_service_v1_organization_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteOrganizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrganizationRequest) ProtoMessage() {}

func (x *DeleteOrganizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrganizationRequest.ProtoReflect.Descriptor instead.
func (*DeleteOrganizationRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteOrganizationRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BatchCreateOrganizationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Organization        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateOrganizationsRequest) Reset() {
	*x = BatchCreateOrganizationsRequest{}
	mi := &file_user_service_v1_organization_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateOrganizationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateOrganizationsRequest) ProtoMessage() {}

func (x *BatchCreateOrganizationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateOrganizationsRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateOrganizationsRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCreateOrganizationsRequest) GetData() []*Organization {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchCreateOrganizationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Organization        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateOrganizationsResponse) Reset() {
	*x = BatchCreateOrganizationsResponse{}
	mi := &file_user_service_v1_organization_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateOrganizationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateOrganizationsResponse) ProtoMessage() {}

func (x *BatchCreateOrganizationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_organization_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateOrganizationsResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateOrganizationsResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_organization_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCreateOrganizationsResponse) GetData() []*Organization {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_user_service_v1_organization_proto protoreflect.FileDescriptor

const file_user_service_v1_organization_proto_rawDesc = "" +
	"\n" +
	"\"user/service/v1/organization.proto\x12\x0fuser.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xfd\x06\n" +
	"\fOrganization\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b组织IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\x04name\x18\x02 \x01(\tB\x12\xbaG\x0f\x92\x02\f组织名称H\x01R\x04name\x88\x01\x01\x120\n" +
	"\asort_id\x18\x03 \x01(\x05B\x12\xbaG\x0f\x92\x02\f排序编号H\x02R\x06sortId\x88\x01\x01\x12?\n" +
	"\x06status\x18\x04 \x01(\tB\"\xbaG\x1f\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\x06状态H\x03R\x06status\x88\x01\x01\x12)\n" +
	"\x06remark\x18\x05 \x01(\tB\f\xbaG\t\x92\x02\x06备注H\x04R\x06remark\x88\x01\x01\x123\n" +
	"\tparent_id\x182 \x01(\rB\x11\xbaG\x0e\x92\x02\v父节点IDH\x05R\bparentId\x88\x01\x01\x12M\n" +
	"\bchildren\x183 \x03(\v2\x1d.user.service.v1.OrganizationB\x12\xbaG\x0f\x92\x02\f子节点树R\bchildren\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\x06R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\aR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\bR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\tR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\n" +
	"R\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_nameB\n" +
	"\n" +
	"\b_sort_idB\t\n" +
	"\a_statusB\t\n" +
	"\a_remarkB\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"e\n" +
	"\x18ListOrganizationResponse\x123\n" +
	"\x05items\x18\x01 \x03(\v2\x1d.user.service.v1.OrganizationR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"(\n" +
	"\x16GetOrganizationRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"N\n" +
	"\x19CreateOrganizationRequest\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.user.service.v1.OrganizationR\x04data\"\x8c\x03\n" +
	"\x19UpdateOrganizationRequest\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.user.service.v1.OrganizationR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"+\n" +
	"\x19DeleteOrganizationRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"T\n" +
	"\x1fBatchCreateOrganizationsRequest\x121\n" +
	"\x04data\x18\x01 \x03(\v2\x1d.user.service.v1.OrganizationR\x04data\"U\n" +
	" BatchCreateOrganizationsResponse\x121\n" +
	"\x04data\x18\x01 \x03(\v2\x1d.user.service.v1.OrganizationR\x04data2\x9c\x04\n" +
	"\x13OrganizationService\x12N\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a).user.service.v1.ListOrganizationResponse\"\x00\x12O\n" +
	"\x03Get\x12'.user.service.v1.GetOrganizationRequest\x1a\x1d.user.service.v1.Organization\"\x00\x12N\n" +
	"\x06Create\x12*.user.service.v1.CreateOrganizationRequest\x1a\x16.google.protobuf.Empty\"\x00\x12N\n" +
	"\x06Update\x12*.user.service.v1.UpdateOrganizationRequest\x1a\x16.google.protobuf.Empty\"\x00\x12N\n" +
	"\x06Delete\x12*.user.service.v1.DeleteOrganizationRequest\x1a\x16.google.protobuf.Empty\"\x00\x12t\n" +
	"\vBatchCreate\x120.user.service.v1.BatchCreateOrganizationsRequest\x1a1.user.service.v1.BatchCreateOrganizationsResponse\"\x00B\xb9\x01\n" +
	"\x13com.user.service.v1B\x11OrganizationProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_organization_proto_rawDescOnce sync.Once
	file_user_service_v1_organization_proto_rawDescData []byte
)

func file_user_service_v1_organization_proto_rawDescGZIP() []byte {
	file_user_service_v1_organization_proto_rawDescOnce.Do(func() {
		file_user_service_v1_organization_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_organization_proto_rawDesc), len(file_user_service_v1_organization_proto_rawDesc)))
	})
	return file_user_service_v1_organization_proto_rawDescData
}

var file_user_service_v1_organization_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_user_service_v1_organization_proto_goTypes = []any{
	(*Organization)(nil),                     // 0: user.service.v1.Organization
	(*ListOrganizationResponse)(nil),         // 1: user.service.v1.ListOrganizationResponse
	(*GetOrganizationRequest)(nil),           // 2: user.service.v1.GetOrganizationRequest
	(*CreateOrganizationRequest)(nil),        // 3: user.service.v1.CreateOrganizationRequest
	(*UpdateOrganizationRequest)(nil),        // 4: user.service.v1.UpdateOrganizationRequest
	(*DeleteOrganizationRequest)(nil),        // 5: user.service.v1.DeleteOrganizationRequest
	(*BatchCreateOrganizationsRequest)(nil),  // 6: user.service.v1.BatchCreateOrganizationsRequest
	(*BatchCreateOrganizationsResponse)(nil), // 7: user.service.v1.BatchCreateOrganizationsResponse
	(*timestamppb.Timestamp)(nil),            // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),            // 9: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),                 // 10: pagination.PagingRequest
	(*emptypb.Empty)(nil),                    // 11: google.protobuf.Empty
}
var file_user_service_v1_organization_proto_depIdxs = []int32{
	0,  // 0: user.service.v1.Organization.children:type_name -> user.service.v1.Organization
	8,  // 1: user.service.v1.Organization.create_time:type_name -> google.protobuf.Timestamp
	8,  // 2: user.service.v1.Organization.update_time:type_name -> google.protobuf.Timestamp
	8,  // 3: user.service.v1.Organization.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: user.service.v1.ListOrganizationResponse.items:type_name -> user.service.v1.Organization
	0,  // 5: user.service.v1.CreateOrganizationRequest.data:type_name -> user.service.v1.Organization
	0,  // 6: user.service.v1.UpdateOrganizationRequest.data:type_name -> user.service.v1.Organization
	9,  // 7: user.service.v1.UpdateOrganizationRequest.update_mask:type_name -> google.protobuf.FieldMask
	0,  // 8: user.service.v1.BatchCreateOrganizationsRequest.data:type_name -> user.service.v1.Organization
	0,  // 9: user.service.v1.BatchCreateOrganizationsResponse.data:type_name -> user.service.v1.Organization
	10, // 10: user.service.v1.OrganizationService.List:input_type -> pagination.PagingRequest
	2,  // 11: user.service.v1.OrganizationService.Get:input_type -> user.service.v1.GetOrganizationRequest
	3,  // 12: user.service.v1.OrganizationService.Create:input_type -> user.service.v1.CreateOrganizationRequest
	4,  // 13: user.service.v1.OrganizationService.Update:input_type -> user.service.v1.UpdateOrganizationRequest
	5,  // 14: user.service.v1.OrganizationService.Delete:input_type -> user.service.v1.DeleteOrganizationRequest
	6,  // 15: user.service.v1.OrganizationService.BatchCreate:input_type -> user.service.v1.BatchCreateOrganizationsRequest
	1,  // 16: user.service.v1.OrganizationService.List:output_type -> user.service.v1.ListOrganizationResponse
	0,  // 17: user.service.v1.OrganizationService.Get:output_type -> user.service.v1.Organization
	11, // 18: user.service.v1.OrganizationService.Create:output_type -> google.protobuf.Empty
	11, // 19: user.service.v1.OrganizationService.Update:output_type -> google.protobuf.Empty
	11, // 20: user.service.v1.OrganizationService.Delete:output_type -> google.protobuf.Empty
	7,  // 21: user.service.v1.OrganizationService.BatchCreate:output_type -> user.service.v1.BatchCreateOrganizationsResponse
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_user_service_v1_organization_proto_init() }
func file_user_service_v1_organization_proto_init() {
	if File_user_service_v1_organization_proto != nil {
		return
	}
	file_user_service_v1_organization_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_service_v1_organization_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_organization_proto_rawDesc), len(file_user_service_v1_organization_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_service_v1_organization_proto_goTypes,
		DependencyIndexes: file_user_service_v1_organization_proto_depIdxs,
		MessageInfos:      file_user_service_v1_organization_proto_msgTypes,
	}.Build()
	File_user_service_v1_organization_proto = out.File
	file_user_service_v1_organization_proto_goTypes = nil
	file_user_service_v1_organization_proto_depIdxs = nil
}
