// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user/service/v1/department.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Department with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Department) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Department with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DepartmentMultiError, or
// nil if none found.
func (m *Department) ValidateAll() error {
	return m.validate(true)
}

func (m *Department) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DepartmentValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.OrganizationId != nil {
		// no validation rules for OrganizationId
	}

	if m.OrganizationName != nil {
		// no validation rules for OrganizationName
	}

	if m.SortId != nil {
		// no validation rules for SortId
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.Remark != nil {
		// no validation rules for Remark
	}

	if m.ParentId != nil {
		// no validation rules for ParentId
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DepartmentValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DepartmentValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DepartmentValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DepartmentValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DepartmentMultiError(errors)
	}

	return nil
}

// DepartmentMultiError is an error wrapping multiple validation errors
// returned by Department.ValidateAll() if the designated constraints aren't met.
type DepartmentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepartmentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepartmentMultiError) AllErrors() []error { return m }

// DepartmentValidationError is the validation error returned by
// Department.Validate if the designated constraints aren't met.
type DepartmentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepartmentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepartmentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepartmentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepartmentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepartmentValidationError) ErrorName() string { return "DepartmentValidationError" }

// Error satisfies the builtin error interface
func (e DepartmentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepartment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepartmentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepartmentValidationError{}

// Validate checks the field values on ListDepartmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDepartmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDepartmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDepartmentResponseMultiError, or nil if none found.
func (m *ListDepartmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDepartmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDepartmentResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDepartmentResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDepartmentResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListDepartmentResponseMultiError(errors)
	}

	return nil
}

// ListDepartmentResponseMultiError is an error wrapping multiple validation
// errors returned by ListDepartmentResponse.ValidateAll() if the designated
// constraints aren't met.
type ListDepartmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDepartmentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDepartmentResponseMultiError) AllErrors() []error { return m }

// ListDepartmentResponseValidationError is the validation error returned by
// ListDepartmentResponse.Validate if the designated constraints aren't met.
type ListDepartmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDepartmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDepartmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDepartmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDepartmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDepartmentResponseValidationError) ErrorName() string {
	return "ListDepartmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListDepartmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDepartmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDepartmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDepartmentResponseValidationError{}

// Validate checks the field values on GetDepartmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDepartmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDepartmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDepartmentRequestMultiError, or nil if none found.
func (m *GetDepartmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDepartmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetDepartmentRequestMultiError(errors)
	}

	return nil
}

// GetDepartmentRequestMultiError is an error wrapping multiple validation
// errors returned by GetDepartmentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDepartmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDepartmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDepartmentRequestMultiError) AllErrors() []error { return m }

// GetDepartmentRequestValidationError is the validation error returned by
// GetDepartmentRequest.Validate if the designated constraints aren't met.
type GetDepartmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDepartmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDepartmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDepartmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDepartmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDepartmentRequestValidationError) ErrorName() string {
	return "GetDepartmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDepartmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDepartmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDepartmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDepartmentRequestValidationError{}

// Validate checks the field values on CreateDepartmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDepartmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDepartmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDepartmentRequestMultiError, or nil if none found.
func (m *CreateDepartmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDepartmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDepartmentRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDepartmentRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDepartmentRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDepartmentRequestMultiError(errors)
	}

	return nil
}

// CreateDepartmentRequestMultiError is an error wrapping multiple validation
// errors returned by CreateDepartmentRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateDepartmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDepartmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDepartmentRequestMultiError) AllErrors() []error { return m }

// CreateDepartmentRequestValidationError is the validation error returned by
// CreateDepartmentRequest.Validate if the designated constraints aren't met.
type CreateDepartmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDepartmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDepartmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDepartmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDepartmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDepartmentRequestValidationError) ErrorName() string {
	return "CreateDepartmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDepartmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDepartmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDepartmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDepartmentRequestValidationError{}

// Validate checks the field values on UpdateDepartmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateDepartmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDepartmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDepartmentRequestMultiError, or nil if none found.
func (m *UpdateDepartmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDepartmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDepartmentRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDepartmentRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDepartmentRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDepartmentRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDepartmentRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDepartmentRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateDepartmentRequestMultiError(errors)
	}

	return nil
}

// UpdateDepartmentRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateDepartmentRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateDepartmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDepartmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDepartmentRequestMultiError) AllErrors() []error { return m }

// UpdateDepartmentRequestValidationError is the validation error returned by
// UpdateDepartmentRequest.Validate if the designated constraints aren't met.
type UpdateDepartmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDepartmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDepartmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDepartmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDepartmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDepartmentRequestValidationError) ErrorName() string {
	return "UpdateDepartmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDepartmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDepartmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDepartmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDepartmentRequestValidationError{}

// Validate checks the field values on DeleteDepartmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDepartmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDepartmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDepartmentRequestMultiError, or nil if none found.
func (m *DeleteDepartmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDepartmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteDepartmentRequestMultiError(errors)
	}

	return nil
}

// DeleteDepartmentRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteDepartmentRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteDepartmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDepartmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDepartmentRequestMultiError) AllErrors() []error { return m }

// DeleteDepartmentRequestValidationError is the validation error returned by
// DeleteDepartmentRequest.Validate if the designated constraints aren't met.
type DeleteDepartmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDepartmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDepartmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDepartmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDepartmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDepartmentRequestValidationError) ErrorName() string {
	return "DeleteDepartmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDepartmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDepartmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDepartmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDepartmentRequestValidationError{}

// Validate checks the field values on BatchCreateDepartmentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreateDepartmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreateDepartmentsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchCreateDepartmentsRequestMultiError, or nil if none found.
func (m *BatchCreateDepartmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreateDepartmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreateDepartmentsRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreateDepartmentsRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreateDepartmentsRequestValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchCreateDepartmentsRequestMultiError(errors)
	}

	return nil
}

// BatchCreateDepartmentsRequestMultiError is an error wrapping multiple
// validation errors returned by BatchCreateDepartmentsRequest.ValidateAll()
// if the designated constraints aren't met.
type BatchCreateDepartmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreateDepartmentsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreateDepartmentsRequestMultiError) AllErrors() []error { return m }

// BatchCreateDepartmentsRequestValidationError is the validation error
// returned by BatchCreateDepartmentsRequest.Validate if the designated
// constraints aren't met.
type BatchCreateDepartmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreateDepartmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreateDepartmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreateDepartmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreateDepartmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreateDepartmentsRequestValidationError) ErrorName() string {
	return "BatchCreateDepartmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreateDepartmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreateDepartmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreateDepartmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreateDepartmentsRequestValidationError{}

// Validate checks the field values on BatchCreateDepartmentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreateDepartmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreateDepartmentsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchCreateDepartmentsResponseMultiError, or nil if none found.
func (m *BatchCreateDepartmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreateDepartmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreateDepartmentsResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreateDepartmentsResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreateDepartmentsResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchCreateDepartmentsResponseMultiError(errors)
	}

	return nil
}

// BatchCreateDepartmentsResponseMultiError is an error wrapping multiple
// validation errors returned by BatchCreateDepartmentsResponse.ValidateAll()
// if the designated constraints aren't met.
type BatchCreateDepartmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreateDepartmentsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreateDepartmentsResponseMultiError) AllErrors() []error { return m }

// BatchCreateDepartmentsResponseValidationError is the validation error
// returned by BatchCreateDepartmentsResponse.Validate if the designated
// constraints aren't met.
type BatchCreateDepartmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreateDepartmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreateDepartmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreateDepartmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreateDepartmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreateDepartmentsResponseValidationError) ErrorName() string {
	return "BatchCreateDepartmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreateDepartmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreateDepartmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreateDepartmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreateDepartmentsResponseValidationError{}
