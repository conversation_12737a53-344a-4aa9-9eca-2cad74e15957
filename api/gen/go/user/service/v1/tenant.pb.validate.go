// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user/service/v1/tenant.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Tenant with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Tenant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Tenant with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TenantMultiError, or nil if none found.
func (m *Tenant) ValidateAll() error {
	return m.validate(true)
}

func (m *Tenant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Code != nil {
		// no validation rules for Code
	}

	if m.MemberCount != nil {
		// no validation rules for MemberCount
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.Remark != nil {
		// no validation rules for Remark
	}

	if m.SubscriptionAt != nil {

		if all {
			switch v := interface{}(m.GetSubscriptionAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "SubscriptionAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "SubscriptionAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSubscriptionAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TenantValidationError{
					field:  "SubscriptionAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UnsubscribeAt != nil {

		if all {
			switch v := interface{}(m.GetUnsubscribeAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "UnsubscribeAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "UnsubscribeAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUnsubscribeAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TenantValidationError{
					field:  "UnsubscribeAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TenantValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TenantValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TenantValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TenantValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TenantMultiError(errors)
	}

	return nil
}

// TenantMultiError is an error wrapping multiple validation errors returned by
// Tenant.ValidateAll() if the designated constraints aren't met.
type TenantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TenantMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TenantMultiError) AllErrors() []error { return m }

// TenantValidationError is the validation error returned by Tenant.Validate if
// the designated constraints aren't met.
type TenantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TenantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TenantValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TenantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TenantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TenantValidationError) ErrorName() string { return "TenantValidationError" }

// Error satisfies the builtin error interface
func (e TenantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTenant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TenantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TenantValidationError{}

// Validate checks the field values on ListTenantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTenantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTenantResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTenantResponseMultiError, or nil if none found.
func (m *ListTenantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTenantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTenantResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTenantResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTenantResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListTenantResponseMultiError(errors)
	}

	return nil
}

// ListTenantResponseMultiError is an error wrapping multiple validation errors
// returned by ListTenantResponse.ValidateAll() if the designated constraints
// aren't met.
type ListTenantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTenantResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTenantResponseMultiError) AllErrors() []error { return m }

// ListTenantResponseValidationError is the validation error returned by
// ListTenantResponse.Validate if the designated constraints aren't met.
type ListTenantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTenantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTenantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTenantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTenantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTenantResponseValidationError) ErrorName() string {
	return "ListTenantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTenantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTenantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTenantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTenantResponseValidationError{}

// Validate checks the field values on GetTenantRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTenantRequestMultiError, or nil if none found.
func (m *GetTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetTenantRequestMultiError(errors)
	}

	return nil
}

// GetTenantRequestMultiError is an error wrapping multiple validation errors
// returned by GetTenantRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTenantRequestMultiError) AllErrors() []error { return m }

// GetTenantRequestValidationError is the validation error returned by
// GetTenantRequest.Validate if the designated constraints aren't met.
type GetTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTenantRequestValidationError) ErrorName() string { return "GetTenantRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTenantRequestValidationError{}

// Validate checks the field values on CreateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTenantRequestMultiError, or nil if none found.
func (m *CreateTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTenantRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTenantRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTenantRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTenantRequestMultiError(errors)
	}

	return nil
}

// CreateTenantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTenantRequestMultiError) AllErrors() []error { return m }

// CreateTenantRequestValidationError is the validation error returned by
// CreateTenantRequest.Validate if the designated constraints aren't met.
type CreateTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTenantRequestValidationError) ErrorName() string {
	return "CreateTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTenantRequestValidationError{}

// Validate checks the field values on UpdateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTenantRequestMultiError, or nil if none found.
func (m *UpdateTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTenantRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTenantRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTenantRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTenantRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTenantRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTenantRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateTenantRequestMultiError(errors)
	}

	return nil
}

// UpdateTenantRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTenantRequestMultiError) AllErrors() []error { return m }

// UpdateTenantRequestValidationError is the validation error returned by
// UpdateTenantRequest.Validate if the designated constraints aren't met.
type UpdateTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTenantRequestValidationError) ErrorName() string {
	return "UpdateTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTenantRequestValidationError{}

// Validate checks the field values on DeleteTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTenantRequestMultiError, or nil if none found.
func (m *DeleteTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteTenantRequestMultiError(errors)
	}

	return nil
}

// DeleteTenantRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTenantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTenantRequestMultiError) AllErrors() []error { return m }

// DeleteTenantRequestValidationError is the validation error returned by
// DeleteTenantRequest.Validate if the designated constraints aren't met.
type DeleteTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTenantRequestValidationError) ErrorName() string {
	return "DeleteTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTenantRequestValidationError{}

// Validate checks the field values on BatchCreateTenantsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreateTenantsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreateTenantsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCreateTenantsRequestMultiError, or nil if none found.
func (m *BatchCreateTenantsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreateTenantsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreateTenantsRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreateTenantsRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreateTenantsRequestValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchCreateTenantsRequestMultiError(errors)
	}

	return nil
}

// BatchCreateTenantsRequestMultiError is an error wrapping multiple validation
// errors returned by BatchCreateTenantsRequest.ValidateAll() if the
// designated constraints aren't met.
type BatchCreateTenantsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreateTenantsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreateTenantsRequestMultiError) AllErrors() []error { return m }

// BatchCreateTenantsRequestValidationError is the validation error returned by
// BatchCreateTenantsRequest.Validate if the designated constraints aren't met.
type BatchCreateTenantsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreateTenantsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreateTenantsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreateTenantsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreateTenantsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreateTenantsRequestValidationError) ErrorName() string {
	return "BatchCreateTenantsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreateTenantsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreateTenantsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreateTenantsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreateTenantsRequestValidationError{}

// Validate checks the field values on BatchCreateTenantsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreateTenantsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreateTenantsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCreateTenantsResponseMultiError, or nil if none found.
func (m *BatchCreateTenantsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreateTenantsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreateTenantsResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreateTenantsResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreateTenantsResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchCreateTenantsResponseMultiError(errors)
	}

	return nil
}

// BatchCreateTenantsResponseMultiError is an error wrapping multiple
// validation errors returned by BatchCreateTenantsResponse.ValidateAll() if
// the designated constraints aren't met.
type BatchCreateTenantsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreateTenantsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreateTenantsResponseMultiError) AllErrors() []error { return m }

// BatchCreateTenantsResponseValidationError is the validation error returned
// by BatchCreateTenantsResponse.Validate if the designated constraints aren't met.
type BatchCreateTenantsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreateTenantsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreateTenantsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreateTenantsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreateTenantsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreateTenantsResponseValidationError) ErrorName() string {
	return "BatchCreateTenantsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreateTenantsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreateTenantsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreateTenantsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreateTenantsResponseValidationError{}
