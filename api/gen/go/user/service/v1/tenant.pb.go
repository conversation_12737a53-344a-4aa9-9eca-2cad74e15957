// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/tenant.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 租户
type Tenant struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                      // 租户ID
	Name           *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`                                   // 租户名称
	Code           *string                `protobuf:"bytes,3,opt,name=code,proto3,oneof" json:"code,omitempty"`                                   // 租户编码
	MemberCount    *int32                 `protobuf:"varint,4,opt,name=member_count,json=memberCount,proto3,oneof" json:"member_count,omitempty"` // 成员数量
	Status         *string                `protobuf:"bytes,5,opt,name=status,proto3,oneof" json:"status,omitempty"`
	Remark         *string                `protobuf:"bytes,6,opt,name=remark,proto3,oneof" json:"remark,omitempty"` // 备注
	SubscriptionAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=subscription_at,json=subscriptionAt,proto3,oneof" json:"subscription_at,omitempty"`
	UnsubscribeAt  *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=unsubscribe_at,json=unsubscribeAt,proto3,oneof" json:"unsubscribe_at,omitempty"`
	CreateBy       *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`      // 创建者ID
	UpdateBy       *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`      // 更新者ID
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"` // 创建时间
	UpdateTime     *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"` // 更新时间
	DeleteTime     *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"` // 删除时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Tenant) Reset() {
	*x = Tenant{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tenant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tenant) ProtoMessage() {}

func (x *Tenant) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tenant.ProtoReflect.Descriptor instead.
func (*Tenant) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{0}
}

func (x *Tenant) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Tenant) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Tenant) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *Tenant) GetMemberCount() int32 {
	if x != nil && x.MemberCount != nil {
		return *x.MemberCount
	}
	return 0
}

func (x *Tenant) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *Tenant) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Tenant) GetSubscriptionAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SubscriptionAt
	}
	return nil
}

func (x *Tenant) GetUnsubscribeAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UnsubscribeAt
	}
	return nil
}

func (x *Tenant) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Tenant) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Tenant) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Tenant) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Tenant) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 租户列表 - 答复
type ListTenantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Tenant              `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTenantResponse) Reset() {
	*x = ListTenantResponse{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTenantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantResponse) ProtoMessage() {}

func (x *ListTenantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantResponse.ProtoReflect.Descriptor instead.
func (*ListTenantResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{1}
}

func (x *ListTenantResponse) GetItems() []*Tenant {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListTenantResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 租户数据 - 请求
type GetTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantRequest) Reset() {
	*x = GetTenantRequest{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantRequest) ProtoMessage() {}

func (x *GetTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantRequest.ProtoReflect.Descriptor instead.
func (*GetTenantRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{2}
}

func (x *GetTenantRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建租户 - 请求
type CreateTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Tenant                `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTenantRequest) Reset() {
	*x = CreateTenantRequest{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTenantRequest) ProtoMessage() {}

func (x *CreateTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTenantRequest.ProtoReflect.Descriptor instead.
func (*CreateTenantRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{3}
}

func (x *CreateTenantRequest) GetData() *Tenant {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新租户 -请求
type UpdateTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Tenant                `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTenantRequest) Reset() {
	*x = UpdateTenantRequest{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTenantRequest) ProtoMessage() {}

func (x *UpdateTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTenantRequest.ProtoReflect.Descriptor instead.
func (*UpdateTenantRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateTenantRequest) GetData() *Tenant {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateTenantRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateTenantRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除租户 - 请求
type DeleteTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTenantRequest) Reset() {
	*x = DeleteTenantRequest{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTenantRequest) ProtoMessage() {}

func (x *DeleteTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTenantRequest.ProtoReflect.Descriptor instead.
func (*DeleteTenantRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteTenantRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BatchCreateTenantsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Tenant              `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateTenantsRequest) Reset() {
	*x = BatchCreateTenantsRequest{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateTenantsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateTenantsRequest) ProtoMessage() {}

func (x *BatchCreateTenantsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateTenantsRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateTenantsRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCreateTenantsRequest) GetData() []*Tenant {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchCreateTenantsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Tenant              `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateTenantsResponse) Reset() {
	*x = BatchCreateTenantsResponse{}
	mi := &file_user_service_v1_tenant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateTenantsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateTenantsResponse) ProtoMessage() {}

func (x *BatchCreateTenantsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_tenant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateTenantsResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateTenantsResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_tenant_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCreateTenantsResponse) GetData() []*Tenant {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_user_service_v1_tenant_proto protoreflect.FileDescriptor

const file_user_service_v1_tenant_proto_rawDesc = "" +
	"\n" +
	"\x1cuser/service/v1/tenant.proto\x12\x0fuser.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1epagination/v1/pagination.proto\"\x8b\b\n" +
	"\x06Tenant\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b租户IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\x04name\x18\x02 \x01(\tB\x12\xbaG\x0f\x92\x02\f租户名称H\x01R\x04name\x88\x01\x01\x12+\n" +
	"\x04code\x18\x03 \x01(\tB\x12\xbaG\x0f\x92\x02\f租户编码H\x02R\x04code\x88\x01\x01\x12:\n" +
	"\fmember_count\x18\x04 \x01(\x05B\x12\xbaG\x0f\x92\x02\f成员数量H\x03R\vmemberCount\x88\x01\x01\x12?\n" +
	"\x06status\x18\x05 \x01(\tB\"\xbaG\x1f\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\x06状态H\x04R\x06status\x88\x01\x01\x12)\n" +
	"\x06remark\x18\x06 \x01(\tB\f\xbaG\t\x92\x02\x06备注H\x05R\x06remark\x88\x01\x01\x12\\\n" +
	"\x0fsubscription_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f订阅时间H\x06R\x0esubscriptionAt\x88\x01\x01\x12Z\n" +
	"\x0eunsubscribe_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f退订时间H\aR\runsubscribeAt\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\bR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\tR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\n" +
	"R\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\vR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\fR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_nameB\a\n" +
	"\x05_codeB\x0f\n" +
	"\r_member_countB\t\n" +
	"\a_statusB\t\n" +
	"\a_remarkB\x12\n" +
	"\x10_subscription_atB\x11\n" +
	"\x0f_unsubscribe_atB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"Y\n" +
	"\x12ListTenantResponse\x12-\n" +
	"\x05items\x18\x01 \x03(\v2\x17.user.service.v1.TenantR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"\"\n" +
	"\x10GetTenantRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"B\n" +
	"\x13CreateTenantRequest\x12+\n" +
	"\x04data\x18\x01 \x01(\v2\x17.user.service.v1.TenantR\x04data\"\x80\x03\n" +
	"\x13UpdateTenantRequest\x12+\n" +
	"\x04data\x18\x01 \x01(\v2\x17.user.service.v1.TenantR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"%\n" +
	"\x13DeleteTenantRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"H\n" +
	"\x19BatchCreateTenantsRequest\x12+\n" +
	"\x04data\x18\x01 \x03(\v2\x17.user.service.v1.TenantR\x04data\"I\n" +
	"\x1aBatchCreateTenantsResponse\x12+\n" +
	"\x04data\x18\x01 \x03(\v2\x17.user.service.v1.TenantR\x04data2\xe6\x03\n" +
	"\rTenantService\x12H\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a#.user.service.v1.ListTenantResponse\"\x00\x12C\n" +
	"\x03Get\x12!.user.service.v1.GetTenantRequest\x1a\x17.user.service.v1.Tenant\"\x00\x12H\n" +
	"\x06Create\x12$.user.service.v1.CreateTenantRequest\x1a\x16.google.protobuf.Empty\"\x00\x12H\n" +
	"\x06Update\x12$.user.service.v1.UpdateTenantRequest\x1a\x16.google.protobuf.Empty\"\x00\x12H\n" +
	"\x06Delete\x12$.user.service.v1.DeleteTenantRequest\x1a\x16.google.protobuf.Empty\"\x00\x12h\n" +
	"\vBatchCreate\x12*.user.service.v1.BatchCreateTenantsRequest\x1a+.user.service.v1.BatchCreateTenantsResponse\"\x00B\xb3\x01\n" +
	"\x13com.user.service.v1B\vTenantProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_tenant_proto_rawDescOnce sync.Once
	file_user_service_v1_tenant_proto_rawDescData []byte
)

func file_user_service_v1_tenant_proto_rawDescGZIP() []byte {
	file_user_service_v1_tenant_proto_rawDescOnce.Do(func() {
		file_user_service_v1_tenant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_tenant_proto_rawDesc), len(file_user_service_v1_tenant_proto_rawDesc)))
	})
	return file_user_service_v1_tenant_proto_rawDescData
}

var file_user_service_v1_tenant_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_user_service_v1_tenant_proto_goTypes = []any{
	(*Tenant)(nil),                     // 0: user.service.v1.Tenant
	(*ListTenantResponse)(nil),         // 1: user.service.v1.ListTenantResponse
	(*GetTenantRequest)(nil),           // 2: user.service.v1.GetTenantRequest
	(*CreateTenantRequest)(nil),        // 3: user.service.v1.CreateTenantRequest
	(*UpdateTenantRequest)(nil),        // 4: user.service.v1.UpdateTenantRequest
	(*DeleteTenantRequest)(nil),        // 5: user.service.v1.DeleteTenantRequest
	(*BatchCreateTenantsRequest)(nil),  // 6: user.service.v1.BatchCreateTenantsRequest
	(*BatchCreateTenantsResponse)(nil), // 7: user.service.v1.BatchCreateTenantsResponse
	(*timestamppb.Timestamp)(nil),      // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),      // 9: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),           // 10: pagination.PagingRequest
	(*emptypb.Empty)(nil),              // 11: google.protobuf.Empty
}
var file_user_service_v1_tenant_proto_depIdxs = []int32{
	8,  // 0: user.service.v1.Tenant.subscription_at:type_name -> google.protobuf.Timestamp
	8,  // 1: user.service.v1.Tenant.unsubscribe_at:type_name -> google.protobuf.Timestamp
	8,  // 2: user.service.v1.Tenant.create_time:type_name -> google.protobuf.Timestamp
	8,  // 3: user.service.v1.Tenant.update_time:type_name -> google.protobuf.Timestamp
	8,  // 4: user.service.v1.Tenant.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 5: user.service.v1.ListTenantResponse.items:type_name -> user.service.v1.Tenant
	0,  // 6: user.service.v1.CreateTenantRequest.data:type_name -> user.service.v1.Tenant
	0,  // 7: user.service.v1.UpdateTenantRequest.data:type_name -> user.service.v1.Tenant
	9,  // 8: user.service.v1.UpdateTenantRequest.update_mask:type_name -> google.protobuf.FieldMask
	0,  // 9: user.service.v1.BatchCreateTenantsRequest.data:type_name -> user.service.v1.Tenant
	0,  // 10: user.service.v1.BatchCreateTenantsResponse.data:type_name -> user.service.v1.Tenant
	10, // 11: user.service.v1.TenantService.List:input_type -> pagination.PagingRequest
	2,  // 12: user.service.v1.TenantService.Get:input_type -> user.service.v1.GetTenantRequest
	3,  // 13: user.service.v1.TenantService.Create:input_type -> user.service.v1.CreateTenantRequest
	4,  // 14: user.service.v1.TenantService.Update:input_type -> user.service.v1.UpdateTenantRequest
	5,  // 15: user.service.v1.TenantService.Delete:input_type -> user.service.v1.DeleteTenantRequest
	6,  // 16: user.service.v1.TenantService.BatchCreate:input_type -> user.service.v1.BatchCreateTenantsRequest
	1,  // 17: user.service.v1.TenantService.List:output_type -> user.service.v1.ListTenantResponse
	0,  // 18: user.service.v1.TenantService.Get:output_type -> user.service.v1.Tenant
	11, // 19: user.service.v1.TenantService.Create:output_type -> google.protobuf.Empty
	11, // 20: user.service.v1.TenantService.Update:output_type -> google.protobuf.Empty
	11, // 21: user.service.v1.TenantService.Delete:output_type -> google.protobuf.Empty
	7,  // 22: user.service.v1.TenantService.BatchCreate:output_type -> user.service.v1.BatchCreateTenantsResponse
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_user_service_v1_tenant_proto_init() }
func file_user_service_v1_tenant_proto_init() {
	if File_user_service_v1_tenant_proto != nil {
		return
	}
	file_user_service_v1_tenant_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_service_v1_tenant_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_tenant_proto_rawDesc), len(file_user_service_v1_tenant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_service_v1_tenant_proto_goTypes,
		DependencyIndexes: file_user_service_v1_tenant_proto_depIdxs,
		MessageInfos:      file_user_service_v1_tenant_proto_msgTypes,
	}.Build()
	File_user_service_v1_tenant_proto = out.File
	file_user_service_v1_tenant_proto_goTypes = nil
	file_user_service_v1_tenant_proto_depIdxs = nil
}
