// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/position.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 职位
type Position struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                    // 职位ID
	Name          *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`                                 // 职位名称
	SortId        *int32                 `protobuf:"varint,3,opt,name=sort_id,json=sortId,proto3,oneof" json:"sort_id,omitempty"`              // 排序编号
	Code          *string                `protobuf:"bytes,4,opt,name=code,proto3,oneof" json:"code,omitempty"`                                 // 职位值
	Status        *string                `protobuf:"bytes,5,opt,name=status,proto3,oneof" json:"status,omitempty"`                             // 状态
	Remark        *string                `protobuf:"bytes,6,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                             // 备注
	ParentId      *uint32                `protobuf:"varint,50,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`       // 父节点ID
	Children      []*Position            `protobuf:"bytes,51,rep,name=children,proto3" json:"children,omitempty"`                              // 子节点树
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`      // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`      // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"` // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"` // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"` // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Position) Reset() {
	*x = Position{}
	mi := &file_user_service_v1_position_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position) ProtoMessage() {}

func (x *Position) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position.ProtoReflect.Descriptor instead.
func (*Position) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{0}
}

func (x *Position) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Position) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Position) GetSortId() int32 {
	if x != nil && x.SortId != nil {
		return *x.SortId
	}
	return 0
}

func (x *Position) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *Position) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *Position) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Position) GetParentId() uint32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *Position) GetChildren() []*Position {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Position) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Position) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Position) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Position) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Position) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 获取职位列表 - 答复
type ListPositionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Position            `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPositionResponse) Reset() {
	*x = ListPositionResponse{}
	mi := &file_user_service_v1_position_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPositionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPositionResponse) ProtoMessage() {}

func (x *ListPositionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPositionResponse.ProtoReflect.Descriptor instead.
func (*ListPositionResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{1}
}

func (x *ListPositionResponse) GetItems() []*Position {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListPositionResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取职位数据 - 请求
type GetPositionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPositionRequest) Reset() {
	*x = GetPositionRequest{}
	mi := &file_user_service_v1_position_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPositionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPositionRequest) ProtoMessage() {}

func (x *GetPositionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPositionRequest.ProtoReflect.Descriptor instead.
func (*GetPositionRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{2}
}

func (x *GetPositionRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建职位 - 请求
type CreatePositionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Position              `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePositionRequest) Reset() {
	*x = CreatePositionRequest{}
	mi := &file_user_service_v1_position_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePositionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePositionRequest) ProtoMessage() {}

func (x *CreatePositionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePositionRequest.ProtoReflect.Descriptor instead.
func (*CreatePositionRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePositionRequest) GetData() *Position {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新职位 - 请求
type UpdatePositionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Position              `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePositionRequest) Reset() {
	*x = UpdatePositionRequest{}
	mi := &file_user_service_v1_position_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePositionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePositionRequest) ProtoMessage() {}

func (x *UpdatePositionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePositionRequest.ProtoReflect.Descriptor instead.
func (*UpdatePositionRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{4}
}

func (x *UpdatePositionRequest) GetData() *Position {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdatePositionRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdatePositionRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除职位 - 请求
type DeletePositionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePositionRequest) Reset() {
	*x = DeletePositionRequest{}
	mi := &file_user_service_v1_position_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePositionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePositionRequest) ProtoMessage() {}

func (x *DeletePositionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePositionRequest.ProtoReflect.Descriptor instead.
func (*DeletePositionRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{5}
}

func (x *DeletePositionRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BatchCreatePositionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Position            `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreatePositionsRequest) Reset() {
	*x = BatchCreatePositionsRequest{}
	mi := &file_user_service_v1_position_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreatePositionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreatePositionsRequest) ProtoMessage() {}

func (x *BatchCreatePositionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreatePositionsRequest.ProtoReflect.Descriptor instead.
func (*BatchCreatePositionsRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCreatePositionsRequest) GetData() []*Position {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchCreatePositionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Position            `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreatePositionsResponse) Reset() {
	*x = BatchCreatePositionsResponse{}
	mi := &file_user_service_v1_position_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreatePositionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreatePositionsResponse) ProtoMessage() {}

func (x *BatchCreatePositionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_position_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreatePositionsResponse.ProtoReflect.Descriptor instead.
func (*BatchCreatePositionsResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_position_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCreatePositionsResponse) GetData() []*Position {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_user_service_v1_position_proto protoreflect.FileDescriptor

const file_user_service_v1_position_proto_rawDesc = "" +
	"\n" +
	"\x1euser/service/v1/position.proto\x12\x0fuser.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xa8\a\n" +
	"\bPosition\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b职位IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\x04name\x18\x02 \x01(\tB\x12\xbaG\x0f\x92\x02\f职位名称H\x01R\x04name\x88\x01\x01\x120\n" +
	"\asort_id\x18\x03 \x01(\x05B\x12\xbaG\x0f\x92\x02\f排序编号H\x02R\x06sortId\x88\x01\x01\x12(\n" +
	"\x04code\x18\x04 \x01(\tB\x0f\xbaG\f\x92\x02\t职位值H\x03R\x04code\x88\x01\x01\x12?\n" +
	"\x06status\x18\x05 \x01(\tB\"\xbaG\x1f\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\x06状态H\x04R\x06status\x88\x01\x01\x12)\n" +
	"\x06remark\x18\x06 \x01(\tB\f\xbaG\t\x92\x02\x06备注H\x05R\x06remark\x88\x01\x01\x123\n" +
	"\tparent_id\x182 \x01(\rB\x11\xbaG\x0e\x92\x02\v父节点IDH\x06R\bparentId\x88\x01\x01\x12I\n" +
	"\bchildren\x183 \x03(\v2\x19.user.service.v1.PositionB\x12\xbaG\x0f\x92\x02\f子节点树R\bchildren\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\aR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\bR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\tR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\n" +
	"R\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\vR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_nameB\n" +
	"\n" +
	"\b_sort_idB\a\n" +
	"\x05_codeB\t\n" +
	"\a_statusB\t\n" +
	"\a_remarkB\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"]\n" +
	"\x14ListPositionResponse\x12/\n" +
	"\x05items\x18\x01 \x03(\v2\x19.user.service.v1.PositionR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"$\n" +
	"\x12GetPositionRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"F\n" +
	"\x15CreatePositionRequest\x12-\n" +
	"\x04data\x18\x01 \x01(\v2\x19.user.service.v1.PositionR\x04data\"\x84\x03\n" +
	"\x15UpdatePositionRequest\x12-\n" +
	"\x04data\x18\x01 \x01(\v2\x19.user.service.v1.PositionR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"'\n" +
	"\x15DeletePositionRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"L\n" +
	"\x1bBatchCreatePositionsRequest\x12-\n" +
	"\x04data\x18\x01 \x03(\v2\x19.user.service.v1.PositionR\x04data\"M\n" +
	"\x1cBatchCreatePositionsResponse\x12-\n" +
	"\x04data\x18\x01 \x03(\v2\x19.user.service.v1.PositionR\x04data2\xf8\x03\n" +
	"\x0fPositionService\x12J\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a%.user.service.v1.ListPositionResponse\"\x00\x12G\n" +
	"\x03Get\x12#.user.service.v1.GetPositionRequest\x1a\x19.user.service.v1.Position\"\x00\x12J\n" +
	"\x06Create\x12&.user.service.v1.CreatePositionRequest\x1a\x16.google.protobuf.Empty\"\x00\x12J\n" +
	"\x06Update\x12&.user.service.v1.UpdatePositionRequest\x1a\x16.google.protobuf.Empty\"\x00\x12J\n" +
	"\x06Delete\x12&.user.service.v1.DeletePositionRequest\x1a\x16.google.protobuf.Empty\"\x00\x12l\n" +
	"\vBatchCreate\x12,.user.service.v1.BatchCreatePositionsRequest\x1a-.user.service.v1.BatchCreatePositionsResponse\"\x00B\xb5\x01\n" +
	"\x13com.user.service.v1B\rPositionProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_position_proto_rawDescOnce sync.Once
	file_user_service_v1_position_proto_rawDescData []byte
)

func file_user_service_v1_position_proto_rawDescGZIP() []byte {
	file_user_service_v1_position_proto_rawDescOnce.Do(func() {
		file_user_service_v1_position_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_position_proto_rawDesc), len(file_user_service_v1_position_proto_rawDesc)))
	})
	return file_user_service_v1_position_proto_rawDescData
}

var file_user_service_v1_position_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_user_service_v1_position_proto_goTypes = []any{
	(*Position)(nil),                     // 0: user.service.v1.Position
	(*ListPositionResponse)(nil),         // 1: user.service.v1.ListPositionResponse
	(*GetPositionRequest)(nil),           // 2: user.service.v1.GetPositionRequest
	(*CreatePositionRequest)(nil),        // 3: user.service.v1.CreatePositionRequest
	(*UpdatePositionRequest)(nil),        // 4: user.service.v1.UpdatePositionRequest
	(*DeletePositionRequest)(nil),        // 5: user.service.v1.DeletePositionRequest
	(*BatchCreatePositionsRequest)(nil),  // 6: user.service.v1.BatchCreatePositionsRequest
	(*BatchCreatePositionsResponse)(nil), // 7: user.service.v1.BatchCreatePositionsResponse
	(*timestamppb.Timestamp)(nil),        // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),        // 9: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),             // 10: pagination.PagingRequest
	(*emptypb.Empty)(nil),                // 11: google.protobuf.Empty
}
var file_user_service_v1_position_proto_depIdxs = []int32{
	0,  // 0: user.service.v1.Position.children:type_name -> user.service.v1.Position
	8,  // 1: user.service.v1.Position.create_time:type_name -> google.protobuf.Timestamp
	8,  // 2: user.service.v1.Position.update_time:type_name -> google.protobuf.Timestamp
	8,  // 3: user.service.v1.Position.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: user.service.v1.ListPositionResponse.items:type_name -> user.service.v1.Position
	0,  // 5: user.service.v1.CreatePositionRequest.data:type_name -> user.service.v1.Position
	0,  // 6: user.service.v1.UpdatePositionRequest.data:type_name -> user.service.v1.Position
	9,  // 7: user.service.v1.UpdatePositionRequest.update_mask:type_name -> google.protobuf.FieldMask
	0,  // 8: user.service.v1.BatchCreatePositionsRequest.data:type_name -> user.service.v1.Position
	0,  // 9: user.service.v1.BatchCreatePositionsResponse.data:type_name -> user.service.v1.Position
	10, // 10: user.service.v1.PositionService.List:input_type -> pagination.PagingRequest
	2,  // 11: user.service.v1.PositionService.Get:input_type -> user.service.v1.GetPositionRequest
	3,  // 12: user.service.v1.PositionService.Create:input_type -> user.service.v1.CreatePositionRequest
	4,  // 13: user.service.v1.PositionService.Update:input_type -> user.service.v1.UpdatePositionRequest
	5,  // 14: user.service.v1.PositionService.Delete:input_type -> user.service.v1.DeletePositionRequest
	6,  // 15: user.service.v1.PositionService.BatchCreate:input_type -> user.service.v1.BatchCreatePositionsRequest
	1,  // 16: user.service.v1.PositionService.List:output_type -> user.service.v1.ListPositionResponse
	0,  // 17: user.service.v1.PositionService.Get:output_type -> user.service.v1.Position
	11, // 18: user.service.v1.PositionService.Create:output_type -> google.protobuf.Empty
	11, // 19: user.service.v1.PositionService.Update:output_type -> google.protobuf.Empty
	11, // 20: user.service.v1.PositionService.Delete:output_type -> google.protobuf.Empty
	7,  // 21: user.service.v1.PositionService.BatchCreate:output_type -> user.service.v1.BatchCreatePositionsResponse
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_user_service_v1_position_proto_init() }
func file_user_service_v1_position_proto_init() {
	if File_user_service_v1_position_proto != nil {
		return
	}
	file_user_service_v1_position_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_service_v1_position_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_position_proto_rawDesc), len(file_user_service_v1_position_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_service_v1_position_proto_goTypes,
		DependencyIndexes: file_user_service_v1_position_proto_depIdxs,
		MessageInfos:      file_user_service_v1_position_proto_msgTypes,
	}.Build()
	File_user_service_v1_position_proto = out.File
	file_user_service_v1_position_proto_goTypes = nil
	file_user_service_v1_position_proto_depIdxs = nil
}
