// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/user.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户权限
type UserAuthority int32

const (
	UserAuthority_GUEST         UserAuthority = 0 // 游客
	UserAuthority_CUSTOMER_USER UserAuthority = 1 // 普通用户
	UserAuthority_TENANT_ADMIN  UserAuthority = 2 // 租户管理
	UserAuthority_SYS_ADMIN     UserAuthority = 3 // 系统管理员
)

// Enum value maps for UserAuthority.
var (
	UserAuthority_name = map[int32]string{
		0: "GUEST",
		1: "CUSTOMER_USER",
		2: "TENANT_ADMIN",
		3: "SYS_ADMIN",
	}
	UserAuthority_value = map[string]int32{
		"GUEST":         0,
		"CUSTOMER_USER": 1,
		"TENANT_ADMIN":  2,
		"SYS_ADMIN":     3,
	}
)

func (x UserAuthority) Enum() *UserAuthority {
	p := new(UserAuthority)
	*p = x
	return p
}

func (x UserAuthority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserAuthority) Descriptor() protoreflect.EnumDescriptor {
	return file_user_service_v1_user_proto_enumTypes[0].Descriptor()
}

func (UserAuthority) Type() protoreflect.EnumType {
	return &file_user_service_v1_user_proto_enumTypes[0]
}

func (x UserAuthority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserAuthority.Descriptor instead.
func (UserAuthority) EnumDescriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{0}
}

// 用户性别
type UserGender int32

const (
	UserGender_SECRET UserGender = 0 // 未知
	UserGender_MALE   UserGender = 1 // 男性
	UserGender_FEMALE UserGender = 2 // 女性
)

// Enum value maps for UserGender.
var (
	UserGender_name = map[int32]string{
		0: "SECRET",
		1: "MALE",
		2: "FEMALE",
	}
	UserGender_value = map[string]int32{
		"SECRET": 0,
		"MALE":   1,
		"FEMALE": 2,
	}
)

func (x UserGender) Enum() *UserGender {
	p := new(UserGender)
	*p = x
	return p
}

func (x UserGender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserGender) Descriptor() protoreflect.EnumDescriptor {
	return file_user_service_v1_user_proto_enumTypes[1].Descriptor()
}

func (UserGender) Type() protoreflect.EnumType {
	return &file_user_service_v1_user_proto_enumTypes[1]
}

func (x UserGender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserGender.Descriptor instead.
func (UserGender) EnumDescriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{1}
}

// 用户状态
type UserStatus int32

const (
	UserStatus_OFF UserStatus = 0
	UserStatus_ON  UserStatus = 1
)

// Enum value maps for UserStatus.
var (
	UserStatus_name = map[int32]string{
		0: "OFF",
		1: "ON",
	}
	UserStatus_value = map[string]int32{
		"OFF": 0,
		"ON":  1,
	}
)

func (x UserStatus) Enum() *UserStatus {
	p := new(UserStatus)
	*p = x
	return p
}

func (x UserStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_user_service_v1_user_proto_enumTypes[2].Descriptor()
}

func (UserStatus) Type() protoreflect.EnumType {
	return &file_user_service_v1_user_proto_enumTypes[2]
}

func (x UserStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserStatus.Descriptor instead.
func (UserStatus) EnumDescriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{2}
}

// 用户
type User struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"` // 用户ID
	//  optional uint32 role_id = 2 [json_name = "roleId", (gnostic.openapi.v3.property) = {description: "角色ID"}];  // 角色ID
	WorkId        *uint32                `protobuf:"varint,3,opt,name=work_id,json=workId,proto3,oneof" json:"work_id,omitempty"`                             // 工号
	OrgId         *uint32                `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3,oneof" json:"org_id,omitempty"`                                // 部门ID
	PositionId    *uint32                `protobuf:"varint,5,opt,name=position_id,json=positionId,proto3,oneof" json:"position_id,omitempty"`                 // 岗位ID
	TenantId      *uint32                `protobuf:"varint,6,opt,name=tenant_id,json=tenantId,proto3,oneof" json:"tenant_id,omitempty"`                       // 租户ID
	Username      *string                `protobuf:"bytes,10,opt,name=username,proto3,oneof" json:"username,omitempty"`                                       // 登录名
	Nickname      *string                `protobuf:"bytes,11,opt,name=nickname,proto3,oneof" json:"nickname,omitempty"`                                       // 昵称
	Realname      *string                `protobuf:"bytes,12,opt,name=realname,proto3,oneof" json:"realname,omitempty"`                                       // 真实姓名
	Avatar        *string                `protobuf:"bytes,13,opt,name=avatar,proto3,oneof" json:"avatar,omitempty"`                                           // 头像
	Email         *string                `protobuf:"bytes,14,opt,name=email,proto3,oneof" json:"email,omitempty"`                                             // 邮箱
	Mobile        *string                `protobuf:"bytes,15,opt,name=mobile,proto3,oneof" json:"mobile,omitempty"`                                           // 手机号
	Telephone     *string                `protobuf:"bytes,16,opt,name=telephone,proto3,oneof" json:"telephone,omitempty"`                                     // 座机号
	Gender        *UserGender            `protobuf:"varint,17,opt,name=gender,proto3,enum=user.service.v1.UserGender,oneof" json:"gender,omitempty"`          // 性别
	Address       *string                `protobuf:"bytes,18,opt,name=address,proto3,oneof" json:"address,omitempty"`                                         // 住址
	Region        *string                `protobuf:"bytes,19,opt,name=region,proto3,oneof" json:"region,omitempty"`                                           // 国家地区
	Description   *string                `protobuf:"bytes,20,opt,name=description,proto3,oneof" json:"description,omitempty"`                                 // 个人描述
	Remark        *string                `protobuf:"bytes,21,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                                           // 备注名
	LastLoginTime *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=last_login_time,json=lastLoginTime,proto3,oneof" json:"last_login_time,omitempty"`      // 最后登录时间
	LastLoginIp   *string                `protobuf:"bytes,31,opt,name=last_login_ip,json=lastLoginIp,proto3,oneof" json:"last_login_ip,omitempty"`            // 最后登录IP
	Status        *UserStatus            `protobuf:"varint,32,opt,name=status,proto3,enum=user.service.v1.UserStatus,oneof" json:"status,omitempty"`          // 用户状态
	Authority     *UserAuthority         `protobuf:"varint,33,opt,name=authority,proto3,enum=user.service.v1.UserAuthority,oneof" json:"authority,omitempty"` // 权限
	Roles         []string               `protobuf:"bytes,34,rep,name=roles,proto3" json:"roles,omitempty"`                                                   // 角色码列表
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                     // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                     // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_user_service_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *User) GetWorkId() uint32 {
	if x != nil && x.WorkId != nil {
		return *x.WorkId
	}
	return 0
}

func (x *User) GetOrgId() uint32 {
	if x != nil && x.OrgId != nil {
		return *x.OrgId
	}
	return 0
}

func (x *User) GetPositionId() uint32 {
	if x != nil && x.PositionId != nil {
		return *x.PositionId
	}
	return 0
}

func (x *User) GetTenantId() uint32 {
	if x != nil && x.TenantId != nil {
		return *x.TenantId
	}
	return 0
}

func (x *User) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *User) GetNickname() string {
	if x != nil && x.Nickname != nil {
		return *x.Nickname
	}
	return ""
}

func (x *User) GetRealname() string {
	if x != nil && x.Realname != nil {
		return *x.Realname
	}
	return ""
}

func (x *User) GetAvatar() string {
	if x != nil && x.Avatar != nil {
		return *x.Avatar
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *User) GetMobile() string {
	if x != nil && x.Mobile != nil {
		return *x.Mobile
	}
	return ""
}

func (x *User) GetTelephone() string {
	if x != nil && x.Telephone != nil {
		return *x.Telephone
	}
	return ""
}

func (x *User) GetGender() UserGender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return UserGender_SECRET
}

func (x *User) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

func (x *User) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *User) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *User) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *User) GetLastLoginTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastLoginTime
	}
	return nil
}

func (x *User) GetLastLoginIp() string {
	if x != nil && x.LastLoginIp != nil {
		return *x.LastLoginIp
	}
	return ""
}

func (x *User) GetStatus() UserStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return UserStatus_OFF
}

func (x *User) GetAuthority() UserAuthority {
	if x != nil && x.Authority != nil {
		return *x.Authority
	}
	return UserAuthority_GUEST
}

func (x *User) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *User) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *User) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *User) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *User) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *User) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 获取用户列表 - 答复
type ListUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*User                `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserResponse) Reset() {
	*x = ListUserResponse{}
	mi := &file_user_service_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserResponse) ProtoMessage() {}

func (x *ListUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserResponse.ProtoReflect.Descriptor instead.
func (*ListUserResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *ListUserResponse) GetItems() []*User {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListUserResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取用户数据 - 请求
type GetUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserByUserNameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"` // 用户登录名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByUserNameRequest) Reset() {
	*x = GetUserByUserNameRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByUserNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByUserNameRequest) ProtoMessage() {}

func (x *GetUserByUserNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByUserNameRequest.ProtoReflect.Descriptor instead.
func (*GetUserByUserNameRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserByUserNameRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// 创建用户 - 请求
type CreateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *User                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Password      *string                `protobuf:"bytes,2,opt,name=password,proto3,oneof" json:"password,omitempty"` // 用户登录密码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *CreateUserRequest) GetData() *User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CreateUserRequest) GetPassword() string {
	if x != nil && x.Password != nil {
		return *x.Password
	}
	return ""
}

// 更新用户 - 请求
type UpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *User                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`                                            // 用户的数据
	Password      *string                `protobuf:"bytes,2,opt,name=password,proto3,oneof" json:"password,omitempty"`                              // 用户登录密码
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,3,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,4,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateUserRequest) GetData() *User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateUserRequest) GetPassword() string {
	if x != nil && x.Password != nil {
		return *x.Password
	}
	return ""
}

func (x *UpdateUserRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateUserRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除用户 - 请求
type DeleteUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteUserRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 用户是否存在 - 请求
type UserExistsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"` // 用户登录名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserExistsRequest) Reset() {
	*x = UserExistsRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserExistsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExistsRequest) ProtoMessage() {}

func (x *UserExistsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExistsRequest.ProtoReflect.Descriptor instead.
func (*UserExistsRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *UserExistsRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// 用户是否存在 - 答复
type UserExistsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Exist         bool                   `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserExistsResponse) Reset() {
	*x = UserExistsResponse{}
	mi := &file_user_service_v1_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserExistsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExistsResponse) ProtoMessage() {}

func (x *UserExistsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExistsResponse.ProtoReflect.Descriptor instead.
func (*UserExistsResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *UserExistsResponse) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

type BatchCreateUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*User                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateUsersRequest) Reset() {
	*x = BatchCreateUsersRequest{}
	mi := &file_user_service_v1_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateUsersRequest) ProtoMessage() {}

func (x *BatchCreateUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateUsersRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateUsersRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{9}
}

func (x *BatchCreateUsersRequest) GetData() []*User {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchCreateUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*User                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateUsersResponse) Reset() {
	*x = BatchCreateUsersResponse{}
	mi := &file_user_service_v1_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateUsersResponse) ProtoMessage() {}

func (x *BatchCreateUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateUsersResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateUsersResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_user_proto_rawDescGZIP(), []int{10}
}

func (x *BatchCreateUsersResponse) GetData() []*User {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_user_service_v1_user_proto protoreflect.FileDescriptor

const file_user_service_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x1auser/service/v1/user.proto\x12\x0fuser.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1epagination/v1/pagination.proto\"\xa4\x0f\n" +
	"\x04User\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b用户IDH\x00R\x02id\x88\x01\x01\x12*\n" +
	"\awork_id\x18\x03 \x01(\rB\f\xbaG\t\x92\x02\x06工号H\x01R\x06workId\x88\x01\x01\x12*\n" +
	"\x06org_id\x18\x04 \x01(\rB\x0e\xbaG\v\x92\x02\b部门IDH\x02R\x05orgId\x88\x01\x01\x124\n" +
	"\vposition_id\x18\x05 \x01(\rB\x0e\xbaG\v\x92\x02\b岗位IDH\x03R\n" +
	"positionId\x88\x01\x01\x120\n" +
	"\ttenant_id\x18\x06 \x01(\rB\x0e\xbaG\v\x92\x02\b租户IDH\x04R\btenantId\x88\x01\x01\x120\n" +
	"\busername\x18\n" +
	" \x01(\tB\x0f\xbaG\f\x92\x02\t登录名H\x05R\busername\x88\x01\x01\x12-\n" +
	"\bnickname\x18\v \x01(\tB\f\xbaG\t\x92\x02\x06昵称H\x06R\bnickname\x88\x01\x01\x123\n" +
	"\brealname\x18\f \x01(\tB\x12\xbaG\x0f\x92\x02\f真实姓名H\aR\brealname\x88\x01\x01\x12)\n" +
	"\x06avatar\x18\r \x01(\tB\f\xbaG\t\x92\x02\x06头像H\bR\x06avatar\x88\x01\x01\x12'\n" +
	"\x05email\x18\x0e \x01(\tB\f\xbaG\t\x92\x02\x06邮箱H\tR\x05email\x88\x01\x01\x12,\n" +
	"\x06mobile\x18\x0f \x01(\tB\x0f\xbaG\f\x92\x02\t手机号H\n" +
	"R\x06mobile\x88\x01\x01\x122\n" +
	"\ttelephone\x18\x10 \x01(\tB\x0f\xbaG\f\x92\x02\t座机号H\vR\ttelephone\x88\x01\x01\x12F\n" +
	"\x06gender\x18\x11 \x01(\x0e2\x1b.user.service.v1.UserGenderB\f\xbaG\t\x92\x02\x06性别H\fR\x06gender\x88\x01\x01\x12+\n" +
	"\aaddress\x18\x12 \x01(\tB\f\xbaG\t\x92\x02\x06住址H\rR\aaddress\x88\x01\x01\x12/\n" +
	"\x06region\x18\x13 \x01(\tB\x12\xbaG\x0f\x92\x02\f国家地区H\x0eR\x06region\x88\x01\x01\x129\n" +
	"\vdescription\x18\x14 \x01(\tB\x12\xbaG\x0f\x92\x02\f个人描述H\x0fR\vdescription\x88\x01\x01\x12,\n" +
	"\x06remark\x18\x15 \x01(\tB\x0f\xbaG\f\x92\x02\t备注名H\x10R\x06remark\x88\x01\x01\x12a\n" +
	"\x0flast_login_time\x18\x1e \x01(\v2\x1a.google.protobuf.TimestampB\x18\xbaG\x15\x92\x02\x12最后登录时间H\x11R\rlastLoginTime\x88\x01\x01\x12=\n" +
	"\rlast_login_ip\x18\x1f \x01(\tB\x14\xbaG\x11\x92\x02\x0e最后登录IPH\x12R\vlastLoginIp\x88\x01\x01\x12b\n" +
	"\x06status\x18  \x01(\x0e2\x1b.user.service.v1.UserStatusB(\xbaG%\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\f用户状态H\x13R\x06status\x88\x01\x01\x12a\n" +
	"\tauthority\x18! \x01(\x0e2\x1e.user.service.v1.UserAuthorityB\x1e\xbaG\x1b\x8a\x02\x0f\x1a\rCUSTOMER_USER\x92\x02\x06权限H\x14R\tauthority\x88\x01\x01\x12+\n" +
	"\x05roles\x18\" \x03(\tB\x15\xbaG\x12\x92\x02\x0f角色码列表R\x05roles\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\x15R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\x16R\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\x17R\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\x18R\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\x19R\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\n" +
	"\n" +
	"\b_work_idB\t\n" +
	"\a_org_idB\x0e\n" +
	"\f_position_idB\f\n" +
	"\n" +
	"_tenant_idB\v\n" +
	"\t_usernameB\v\n" +
	"\t_nicknameB\v\n" +
	"\t_realnameB\t\n" +
	"\a_avatarB\b\n" +
	"\x06_emailB\t\n" +
	"\a_mobileB\f\n" +
	"\n" +
	"_telephoneB\t\n" +
	"\a_genderB\n" +
	"\n" +
	"\b_addressB\t\n" +
	"\a_regionB\x0e\n" +
	"\f_descriptionB\t\n" +
	"\a_remarkB\x12\n" +
	"\x10_last_login_timeB\x10\n" +
	"\x0e_last_login_ipB\t\n" +
	"\a_statusB\f\n" +
	"\n" +
	"_authorityB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"U\n" +
	"\x10ListUserResponse\x12+\n" +
	"\x05items\x18\x01 \x03(\v2\x15.user.service.v1.UserR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\" \n" +
	"\x0eGetUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"O\n" +
	"\x18GetUserByUserNameRequest\x123\n" +
	"\busername\x18\x01 \x01(\tB\x17\xbaG\x14\x18\x01\x92\x02\x0f用户登录名R\busername\"\x88\x01\n" +
	"\x11CreateUserRequest\x12)\n" +
	"\x04data\x18\x01 \x01(\v2\x15.user.service.v1.UserR\x04data\x12;\n" +
	"\bpassword\x18\x02 \x01(\tB\x1a\xbaG\x17\x18\x01\x92\x02\x12用户登录密码H\x00R\bpassword\x88\x01\x01B\v\n" +
	"\t_password\"\xe0\x03\n" +
	"\x11UpdateUserRequest\x12C\n" +
	"\x04data\x18\x01 \x01(\v2\x15.user.service.v1.UserB\x18\xe0A\x02\xbaG\x12\x92\x02\x0f用户的数据R\x04data\x12;\n" +
	"\bpassword\x18\x02 \x01(\tB\x1a\xbaG\x17\x18\x01\x92\x02\x12用户登录密码H\x00R\bpassword\x88\x01\x01\x12s\n" +
	"\vupdate_mask\x18\x03 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x04 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x01R\fallowMissing\x88\x01\x01B\v\n" +
	"\t_passwordB\x10\n" +
	"\x0e_allow_missing\"#\n" +
	"\x11DeleteUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"H\n" +
	"\x11UserExistsRequest\x123\n" +
	"\busername\x18\x01 \x01(\tB\x17\xbaG\x14\x18\x01\x92\x02\x0f用户登录名R\busername\"*\n" +
	"\x12UserExistsResponse\x12\x14\n" +
	"\x05exist\x18\x01 \x01(\bR\x05exist\"D\n" +
	"\x17BatchCreateUsersRequest\x12)\n" +
	"\x04data\x18\x01 \x03(\v2\x15.user.service.v1.UserR\x04data\"E\n" +
	"\x18BatchCreateUsersResponse\x12)\n" +
	"\x04data\x18\x01 \x03(\v2\x15.user.service.v1.UserR\x04data*N\n" +
	"\rUserAuthority\x12\t\n" +
	"\x05GUEST\x10\x00\x12\x11\n" +
	"\rCUSTOMER_USER\x10\x01\x12\x10\n" +
	"\fTENANT_ADMIN\x10\x02\x12\r\n" +
	"\tSYS_ADMIN\x10\x03*.\n" +
	"\n" +
	"UserGender\x12\n" +
	"\n" +
	"\x06SECRET\x10\x00\x12\b\n" +
	"\x04MALE\x10\x01\x12\n" +
	"\n" +
	"\x06FEMALE\x10\x02*\x1d\n" +
	"\n" +
	"UserStatus\x12\a\n" +
	"\x03OFF\x10\x00\x12\x06\n" +
	"\x02ON\x10\x012\x86\x05\n" +
	"\vUserService\x12F\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a!.user.service.v1.ListUserResponse\"\x00\x12?\n" +
	"\x03Get\x12\x1f.user.service.v1.GetUserRequest\x1a\x15.user.service.v1.User\"\x00\x12F\n" +
	"\x06Create\x12\".user.service.v1.CreateUserRequest\x1a\x16.google.protobuf.Empty\"\x00\x12F\n" +
	"\x06Update\x12\".user.service.v1.UpdateUserRequest\x1a\x16.google.protobuf.Empty\"\x00\x12F\n" +
	"\x06Delete\x12\".user.service.v1.DeleteUserRequest\x1a\x16.google.protobuf.Empty\"\x00\x12d\n" +
	"\vBatchCreate\x12(.user.service.v1.BatchCreateUsersRequest\x1a).user.service.v1.BatchCreateUsersResponse\"\x00\x12W\n" +
	"\x11GetUserByUserName\x12).user.service.v1.GetUserByUserNameRequest\x1a\x15.user.service.v1.User\"\x00\x12W\n" +
	"\n" +
	"UserExists\x12\".user.service.v1.UserExistsRequest\x1a#.user.service.v1.UserExistsResponse\"\x00B\xb1\x01\n" +
	"\x13com.user.service.v1B\tUserProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_user_proto_rawDescOnce sync.Once
	file_user_service_v1_user_proto_rawDescData []byte
)

func file_user_service_v1_user_proto_rawDescGZIP() []byte {
	file_user_service_v1_user_proto_rawDescOnce.Do(func() {
		file_user_service_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_user_proto_rawDesc), len(file_user_service_v1_user_proto_rawDesc)))
	})
	return file_user_service_v1_user_proto_rawDescData
}

var file_user_service_v1_user_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_user_service_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_user_service_v1_user_proto_goTypes = []any{
	(UserAuthority)(0),               // 0: user.service.v1.UserAuthority
	(UserGender)(0),                  // 1: user.service.v1.UserGender
	(UserStatus)(0),                  // 2: user.service.v1.UserStatus
	(*User)(nil),                     // 3: user.service.v1.User
	(*ListUserResponse)(nil),         // 4: user.service.v1.ListUserResponse
	(*GetUserRequest)(nil),           // 5: user.service.v1.GetUserRequest
	(*GetUserByUserNameRequest)(nil), // 6: user.service.v1.GetUserByUserNameRequest
	(*CreateUserRequest)(nil),        // 7: user.service.v1.CreateUserRequest
	(*UpdateUserRequest)(nil),        // 8: user.service.v1.UpdateUserRequest
	(*DeleteUserRequest)(nil),        // 9: user.service.v1.DeleteUserRequest
	(*UserExistsRequest)(nil),        // 10: user.service.v1.UserExistsRequest
	(*UserExistsResponse)(nil),       // 11: user.service.v1.UserExistsResponse
	(*BatchCreateUsersRequest)(nil),  // 12: user.service.v1.BatchCreateUsersRequest
	(*BatchCreateUsersResponse)(nil), // 13: user.service.v1.BatchCreateUsersResponse
	(*timestamppb.Timestamp)(nil),    // 14: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),    // 15: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),         // 16: pagination.PagingRequest
	(*emptypb.Empty)(nil),            // 17: google.protobuf.Empty
}
var file_user_service_v1_user_proto_depIdxs = []int32{
	1,  // 0: user.service.v1.User.gender:type_name -> user.service.v1.UserGender
	14, // 1: user.service.v1.User.last_login_time:type_name -> google.protobuf.Timestamp
	2,  // 2: user.service.v1.User.status:type_name -> user.service.v1.UserStatus
	0,  // 3: user.service.v1.User.authority:type_name -> user.service.v1.UserAuthority
	14, // 4: user.service.v1.User.create_time:type_name -> google.protobuf.Timestamp
	14, // 5: user.service.v1.User.update_time:type_name -> google.protobuf.Timestamp
	14, // 6: user.service.v1.User.delete_time:type_name -> google.protobuf.Timestamp
	3,  // 7: user.service.v1.ListUserResponse.items:type_name -> user.service.v1.User
	3,  // 8: user.service.v1.CreateUserRequest.data:type_name -> user.service.v1.User
	3,  // 9: user.service.v1.UpdateUserRequest.data:type_name -> user.service.v1.User
	15, // 10: user.service.v1.UpdateUserRequest.update_mask:type_name -> google.protobuf.FieldMask
	3,  // 11: user.service.v1.BatchCreateUsersRequest.data:type_name -> user.service.v1.User
	3,  // 12: user.service.v1.BatchCreateUsersResponse.data:type_name -> user.service.v1.User
	16, // 13: user.service.v1.UserService.List:input_type -> pagination.PagingRequest
	5,  // 14: user.service.v1.UserService.Get:input_type -> user.service.v1.GetUserRequest
	7,  // 15: user.service.v1.UserService.Create:input_type -> user.service.v1.CreateUserRequest
	8,  // 16: user.service.v1.UserService.Update:input_type -> user.service.v1.UpdateUserRequest
	9,  // 17: user.service.v1.UserService.Delete:input_type -> user.service.v1.DeleteUserRequest
	12, // 18: user.service.v1.UserService.BatchCreate:input_type -> user.service.v1.BatchCreateUsersRequest
	6,  // 19: user.service.v1.UserService.GetUserByUserName:input_type -> user.service.v1.GetUserByUserNameRequest
	10, // 20: user.service.v1.UserService.UserExists:input_type -> user.service.v1.UserExistsRequest
	4,  // 21: user.service.v1.UserService.List:output_type -> user.service.v1.ListUserResponse
	3,  // 22: user.service.v1.UserService.Get:output_type -> user.service.v1.User
	17, // 23: user.service.v1.UserService.Create:output_type -> google.protobuf.Empty
	17, // 24: user.service.v1.UserService.Update:output_type -> google.protobuf.Empty
	17, // 25: user.service.v1.UserService.Delete:output_type -> google.protobuf.Empty
	13, // 26: user.service.v1.UserService.BatchCreate:output_type -> user.service.v1.BatchCreateUsersResponse
	3,  // 27: user.service.v1.UserService.GetUserByUserName:output_type -> user.service.v1.User
	11, // 28: user.service.v1.UserService.UserExists:output_type -> user.service.v1.UserExistsResponse
	21, // [21:29] is the sub-list for method output_type
	13, // [13:21] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_user_service_v1_user_proto_init() }
func file_user_service_v1_user_proto_init() {
	if File_user_service_v1_user_proto != nil {
		return
	}
	file_user_service_v1_user_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_service_v1_user_proto_msgTypes[4].OneofWrappers = []any{}
	file_user_service_v1_user_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_user_proto_rawDesc), len(file_user_service_v1_user_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_service_v1_user_proto_goTypes,
		DependencyIndexes: file_user_service_v1_user_proto_depIdxs,
		EnumInfos:         file_user_service_v1_user_proto_enumTypes,
		MessageInfos:      file_user_service_v1_user_proto_msgTypes,
	}.Build()
	File_user_service_v1_user_proto = out.File
	file_user_service_v1_user_proto_goTypes = nil
	file_user_service_v1_user_proto_depIdxs = nil
}
