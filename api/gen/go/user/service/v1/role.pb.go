// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/role.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 角色
type Role struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                       // 角色ID
	Name          *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`                    // 角色名称
	SortId        *int32                 `protobuf:"varint,3,opt,name=sort_id,json=sortId,proto3,oneof" json:"sort_id,omitempty"` // 排序编号
	Code          *string                `protobuf:"bytes,4,opt,name=code,proto3,oneof" json:"code,omitempty"`                    // 角色值
	Status        *string                `protobuf:"bytes,5,opt,name=status,proto3,oneof" json:"status,omitempty"`
	Remark        *string                `protobuf:"bytes,6,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                             // 备注
	Menus         []uint32               `protobuf:"varint,7,rep,packed,name=menus,proto3" json:"menus,omitempty"`                             // 分配的菜单列表
	Apis          []uint32               `protobuf:"varint,8,rep,packed,name=apis,proto3" json:"apis,omitempty"`                               // 分配的API列表
	ParentId      *uint32                `protobuf:"varint,50,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`       // 父节点ID
	Children      []*Role                `protobuf:"bytes,51,rep,name=children,proto3" json:"children,omitempty"`                              // 子节点树
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`      // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`      // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"` // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"` // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"` // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Role) Reset() {
	*x = Role{}
	mi := &file_user_service_v1_role_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Role) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Role) ProtoMessage() {}

func (x *Role) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Role.ProtoReflect.Descriptor instead.
func (*Role) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{0}
}

func (x *Role) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Role) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Role) GetSortId() int32 {
	if x != nil && x.SortId != nil {
		return *x.SortId
	}
	return 0
}

func (x *Role) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *Role) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *Role) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Role) GetMenus() []uint32 {
	if x != nil {
		return x.Menus
	}
	return nil
}

func (x *Role) GetApis() []uint32 {
	if x != nil {
		return x.Apis
	}
	return nil
}

func (x *Role) GetParentId() uint32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *Role) GetChildren() []*Role {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Role) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Role) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Role) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Role) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Role) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 角色列表 - 答复
type ListRoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Role                `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoleResponse) Reset() {
	*x = ListRoleResponse{}
	mi := &file_user_service_v1_role_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoleResponse) ProtoMessage() {}

func (x *ListRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoleResponse.ProtoReflect.Descriptor instead.
func (*ListRoleResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{1}
}

func (x *ListRoleResponse) GetItems() []*Role {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListRoleResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 角色数据 - 请求
type GetRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoleRequest) Reset() {
	*x = GetRoleRequest{}
	mi := &file_user_service_v1_role_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleRequest) ProtoMessage() {}

func (x *GetRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleRequest.ProtoReflect.Descriptor instead.
func (*GetRoleRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{2}
}

func (x *GetRoleRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建角色 - 请求
type CreateRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Role                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoleRequest) Reset() {
	*x = CreateRoleRequest{}
	mi := &file_user_service_v1_role_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleRequest) ProtoMessage() {}

func (x *CreateRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleRequest.ProtoReflect.Descriptor instead.
func (*CreateRoleRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{3}
}

func (x *CreateRoleRequest) GetData() *Role {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新角色 - 请求
type UpdateRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Role                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoleRequest) Reset() {
	*x = UpdateRoleRequest{}
	mi := &file_user_service_v1_role_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleRequest) ProtoMessage() {}

func (x *UpdateRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoleRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateRoleRequest) GetData() *Role {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateRoleRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateRoleRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除角色 - 请求
type DeleteRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoleRequest) Reset() {
	*x = DeleteRoleRequest{}
	mi := &file_user_service_v1_role_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleRequest) ProtoMessage() {}

func (x *DeleteRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleRequest.ProtoReflect.Descriptor instead.
func (*DeleteRoleRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteRoleRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BatchCreateRolesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Role                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateRolesRequest) Reset() {
	*x = BatchCreateRolesRequest{}
	mi := &file_user_service_v1_role_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateRolesRequest) ProtoMessage() {}

func (x *BatchCreateRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateRolesRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateRolesRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCreateRolesRequest) GetData() []*Role {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchCreateRolesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Role                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateRolesResponse) Reset() {
	*x = BatchCreateRolesResponse{}
	mi := &file_user_service_v1_role_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateRolesResponse) ProtoMessage() {}

func (x *BatchCreateRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_role_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateRolesResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateRolesResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_role_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCreateRolesResponse) GetData() []*Role {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_user_service_v1_role_proto protoreflect.FileDescriptor

const file_user_service_v1_role_proto_rawDesc = "" +
	"\n" +
	"\x1auser/service/v1/role.proto\x12\x0fuser.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\x81\b\n" +
	"\x04Role\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b角色IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\x04name\x18\x02 \x01(\tB\x12\xbaG\x0f\x92\x02\f角色名称H\x01R\x04name\x88\x01\x01\x120\n" +
	"\asort_id\x18\x03 \x01(\x05B\x12\xbaG\x0f\x92\x02\f排序编号H\x02R\x06sortId\x88\x01\x01\x12(\n" +
	"\x04code\x18\x04 \x01(\tB\x0f\xbaG\f\x92\x02\t角色值H\x03R\x04code\x88\x01\x01\x12?\n" +
	"\x06status\x18\x05 \x01(\tB\"\xbaG\x1f\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\x06状态H\x04R\x06status\x88\x01\x01\x12)\n" +
	"\x06remark\x18\x06 \x01(\tB\f\xbaG\t\x92\x02\x06备注H\x05R\x06remark\x88\x01\x01\x121\n" +
	"\x05menus\x18\a \x03(\rB\x1b\xbaG\x18\x92\x02\x15分配的菜单列表R\x05menus\x12,\n" +
	"\x04apis\x18\b \x03(\rB\x18\xbaG\x15\x92\x02\x12分配的API列表R\x04apis\x123\n" +
	"\tparent_id\x182 \x01(\rB\x11\xbaG\x0e\x92\x02\v父节点IDH\x06R\bparentId\x88\x01\x01\x12E\n" +
	"\bchildren\x183 \x03(\v2\x15.user.service.v1.RoleB\x12\xbaG\x0f\x92\x02\f子节点树R\bchildren\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\aR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\bR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\tR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\n" +
	"R\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\vR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_nameB\n" +
	"\n" +
	"\b_sort_idB\a\n" +
	"\x05_codeB\t\n" +
	"\a_statusB\t\n" +
	"\a_remarkB\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"U\n" +
	"\x10ListRoleResponse\x12+\n" +
	"\x05items\x18\x01 \x03(\v2\x15.user.service.v1.RoleR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\" \n" +
	"\x0eGetRoleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\">\n" +
	"\x11CreateRoleRequest\x12)\n" +
	"\x04data\x18\x01 \x01(\v2\x15.user.service.v1.RoleR\x04data\"\xfc\x02\n" +
	"\x11UpdateRoleRequest\x12)\n" +
	"\x04data\x18\x01 \x01(\v2\x15.user.service.v1.RoleR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"#\n" +
	"\x11DeleteRoleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"D\n" +
	"\x17BatchCreateRolesRequest\x12)\n" +
	"\x04data\x18\x01 \x03(\v2\x15.user.service.v1.RoleR\x04data\"E\n" +
	"\x18BatchCreateRolesResponse\x12)\n" +
	"\x04data\x18\x01 \x03(\v2\x15.user.service.v1.RoleR\x04data2\xd4\x03\n" +
	"\vRoleService\x12F\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a!.user.service.v1.ListRoleResponse\"\x00\x12?\n" +
	"\x03Get\x12\x1f.user.service.v1.GetRoleRequest\x1a\x15.user.service.v1.Role\"\x00\x12F\n" +
	"\x06Create\x12\".user.service.v1.CreateRoleRequest\x1a\x16.google.protobuf.Empty\"\x00\x12F\n" +
	"\x06Update\x12\".user.service.v1.UpdateRoleRequest\x1a\x16.google.protobuf.Empty\"\x00\x12F\n" +
	"\x06Delete\x12\".user.service.v1.DeleteRoleRequest\x1a\x16.google.protobuf.Empty\"\x00\x12d\n" +
	"\vBatchCreate\x12(.user.service.v1.BatchCreateRolesRequest\x1a).user.service.v1.BatchCreateRolesResponse\"\x00B\xb1\x01\n" +
	"\x13com.user.service.v1B\tRoleProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_role_proto_rawDescOnce sync.Once
	file_user_service_v1_role_proto_rawDescData []byte
)

func file_user_service_v1_role_proto_rawDescGZIP() []byte {
	file_user_service_v1_role_proto_rawDescOnce.Do(func() {
		file_user_service_v1_role_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_role_proto_rawDesc), len(file_user_service_v1_role_proto_rawDesc)))
	})
	return file_user_service_v1_role_proto_rawDescData
}

var file_user_service_v1_role_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_user_service_v1_role_proto_goTypes = []any{
	(*Role)(nil),                     // 0: user.service.v1.Role
	(*ListRoleResponse)(nil),         // 1: user.service.v1.ListRoleResponse
	(*GetRoleRequest)(nil),           // 2: user.service.v1.GetRoleRequest
	(*CreateRoleRequest)(nil),        // 3: user.service.v1.CreateRoleRequest
	(*UpdateRoleRequest)(nil),        // 4: user.service.v1.UpdateRoleRequest
	(*DeleteRoleRequest)(nil),        // 5: user.service.v1.DeleteRoleRequest
	(*BatchCreateRolesRequest)(nil),  // 6: user.service.v1.BatchCreateRolesRequest
	(*BatchCreateRolesResponse)(nil), // 7: user.service.v1.BatchCreateRolesResponse
	(*timestamppb.Timestamp)(nil),    // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),    // 9: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),         // 10: pagination.PagingRequest
	(*emptypb.Empty)(nil),            // 11: google.protobuf.Empty
}
var file_user_service_v1_role_proto_depIdxs = []int32{
	0,  // 0: user.service.v1.Role.children:type_name -> user.service.v1.Role
	8,  // 1: user.service.v1.Role.create_time:type_name -> google.protobuf.Timestamp
	8,  // 2: user.service.v1.Role.update_time:type_name -> google.protobuf.Timestamp
	8,  // 3: user.service.v1.Role.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: user.service.v1.ListRoleResponse.items:type_name -> user.service.v1.Role
	0,  // 5: user.service.v1.CreateRoleRequest.data:type_name -> user.service.v1.Role
	0,  // 6: user.service.v1.UpdateRoleRequest.data:type_name -> user.service.v1.Role
	9,  // 7: user.service.v1.UpdateRoleRequest.update_mask:type_name -> google.protobuf.FieldMask
	0,  // 8: user.service.v1.BatchCreateRolesRequest.data:type_name -> user.service.v1.Role
	0,  // 9: user.service.v1.BatchCreateRolesResponse.data:type_name -> user.service.v1.Role
	10, // 10: user.service.v1.RoleService.List:input_type -> pagination.PagingRequest
	2,  // 11: user.service.v1.RoleService.Get:input_type -> user.service.v1.GetRoleRequest
	3,  // 12: user.service.v1.RoleService.Create:input_type -> user.service.v1.CreateRoleRequest
	4,  // 13: user.service.v1.RoleService.Update:input_type -> user.service.v1.UpdateRoleRequest
	5,  // 14: user.service.v1.RoleService.Delete:input_type -> user.service.v1.DeleteRoleRequest
	6,  // 15: user.service.v1.RoleService.BatchCreate:input_type -> user.service.v1.BatchCreateRolesRequest
	1,  // 16: user.service.v1.RoleService.List:output_type -> user.service.v1.ListRoleResponse
	0,  // 17: user.service.v1.RoleService.Get:output_type -> user.service.v1.Role
	11, // 18: user.service.v1.RoleService.Create:output_type -> google.protobuf.Empty
	11, // 19: user.service.v1.RoleService.Update:output_type -> google.protobuf.Empty
	11, // 20: user.service.v1.RoleService.Delete:output_type -> google.protobuf.Empty
	7,  // 21: user.service.v1.RoleService.BatchCreate:output_type -> user.service.v1.BatchCreateRolesResponse
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_user_service_v1_role_proto_init() }
func file_user_service_v1_role_proto_init() {
	if File_user_service_v1_role_proto != nil {
		return
	}
	file_user_service_v1_role_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_service_v1_role_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_role_proto_rawDesc), len(file_user_service_v1_role_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_service_v1_role_proto_goTypes,
		DependencyIndexes: file_user_service_v1_role_proto_depIdxs,
		MessageInfos:      file_user_service_v1_role_proto_msgTypes,
	}.Build()
	File_user_service_v1_role_proto = out.File
	file_user_service_v1_role_proto_goTypes = nil
	file_user_service_v1_role_proto_depIdxs = nil
}
