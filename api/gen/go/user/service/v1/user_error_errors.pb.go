// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package servicev1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 400
func IsBadRequest(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_BAD_REQUEST.String() && e.Code == 400
}

// 400
func ErrorBadRequest(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_BAD_REQUEST.String(), fmt.Sprintf(format, args...))
}

// 用户ID无效
func IsInvalidUserid(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INVALID_USERID.String() && e.Code == 400
}

// 用户ID无效
func ErrorInvalidUserid(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_INVALID_USERID.String(), fmt.Sprintf(format, args...))
}

// 密码无效
func IsInvalidPassword(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INVALID_PASSWORD.String() && e.Code == 400
}

// 密码无效
func ErrorInvalidPassword(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_INVALID_PASSWORD.String(), fmt.Sprintf(format, args...))
}

// 401
func IsUnauthorized(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_UNAUTHORIZED.String() && e.Code == 401
}

// 401
func ErrorUnauthorized(format string, args ...interface{}) *errors.Error {
	return errors.New(401, UserErrorReason_UNAUTHORIZED.String(), fmt.Sprintf(format, args...))
}

// 用户被冻结
func IsUserFreeze(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_FREEZE.String() && e.Code == 401
}

// 用户被冻结
func ErrorUserFreeze(format string, args ...interface{}) *errors.Error {
	return errors.New(401, UserErrorReason_USER_FREEZE.String(), fmt.Sprintf(format, args...))
}

// 密码错误
func IsIncorrectPassword(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INCORRECT_PASSWORD.String() && e.Code == 401
}

// 密码错误
func ErrorIncorrectPassword(format string, args ...interface{}) *errors.Error {
	return errors.New(401, UserErrorReason_INCORRECT_PASSWORD.String(), fmt.Sprintf(format, args...))
}

// 402
func IsPaymentRequired(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_PAYMENT_REQUIRED.String() && e.Code == 402
}

// 402
func ErrorPaymentRequired(format string, args ...interface{}) *errors.Error {
	return errors.New(402, UserErrorReason_PAYMENT_REQUIRED.String(), fmt.Sprintf(format, args...))
}

// 403
func IsForbidden(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_FORBIDDEN.String() && e.Code == 403
}

// 403
func ErrorForbidden(format string, args ...interface{}) *errors.Error {
	return errors.New(403, UserErrorReason_FORBIDDEN.String(), fmt.Sprintf(format, args...))
}

// 404
func IsNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NOT_FOUND.String() && e.Code == 404
}

// 404
func ErrorNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 用户不存在
func IsUserNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_NOT_FOUND.String() && e.Code == 404
}

// 用户不存在
func ErrorUserNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_USER_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 角色不存在
func IsRoleNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_ROLE_NOT_FOUND.String() && e.Code == 404
}

// 角色不存在
func ErrorRoleNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_ROLE_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 部门不存在
func IsDepartmentNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_DEPARTMENT_NOT_FOUND.String() && e.Code == 404
}

// 部门不存在
func ErrorDepartmentNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_DEPARTMENT_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 组织不存在
func IsOrganizationNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_ORGANIZATION_NOT_FOUND.String() && e.Code == 404
}

// 组织不存在
func ErrorOrganizationNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_ORGANIZATION_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 职位不存在
func IsPositionNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_POSITION_NOT_FOUND.String() && e.Code == 404
}

// 职位不存在
func ErrorPositionNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_POSITION_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 租户不存在
func IsTenantNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_TENANT_NOT_FOUND.String() && e.Code == 404
}

// 租户不存在
func ErrorTenantNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_TENANT_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 405
func IsMethodNotAllowed(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_METHOD_NOT_ALLOWED.String() && e.Code == 405
}

// 405
func ErrorMethodNotAllowed(format string, args ...interface{}) *errors.Error {
	return errors.New(405, UserErrorReason_METHOD_NOT_ALLOWED.String(), fmt.Sprintf(format, args...))
}

// 406
func IsNotAcceptable(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NOT_ACCEPTABLE.String() && e.Code == 406
}

// 406
func ErrorNotAcceptable(format string, args ...interface{}) *errors.Error {
	return errors.New(406, UserErrorReason_NOT_ACCEPTABLE.String(), fmt.Sprintf(format, args...))
}

// 407
func IsProxyAuthenticationRequired(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_PROXY_AUTHENTICATION_REQUIRED.String() && e.Code == 407
}

// 407
func ErrorProxyAuthenticationRequired(format string, args ...interface{}) *errors.Error {
	return errors.New(407, UserErrorReason_PROXY_AUTHENTICATION_REQUIRED.String(), fmt.Sprintf(format, args...))
}

// 408
func IsRequestTimeout(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_REQUEST_TIMEOUT.String() && e.Code == 408
}

// 408
func ErrorRequestTimeout(format string, args ...interface{}) *errors.Error {
	return errors.New(408, UserErrorReason_REQUEST_TIMEOUT.String(), fmt.Sprintf(format, args...))
}

// 409
func IsConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_CONFLICT.String() && e.Code == 409
}

// 409
func ErrorConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_CONFLICT.String(), fmt.Sprintf(format, args...))
}

// 410
func IsGone(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_GONE.String() && e.Code == 410
}

// 410
func ErrorGone(format string, args ...interface{}) *errors.Error {
	return errors.New(410, UserErrorReason_GONE.String(), fmt.Sprintf(format, args...))
}

// 411
func IsLengthRequired(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_LENGTH_REQUIRED.String() && e.Code == 411
}

// 411
func ErrorLengthRequired(format string, args ...interface{}) *errors.Error {
	return errors.New(411, UserErrorReason_LENGTH_REQUIRED.String(), fmt.Sprintf(format, args...))
}

// 412
func IsPreconditionFailed(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_PRECONDITION_FAILED.String() && e.Code == 412
}

// 412
func ErrorPreconditionFailed(format string, args ...interface{}) *errors.Error {
	return errors.New(412, UserErrorReason_PRECONDITION_FAILED.String(), fmt.Sprintf(format, args...))
}

// 413
func IsPayloadTooLarge(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_PAYLOAD_TOO_LARGE.String() && e.Code == 413
}

// 413
func ErrorPayloadTooLarge(format string, args ...interface{}) *errors.Error {
	return errors.New(413, UserErrorReason_PAYLOAD_TOO_LARGE.String(), fmt.Sprintf(format, args...))
}

// 414
func IsUriTooLong(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_URI_TOO_LONG.String() && e.Code == 414
}

// 414
func ErrorUriTooLong(format string, args ...interface{}) *errors.Error {
	return errors.New(414, UserErrorReason_URI_TOO_LONG.String(), fmt.Sprintf(format, args...))
}

// 415
func IsUnsupportedMediaType(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_UNSUPPORTED_MEDIA_TYPE.String() && e.Code == 415
}

// 415
func ErrorUnsupportedMediaType(format string, args ...interface{}) *errors.Error {
	return errors.New(415, UserErrorReason_UNSUPPORTED_MEDIA_TYPE.String(), fmt.Sprintf(format, args...))
}

// 416
func IsRangeNotSatisfiable(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_RANGE_NOT_SATISFIABLE.String() && e.Code == 416
}

// 416
func ErrorRangeNotSatisfiable(format string, args ...interface{}) *errors.Error {
	return errors.New(416, UserErrorReason_RANGE_NOT_SATISFIABLE.String(), fmt.Sprintf(format, args...))
}

// 417
func IsExpectationFailed(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_EXPECTATION_FAILED.String() && e.Code == 417
}

// 417
func ErrorExpectationFailed(format string, args ...interface{}) *errors.Error {
	return errors.New(417, UserErrorReason_EXPECTATION_FAILED.String(), fmt.Sprintf(format, args...))
}

// 418
func IsImATeapot(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_IM_A_TEAPOT.String() && e.Code == 418
}

// 418
func ErrorImATeapot(format string, args ...interface{}) *errors.Error {
	return errors.New(418, UserErrorReason_IM_A_TEAPOT.String(), fmt.Sprintf(format, args...))
}

// 421
func IsMisdirectedRequest(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_MISDIRECTED_REQUEST.String() && e.Code == 421
}

// 421
func ErrorMisdirectedRequest(format string, args ...interface{}) *errors.Error {
	return errors.New(421, UserErrorReason_MISDIRECTED_REQUEST.String(), fmt.Sprintf(format, args...))
}

// 422
func IsUnprocessableEntity(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_UNPROCESSABLE_ENTITY.String() && e.Code == 422
}

// 422
func ErrorUnprocessableEntity(format string, args ...interface{}) *errors.Error {
	return errors.New(422, UserErrorReason_UNPROCESSABLE_ENTITY.String(), fmt.Sprintf(format, args...))
}

// 423
func IsLocked(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_LOCKED.String() && e.Code == 423
}

// 423
func ErrorLocked(format string, args ...interface{}) *errors.Error {
	return errors.New(423, UserErrorReason_LOCKED.String(), fmt.Sprintf(format, args...))
}

// 424
func IsFailedDependency(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_FAILED_DEPENDENCY.String() && e.Code == 424
}

// 424
func ErrorFailedDependency(format string, args ...interface{}) *errors.Error {
	return errors.New(424, UserErrorReason_FAILED_DEPENDENCY.String(), fmt.Sprintf(format, args...))
}

// 425
func IsTooEarly(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_TOO_EARLY.String() && e.Code == 425
}

// 425
func ErrorTooEarly(format string, args ...interface{}) *errors.Error {
	return errors.New(425, UserErrorReason_TOO_EARLY.String(), fmt.Sprintf(format, args...))
}

// 426
func IsUpgradeRequired(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_UPGRADE_REQUIRED.String() && e.Code == 426
}

// 426
func ErrorUpgradeRequired(format string, args ...interface{}) *errors.Error {
	return errors.New(426, UserErrorReason_UPGRADE_REQUIRED.String(), fmt.Sprintf(format, args...))
}

// 428
func IsPreconditionRequired(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_PRECONDITION_REQUIRED.String() && e.Code == 428
}

// 428
func ErrorPreconditionRequired(format string, args ...interface{}) *errors.Error {
	return errors.New(428, UserErrorReason_PRECONDITION_REQUIRED.String(), fmt.Sprintf(format, args...))
}

// 429
func IsTooManyRequests(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_TOO_MANY_REQUESTS.String() && e.Code == 429
}

// 429
func ErrorTooManyRequests(format string, args ...interface{}) *errors.Error {
	return errors.New(429, UserErrorReason_TOO_MANY_REQUESTS.String(), fmt.Sprintf(format, args...))
}

// 431
func IsRequestHeaderFieldsTooLarge(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_REQUEST_HEADER_FIELDS_TOO_LARGE.String() && e.Code == 431
}

// 431
func ErrorRequestHeaderFieldsTooLarge(format string, args ...interface{}) *errors.Error {
	return errors.New(431, UserErrorReason_REQUEST_HEADER_FIELDS_TOO_LARGE.String(), fmt.Sprintf(format, args...))
}

// 451
func IsUnavailableForLegalReasons(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_UNAVAILABLE_FOR_LEGAL_REASONS.String() && e.Code == 451
}

// 451
func ErrorUnavailableForLegalReasons(format string, args ...interface{}) *errors.Error {
	return errors.New(451, UserErrorReason_UNAVAILABLE_FOR_LEGAL_REASONS.String(), fmt.Sprintf(format, args...))
}

// 500
func IsInternalServerError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INTERNAL_SERVER_ERROR.String() && e.Code == 500
}

// 500
func ErrorInternalServerError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, UserErrorReason_INTERNAL_SERVER_ERROR.String(), fmt.Sprintf(format, args...))
}

// 501
func IsNotImplemented(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NOT_IMPLEMENTED.String() && e.Code == 501
}

// 501
func ErrorNotImplemented(format string, args ...interface{}) *errors.Error {
	return errors.New(501, UserErrorReason_NOT_IMPLEMENTED.String(), fmt.Sprintf(format, args...))
}

// 502
func IsBadGateway(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_BAD_GATEWAY.String() && e.Code == 502
}

// 502
func ErrorBadGateway(format string, args ...interface{}) *errors.Error {
	return errors.New(502, UserErrorReason_BAD_GATEWAY.String(), fmt.Sprintf(format, args...))
}

// 503
func IsServiceUnavailable(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_SERVICE_UNAVAILABLE.String() && e.Code == 503
}

// 503
func ErrorServiceUnavailable(format string, args ...interface{}) *errors.Error {
	return errors.New(503, UserErrorReason_SERVICE_UNAVAILABLE.String(), fmt.Sprintf(format, args...))
}

// 504
func IsGatewayTimeout(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_GATEWAY_TIMEOUT.String() && e.Code == 504
}

// 504
func ErrorGatewayTimeout(format string, args ...interface{}) *errors.Error {
	return errors.New(504, UserErrorReason_GATEWAY_TIMEOUT.String(), fmt.Sprintf(format, args...))
}

// 505
func IsHttpVersionNotSupported(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_HTTP_VERSION_NOT_SUPPORTED.String() && e.Code == 505
}

// 505
func ErrorHttpVersionNotSupported(format string, args ...interface{}) *errors.Error {
	return errors.New(505, UserErrorReason_HTTP_VERSION_NOT_SUPPORTED.String(), fmt.Sprintf(format, args...))
}

// 506
func IsVariantAlsoNegotiates(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_VARIANT_ALSO_NEGOTIATES.String() && e.Code == 506
}

// 506
func ErrorVariantAlsoNegotiates(format string, args ...interface{}) *errors.Error {
	return errors.New(506, UserErrorReason_VARIANT_ALSO_NEGOTIATES.String(), fmt.Sprintf(format, args...))
}

// 507
func IsInsufficientStorage(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_INSUFFICIENT_STORAGE.String() && e.Code == 507
}

// 507
func ErrorInsufficientStorage(format string, args ...interface{}) *errors.Error {
	return errors.New(507, UserErrorReason_INSUFFICIENT_STORAGE.String(), fmt.Sprintf(format, args...))
}

// 508
func IsLoopDetected(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_LOOP_DETECTED.String() && e.Code == 508
}

// 508
func ErrorLoopDetected(format string, args ...interface{}) *errors.Error {
	return errors.New(508, UserErrorReason_LOOP_DETECTED.String(), fmt.Sprintf(format, args...))
}

// 510
func IsNotExtended(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NOT_EXTENDED.String() && e.Code == 510
}

// 510
func ErrorNotExtended(format string, args ...interface{}) *errors.Error {
	return errors.New(510, UserErrorReason_NOT_EXTENDED.String(), fmt.Sprintf(format, args...))
}

// 511
func IsNetworkAuthenticationRequired(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NETWORK_AUTHENTICATION_REQUIRED.String() && e.Code == 511
}

// 511
func ErrorNetworkAuthenticationRequired(format string, args ...interface{}) *errors.Error {
	return errors.New(511, UserErrorReason_NETWORK_AUTHENTICATION_REQUIRED.String(), fmt.Sprintf(format, args...))
}

// 598
func IsNetworkReadTimeoutError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NETWORK_READ_TIMEOUT_ERROR.String() && e.Code == 598
}

// 598
func ErrorNetworkReadTimeoutError(format string, args ...interface{}) *errors.Error {
	return errors.New(598, UserErrorReason_NETWORK_READ_TIMEOUT_ERROR.String(), fmt.Sprintf(format, args...))
}

// 599
func IsNetworkConnectTimeoutError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_NETWORK_CONNECT_TIMEOUT_ERROR.String() && e.Code == 599
}

// 599
func ErrorNetworkConnectTimeoutError(format string, args ...interface{}) *errors.Error {
	return errors.New(599, UserErrorReason_NETWORK_CONNECT_TIMEOUT_ERROR.String(), fmt.Sprintf(format, args...))
}
