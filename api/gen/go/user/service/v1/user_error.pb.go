// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/user_error.proto

package servicev1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserErrorReason int32

const (
	// 400
	UserErrorReason_BAD_REQUEST      UserErrorReason = 0 // 错误请求
	UserErrorReason_INVALID_USERID   UserErrorReason = 1 // 用户ID无效
	UserErrorReason_INVALID_PASSWORD UserErrorReason = 2 // 密码无效
	// 401
	UserErrorReason_UNAUTHORIZED       UserErrorReason = 100 // 未授权
	UserErrorReason_USER_FREEZE        UserErrorReason = 101 // 用户被冻结
	UserErrorReason_INCORRECT_PASSWORD UserErrorReason = 102 // 密码错误
	// 402
	UserErrorReason_PAYMENT_REQUIRED UserErrorReason = 200 // 需要支付
	// 403
	UserErrorReason_FORBIDDEN UserErrorReason = 300 // 禁止访问
	// 404
	UserErrorReason_NOT_FOUND              UserErrorReason = 400 // 找不到资源
	UserErrorReason_USER_NOT_FOUND         UserErrorReason = 401 // 用户不存在
	UserErrorReason_ROLE_NOT_FOUND         UserErrorReason = 402 // 角色不存在
	UserErrorReason_DEPARTMENT_NOT_FOUND   UserErrorReason = 403 // 部门不存在
	UserErrorReason_ORGANIZATION_NOT_FOUND UserErrorReason = 404 // 组织不存在
	UserErrorReason_POSITION_NOT_FOUND     UserErrorReason = 405 // 职位不存在
	UserErrorReason_TENANT_NOT_FOUND       UserErrorReason = 406 // 租户不存在
	// 405
	UserErrorReason_METHOD_NOT_ALLOWED UserErrorReason = 500 // 方法不允许
	// 406
	UserErrorReason_NOT_ACCEPTABLE UserErrorReason = 600 // 不可接受的请求
	// 407
	UserErrorReason_PROXY_AUTHENTICATION_REQUIRED UserErrorReason = 700 // 代理身份验证需要
	// 408
	UserErrorReason_REQUEST_TIMEOUT UserErrorReason = 800 // 请求超时
	// 409
	UserErrorReason_CONFLICT UserErrorReason = 900 // 冲突
	// 410
	UserErrorReason_GONE UserErrorReason = 1000 // 已删除
	// 411
	UserErrorReason_LENGTH_REQUIRED UserErrorReason = 1010 // 需要Content-Length
	// 412
	UserErrorReason_PRECONDITION_FAILED UserErrorReason = 1020 // 前置条件失败
	// 413
	UserErrorReason_PAYLOAD_TOO_LARGE UserErrorReason = 1030 // 负载过大
	// 414
	UserErrorReason_URI_TOO_LONG UserErrorReason = 1040 // URI过长
	// 415
	UserErrorReason_UNSUPPORTED_MEDIA_TYPE UserErrorReason = 1050 // 不支持的媒体类型
	// 416
	UserErrorReason_RANGE_NOT_SATISFIABLE UserErrorReason = 1060 // 请求范围无法满足
	// 417
	UserErrorReason_EXPECTATION_FAILED UserErrorReason = 1070 // 期望失败
	// 418
	UserErrorReason_IM_A_TEAPOT UserErrorReason = 1080 // 我是茶壶 (RFC 2324)
	// 421
	UserErrorReason_MISDIRECTED_REQUEST UserErrorReason = 1090 // 错误的请求
	// 422
	UserErrorReason_UNPROCESSABLE_ENTITY UserErrorReason = 1100 // 不可处理的实体
	// 423
	UserErrorReason_LOCKED UserErrorReason = 1110 // 已锁定
	// 424
	UserErrorReason_FAILED_DEPENDENCY UserErrorReason = 1120 // 依赖失败
	// 425
	UserErrorReason_TOO_EARLY UserErrorReason = 1130 // 请求过早
	// 426
	UserErrorReason_UPGRADE_REQUIRED UserErrorReason = 1140 // 需要升级
	// 428
	UserErrorReason_PRECONDITION_REQUIRED UserErrorReason = 1150 // 需要前置条件
	// 429
	UserErrorReason_TOO_MANY_REQUESTS UserErrorReason = 1160 // 请求过多
	// 431
	UserErrorReason_REQUEST_HEADER_FIELDS_TOO_LARGE UserErrorReason = 1170 // 请求头字段过大
	// 451
	UserErrorReason_UNAVAILABLE_FOR_LEGAL_REASONS UserErrorReason = 1180 // 因法律原因不可用
	// 500
	UserErrorReason_INTERNAL_SERVER_ERROR UserErrorReason = 2000 // 内部服务器错误
	// 501
	UserErrorReason_NOT_IMPLEMENTED UserErrorReason = 2100 // 未实现
	// 502
	UserErrorReason_BAD_GATEWAY UserErrorReason = 2200 // 错误网关
	// 503
	UserErrorReason_SERVICE_UNAVAILABLE UserErrorReason = 2300 // 服务不可用
	// 504
	UserErrorReason_GATEWAY_TIMEOUT UserErrorReason = 2400 // 网关超时
	// 505
	UserErrorReason_HTTP_VERSION_NOT_SUPPORTED UserErrorReason = 2500 // HTTP版本不支持
	// 506
	UserErrorReason_VARIANT_ALSO_NEGOTIATES UserErrorReason = 2600 // 变体也协商
	// 507
	UserErrorReason_INSUFFICIENT_STORAGE UserErrorReason = 2700 // 存储空间不足
	// 508
	UserErrorReason_LOOP_DETECTED UserErrorReason = 2800 // 检测到循环
	// 510
	UserErrorReason_NOT_EXTENDED UserErrorReason = 2900 // 未扩展
	// 511
	UserErrorReason_NETWORK_AUTHENTICATION_REQUIRED UserErrorReason = 3000 // 需要网络认证
	// 598
	UserErrorReason_NETWORK_READ_TIMEOUT_ERROR UserErrorReason = 3100 // 网络读取超时
	// 599
	UserErrorReason_NETWORK_CONNECT_TIMEOUT_ERROR UserErrorReason = 3200 // 网络连接超时
)

// Enum value maps for UserErrorReason.
var (
	UserErrorReason_name = map[int32]string{
		0:    "BAD_REQUEST",
		1:    "INVALID_USERID",
		2:    "INVALID_PASSWORD",
		100:  "UNAUTHORIZED",
		101:  "USER_FREEZE",
		102:  "INCORRECT_PASSWORD",
		200:  "PAYMENT_REQUIRED",
		300:  "FORBIDDEN",
		400:  "NOT_FOUND",
		401:  "USER_NOT_FOUND",
		402:  "ROLE_NOT_FOUND",
		403:  "DEPARTMENT_NOT_FOUND",
		404:  "ORGANIZATION_NOT_FOUND",
		405:  "POSITION_NOT_FOUND",
		406:  "TENANT_NOT_FOUND",
		500:  "METHOD_NOT_ALLOWED",
		600:  "NOT_ACCEPTABLE",
		700:  "PROXY_AUTHENTICATION_REQUIRED",
		800:  "REQUEST_TIMEOUT",
		900:  "CONFLICT",
		1000: "GONE",
		1010: "LENGTH_REQUIRED",
		1020: "PRECONDITION_FAILED",
		1030: "PAYLOAD_TOO_LARGE",
		1040: "URI_TOO_LONG",
		1050: "UNSUPPORTED_MEDIA_TYPE",
		1060: "RANGE_NOT_SATISFIABLE",
		1070: "EXPECTATION_FAILED",
		1080: "IM_A_TEAPOT",
		1090: "MISDIRECTED_REQUEST",
		1100: "UNPROCESSABLE_ENTITY",
		1110: "LOCKED",
		1120: "FAILED_DEPENDENCY",
		1130: "TOO_EARLY",
		1140: "UPGRADE_REQUIRED",
		1150: "PRECONDITION_REQUIRED",
		1160: "TOO_MANY_REQUESTS",
		1170: "REQUEST_HEADER_FIELDS_TOO_LARGE",
		1180: "UNAVAILABLE_FOR_LEGAL_REASONS",
		2000: "INTERNAL_SERVER_ERROR",
		2100: "NOT_IMPLEMENTED",
		2200: "BAD_GATEWAY",
		2300: "SERVICE_UNAVAILABLE",
		2400: "GATEWAY_TIMEOUT",
		2500: "HTTP_VERSION_NOT_SUPPORTED",
		2600: "VARIANT_ALSO_NEGOTIATES",
		2700: "INSUFFICIENT_STORAGE",
		2800: "LOOP_DETECTED",
		2900: "NOT_EXTENDED",
		3000: "NETWORK_AUTHENTICATION_REQUIRED",
		3100: "NETWORK_READ_TIMEOUT_ERROR",
		3200: "NETWORK_CONNECT_TIMEOUT_ERROR",
	}
	UserErrorReason_value = map[string]int32{
		"BAD_REQUEST":                     0,
		"INVALID_USERID":                  1,
		"INVALID_PASSWORD":                2,
		"UNAUTHORIZED":                    100,
		"USER_FREEZE":                     101,
		"INCORRECT_PASSWORD":              102,
		"PAYMENT_REQUIRED":                200,
		"FORBIDDEN":                       300,
		"NOT_FOUND":                       400,
		"USER_NOT_FOUND":                  401,
		"ROLE_NOT_FOUND":                  402,
		"DEPARTMENT_NOT_FOUND":            403,
		"ORGANIZATION_NOT_FOUND":          404,
		"POSITION_NOT_FOUND":              405,
		"TENANT_NOT_FOUND":                406,
		"METHOD_NOT_ALLOWED":              500,
		"NOT_ACCEPTABLE":                  600,
		"PROXY_AUTHENTICATION_REQUIRED":   700,
		"REQUEST_TIMEOUT":                 800,
		"CONFLICT":                        900,
		"GONE":                            1000,
		"LENGTH_REQUIRED":                 1010,
		"PRECONDITION_FAILED":             1020,
		"PAYLOAD_TOO_LARGE":               1030,
		"URI_TOO_LONG":                    1040,
		"UNSUPPORTED_MEDIA_TYPE":          1050,
		"RANGE_NOT_SATISFIABLE":           1060,
		"EXPECTATION_FAILED":              1070,
		"IM_A_TEAPOT":                     1080,
		"MISDIRECTED_REQUEST":             1090,
		"UNPROCESSABLE_ENTITY":            1100,
		"LOCKED":                          1110,
		"FAILED_DEPENDENCY":               1120,
		"TOO_EARLY":                       1130,
		"UPGRADE_REQUIRED":                1140,
		"PRECONDITION_REQUIRED":           1150,
		"TOO_MANY_REQUESTS":               1160,
		"REQUEST_HEADER_FIELDS_TOO_LARGE": 1170,
		"UNAVAILABLE_FOR_LEGAL_REASONS":   1180,
		"INTERNAL_SERVER_ERROR":           2000,
		"NOT_IMPLEMENTED":                 2100,
		"BAD_GATEWAY":                     2200,
		"SERVICE_UNAVAILABLE":             2300,
		"GATEWAY_TIMEOUT":                 2400,
		"HTTP_VERSION_NOT_SUPPORTED":      2500,
		"VARIANT_ALSO_NEGOTIATES":         2600,
		"INSUFFICIENT_STORAGE":            2700,
		"LOOP_DETECTED":                   2800,
		"NOT_EXTENDED":                    2900,
		"NETWORK_AUTHENTICATION_REQUIRED": 3000,
		"NETWORK_READ_TIMEOUT_ERROR":      3100,
		"NETWORK_CONNECT_TIMEOUT_ERROR":   3200,
	}
)

func (x UserErrorReason) Enum() *UserErrorReason {
	p := new(UserErrorReason)
	*p = x
	return p
}

func (x UserErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_user_service_v1_user_error_proto_enumTypes[0].Descriptor()
}

func (UserErrorReason) Type() protoreflect.EnumType {
	return &file_user_service_v1_user_error_proto_enumTypes[0]
}

func (x UserErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserErrorReason.Descriptor instead.
func (UserErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_user_service_v1_user_error_proto_rawDescGZIP(), []int{0}
}

var File_user_service_v1_user_error_proto protoreflect.FileDescriptor

const file_user_service_v1_user_error_proto_rawDesc = "" +
	"\n" +
	" user/service/v1/user_error.proto\x12\x0fuser.service.v1\x1a\x13errors/errors.proto*\xae\f\n" +
	"\x0fUserErrorReason\x12\x15\n" +
	"\vBAD_REQUEST\x10\x00\x1a\x04\xa8E\x90\x03\x12\x18\n" +
	"\x0eINVALID_USERID\x10\x01\x1a\x04\xa8E\x90\x03\x12\x1a\n" +
	"\x10INVALID_PASSWORD\x10\x02\x1a\x04\xa8E\x90\x03\x12\x16\n" +
	"\fUNAUTHORIZED\x10d\x1a\x04\xa8E\x91\x03\x12\x15\n" +
	"\vUSER_FREEZE\x10e\x1a\x04\xa8E\x91\x03\x12\x1c\n" +
	"\x12INCORRECT_PASSWORD\x10f\x1a\x04\xa8E\x91\x03\x12\x1b\n" +
	"\x10PAYMENT_REQUIRED\x10\xc8\x01\x1a\x04\xa8E\x92\x03\x12\x14\n" +
	"\tFORBIDDEN\x10\xac\x02\x1a\x04\xa8E\x93\x03\x12\x14\n" +
	"\tNOT_FOUND\x10\x90\x03\x1a\x04\xa8E\x94\x03\x12\x19\n" +
	"\x0eUSER_NOT_FOUND\x10\x91\x03\x1a\x04\xa8E\x94\x03\x12\x19\n" +
	"\x0eROLE_NOT_FOUND\x10\x92\x03\x1a\x04\xa8E\x94\x03\x12\x1f\n" +
	"\x14DEPARTMENT_NOT_FOUND\x10\x93\x03\x1a\x04\xa8E\x94\x03\x12!\n" +
	"\x16ORGANIZATION_NOT_FOUND\x10\x94\x03\x1a\x04\xa8E\x94\x03\x12\x1d\n" +
	"\x12POSITION_NOT_FOUND\x10\x95\x03\x1a\x04\xa8E\x94\x03\x12\x1b\n" +
	"\x10TENANT_NOT_FOUND\x10\x96\x03\x1a\x04\xa8E\x94\x03\x12\x1d\n" +
	"\x12METHOD_NOT_ALLOWED\x10\xf4\x03\x1a\x04\xa8E\x95\x03\x12\x19\n" +
	"\x0eNOT_ACCEPTABLE\x10\xd8\x04\x1a\x04\xa8E\x96\x03\x12(\n" +
	"\x1dPROXY_AUTHENTICATION_REQUIRED\x10\xbc\x05\x1a\x04\xa8E\x97\x03\x12\x1a\n" +
	"\x0fREQUEST_TIMEOUT\x10\xa0\x06\x1a\x04\xa8E\x98\x03\x12\x13\n" +
	"\bCONFLICT\x10\x84\a\x1a\x04\xa8E\x99\x03\x12\x0f\n" +
	"\x04GONE\x10\xe8\a\x1a\x04\xa8E\x9a\x03\x12\x1a\n" +
	"\x0fLENGTH_REQUIRED\x10\xf2\a\x1a\x04\xa8E\x9b\x03\x12\x1e\n" +
	"\x13PRECONDITION_FAILED\x10\xfc\a\x1a\x04\xa8E\x9c\x03\x12\x1c\n" +
	"\x11PAYLOAD_TOO_LARGE\x10\x86\b\x1a\x04\xa8E\x9d\x03\x12\x17\n" +
	"\fURI_TOO_LONG\x10\x90\b\x1a\x04\xa8E\x9e\x03\x12!\n" +
	"\x16UNSUPPORTED_MEDIA_TYPE\x10\x9a\b\x1a\x04\xa8E\x9f\x03\x12 \n" +
	"\x15RANGE_NOT_SATISFIABLE\x10\xa4\b\x1a\x04\xa8E\xa0\x03\x12\x1d\n" +
	"\x12EXPECTATION_FAILED\x10\xae\b\x1a\x04\xa8E\xa1\x03\x12\x16\n" +
	"\vIM_A_TEAPOT\x10\xb8\b\x1a\x04\xa8E\xa2\x03\x12\x1e\n" +
	"\x13MISDIRECTED_REQUEST\x10\xc2\b\x1a\x04\xa8E\xa5\x03\x12\x1f\n" +
	"\x14UNPROCESSABLE_ENTITY\x10\xcc\b\x1a\x04\xa8E\xa6\x03\x12\x11\n" +
	"\x06LOCKED\x10\xd6\b\x1a\x04\xa8E\xa7\x03\x12\x1c\n" +
	"\x11FAILED_DEPENDENCY\x10\xe0\b\x1a\x04\xa8E\xa8\x03\x12\x14\n" +
	"\tTOO_EARLY\x10\xea\b\x1a\x04\xa8E\xa9\x03\x12\x1b\n" +
	"\x10UPGRADE_REQUIRED\x10\xf4\b\x1a\x04\xa8E\xaa\x03\x12 \n" +
	"\x15PRECONDITION_REQUIRED\x10\xfe\b\x1a\x04\xa8E\xac\x03\x12\x1c\n" +
	"\x11TOO_MANY_REQUESTS\x10\x88\t\x1a\x04\xa8E\xad\x03\x12*\n" +
	"\x1fREQUEST_HEADER_FIELDS_TOO_LARGE\x10\x92\t\x1a\x04\xa8E\xaf\x03\x12(\n" +
	"\x1dUNAVAILABLE_FOR_LEGAL_REASONS\x10\x9c\t\x1a\x04\xa8E\xc3\x03\x12 \n" +
	"\x15INTERNAL_SERVER_ERROR\x10\xd0\x0f\x1a\x04\xa8E\xf4\x03\x12\x1a\n" +
	"\x0fNOT_IMPLEMENTED\x10\xb4\x10\x1a\x04\xa8E\xf5\x03\x12\x16\n" +
	"\vBAD_GATEWAY\x10\x98\x11\x1a\x04\xa8E\xf6\x03\x12\x1e\n" +
	"\x13SERVICE_UNAVAILABLE\x10\xfc\x11\x1a\x04\xa8E\xf7\x03\x12\x1a\n" +
	"\x0fGATEWAY_TIMEOUT\x10\xe0\x12\x1a\x04\xa8E\xf8\x03\x12%\n" +
	"\x1aHTTP_VERSION_NOT_SUPPORTED\x10\xc4\x13\x1a\x04\xa8E\xf9\x03\x12\"\n" +
	"\x17VARIANT_ALSO_NEGOTIATES\x10\xa8\x14\x1a\x04\xa8E\xfa\x03\x12\x1f\n" +
	"\x14INSUFFICIENT_STORAGE\x10\x8c\x15\x1a\x04\xa8E\xfb\x03\x12\x18\n" +
	"\rLOOP_DETECTED\x10\xf0\x15\x1a\x04\xa8E\xfc\x03\x12\x17\n" +
	"\fNOT_EXTENDED\x10\xd4\x16\x1a\x04\xa8E\xfe\x03\x12*\n" +
	"\x1fNETWORK_AUTHENTICATION_REQUIRED\x10\xb8\x17\x1a\x04\xa8E\xff\x03\x12%\n" +
	"\x1aNETWORK_READ_TIMEOUT_ERROR\x10\x9c\x18\x1a\x04\xa8E\xd6\x04\x12(\n" +
	"\x1dNETWORK_CONNECT_TIMEOUT_ERROR\x10\x80\x19\x1a\x04\xa8E\xd7\x04\x1a\x04\xa0E\xf4\x03B\xb6\x01\n" +
	"\x13com.user.service.v1B\x0eUserErrorProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_user_error_proto_rawDescOnce sync.Once
	file_user_service_v1_user_error_proto_rawDescData []byte
)

func file_user_service_v1_user_error_proto_rawDescGZIP() []byte {
	file_user_service_v1_user_error_proto_rawDescOnce.Do(func() {
		file_user_service_v1_user_error_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_user_error_proto_rawDesc), len(file_user_service_v1_user_error_proto_rawDesc)))
	})
	return file_user_service_v1_user_error_proto_rawDescData
}

var file_user_service_v1_user_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_user_service_v1_user_error_proto_goTypes = []any{
	(UserErrorReason)(0), // 0: user.service.v1.UserErrorReason
}
var file_user_service_v1_user_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_user_service_v1_user_error_proto_init() }
func file_user_service_v1_user_error_proto_init() {
	if File_user_service_v1_user_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_user_error_proto_rawDesc), len(file_user_service_v1_user_error_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_service_v1_user_error_proto_goTypes,
		DependencyIndexes: file_user_service_v1_user_error_proto_depIdxs,
		EnumInfos:         file_user_service_v1_user_error_proto_enumTypes,
	}.Build()
	File_user_service_v1_user_error_proto = out.File
	file_user_service_v1_user_error_proto_goTypes = nil
	file_user_service_v1_user_error_proto_depIdxs = nil
}
