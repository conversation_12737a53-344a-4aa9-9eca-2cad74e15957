// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: user/service/v1/department.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 部门
type Department struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                    // 部门ID
	Name             *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`                                                 // 部门名称
	OrganizationId   *int32                 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3,oneof" json:"organization_id,omitempty"`      // 所属组织ID
	OrganizationName *string                `protobuf:"bytes,4,opt,name=organization_name,json=organizationName,proto3,oneof" json:"organization_name,omitempty"` // 所属组织名称
	SortId           *int32                 `protobuf:"varint,10,opt,name=sort_id,json=sortId,proto3,oneof" json:"sort_id,omitempty"`                             // 排序编号
	Status           *string                `protobuf:"bytes,11,opt,name=status,proto3,oneof" json:"status,omitempty"`                                            // 状态
	Remark           *string                `protobuf:"bytes,12,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                                            // 备注
	ParentId         *uint32                `protobuf:"varint,50,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`                       // 父节点ID
	Children         []*Department          `protobuf:"bytes,51,rep,name=children,proto3" json:"children,omitempty"`                                              // 子节点树
	CreateBy         *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                      // 创建者ID
	UpdateBy         *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                      // 更新者ID
	CreateTime       *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                 // 创建时间
	UpdateTime       *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                 // 更新时间
	DeleteTime       *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                 // 删除时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Department) Reset() {
	*x = Department{}
	mi := &file_user_service_v1_department_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Department) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Department) ProtoMessage() {}

func (x *Department) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Department.ProtoReflect.Descriptor instead.
func (*Department) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{0}
}

func (x *Department) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Department) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Department) GetOrganizationId() int32 {
	if x != nil && x.OrganizationId != nil {
		return *x.OrganizationId
	}
	return 0
}

func (x *Department) GetOrganizationName() string {
	if x != nil && x.OrganizationName != nil {
		return *x.OrganizationName
	}
	return ""
}

func (x *Department) GetSortId() int32 {
	if x != nil && x.SortId != nil {
		return *x.SortId
	}
	return 0
}

func (x *Department) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *Department) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Department) GetParentId() uint32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *Department) GetChildren() []*Department {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Department) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Department) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Department) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Department) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Department) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 部门列表 - 答复
type ListDepartmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Department          `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDepartmentResponse) Reset() {
	*x = ListDepartmentResponse{}
	mi := &file_user_service_v1_department_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDepartmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepartmentResponse) ProtoMessage() {}

func (x *ListDepartmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepartmentResponse.ProtoReflect.Descriptor instead.
func (*ListDepartmentResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{1}
}

func (x *ListDepartmentResponse) GetItems() []*Department {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListDepartmentResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 部门数据 - 请求
type GetDepartmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDepartmentRequest) Reset() {
	*x = GetDepartmentRequest{}
	mi := &file_user_service_v1_department_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDepartmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepartmentRequest) ProtoMessage() {}

func (x *GetDepartmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepartmentRequest.ProtoReflect.Descriptor instead.
func (*GetDepartmentRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{2}
}

func (x *GetDepartmentRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建部门 - 请求
type CreateDepartmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Department            `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDepartmentRequest) Reset() {
	*x = CreateDepartmentRequest{}
	mi := &file_user_service_v1_department_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDepartmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepartmentRequest) ProtoMessage() {}

func (x *CreateDepartmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepartmentRequest.ProtoReflect.Descriptor instead.
func (*CreateDepartmentRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDepartmentRequest) GetData() *Department {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新部门 - 请求
type UpdateDepartmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Department            `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDepartmentRequest) Reset() {
	*x = UpdateDepartmentRequest{}
	mi := &file_user_service_v1_department_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDepartmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDepartmentRequest) ProtoMessage() {}

func (x *UpdateDepartmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDepartmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateDepartmentRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDepartmentRequest) GetData() *Department {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateDepartmentRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateDepartmentRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除部门 - 请求
type DeleteDepartmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDepartmentRequest) Reset() {
	*x = DeleteDepartmentRequest{}
	mi := &file_user_service_v1_department_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDepartmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDepartmentRequest) ProtoMessage() {}

func (x *DeleteDepartmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDepartmentRequest.ProtoReflect.Descriptor instead.
func (*DeleteDepartmentRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDepartmentRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BatchCreateDepartmentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Department          `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateDepartmentsRequest) Reset() {
	*x = BatchCreateDepartmentsRequest{}
	mi := &file_user_service_v1_department_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateDepartmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateDepartmentsRequest) ProtoMessage() {}

func (x *BatchCreateDepartmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateDepartmentsRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateDepartmentsRequest) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCreateDepartmentsRequest) GetData() []*Department {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchCreateDepartmentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Department          `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreateDepartmentsResponse) Reset() {
	*x = BatchCreateDepartmentsResponse{}
	mi := &file_user_service_v1_department_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreateDepartmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateDepartmentsResponse) ProtoMessage() {}

func (x *BatchCreateDepartmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_service_v1_department_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateDepartmentsResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateDepartmentsResponse) Descriptor() ([]byte, []int) {
	return file_user_service_v1_department_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCreateDepartmentsResponse) GetData() []*Department {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_user_service_v1_department_proto protoreflect.FileDescriptor

const file_user_service_v1_department_proto_rawDesc = "" +
	"\n" +
	" user/service/v1/department.proto\x12\x0fuser.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xb3\b\n" +
	"\n" +
	"Department\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b部门IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\x04name\x18\x02 \x01(\tB\x12\xbaG\x0f\x92\x02\f部门名称H\x01R\x04name\x88\x01\x01\x12B\n" +
	"\x0forganization_id\x18\x03 \x01(\x05B\x14\xbaG\x11\x92\x02\x0e所属组织IDH\x02R\x0eorganizationId\x88\x01\x01\x12J\n" +
	"\x11organization_name\x18\x04 \x01(\tB\x18\xbaG\x15\x92\x02\x12所属组织名称H\x03R\x10organizationName\x88\x01\x01\x120\n" +
	"\asort_id\x18\n" +
	" \x01(\x05B\x12\xbaG\x0f\x92\x02\f排序编号H\x04R\x06sortId\x88\x01\x01\x12?\n" +
	"\x06status\x18\v \x01(\tB\"\xbaG\x1f\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\x06状态H\x05R\x06status\x88\x01\x01\x12)\n" +
	"\x06remark\x18\f \x01(\tB\f\xbaG\t\x92\x02\x06备注H\x06R\x06remark\x88\x01\x01\x123\n" +
	"\tparent_id\x182 \x01(\rB\x11\xbaG\x0e\x92\x02\v父节点IDH\aR\bparentId\x88\x01\x01\x12K\n" +
	"\bchildren\x183 \x03(\v2\x1b.user.service.v1.DepartmentB\x12\xbaG\x0f\x92\x02\f子节点树R\bchildren\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\bR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\tR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\n" +
	"R\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\vR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\fR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_nameB\x12\n" +
	"\x10_organization_idB\x14\n" +
	"\x12_organization_nameB\n" +
	"\n" +
	"\b_sort_idB\t\n" +
	"\a_statusB\t\n" +
	"\a_remarkB\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"a\n" +
	"\x16ListDepartmentResponse\x121\n" +
	"\x05items\x18\x01 \x03(\v2\x1b.user.service.v1.DepartmentR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"&\n" +
	"\x14GetDepartmentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"J\n" +
	"\x17CreateDepartmentRequest\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.user.service.v1.DepartmentR\x04data\"\x88\x03\n" +
	"\x17UpdateDepartmentRequest\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.user.service.v1.DepartmentR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\")\n" +
	"\x17DeleteDepartmentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"P\n" +
	"\x1dBatchCreateDepartmentsRequest\x12/\n" +
	"\x04data\x18\x01 \x03(\v2\x1b.user.service.v1.DepartmentR\x04data\"Q\n" +
	"\x1eBatchCreateDepartmentsResponse\x12/\n" +
	"\x04data\x18\x01 \x03(\v2\x1b.user.service.v1.DepartmentR\x04data2\x8a\x04\n" +
	"\x11DepartmentService\x12L\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a'.user.service.v1.ListDepartmentResponse\"\x00\x12K\n" +
	"\x03Get\x12%.user.service.v1.GetDepartmentRequest\x1a\x1b.user.service.v1.Department\"\x00\x12L\n" +
	"\x06Create\x12(.user.service.v1.CreateDepartmentRequest\x1a\x16.google.protobuf.Empty\"\x00\x12L\n" +
	"\x06Update\x12(.user.service.v1.UpdateDepartmentRequest\x1a\x16.google.protobuf.Empty\"\x00\x12L\n" +
	"\x06Delete\x12(.user.service.v1.DeleteDepartmentRequest\x1a\x16.google.protobuf.Empty\"\x00\x12p\n" +
	"\vBatchCreate\x12..user.service.v1.BatchCreateDepartmentsRequest\x1a/.user.service.v1.BatchCreateDepartmentsResponse\"\x00B\xb7\x01\n" +
	"\x13com.user.service.v1B\x0fDepartmentProtoP\x01Z1kratos-admin/api/gen/go/user/service/v1;servicev1\xa2\x02\x03USX\xaa\x02\x0fUser.Service.V1\xca\x02\x0fUser\\Service\\V1\xe2\x02\x1bUser\\Service\\V1\\GPBMetadata\xea\x02\x11User::Service::V1b\x06proto3"

var (
	file_user_service_v1_department_proto_rawDescOnce sync.Once
	file_user_service_v1_department_proto_rawDescData []byte
)

func file_user_service_v1_department_proto_rawDescGZIP() []byte {
	file_user_service_v1_department_proto_rawDescOnce.Do(func() {
		file_user_service_v1_department_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_service_v1_department_proto_rawDesc), len(file_user_service_v1_department_proto_rawDesc)))
	})
	return file_user_service_v1_department_proto_rawDescData
}

var file_user_service_v1_department_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_user_service_v1_department_proto_goTypes = []any{
	(*Department)(nil),                     // 0: user.service.v1.Department
	(*ListDepartmentResponse)(nil),         // 1: user.service.v1.ListDepartmentResponse
	(*GetDepartmentRequest)(nil),           // 2: user.service.v1.GetDepartmentRequest
	(*CreateDepartmentRequest)(nil),        // 3: user.service.v1.CreateDepartmentRequest
	(*UpdateDepartmentRequest)(nil),        // 4: user.service.v1.UpdateDepartmentRequest
	(*DeleteDepartmentRequest)(nil),        // 5: user.service.v1.DeleteDepartmentRequest
	(*BatchCreateDepartmentsRequest)(nil),  // 6: user.service.v1.BatchCreateDepartmentsRequest
	(*BatchCreateDepartmentsResponse)(nil), // 7: user.service.v1.BatchCreateDepartmentsResponse
	(*timestamppb.Timestamp)(nil),          // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),          // 9: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),               // 10: pagination.PagingRequest
	(*emptypb.Empty)(nil),                  // 11: google.protobuf.Empty
}
var file_user_service_v1_department_proto_depIdxs = []int32{
	0,  // 0: user.service.v1.Department.children:type_name -> user.service.v1.Department
	8,  // 1: user.service.v1.Department.create_time:type_name -> google.protobuf.Timestamp
	8,  // 2: user.service.v1.Department.update_time:type_name -> google.protobuf.Timestamp
	8,  // 3: user.service.v1.Department.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: user.service.v1.ListDepartmentResponse.items:type_name -> user.service.v1.Department
	0,  // 5: user.service.v1.CreateDepartmentRequest.data:type_name -> user.service.v1.Department
	0,  // 6: user.service.v1.UpdateDepartmentRequest.data:type_name -> user.service.v1.Department
	9,  // 7: user.service.v1.UpdateDepartmentRequest.update_mask:type_name -> google.protobuf.FieldMask
	0,  // 8: user.service.v1.BatchCreateDepartmentsRequest.data:type_name -> user.service.v1.Department
	0,  // 9: user.service.v1.BatchCreateDepartmentsResponse.data:type_name -> user.service.v1.Department
	10, // 10: user.service.v1.DepartmentService.List:input_type -> pagination.PagingRequest
	2,  // 11: user.service.v1.DepartmentService.Get:input_type -> user.service.v1.GetDepartmentRequest
	3,  // 12: user.service.v1.DepartmentService.Create:input_type -> user.service.v1.CreateDepartmentRequest
	4,  // 13: user.service.v1.DepartmentService.Update:input_type -> user.service.v1.UpdateDepartmentRequest
	5,  // 14: user.service.v1.DepartmentService.Delete:input_type -> user.service.v1.DeleteDepartmentRequest
	6,  // 15: user.service.v1.DepartmentService.BatchCreate:input_type -> user.service.v1.BatchCreateDepartmentsRequest
	1,  // 16: user.service.v1.DepartmentService.List:output_type -> user.service.v1.ListDepartmentResponse
	0,  // 17: user.service.v1.DepartmentService.Get:output_type -> user.service.v1.Department
	11, // 18: user.service.v1.DepartmentService.Create:output_type -> google.protobuf.Empty
	11, // 19: user.service.v1.DepartmentService.Update:output_type -> google.protobuf.Empty
	11, // 20: user.service.v1.DepartmentService.Delete:output_type -> google.protobuf.Empty
	7,  // 21: user.service.v1.DepartmentService.BatchCreate:output_type -> user.service.v1.BatchCreateDepartmentsResponse
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_user_service_v1_department_proto_init() }
func file_user_service_v1_department_proto_init() {
	if File_user_service_v1_department_proto != nil {
		return
	}
	file_user_service_v1_department_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_service_v1_department_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_service_v1_department_proto_rawDesc), len(file_user_service_v1_department_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_service_v1_department_proto_goTypes,
		DependencyIndexes: file_user_service_v1_department_proto_depIdxs,
		MessageInfos:      file_user_service_v1_department_proto_msgTypes,
	}.Build()
	File_user_service_v1_department_proto = out.File
	file_user_service_v1_department_proto_goTypes = nil
	file_user_service_v1_department_proto_depIdxs = nil
}
