// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: goofish/service/v1/goofish.proto

package goofishv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PlatformInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlatformInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlatformInfoRequestMultiError, or nil if none found.
func (m *PlatformInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	if len(errors) > 0 {
		return PlatformInfoRequestMultiError(errors)
	}

	return nil
}

// PlatformInfoRequestMultiError is an error wrapping multiple validation
// errors returned by PlatformInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type PlatformInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformInfoRequestMultiError) AllErrors() []error { return m }

// PlatformInfoRequestValidationError is the validation error returned by
// PlatformInfoRequest.Validate if the designated constraints aren't met.
type PlatformInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformInfoRequestValidationError) ErrorName() string {
	return "PlatformInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PlatformInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformInfoRequestValidationError{}

// Validate checks the field values on PlatformInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlatformInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlatformInfoResponseMultiError, or nil if none found.
func (m *PlatformInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlatformInfoResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlatformInfoResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlatformInfoResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PlatformInfoResponseMultiError(errors)
	}

	return nil
}

// PlatformInfoResponseMultiError is an error wrapping multiple validation
// errors returned by PlatformInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type PlatformInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformInfoResponseMultiError) AllErrors() []error { return m }

// PlatformInfoResponseValidationError is the validation error returned by
// PlatformInfoResponse.Validate if the designated constraints aren't met.
type PlatformInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformInfoResponseValidationError) ErrorName() string {
	return "PlatformInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PlatformInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformInfoResponseValidationError{}

// Validate checks the field values on PlatformInfoData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PlatformInfoData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformInfoData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlatformInfoDataMultiError, or nil if none found.
func (m *PlatformInfoData) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformInfoData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	if len(errors) > 0 {
		return PlatformInfoDataMultiError(errors)
	}

	return nil
}

// PlatformInfoDataMultiError is an error wrapping multiple validation errors
// returned by PlatformInfoData.ValidateAll() if the designated constraints
// aren't met.
type PlatformInfoDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformInfoDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformInfoDataMultiError) AllErrors() []error { return m }

// PlatformInfoDataValidationError is the validation error returned by
// PlatformInfoData.Validate if the designated constraints aren't met.
type PlatformInfoDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformInfoDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformInfoDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformInfoDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformInfoDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformInfoDataValidationError) ErrorName() string { return "PlatformInfoDataValidationError" }

// Error satisfies the builtin error interface
func (e PlatformInfoDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformInfoData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformInfoDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformInfoDataValidationError{}

// Validate checks the field values on UserInfoRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserInfoRequestMultiError, or nil if none found.
func (m *UserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	if len(errors) > 0 {
		return UserInfoRequestMultiError(errors)
	}

	return nil
}

// UserInfoRequestMultiError is an error wrapping multiple validation errors
// returned by UserInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type UserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoRequestMultiError) AllErrors() []error { return m }

// UserInfoRequestValidationError is the validation error returned by
// UserInfoRequest.Validate if the designated constraints aren't met.
type UserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoRequestValidationError) ErrorName() string { return "UserInfoRequestValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoRequestValidationError{}

// Validate checks the field values on UserInfoResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserInfoResponseMultiError, or nil if none found.
func (m *UserInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserInfoResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserInfoResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserInfoResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserInfoResponseMultiError(errors)
	}

	return nil
}

// UserInfoResponseMultiError is an error wrapping multiple validation errors
// returned by UserInfoResponse.ValidateAll() if the designated constraints
// aren't met.
type UserInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoResponseMultiError) AllErrors() []error { return m }

// UserInfoResponseValidationError is the validation error returned by
// UserInfoResponse.Validate if the designated constraints aren't met.
type UserInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoResponseValidationError) ErrorName() string { return "UserInfoResponseValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoResponseValidationError{}

// Validate checks the field values on UserInfoData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInfoData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfoData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInfoDataMultiError, or
// nil if none found.
func (m *UserInfoData) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfoData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Balance

	if len(errors) > 0 {
		return UserInfoDataMultiError(errors)
	}

	return nil
}

// UserInfoDataMultiError is an error wrapping multiple validation errors
// returned by UserInfoData.ValidateAll() if the designated constraints aren't met.
type UserInfoDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoDataMultiError) AllErrors() []error { return m }

// UserInfoDataValidationError is the validation error returned by
// UserInfoData.Validate if the designated constraints aren't met.
type UserInfoDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoDataValidationError) ErrorName() string { return "UserInfoDataValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfoData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoDataValidationError{}

// Validate checks the field values on GoodsTemplate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GoodsTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GoodsTemplateMultiError, or
// nil if none found.
func (m *GoodsTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Check

	if len(errors) > 0 {
		return GoodsTemplateMultiError(errors)
	}

	return nil
}

// GoodsTemplateMultiError is an error wrapping multiple validation errors
// returned by GoodsTemplate.ValidateAll() if the designated constraints
// aren't met.
type GoodsTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsTemplateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsTemplateMultiError) AllErrors() []error { return m }

// GoodsTemplateValidationError is the validation error returned by
// GoodsTemplate.Validate if the designated constraints aren't met.
type GoodsTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsTemplateValidationError) ErrorName() string { return "GoodsTemplateValidationError" }

// Error satisfies the builtin error interface
func (e GoodsTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsTemplateValidationError{}

// Validate checks the field values on GoodsDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GoodsDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GoodsDetailMultiError, or
// nil if none found.
func (m *GoodsDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GoodsNo

	// no validation rules for GoodsType

	// no validation rules for GoodsName

	// no validation rules for Price

	// no validation rules for Stock

	// no validation rules for Status

	// no validation rules for UpdateTime

	for idx, item := range m.GetTemplate() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GoodsDetailValidationError{
						field:  fmt.Sprintf("Template[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GoodsDetailValidationError{
						field:  fmt.Sprintf("Template[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GoodsDetailValidationError{
					field:  fmt.Sprintf("Template[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GoodsDetailMultiError(errors)
	}

	return nil
}

// GoodsDetailMultiError is an error wrapping multiple validation errors
// returned by GoodsDetail.ValidateAll() if the designated constraints aren't met.
type GoodsDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsDetailMultiError) AllErrors() []error { return m }

// GoodsDetailValidationError is the validation error returned by
// GoodsDetail.Validate if the designated constraints aren't met.
type GoodsDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsDetailValidationError) ErrorName() string { return "GoodsDetailValidationError" }

// Error satisfies the builtin error interface
func (e GoodsDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsDetailValidationError{}

// Validate checks the field values on GoodsDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsDetailRequestMultiError, or nil if none found.
func (m *GoodsDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for GoodsType

	// no validation rules for GoodsNo

	if len(errors) > 0 {
		return GoodsDetailRequestMultiError(errors)
	}

	return nil
}

// GoodsDetailRequestMultiError is an error wrapping multiple validation errors
// returned by GoodsDetailRequest.ValidateAll() if the designated constraints
// aren't met.
type GoodsDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsDetailRequestMultiError) AllErrors() []error { return m }

// GoodsDetailRequestValidationError is the validation error returned by
// GoodsDetailRequest.Validate if the designated constraints aren't met.
type GoodsDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsDetailRequestValidationError) ErrorName() string {
	return "GoodsDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsDetailRequestValidationError{}

// Validate checks the field values on GoodsDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsDetailResponseMultiError, or nil if none found.
func (m *GoodsDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GoodsDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GoodsDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GoodsDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GoodsDetailResponseMultiError(errors)
	}

	return nil
}

// GoodsDetailResponseMultiError is an error wrapping multiple validation
// errors returned by GoodsDetailResponse.ValidateAll() if the designated
// constraints aren't met.
type GoodsDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsDetailResponseMultiError) AllErrors() []error { return m }

// GoodsDetailResponseValidationError is the validation error returned by
// GoodsDetailResponse.Validate if the designated constraints aren't met.
type GoodsDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsDetailResponseValidationError) ErrorName() string {
	return "GoodsDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsDetailResponseValidationError{}

// Validate checks the field values on GoodsListRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GoodsListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsListRequestMultiError, or nil if none found.
func (m *GoodsListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for Keyword

	// no validation rules for GoodsType

	// no validation rules for PageNo

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GoodsListRequestMultiError(errors)
	}

	return nil
}

// GoodsListRequestMultiError is an error wrapping multiple validation errors
// returned by GoodsListRequest.ValidateAll() if the designated constraints
// aren't met.
type GoodsListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsListRequestMultiError) AllErrors() []error { return m }

// GoodsListRequestValidationError is the validation error returned by
// GoodsListRequest.Validate if the designated constraints aren't met.
type GoodsListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsListRequestValidationError) ErrorName() string { return "GoodsListRequestValidationError" }

// Error satisfies the builtin error interface
func (e GoodsListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsListRequestValidationError{}

// Validate checks the field values on GoodsListResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GoodsListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsListResponseMultiError, or nil if none found.
func (m *GoodsListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GoodsListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GoodsListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GoodsListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GoodsListResponseMultiError(errors)
	}

	return nil
}

// GoodsListResponseMultiError is an error wrapping multiple validation errors
// returned by GoodsListResponse.ValidateAll() if the designated constraints
// aren't met.
type GoodsListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsListResponseMultiError) AllErrors() []error { return m }

// GoodsListResponseValidationError is the validation error returned by
// GoodsListResponse.Validate if the designated constraints aren't met.
type GoodsListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsListResponseValidationError) ErrorName() string {
	return "GoodsListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsListResponseValidationError{}

// Validate checks the field values on GoodsListData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GoodsListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsListData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GoodsListDataMultiError, or
// nil if none found.
func (m *GoodsListData) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GoodsListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GoodsListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GoodsListDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GoodsListDataMultiError(errors)
	}

	return nil
}

// GoodsListDataMultiError is an error wrapping multiple validation errors
// returned by GoodsListData.ValidateAll() if the designated constraints
// aren't met.
type GoodsListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsListDataMultiError) AllErrors() []error { return m }

// GoodsListDataValidationError is the validation error returned by
// GoodsListData.Validate if the designated constraints aren't met.
type GoodsListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsListDataValidationError) ErrorName() string { return "GoodsListDataValidationError" }

// Error satisfies the builtin error interface
func (e GoodsListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsListDataValidationError{}

// Validate checks the field values on GoodsChangeSubscribeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeSubscribeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeRequestMultiError, or nil if none found.
func (m *GoodsChangeSubscribeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for GoodsType

	// no validation rules for GoodsNo

	// no validation rules for Token

	// no validation rules for NotifyUrl

	if len(errors) > 0 {
		return GoodsChangeSubscribeRequestMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeRequestMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeSubscribeRequest.ValidateAll() if
// the designated constraints aren't met.
type GoodsChangeSubscribeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeRequestMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeRequestValidationError is the validation error returned
// by GoodsChangeSubscribeRequest.Validate if the designated constraints
// aren't met.
type GoodsChangeSubscribeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeRequestValidationError) ErrorName() string {
	return "GoodsChangeSubscribeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeRequestValidationError{}

// Validate checks the field values on GoodsChangeSubscribeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeSubscribeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeResponseMultiError, or nil if none found.
func (m *GoodsChangeSubscribeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return GoodsChangeSubscribeResponseMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeResponseMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeSubscribeResponse.ValidateAll() if
// the designated constraints aren't met.
type GoodsChangeSubscribeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeResponseMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeResponseValidationError is the validation error returned
// by GoodsChangeSubscribeResponse.Validate if the designated constraints
// aren't met.
type GoodsChangeSubscribeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeResponseValidationError) ErrorName() string {
	return "GoodsChangeSubscribeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeResponseValidationError{}

// Validate checks the field values on GoodsChangeUnsubscribeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeUnsubscribeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeUnsubscribeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GoodsChangeUnsubscribeRequestMultiError, or nil if none found.
func (m *GoodsChangeUnsubscribeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeUnsubscribeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for GoodsType

	// no validation rules for GoodsNo

	// no validation rules for Token

	if len(errors) > 0 {
		return GoodsChangeUnsubscribeRequestMultiError(errors)
	}

	return nil
}

// GoodsChangeUnsubscribeRequestMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeUnsubscribeRequest.ValidateAll()
// if the designated constraints aren't met.
type GoodsChangeUnsubscribeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeUnsubscribeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeUnsubscribeRequestMultiError) AllErrors() []error { return m }

// GoodsChangeUnsubscribeRequestValidationError is the validation error
// returned by GoodsChangeUnsubscribeRequest.Validate if the designated
// constraints aren't met.
type GoodsChangeUnsubscribeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeUnsubscribeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeUnsubscribeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeUnsubscribeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeUnsubscribeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeUnsubscribeRequestValidationError) ErrorName() string {
	return "GoodsChangeUnsubscribeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeUnsubscribeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeUnsubscribeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeUnsubscribeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeUnsubscribeRequestValidationError{}

// Validate checks the field values on GoodsChangeUnsubscribeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeUnsubscribeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeUnsubscribeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GoodsChangeUnsubscribeResponseMultiError, or nil if none found.
func (m *GoodsChangeUnsubscribeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeUnsubscribeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return GoodsChangeUnsubscribeResponseMultiError(errors)
	}

	return nil
}

// GoodsChangeUnsubscribeResponseMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeUnsubscribeResponse.ValidateAll()
// if the designated constraints aren't met.
type GoodsChangeUnsubscribeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeUnsubscribeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeUnsubscribeResponseMultiError) AllErrors() []error { return m }

// GoodsChangeUnsubscribeResponseValidationError is the validation error
// returned by GoodsChangeUnsubscribeResponse.Validate if the designated
// constraints aren't met.
type GoodsChangeUnsubscribeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeUnsubscribeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeUnsubscribeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeUnsubscribeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeUnsubscribeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeUnsubscribeResponseValidationError) ErrorName() string {
	return "GoodsChangeUnsubscribeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeUnsubscribeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeUnsubscribeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeUnsubscribeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeUnsubscribeResponseValidationError{}

// Validate checks the field values on GoodsChangeSubscribeListBody with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeSubscribeListBody) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeListBody with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeListBodyMultiError, or nil if none found.
func (m *GoodsChangeSubscribeListBody) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeListBody) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GoodsType

	// no validation rules for GoodsNo

	// no validation rules for PageNo

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GoodsChangeSubscribeListBodyMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeListBodyMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeSubscribeListBody.ValidateAll() if
// the designated constraints aren't met.
type GoodsChangeSubscribeListBodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeListBodyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeListBodyMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeListBodyValidationError is the validation error returned
// by GoodsChangeSubscribeListBody.Validate if the designated constraints
// aren't met.
type GoodsChangeSubscribeListBodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeListBodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeListBodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeListBodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeListBodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeListBodyValidationError) ErrorName() string {
	return "GoodsChangeSubscribeListBodyValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeListBodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeListBody.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeListBodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeListBodyValidationError{}

// Validate checks the field values on GoodsChangeSubscribeListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeSubscribeListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeListRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeListRequestMultiError, or nil if none found.
func (m *GoodsChangeSubscribeListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for GoodsType

	// no validation rules for GoodsNo

	// no validation rules for PageNo

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GoodsChangeSubscribeListRequestMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeListRequestMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeSubscribeListRequest.ValidateAll()
// if the designated constraints aren't met.
type GoodsChangeSubscribeListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeListRequestMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeListRequestValidationError is the validation error
// returned by GoodsChangeSubscribeListRequest.Validate if the designated
// constraints aren't met.
type GoodsChangeSubscribeListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeListRequestValidationError) ErrorName() string {
	return "GoodsChangeSubscribeListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeListRequestValidationError{}

// Validate checks the field values on GoodsChangeSubscribeListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GoodsChangeSubscribeListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeListResponseMultiError, or nil if none found.
func (m *GoodsChangeSubscribeListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GoodsChangeSubscribeListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GoodsChangeSubscribeListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GoodsChangeSubscribeListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GoodsChangeSubscribeListResponseMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeListResponseMultiError is an error wrapping multiple
// validation errors returned by
// GoodsChangeSubscribeListResponse.ValidateAll() if the designated
// constraints aren't met.
type GoodsChangeSubscribeListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeListResponseMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeListResponseValidationError is the validation error
// returned by GoodsChangeSubscribeListResponse.Validate if the designated
// constraints aren't met.
type GoodsChangeSubscribeListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeListResponseValidationError) ErrorName() string {
	return "GoodsChangeSubscribeListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeListResponseValidationError{}

// Validate checks the field values on GoodsChangeSubscribeListData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeSubscribeListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeListData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeListDataMultiError, or nil if none found.
func (m *GoodsChangeSubscribeListData) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GoodsChangeSubscribeListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GoodsChangeSubscribeListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GoodsChangeSubscribeListDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GoodsChangeSubscribeListDataMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeListDataMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeSubscribeListData.ValidateAll() if
// the designated constraints aren't met.
type GoodsChangeSubscribeListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeListDataMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeListDataValidationError is the validation error returned
// by GoodsChangeSubscribeListData.Validate if the designated constraints
// aren't met.
type GoodsChangeSubscribeListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeListDataValidationError) ErrorName() string {
	return "GoodsChangeSubscribeListDataValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeListDataValidationError{}

// Validate checks the field values on GoodsChangeSubscribeListItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsChangeSubscribeListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsChangeSubscribeListItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsChangeSubscribeListItemMultiError, or nil if none found.
func (m *GoodsChangeSubscribeListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsChangeSubscribeListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GoodsType

	// no validation rules for GoodsNo

	// no validation rules for SubscribeTime

	// no validation rules for Token

	// no validation rules for NotifyUrl

	if len(errors) > 0 {
		return GoodsChangeSubscribeListItemMultiError(errors)
	}

	return nil
}

// GoodsChangeSubscribeListItemMultiError is an error wrapping multiple
// validation errors returned by GoodsChangeSubscribeListItem.ValidateAll() if
// the designated constraints aren't met.
type GoodsChangeSubscribeListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsChangeSubscribeListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsChangeSubscribeListItemMultiError) AllErrors() []error { return m }

// GoodsChangeSubscribeListItemValidationError is the validation error returned
// by GoodsChangeSubscribeListItem.Validate if the designated constraints
// aren't met.
type GoodsChangeSubscribeListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsChangeSubscribeListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsChangeSubscribeListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsChangeSubscribeListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsChangeSubscribeListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsChangeSubscribeListItemValidationError) ErrorName() string {
	return "GoodsChangeSubscribeListItemValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsChangeSubscribeListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsChangeSubscribeListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsChangeSubscribeListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsChangeSubscribeListItemValidationError{}

// Validate checks the field values on GoodsCallbackItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GoodsCallbackItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsCallbackItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsCallbackItemMultiError, or nil if none found.
func (m *GoodsCallbackItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsCallbackItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GoodsNo

	// no validation rules for GoodsType

	// no validation rules for Price

	// no validation rules for Stock

	// no validation rules for Status

	// no validation rules for ChangeTime

	if len(errors) > 0 {
		return GoodsCallbackItemMultiError(errors)
	}

	return nil
}

// GoodsCallbackItemMultiError is an error wrapping multiple validation errors
// returned by GoodsCallbackItem.ValidateAll() if the designated constraints
// aren't met.
type GoodsCallbackItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsCallbackItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsCallbackItemMultiError) AllErrors() []error { return m }

// GoodsCallbackItemValidationError is the validation error returned by
// GoodsCallbackItem.Validate if the designated constraints aren't met.
type GoodsCallbackItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsCallbackItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsCallbackItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsCallbackItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsCallbackItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsCallbackItemValidationError) ErrorName() string {
	return "GoodsCallbackItemValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsCallbackItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsCallbackItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsCallbackItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsCallbackItemValidationError{}

// Validate checks the field values on GoodsCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsCallbackRequestMultiError, or nil if none found.
func (m *GoodsCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GoodsCallbackRequestValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GoodsCallbackRequestValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GoodsCallbackRequestValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GoodsCallbackRequestMultiError(errors)
	}

	return nil
}

// GoodsCallbackRequestMultiError is an error wrapping multiple validation
// errors returned by GoodsCallbackRequest.ValidateAll() if the designated
// constraints aren't met.
type GoodsCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsCallbackRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsCallbackRequestMultiError) AllErrors() []error { return m }

// GoodsCallbackRequestValidationError is the validation error returned by
// GoodsCallbackRequest.Validate if the designated constraints aren't met.
type GoodsCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsCallbackRequestValidationError) ErrorName() string {
	return "GoodsCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsCallbackRequestValidationError{}

// Validate checks the field values on GoodsCallbackResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GoodsCallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GoodsCallbackResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GoodsCallbackResponseMultiError, or nil if none found.
func (m *GoodsCallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GoodsCallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return GoodsCallbackResponseMultiError(errors)
	}

	return nil
}

// GoodsCallbackResponseMultiError is an error wrapping multiple validation
// errors returned by GoodsCallbackResponse.ValidateAll() if the designated
// constraints aren't met.
type GoodsCallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoodsCallbackResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoodsCallbackResponseMultiError) AllErrors() []error { return m }

// GoodsCallbackResponseValidationError is the validation error returned by
// GoodsCallbackResponse.Validate if the designated constraints aren't met.
type GoodsCallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoodsCallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoodsCallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoodsCallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoodsCallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoodsCallbackResponseValidationError) ErrorName() string {
	return "GoodsCallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GoodsCallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoodsCallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoodsCallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoodsCallbackResponseValidationError{}

// Validate checks the field values on BizContent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BizContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BizContent with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BizContentMultiError, or
// nil if none found.
func (m *BizContent) ValidateAll() error {
	return m.validate(true)
}

func (m *BizContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Account

	// no validation rules for GameName

	// no validation rules for GameRole

	// no validation rules for GameArea

	// no validation rules for GameServer

	// no validation rules for BuyerIp

	// no validation rules for BuyerArea

	if len(errors) > 0 {
		return BizContentMultiError(errors)
	}

	return nil
}

// BizContentMultiError is an error wrapping multiple validation errors
// returned by BizContent.ValidateAll() if the designated constraints aren't met.
type BizContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BizContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BizContentMultiError) AllErrors() []error { return m }

// BizContentValidationError is the validation error returned by
// BizContent.Validate if the designated constraints aren't met.
type BizContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BizContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BizContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BizContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BizContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BizContentValidationError) ErrorName() string { return "BizContentValidationError" }

// Error satisfies the builtin error interface
func (e BizContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBizContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BizContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BizContentValidationError{}

// Validate checks the field values on CardItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardItemMultiError, or nil
// if none found.
func (m *CardItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CardItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardNo

	// no validation rules for CardPwd

	if len(errors) > 0 {
		return CardItemMultiError(errors)
	}

	return nil
}

// CardItemMultiError is an error wrapping multiple validation errors returned
// by CardItem.ValidateAll() if the designated constraints aren't met.
type CardItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardItemMultiError) AllErrors() []error { return m }

// CardItemValidationError is the validation error returned by
// CardItem.Validate if the designated constraints aren't met.
type CardItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardItemValidationError) ErrorName() string { return "CardItemValidationError" }

// Error satisfies the builtin error interface
func (e CardItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardItemValidationError{}

// Validate checks the field values on CreateRechargeOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderRequestMultiError, or nil if none found.
func (m *CreateRechargeOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for OrderNo

	// no validation rules for GoodsNo

	if all {
		switch v := interface{}(m.GetBizContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderRequestValidationError{
					field:  "BizContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderRequestValidationError{
					field:  "BizContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBizContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderRequestValidationError{
				field:  "BizContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BuyQuantity

	// no validation rules for MaxAmount

	// no validation rules for NotifyUrl

	// no validation rules for BizOrderNo

	if len(errors) > 0 {
		return CreateRechargeOrderRequestMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateRechargeOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderRequestMultiError) AllErrors() []error { return m }

// CreateRechargeOrderRequestValidationError is the validation error returned
// by CreateRechargeOrderRequest.Validate if the designated constraints aren't met.
type CreateRechargeOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderRequestValidationError) ErrorName() string {
	return "CreateRechargeOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderRequestValidationError{}

// Validate checks the field values on CreateRechargeOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderResponseMultiError, or nil if none found.
func (m *CreateRechargeOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRechargeOrderResponseMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateRechargeOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderResponseMultiError) AllErrors() []error { return m }

// CreateRechargeOrderResponseValidationError is the validation error returned
// by CreateRechargeOrderResponse.Validate if the designated constraints
// aren't met.
type CreateRechargeOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderResponseValidationError) ErrorName() string {
	return "CreateRechargeOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderResponseValidationError{}

// Validate checks the field values on RechargeOrderData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RechargeOrderData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeOrderData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeOrderDataMultiError, or nil if none found.
func (m *RechargeOrderData) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeOrderData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderNo

	// no validation rules for OutOrderNo

	// no validation rules for OrderStatus

	// no validation rules for OrderAmount

	// no validation rules for GoodsName

	// no validation rules for OrderTime

	// no validation rules for EndTime

	// no validation rules for Remark

	if len(errors) > 0 {
		return RechargeOrderDataMultiError(errors)
	}

	return nil
}

// RechargeOrderDataMultiError is an error wrapping multiple validation errors
// returned by RechargeOrderData.ValidateAll() if the designated constraints
// aren't met.
type RechargeOrderDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeOrderDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeOrderDataMultiError) AllErrors() []error { return m }

// RechargeOrderDataValidationError is the validation error returned by
// RechargeOrderData.Validate if the designated constraints aren't met.
type RechargeOrderDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeOrderDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeOrderDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeOrderDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeOrderDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeOrderDataValidationError) ErrorName() string {
	return "RechargeOrderDataValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeOrderDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeOrderData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeOrderDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeOrderDataValidationError{}

// Validate checks the field values on CreateCardOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCardOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCardOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCardOrderRequestMultiError, or nil if none found.
func (m *CreateCardOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCardOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for OrderNo

	// no validation rules for GoodsNo

	// no validation rules for BuyQuantity

	// no validation rules for MaxAmount

	// no validation rules for NotifyUrl

	// no validation rules for BizOrderNo

	if len(errors) > 0 {
		return CreateCardOrderRequestMultiError(errors)
	}

	return nil
}

// CreateCardOrderRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCardOrderRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCardOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCardOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCardOrderRequestMultiError) AllErrors() []error { return m }

// CreateCardOrderRequestValidationError is the validation error returned by
// CreateCardOrderRequest.Validate if the designated constraints aren't met.
type CreateCardOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCardOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCardOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCardOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCardOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCardOrderRequestValidationError) ErrorName() string {
	return "CreateCardOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCardOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCardOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCardOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCardOrderRequestValidationError{}

// Validate checks the field values on CreateCardOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCardOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCardOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCardOrderResponseMultiError, or nil if none found.
func (m *CreateCardOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCardOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCardOrderResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCardOrderResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCardOrderResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCardOrderResponseMultiError(errors)
	}

	return nil
}

// CreateCardOrderResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCardOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCardOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCardOrderResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCardOrderResponseMultiError) AllErrors() []error { return m }

// CreateCardOrderResponseValidationError is the validation error returned by
// CreateCardOrderResponse.Validate if the designated constraints aren't met.
type CreateCardOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCardOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCardOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCardOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCardOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCardOrderResponseValidationError) ErrorName() string {
	return "CreateCardOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCardOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCardOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCardOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCardOrderResponseValidationError{}

// Validate checks the field values on CardOrderData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardOrderData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardOrderData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardOrderDataMultiError, or
// nil if none found.
func (m *CardOrderData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardOrderData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderNo

	// no validation rules for OutOrderNo

	// no validation rules for OrderStatus

	// no validation rules for OrderAmount

	// no validation rules for OrderTime

	// no validation rules for EndTime

	for idx, item := range m.GetCardItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardOrderDataValidationError{
						field:  fmt.Sprintf("CardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardOrderDataValidationError{
						field:  fmt.Sprintf("CardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardOrderDataValidationError{
					field:  fmt.Sprintf("CardItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Remark

	if len(errors) > 0 {
		return CardOrderDataMultiError(errors)
	}

	return nil
}

// CardOrderDataMultiError is an error wrapping multiple validation errors
// returned by CardOrderData.ValidateAll() if the designated constraints
// aren't met.
type CardOrderDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardOrderDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardOrderDataMultiError) AllErrors() []error { return m }

// CardOrderDataValidationError is the validation error returned by
// CardOrderData.Validate if the designated constraints aren't met.
type CardOrderDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardOrderDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardOrderDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardOrderDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardOrderDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardOrderDataValidationError) ErrorName() string { return "CardOrderDataValidationError" }

// Error satisfies the builtin error interface
func (e CardOrderDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardOrderData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardOrderDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardOrderDataValidationError{}

// Validate checks the field values on OrderDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderDetailRequestMultiError, or nil if none found.
func (m *OrderDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MchId

	// no validation rules for Timestamp

	// no validation rules for Sign

	// no validation rules for OrderType

	// no validation rules for OrderNo

	// no validation rules for OutOrderNo

	if len(errors) > 0 {
		return OrderDetailRequestMultiError(errors)
	}

	return nil
}

// OrderDetailRequestMultiError is an error wrapping multiple validation errors
// returned by OrderDetailRequest.ValidateAll() if the designated constraints
// aren't met.
type OrderDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderDetailRequestMultiError) AllErrors() []error { return m }

// OrderDetailRequestValidationError is the validation error returned by
// OrderDetailRequest.Validate if the designated constraints aren't met.
type OrderDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderDetailRequestValidationError) ErrorName() string {
	return "OrderDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OrderDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderDetailRequestValidationError{}

// Validate checks the field values on OrderDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderDetailResponseMultiError, or nil if none found.
func (m *OrderDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrderDetailResponseMultiError(errors)
	}

	return nil
}

// OrderDetailResponseMultiError is an error wrapping multiple validation
// errors returned by OrderDetailResponse.ValidateAll() if the designated
// constraints aren't met.
type OrderDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderDetailResponseMultiError) AllErrors() []error { return m }

// OrderDetailResponseValidationError is the validation error returned by
// OrderDetailResponse.Validate if the designated constraints aren't met.
type OrderDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderDetailResponseValidationError) ErrorName() string {
	return "OrderDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OrderDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderDetailResponseValidationError{}

// Validate checks the field values on OrderDetailData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OrderDetailData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderDetailData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderDetailDataMultiError, or nil if none found.
func (m *OrderDetailData) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderDetailData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderType

	// no validation rules for OrderNo

	// no validation rules for OutOrderNo

	// no validation rules for OrderStatus

	// no validation rules for OrderAmount

	// no validation rules for GoodsNo

	// no validation rules for GoodsName

	// no validation rules for BuyQuantity

	// no validation rules for OrderTime

	// no validation rules for EndTime

	if all {
		switch v := interface{}(m.GetBizContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderDetailDataValidationError{
					field:  "BizContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderDetailDataValidationError{
					field:  "BizContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBizContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderDetailDataValidationError{
				field:  "BizContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCardItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OrderDetailDataValidationError{
						field:  fmt.Sprintf("CardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OrderDetailDataValidationError{
						field:  fmt.Sprintf("CardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OrderDetailDataValidationError{
					field:  fmt.Sprintf("CardItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Remark

	if len(errors) > 0 {
		return OrderDetailDataMultiError(errors)
	}

	return nil
}

// OrderDetailDataMultiError is an error wrapping multiple validation errors
// returned by OrderDetailData.ValidateAll() if the designated constraints
// aren't met.
type OrderDetailDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderDetailDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderDetailDataMultiError) AllErrors() []error { return m }

// OrderDetailDataValidationError is the validation error returned by
// OrderDetailData.Validate if the designated constraints aren't met.
type OrderDetailDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderDetailDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderDetailDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderDetailDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderDetailDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderDetailDataValidationError) ErrorName() string { return "OrderDetailDataValidationError" }

// Error satisfies the builtin error interface
func (e OrderDetailDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderDetailData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderDetailDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderDetailDataValidationError{}

// Validate checks the field values on OrderCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderCallbackRequestMultiError, or nil if none found.
func (m *OrderCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for OrderType

	// no validation rules for OrderNo

	// no validation rules for OutOrderNo

	// no validation rules for OrderStatus

	// no validation rules for EndTime

	for idx, item := range m.GetCardItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OrderCallbackRequestValidationError{
						field:  fmt.Sprintf("CardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OrderCallbackRequestValidationError{
						field:  fmt.Sprintf("CardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OrderCallbackRequestValidationError{
					field:  fmt.Sprintf("CardItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Remark

	if len(errors) > 0 {
		return OrderCallbackRequestMultiError(errors)
	}

	return nil
}

// OrderCallbackRequestMultiError is an error wrapping multiple validation
// errors returned by OrderCallbackRequest.ValidateAll() if the designated
// constraints aren't met.
type OrderCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderCallbackRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderCallbackRequestMultiError) AllErrors() []error { return m }

// OrderCallbackRequestValidationError is the validation error returned by
// OrderCallbackRequest.Validate if the designated constraints aren't met.
type OrderCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderCallbackRequestValidationError) ErrorName() string {
	return "OrderCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OrderCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderCallbackRequestValidationError{}

// Validate checks the field values on OrderCallbackResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderCallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderCallbackResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderCallbackResponseMultiError, or nil if none found.
func (m *OrderCallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderCallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return OrderCallbackResponseMultiError(errors)
	}

	return nil
}

// OrderCallbackResponseMultiError is an error wrapping multiple validation
// errors returned by OrderCallbackResponse.ValidateAll() if the designated
// constraints aren't met.
type OrderCallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderCallbackResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderCallbackResponseMultiError) AllErrors() []error { return m }

// OrderCallbackResponseValidationError is the validation error returned by
// OrderCallbackResponse.Validate if the designated constraints aren't met.
type OrderCallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderCallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderCallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderCallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderCallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderCallbackResponseValidationError) ErrorName() string {
	return "OrderCallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OrderCallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderCallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderCallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderCallbackResponseValidationError{}
