// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: authentication/service/v1/user_credential.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 身份类型
type IdentityType int32

const (
	IdentityType_USERNAME    IdentityType = 0   // 用户名
	IdentityType_USERID      IdentityType = 1   // 用户ID
	IdentityType_EMAIL       IdentityType = 2   // 邮箱地址
	IdentityType_PHONE       IdentityType = 3   // 手机号
	IdentityType_WECHAT      IdentityType = 100 // 微信
	IdentityType_QQ          IdentityType = 101 // QQ
	IdentityType_WEIBO       IdentityType = 102 // 微博
	IdentityType_DOUYIN      IdentityType = 103 // 抖音
	IdentityType_KUAISHOU    IdentityType = 104 // 快手
	IdentityType_BAIDU       IdentityType = 105 // 百度
	IdentityType_ALIPAY      IdentityType = 106 // 支付宝
	IdentityType_TAOBAO      IdentityType = 107 // 淘宝
	IdentityType_JD          IdentityType = 108 // 京东
	IdentityType_MEITUAN     IdentityType = 109 // 美团
	IdentityType_DINGTALK    IdentityType = 110 // 钉钉
	IdentityType_BILIBILI    IdentityType = 111 // 哔哩哔哩
	IdentityType_XIAOHONGSHU IdentityType = 112 // 小红书
	IdentityType_GOOGLE      IdentityType = 200 // Google
	IdentityType_FACEBOOK    IdentityType = 201 // Facebook
	IdentityType_APPLE       IdentityType = 202 // Apple
	IdentityType_TELEGRAM    IdentityType = 203 // Telegram
	IdentityType_TWITTER     IdentityType = 204 // Twitter
	IdentityType_LINKEDIN    IdentityType = 205 // LinkedIn
	IdentityType_GITHUB      IdentityType = 206 // GitHub
	IdentityType_MICROSOFT   IdentityType = 207 // Microsoft
	IdentityType_DISCORD     IdentityType = 208 // Discord
	IdentityType_SLACK       IdentityType = 209 // Slack
	IdentityType_INSTAGRAM   IdentityType = 210 // Instagram
	IdentityType_TIKTOK      IdentityType = 211 // TikTok
	IdentityType_REDDIT      IdentityType = 212 // Reddit
	IdentityType_YOUTUBE     IdentityType = 213 // YouTube
	IdentityType_SPOTIFY     IdentityType = 214 // Spotify
	IdentityType_PINTEREST   IdentityType = 215 // Pinterest
	IdentityType_SNAPCHAT    IdentityType = 216 // Snapchat
	IdentityType_TUMBLR      IdentityType = 217 // Tumblr
	IdentityType_YAHOO       IdentityType = 218 // Yahoo
	IdentityType_WHATSAPP    IdentityType = 219 // WhatsApp
	IdentityType_LINE        IdentityType = 220 // LINE
)

// Enum value maps for IdentityType.
var (
	IdentityType_name = map[int32]string{
		0:   "USERNAME",
		1:   "USERID",
		2:   "EMAIL",
		3:   "PHONE",
		100: "WECHAT",
		101: "QQ",
		102: "WEIBO",
		103: "DOUYIN",
		104: "KUAISHOU",
		105: "BAIDU",
		106: "ALIPAY",
		107: "TAOBAO",
		108: "JD",
		109: "MEITUAN",
		110: "DINGTALK",
		111: "BILIBILI",
		112: "XIAOHONGSHU",
		200: "GOOGLE",
		201: "FACEBOOK",
		202: "APPLE",
		203: "TELEGRAM",
		204: "TWITTER",
		205: "LINKEDIN",
		206: "GITHUB",
		207: "MICROSOFT",
		208: "DISCORD",
		209: "SLACK",
		210: "INSTAGRAM",
		211: "TIKTOK",
		212: "REDDIT",
		213: "YOUTUBE",
		214: "SPOTIFY",
		215: "PINTEREST",
		216: "SNAPCHAT",
		217: "TUMBLR",
		218: "YAHOO",
		219: "WHATSAPP",
		220: "LINE",
	}
	IdentityType_value = map[string]int32{
		"USERNAME":    0,
		"USERID":      1,
		"EMAIL":       2,
		"PHONE":       3,
		"WECHAT":      100,
		"QQ":          101,
		"WEIBO":       102,
		"DOUYIN":      103,
		"KUAISHOU":    104,
		"BAIDU":       105,
		"ALIPAY":      106,
		"TAOBAO":      107,
		"JD":          108,
		"MEITUAN":     109,
		"DINGTALK":    110,
		"BILIBILI":    111,
		"XIAOHONGSHU": 112,
		"GOOGLE":      200,
		"FACEBOOK":    201,
		"APPLE":       202,
		"TELEGRAM":    203,
		"TWITTER":     204,
		"LINKEDIN":    205,
		"GITHUB":      206,
		"MICROSOFT":   207,
		"DISCORD":     208,
		"SLACK":       209,
		"INSTAGRAM":   210,
		"TIKTOK":      211,
		"REDDIT":      212,
		"YOUTUBE":     213,
		"SPOTIFY":     214,
		"PINTEREST":   215,
		"SNAPCHAT":    216,
		"TUMBLR":      217,
		"YAHOO":       218,
		"WHATSAPP":    219,
		"LINE":        220,
	}
)

func (x IdentityType) Enum() *IdentityType {
	p := new(IdentityType)
	*p = x
	return p
}

func (x IdentityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentityType) Descriptor() protoreflect.EnumDescriptor {
	return file_authentication_service_v1_user_credential_proto_enumTypes[0].Descriptor()
}

func (IdentityType) Type() protoreflect.EnumType {
	return &file_authentication_service_v1_user_credential_proto_enumTypes[0]
}

func (x IdentityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentityType.Descriptor instead.
func (IdentityType) EnumDescriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{0}
}

// 凭证类型
type CredentialType int32

const (
	CredentialType_PASSWORD_HASH               CredentialType = 0  // 加密密码
	CredentialType_ACCESS_TOKEN                CredentialType = 1  // 访问令牌
	CredentialType_REFRESH_TOKEN               CredentialType = 2  // 刷新令牌
	CredentialType_EMAIL_VERIFICATION_CODE     CredentialType = 3  // 邮箱验证码
	CredentialType_PHONE_VERIFICATION_CODE     CredentialType = 4  // 手机验证码
	CredentialType_OAUTH_TOKEN                 CredentialType = 5  // OAuth令牌
	CredentialType_API_KEY                     CredentialType = 6  // API密钥
	CredentialType_SSO_TOKEN                   CredentialType = 7  // 单点登录令牌
	CredentialType_JWT                         CredentialType = 8  // JSON Web Token
	CredentialType_SAML_ASSERTION              CredentialType = 9  // SAML断言
	CredentialType_OPENID_CONNECT_ID_TOKEN     CredentialType = 10 // OpenID Connect ID令牌
	CredentialType_SESSION_COOKIE              CredentialType = 11 // 会话Cookie
	CredentialType_TEMPORARY_CREDENTIAL        CredentialType = 12 // 临时凭证
	CredentialType_CUSTOM_CREDENTIAL           CredentialType = 13 // 自定义凭证类型
	CredentialType_BIOMETRIC_DATA              CredentialType = 14 // 生物识别数据（如指纹、面部识别等）
	CredentialType_SECURITY_KEY                CredentialType = 15 // 安全密钥（如FIDO2/WebAuthn等）
	CredentialType_OTP                         CredentialType = 16 // 一次性密码（One-Time Password）
	CredentialType_SMART_CARD                  CredentialType = 17 // 智能卡凭证
	CredentialType_CRYPTOGRAPHIC_CERTIFICATE   CredentialType = 18 // 加密证书
	CredentialType_BIOMETRIC_TOKEN             CredentialType = 19 // 生物识别令牌
	CredentialType_DEVICE_FINGERPRINT          CredentialType = 20 // 设备指纹
	CredentialType_HARDWARE_TOKEN              CredentialType = 21 // 硬件令牌
	CredentialType_SOFTWARE_TOKEN              CredentialType = 22 // 软件令牌
	CredentialType_SECURITY_QUESTION           CredentialType = 23 // 安全问题答案
	CredentialType_SECURITY_PIN                CredentialType = 24 // 安全PIN码
	CredentialType_TWO_FACTOR_AUTHENTICATION   CredentialType = 25 // 双因素认证
	CredentialType_MULTI_FACTOR_AUTHENTICATION CredentialType = 26 // 多因素认证
	CredentialType_PASSWORDLESS_AUTHENTICATION CredentialType = 27 // 无密码认证
	CredentialType_SOCIAL_LOGIN_TOKEN          CredentialType = 28 // 社交登录令牌
	CredentialType_SSO_SESSION                 CredentialType = 29 // 单点登录会话
	CredentialType_API_SECRET                  CredentialType = 30 // API密钥
	CredentialType_CUSTOM_TOKEN                CredentialType = 31 // 自定义令牌
	CredentialType_OAUTH2_CLIENT_CREDENTIALS   CredentialType = 32 // OAuth2客户端凭证
	CredentialType_OAUTH2_AUTHORIZATION_CODE   CredentialType = 33 // OAuth2授权码
	CredentialType_OAUTH2_IMPLICIT_GRANT       CredentialType = 34 // OAuth2隐式授权
	CredentialType_OAUTH2_PASSWORD_GRANT       CredentialType = 35 // OAuth2密码授权
	CredentialType_OAUTH2_REFRESH_GRANT        CredentialType = 36 // OAuth2刷新授权
)

// Enum value maps for CredentialType.
var (
	CredentialType_name = map[int32]string{
		0:  "PASSWORD_HASH",
		1:  "ACCESS_TOKEN",
		2:  "REFRESH_TOKEN",
		3:  "EMAIL_VERIFICATION_CODE",
		4:  "PHONE_VERIFICATION_CODE",
		5:  "OAUTH_TOKEN",
		6:  "API_KEY",
		7:  "SSO_TOKEN",
		8:  "JWT",
		9:  "SAML_ASSERTION",
		10: "OPENID_CONNECT_ID_TOKEN",
		11: "SESSION_COOKIE",
		12: "TEMPORARY_CREDENTIAL",
		13: "CUSTOM_CREDENTIAL",
		14: "BIOMETRIC_DATA",
		15: "SECURITY_KEY",
		16: "OTP",
		17: "SMART_CARD",
		18: "CRYPTOGRAPHIC_CERTIFICATE",
		19: "BIOMETRIC_TOKEN",
		20: "DEVICE_FINGERPRINT",
		21: "HARDWARE_TOKEN",
		22: "SOFTWARE_TOKEN",
		23: "SECURITY_QUESTION",
		24: "SECURITY_PIN",
		25: "TWO_FACTOR_AUTHENTICATION",
		26: "MULTI_FACTOR_AUTHENTICATION",
		27: "PASSWORDLESS_AUTHENTICATION",
		28: "SOCIAL_LOGIN_TOKEN",
		29: "SSO_SESSION",
		30: "API_SECRET",
		31: "CUSTOM_TOKEN",
		32: "OAUTH2_CLIENT_CREDENTIALS",
		33: "OAUTH2_AUTHORIZATION_CODE",
		34: "OAUTH2_IMPLICIT_GRANT",
		35: "OAUTH2_PASSWORD_GRANT",
		36: "OAUTH2_REFRESH_GRANT",
	}
	CredentialType_value = map[string]int32{
		"PASSWORD_HASH":               0,
		"ACCESS_TOKEN":                1,
		"REFRESH_TOKEN":               2,
		"EMAIL_VERIFICATION_CODE":     3,
		"PHONE_VERIFICATION_CODE":     4,
		"OAUTH_TOKEN":                 5,
		"API_KEY":                     6,
		"SSO_TOKEN":                   7,
		"JWT":                         8,
		"SAML_ASSERTION":              9,
		"OPENID_CONNECT_ID_TOKEN":     10,
		"SESSION_COOKIE":              11,
		"TEMPORARY_CREDENTIAL":        12,
		"CUSTOM_CREDENTIAL":           13,
		"BIOMETRIC_DATA":              14,
		"SECURITY_KEY":                15,
		"OTP":                         16,
		"SMART_CARD":                  17,
		"CRYPTOGRAPHIC_CERTIFICATE":   18,
		"BIOMETRIC_TOKEN":             19,
		"DEVICE_FINGERPRINT":          20,
		"HARDWARE_TOKEN":              21,
		"SOFTWARE_TOKEN":              22,
		"SECURITY_QUESTION":           23,
		"SECURITY_PIN":                24,
		"TWO_FACTOR_AUTHENTICATION":   25,
		"MULTI_FACTOR_AUTHENTICATION": 26,
		"PASSWORDLESS_AUTHENTICATION": 27,
		"SOCIAL_LOGIN_TOKEN":          28,
		"SSO_SESSION":                 29,
		"API_SECRET":                  30,
		"CUSTOM_TOKEN":                31,
		"OAUTH2_CLIENT_CREDENTIALS":   32,
		"OAUTH2_AUTHORIZATION_CODE":   33,
		"OAUTH2_IMPLICIT_GRANT":       34,
		"OAUTH2_PASSWORD_GRANT":       35,
		"OAUTH2_REFRESH_GRANT":        36,
	}
)

func (x CredentialType) Enum() *CredentialType {
	p := new(CredentialType)
	*p = x
	return p
}

func (x CredentialType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CredentialType) Descriptor() protoreflect.EnumDescriptor {
	return file_authentication_service_v1_user_credential_proto_enumTypes[1].Descriptor()
}

func (CredentialType) Type() protoreflect.EnumType {
	return &file_authentication_service_v1_user_credential_proto_enumTypes[1]
}

func (x CredentialType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CredentialType.Descriptor instead.
func (CredentialType) EnumDescriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{1}
}

// 用户凭证状态
type UserCredentialStatus int32

const (
	UserCredentialStatus_DISABLED   UserCredentialStatus = 0 // 凭证被禁用，用户无法使用该凭证进行认证（如账号被冻结）。
	UserCredentialStatus_ENABLED    UserCredentialStatus = 1 // 凭证有效，用户可正常使用该凭证登录或注册。
	UserCredentialStatus_EXPIRED    UserCredentialStatus = 2 // 凭证已过期（如临时凭证超期）。
	UserCredentialStatus_UNVERIFIED UserCredentialStatus = 3 // 凭证未验证（需用户完成验证流程后才能生效）。
	UserCredentialStatus_REMOVED    UserCredentialStatus = 4 // 凭证已删除（逻辑删除，非物理删除，保留审计记录）。
	UserCredentialStatus_BLOCKED    UserCredentialStatus = 5 // 凭证被锁定（通常因多次错误尝试触发安全机制）。
	UserCredentialStatus_TEMPORARY  UserCredentialStatus = 6 // 临时凭证（仅在特定时间段内有效）。
)

// Enum value maps for UserCredentialStatus.
var (
	UserCredentialStatus_name = map[int32]string{
		0: "DISABLED",
		1: "ENABLED",
		2: "EXPIRED",
		3: "UNVERIFIED",
		4: "REMOVED",
		5: "BLOCKED",
		6: "TEMPORARY",
	}
	UserCredentialStatus_value = map[string]int32{
		"DISABLED":   0,
		"ENABLED":    1,
		"EXPIRED":    2,
		"UNVERIFIED": 3,
		"REMOVED":    4,
		"BLOCKED":    5,
		"TEMPORARY":  6,
	}
)

func (x UserCredentialStatus) Enum() *UserCredentialStatus {
	p := new(UserCredentialStatus)
	*p = x
	return p
}

func (x UserCredentialStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserCredentialStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_authentication_service_v1_user_credential_proto_enumTypes[2].Descriptor()
}

func (UserCredentialStatus) Type() protoreflect.EnumType {
	return &file_authentication_service_v1_user_credential_proto_enumTypes[2]
}

func (x UserCredentialStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserCredentialStatus.Descriptor instead.
func (UserCredentialStatus) EnumDescriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{2}
}

// 用户凭证
type UserCredential struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                    // 主键ID
	UserId         *uint32                `protobuf:"varint,2,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`                                                                        // 关联主表的用户ID
	TenantId       *uint32                `protobuf:"varint,3,opt,name=tenant_id,json=tenantId,proto3,oneof" json:"tenant_id,omitempty"`                                                                  // 租户ID
	IdentityType   *IdentityType          `protobuf:"varint,10,opt,name=identity_type,json=identityType,proto3,enum=authentication.service.v1.IdentityType,oneof" json:"identity_type,omitempty"`         // 认证方式类型
	Identifier     *string                `protobuf:"bytes,11,opt,name=identifier,proto3,oneof" json:"identifier,omitempty"`                                                                              // 身份唯一标识符
	CredentialType *CredentialType        `protobuf:"varint,20,opt,name=credential_type,json=credentialType,proto3,enum=authentication.service.v1.CredentialType,oneof" json:"credential_type,omitempty"` // 凭证类型
	Credential     *string                `protobuf:"bytes,21,opt,name=credential,proto3,oneof" json:"credential,omitempty"`                                                                              // 凭证
	IsPrimary      *bool                  `protobuf:"varint,30,opt,name=is_primary,json=isPrimary,proto3,oneof" json:"is_primary,omitempty"`                                                              // 是否主认证方式
	Status         *UserCredentialStatus  `protobuf:"varint,31,opt,name=status,proto3,enum=authentication.service.v1.UserCredentialStatus,oneof" json:"status,omitempty"`                                 // 凭证状态
	ExtraInfo      *string                `protobuf:"bytes,32,opt,name=extra_info,json=extraInfo,proto3,oneof" json:"extra_info,omitempty"`                                                               // 扩展信息
	CreateBy       *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                                                                // 创建者ID
	UpdateBy       *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                                                                // 更新者ID
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                                                           // 创建时间
	UpdateTime     *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                                                           // 更新时间
	DeleteTime     *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                                                           // 删除时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserCredential) Reset() {
	*x = UserCredential{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserCredential) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCredential) ProtoMessage() {}

func (x *UserCredential) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCredential.ProtoReflect.Descriptor instead.
func (*UserCredential) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{0}
}

func (x *UserCredential) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserCredential) GetUserId() uint32 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

func (x *UserCredential) GetTenantId() uint32 {
	if x != nil && x.TenantId != nil {
		return *x.TenantId
	}
	return 0
}

func (x *UserCredential) GetIdentityType() IdentityType {
	if x != nil && x.IdentityType != nil {
		return *x.IdentityType
	}
	return IdentityType_USERNAME
}

func (x *UserCredential) GetIdentifier() string {
	if x != nil && x.Identifier != nil {
		return *x.Identifier
	}
	return ""
}

func (x *UserCredential) GetCredentialType() CredentialType {
	if x != nil && x.CredentialType != nil {
		return *x.CredentialType
	}
	return CredentialType_PASSWORD_HASH
}

func (x *UserCredential) GetCredential() string {
	if x != nil && x.Credential != nil {
		return *x.Credential
	}
	return ""
}

func (x *UserCredential) GetIsPrimary() bool {
	if x != nil && x.IsPrimary != nil {
		return *x.IsPrimary
	}
	return false
}

func (x *UserCredential) GetStatus() UserCredentialStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return UserCredentialStatus_DISABLED
}

func (x *UserCredential) GetExtraInfo() string {
	if x != nil && x.ExtraInfo != nil {
		return *x.ExtraInfo
	}
	return ""
}

func (x *UserCredential) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *UserCredential) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *UserCredential) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *UserCredential) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *UserCredential) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询列表 - 答复
type ListUserCredentialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*UserCredential      `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserCredentialResponse) Reset() {
	*x = ListUserCredentialResponse{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserCredentialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserCredentialResponse) ProtoMessage() {}

func (x *ListUserCredentialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserCredentialResponse.ProtoReflect.Descriptor instead.
func (*ListUserCredentialResponse) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{1}
}

func (x *ListUserCredentialResponse) GetItems() []*UserCredential {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListUserCredentialResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 更新 - 请求
type UpdateUserCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *UserCredential        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserCredentialRequest) Reset() {
	*x = UpdateUserCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserCredentialRequest) ProtoMessage() {}

func (x *UpdateUserCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserCredentialRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateUserCredentialRequest) GetData() *UserCredential {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateUserCredentialRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateUserCredentialRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 创建 - 请求
type CreateUserCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *UserCredential        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserCredentialRequest) Reset() {
	*x = CreateUserCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserCredentialRequest) ProtoMessage() {}

func (x *CreateUserCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserCredentialRequest.ProtoReflect.Descriptor instead.
func (*CreateUserCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{3}
}

func (x *CreateUserCredentialRequest) GetData() *UserCredential {
	if x != nil {
		return x.Data
	}
	return nil
}

// 删除 - 请求
type DeleteUserCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserCredentialRequest) Reset() {
	*x = DeleteUserCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserCredentialRequest) ProtoMessage() {}

func (x *DeleteUserCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserCredentialRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteUserCredentialRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 查询 - 请求
type GetUserCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserCredentialRequest) Reset() {
	*x = GetUserCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCredentialRequest) ProtoMessage() {}

func (x *GetUserCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCredentialRequest.ProtoReflect.Descriptor instead.
func (*GetUserCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserCredentialRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 查询 - 请求
type GetUserCredentialByIdentifierRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdentityType  IdentityType           `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3,enum=authentication.service.v1.IdentityType" json:"identity_type,omitempty"` // 身份类型
	Identifier    string                 `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`                                                                      // 身份唯一标识符
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserCredentialByIdentifierRequest) Reset() {
	*x = GetUserCredentialByIdentifierRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserCredentialByIdentifierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCredentialByIdentifierRequest) ProtoMessage() {}

func (x *GetUserCredentialByIdentifierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCredentialByIdentifierRequest.ProtoReflect.Descriptor instead.
func (*GetUserCredentialByIdentifierRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserCredentialByIdentifierRequest) GetIdentityType() IdentityType {
	if x != nil {
		return x.IdentityType
	}
	return IdentityType_USERNAME
}

func (x *GetUserCredentialByIdentifierRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

// 验证凭证 - 请求
type VerifyCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdentityType  IdentityType           `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3,enum=authentication.service.v1.IdentityType" json:"identity_type,omitempty"` // 身份类型
	Identifier    string                 `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`                                                                      // 身份唯一标识符
	Credential    string                 `protobuf:"bytes,3,opt,name=credential,proto3" json:"credential,omitempty"`                                                                      // 凭证
	NeedDecrypt   bool                   `protobuf:"varint,4,opt,name=need_decrypt,json=needDecrypt,proto3" json:"need_decrypt,omitempty"`                                                // 是否需要解码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyCredentialRequest) Reset() {
	*x = VerifyCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCredentialRequest) ProtoMessage() {}

func (x *VerifyCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCredentialRequest.ProtoReflect.Descriptor instead.
func (*VerifyCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{7}
}

func (x *VerifyCredentialRequest) GetIdentityType() IdentityType {
	if x != nil {
		return x.IdentityType
	}
	return IdentityType_USERNAME
}

func (x *VerifyCredentialRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *VerifyCredentialRequest) GetCredential() string {
	if x != nil {
		return x.Credential
	}
	return ""
}

func (x *VerifyCredentialRequest) GetNeedDecrypt() bool {
	if x != nil {
		return x.NeedDecrypt
	}
	return false
}

// 验证凭证 - 答复
type VerifyCredentialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyCredentialResponse) Reset() {
	*x = VerifyCredentialResponse{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyCredentialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCredentialResponse) ProtoMessage() {}

func (x *VerifyCredentialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCredentialResponse.ProtoReflect.Descriptor instead.
func (*VerifyCredentialResponse) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{8}
}

func (x *VerifyCredentialResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 修改凭证 - 请求
type ChangeCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdentityType  IdentityType           `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3,enum=authentication.service.v1.IdentityType" json:"identity_type,omitempty"` // 身份类型
	Identifier    string                 `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`                                                                      // 身份唯一标识符
	OldCredential string                 `protobuf:"bytes,3,opt,name=old_credential,json=oldCredential,proto3" json:"old_credential,omitempty"`                                           // 旧凭证
	NewCredential string                 `protobuf:"bytes,4,opt,name=new_credential,json=newCredential,proto3" json:"new_credential,omitempty"`                                           // 新凭证
	NeedDecrypt   bool                   `protobuf:"varint,5,opt,name=need_decrypt,json=needDecrypt,proto3" json:"need_decrypt,omitempty"`                                                // 是否需要解码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeCredentialRequest) Reset() {
	*x = ChangeCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeCredentialRequest) ProtoMessage() {}

func (x *ChangeCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeCredentialRequest.ProtoReflect.Descriptor instead.
func (*ChangeCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{9}
}

func (x *ChangeCredentialRequest) GetIdentityType() IdentityType {
	if x != nil {
		return x.IdentityType
	}
	return IdentityType_USERNAME
}

func (x *ChangeCredentialRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *ChangeCredentialRequest) GetOldCredential() string {
	if x != nil {
		return x.OldCredential
	}
	return ""
}

func (x *ChangeCredentialRequest) GetNewCredential() string {
	if x != nil {
		return x.NewCredential
	}
	return ""
}

func (x *ChangeCredentialRequest) GetNeedDecrypt() bool {
	if x != nil {
		return x.NeedDecrypt
	}
	return false
}

// 重设凭证 - 请求
type ResetCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdentityType  IdentityType           `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3,enum=authentication.service.v1.IdentityType" json:"identity_type,omitempty"` // 身份类型
	Identifier    string                 `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`                                                                      // 身份唯一标识符
	NewCredential string                 `protobuf:"bytes,3,opt,name=new_credential,json=newCredential,proto3" json:"new_credential,omitempty"`                                           // 新凭证
	NeedDecrypt   bool                   `protobuf:"varint,4,opt,name=need_decrypt,json=needDecrypt,proto3" json:"need_decrypt,omitempty"`                                                // 是否需要解码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetCredentialRequest) Reset() {
	*x = ResetCredentialRequest{}
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetCredentialRequest) ProtoMessage() {}

func (x *ResetCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_authentication_service_v1_user_credential_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetCredentialRequest.ProtoReflect.Descriptor instead.
func (*ResetCredentialRequest) Descriptor() ([]byte, []int) {
	return file_authentication_service_v1_user_credential_proto_rawDescGZIP(), []int{10}
}

func (x *ResetCredentialRequest) GetIdentityType() IdentityType {
	if x != nil {
		return x.IdentityType
	}
	return IdentityType_USERNAME
}

func (x *ResetCredentialRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *ResetCredentialRequest) GetNewCredential() string {
	if x != nil {
		return x.NewCredential
	}
	return ""
}

func (x *ResetCredentialRequest) GetNeedDecrypt() bool {
	if x != nil {
		return x.NeedDecrypt
	}
	return false
}

var File_authentication_service_v1_user_credential_proto protoreflect.FileDescriptor

const file_authentication_service_v1_user_credential_proto_rawDesc = "" +
	"\n" +
	"/authentication/service/v1/user_credential.proto\x12\x19authentication.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1epagination/v1/pagination.proto\"\x8a\x10\n" +
	"\x0eUserCredential\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12;\n" +
	"\auser_id\x18\x02 \x01(\rB\x1d\xbaG\x1a\x92\x02\x17关联主表的用户IDH\x00R\x06userId\x88\x01\x01\x120\n" +
	"\ttenant_id\x18\x03 \x01(\rB\x0e\xbaG\v\x92\x02\b租户IDH\x01R\btenantId\x88\x01\x01\x12\xc2\x01\n" +
	"\ridentity_type\x18\n" +
	" \x01(\x0e2'.authentication.service.v1.IdentityTypeBo\xbaGl\x92\x02i认证方式类型，如用户名+密码、邮箱+密码、手机号+验证码、第三方平台认证等H\x02R\fidentityType\x88\x01\x01\x12\xa3\x02\n" +
	"\n" +
	"identifier\x18\v \x01(\tB\xfd\x01\xbaG\xf9\x01\x92\x02\xf5\x01身份唯一标识符，如果是密码登录，则是用户名；如果是邮箱登录，则是邮箱地址；如果是手机号登录，则是手机号；如果是第三方平台登录，则是第三方平台的唯一ID（如微信的OpenID）H\x03R\n" +
	"identifier\x88\x01\x01\x12\x9e\x01\n" +
	"\x0fcredential_type\x18\x14 \x01(\x0e2).authentication.service.v1.CredentialTypeBE\xbaGB\x92\x02?凭证类型，如加密密码、访问令牌、刷新令牌等H\x04R\x0ecredentialType\x88\x01\x01\x12\x99\x02\n" +
	"\n" +
	"credential\x18\x15 \x01(\tB\xf3\x01\xbaG\xef\x01\x92\x02\xeb\x01凭证，如果是密码登录，则是密码的hash值；如果是邮箱登录，则是邮箱的验证码；如果是手机号登录，则是手机号的验证码；如果是第三方平台登录，则是第三方平台的access_tokenH\x05R\n" +
	"credential\x88\x01\x01\x12\xa2\x01\n" +
	"\n" +
	"is_primary\x18\x1e \x01(\bB~\xbaG{\x92\x02x是否主认证方式，如果用户同时绑定了邮箱和手机号，那么可以指定邮箱为主要认证方式。H\x06R\tisPrimary\x88\x01\x01\x12`\n" +
	"\x06status\x18\x1f \x01(\x0e2/.authentication.service.v1.UserCredentialStatusB\x12\xbaG\x0f\x92\x02\f凭证状态H\aR\x06status\x88\x01\x01\x12\x87\x01\n" +
	"\n" +
	"extra_info\x18  \x01(\tBc\xbaG`\x92\x02]扩展信息，如果是第三方平台认证，可以记录第三方平台的用户信息。H\bR\textraInfo\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\tR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\n" +
	"R\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\vR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\fR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\rR\n" +
	"deleteTime\x88\x01\x01B\n" +
	"\n" +
	"\b_user_idB\f\n" +
	"\n" +
	"_tenant_idB\x10\n" +
	"\x0e_identity_typeB\r\n" +
	"\v_identifierB\x12\n" +
	"\x10_credential_typeB\r\n" +
	"\v_credentialB\r\n" +
	"\v_is_primaryB\t\n" +
	"\a_statusB\r\n" +
	"\v_extra_infoB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"s\n" +
	"\x1aListUserCredentialResponse\x12?\n" +
	"\x05items\x18\x01 \x03(\v2).authentication.service.v1.UserCredentialR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"\x9a\x03\n" +
	"\x1bUpdateUserCredentialRequest\x12=\n" +
	"\x04data\x18\x01 \x01(\v2).authentication.service.v1.UserCredentialR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"\\\n" +
	"\x1bCreateUserCredentialRequest\x12=\n" +
	"\x04data\x18\x01 \x01(\v2).authentication.service.v1.UserCredentialR\x04data\"-\n" +
	"\x1bDeleteUserCredentialRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"*\n" +
	"\x18GetUserCredentialRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"\xc5\x01\n" +
	"$GetUserCredentialByIdentifierRequest\x12`\n" +
	"\ridentity_type\x18\x01 \x01(\x0e2'.authentication.service.v1.IdentityTypeB\x12\xbaG\x0f\x92\x02\f身份类型R\fidentityType\x12;\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tB\x1b\xbaG\x18\x92\x02\x15身份唯一标识符R\n" +
	"identifier\"\xa3\x02\n" +
	"\x17VerifyCredentialRequest\x12`\n" +
	"\ridentity_type\x18\x01 \x01(\x0e2'.authentication.service.v1.IdentityTypeB\x12\xbaG\x0f\x92\x02\f身份类型R\fidentityType\x12;\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tB\x1b\xbaG\x18\x92\x02\x15身份唯一标识符R\n" +
	"identifier\x12,\n" +
	"\n" +
	"credential\x18\x03 \x01(\tB\f\xbaG\t\x92\x02\x06凭证R\n" +
	"credential\x12;\n" +
	"\fneed_decrypt\x18\x04 \x01(\bB\x18\xbaG\x15\x92\x02\x12是否需要解码R\vneedDecrypt\"4\n" +
	"\x18VerifyCredentialResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\xe5\x02\n" +
	"\x17ChangeCredentialRequest\x12`\n" +
	"\ridentity_type\x18\x01 \x01(\x0e2'.authentication.service.v1.IdentityTypeB\x12\xbaG\x0f\x92\x02\f身份类型R\fidentityType\x12;\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tB\x1b\xbaG\x18\x92\x02\x15身份唯一标识符R\n" +
	"identifier\x126\n" +
	"\x0eold_credential\x18\x03 \x01(\tB\x0f\xbaG\f\x92\x02\t旧凭证R\roldCredential\x126\n" +
	"\x0enew_credential\x18\x04 \x01(\tB\x0f\xbaG\f\x92\x02\t新凭证R\rnewCredential\x12;\n" +
	"\fneed_decrypt\x18\x05 \x01(\bB\x18\xbaG\x15\x92\x02\x12是否需要解码R\vneedDecrypt\"\xac\x02\n" +
	"\x16ResetCredentialRequest\x12`\n" +
	"\ridentity_type\x18\x01 \x01(\x0e2'.authentication.service.v1.IdentityTypeB\x12\xbaG\x0f\x92\x02\f身份类型R\fidentityType\x12;\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tB\x1b\xbaG\x18\x92\x02\x15身份唯一标识符R\n" +
	"identifier\x126\n" +
	"\x0enew_credential\x18\x03 \x01(\tB\x0f\xbaG\f\x92\x02\t新凭证R\rnewCredential\x12;\n" +
	"\fneed_decrypt\x18\x04 \x01(\bB\x18\xbaG\x15\x92\x02\x12是否需要解码R\vneedDecrypt*\xff\x03\n" +
	"\fIdentityType\x12\f\n" +
	"\bUSERNAME\x10\x00\x12\n" +
	"\n" +
	"\x06USERID\x10\x01\x12\t\n" +
	"\x05EMAIL\x10\x02\x12\t\n" +
	"\x05PHONE\x10\x03\x12\n" +
	"\n" +
	"\x06WECHAT\x10d\x12\x06\n" +
	"\x02QQ\x10e\x12\t\n" +
	"\x05WEIBO\x10f\x12\n" +
	"\n" +
	"\x06DOUYIN\x10g\x12\f\n" +
	"\bKUAISHOU\x10h\x12\t\n" +
	"\x05BAIDU\x10i\x12\n" +
	"\n" +
	"\x06ALIPAY\x10j\x12\n" +
	"\n" +
	"\x06TAOBAO\x10k\x12\x06\n" +
	"\x02JD\x10l\x12\v\n" +
	"\aMEITUAN\x10m\x12\f\n" +
	"\bDINGTALK\x10n\x12\f\n" +
	"\bBILIBILI\x10o\x12\x0f\n" +
	"\vXIAOHONGSHU\x10p\x12\v\n" +
	"\x06GOOGLE\x10\xc8\x01\x12\r\n" +
	"\bFACEBOOK\x10\xc9\x01\x12\n" +
	"\n" +
	"\x05APPLE\x10\xca\x01\x12\r\n" +
	"\bTELEGRAM\x10\xcb\x01\x12\f\n" +
	"\aTWITTER\x10\xcc\x01\x12\r\n" +
	"\bLINKEDIN\x10\xcd\x01\x12\v\n" +
	"\x06GITHUB\x10\xce\x01\x12\x0e\n" +
	"\tMICROSOFT\x10\xcf\x01\x12\f\n" +
	"\aDISCORD\x10\xd0\x01\x12\n" +
	"\n" +
	"\x05SLACK\x10\xd1\x01\x12\x0e\n" +
	"\tINSTAGRAM\x10\xd2\x01\x12\v\n" +
	"\x06TIKTOK\x10\xd3\x01\x12\v\n" +
	"\x06REDDIT\x10\xd4\x01\x12\f\n" +
	"\aYOUTUBE\x10\xd5\x01\x12\f\n" +
	"\aSPOTIFY\x10\xd6\x01\x12\x0e\n" +
	"\tPINTEREST\x10\xd7\x01\x12\r\n" +
	"\bSNAPCHAT\x10\xd8\x01\x12\v\n" +
	"\x06TUMBLR\x10\xd9\x01\x12\n" +
	"\n" +
	"\x05YAHOO\x10\xda\x01\x12\r\n" +
	"\bWHATSAPP\x10\xdb\x01\x12\t\n" +
	"\x04LINE\x10\xdc\x01*\xc4\x06\n" +
	"\x0eCredentialType\x12\x11\n" +
	"\rPASSWORD_HASH\x10\x00\x12\x10\n" +
	"\fACCESS_TOKEN\x10\x01\x12\x11\n" +
	"\rREFRESH_TOKEN\x10\x02\x12\x1b\n" +
	"\x17EMAIL_VERIFICATION_CODE\x10\x03\x12\x1b\n" +
	"\x17PHONE_VERIFICATION_CODE\x10\x04\x12\x0f\n" +
	"\vOAUTH_TOKEN\x10\x05\x12\v\n" +
	"\aAPI_KEY\x10\x06\x12\r\n" +
	"\tSSO_TOKEN\x10\a\x12\a\n" +
	"\x03JWT\x10\b\x12\x12\n" +
	"\x0eSAML_ASSERTION\x10\t\x12\x1b\n" +
	"\x17OPENID_CONNECT_ID_TOKEN\x10\n" +
	"\x12\x12\n" +
	"\x0eSESSION_COOKIE\x10\v\x12\x18\n" +
	"\x14TEMPORARY_CREDENTIAL\x10\f\x12\x15\n" +
	"\x11CUSTOM_CREDENTIAL\x10\r\x12\x12\n" +
	"\x0eBIOMETRIC_DATA\x10\x0e\x12\x10\n" +
	"\fSECURITY_KEY\x10\x0f\x12\a\n" +
	"\x03OTP\x10\x10\x12\x0e\n" +
	"\n" +
	"SMART_CARD\x10\x11\x12\x1d\n" +
	"\x19CRYPTOGRAPHIC_CERTIFICATE\x10\x12\x12\x13\n" +
	"\x0fBIOMETRIC_TOKEN\x10\x13\x12\x16\n" +
	"\x12DEVICE_FINGERPRINT\x10\x14\x12\x12\n" +
	"\x0eHARDWARE_TOKEN\x10\x15\x12\x12\n" +
	"\x0eSOFTWARE_TOKEN\x10\x16\x12\x15\n" +
	"\x11SECURITY_QUESTION\x10\x17\x12\x10\n" +
	"\fSECURITY_PIN\x10\x18\x12\x1d\n" +
	"\x19TWO_FACTOR_AUTHENTICATION\x10\x19\x12\x1f\n" +
	"\x1bMULTI_FACTOR_AUTHENTICATION\x10\x1a\x12\x1f\n" +
	"\x1bPASSWORDLESS_AUTHENTICATION\x10\x1b\x12\x16\n" +
	"\x12SOCIAL_LOGIN_TOKEN\x10\x1c\x12\x0f\n" +
	"\vSSO_SESSION\x10\x1d\x12\x0e\n" +
	"\n" +
	"API_SECRET\x10\x1e\x12\x10\n" +
	"\fCUSTOM_TOKEN\x10\x1f\x12\x1d\n" +
	"\x19OAUTH2_CLIENT_CREDENTIALS\x10 \x12\x1d\n" +
	"\x19OAUTH2_AUTHORIZATION_CODE\x10!\x12\x19\n" +
	"\x15OAUTH2_IMPLICIT_GRANT\x10\"\x12\x19\n" +
	"\x15OAUTH2_PASSWORD_GRANT\x10#\x12\x18\n" +
	"\x14OAUTH2_REFRESH_GRANT\x10$*w\n" +
	"\x14UserCredentialStatus\x12\f\n" +
	"\bDISABLED\x10\x00\x12\v\n" +
	"\aENABLED\x10\x01\x12\v\n" +
	"\aEXPIRED\x10\x02\x12\x0e\n" +
	"\n" +
	"UNVERIFIED\x10\x03\x12\v\n" +
	"\aREMOVED\x10\x04\x12\v\n" +
	"\aBLOCKED\x10\x05\x12\r\n" +
	"\tTEMPORARY\x10\x062\xb2\a\n" +
	"\x15UserCredentialService\x12Z\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a5.authentication.service.v1.ListUserCredentialResponse\"\x00\x12g\n" +
	"\x03Get\x123.authentication.service.v1.GetUserCredentialRequest\x1a).authentication.service.v1.UserCredential\"\x00\x12\x7f\n" +
	"\x0fGetByIdentifier\x12?.authentication.service.v1.GetUserCredentialByIdentifierRequest\x1a).authentication.service.v1.UserCredential\"\x00\x12Z\n" +
	"\x06Create\x126.authentication.service.v1.CreateUserCredentialRequest\x1a\x16.google.protobuf.Empty\"\x00\x12Z\n" +
	"\x06Update\x126.authentication.service.v1.UpdateUserCredentialRequest\x1a\x16.google.protobuf.Empty\"\x00\x12Z\n" +
	"\x06Delete\x126.authentication.service.v1.DeleteUserCredentialRequest\x1a\x16.google.protobuf.Empty\"\x00\x12}\n" +
	"\x10VerifyCredential\x122.authentication.service.v1.VerifyCredentialRequest\x1a3.authentication.service.v1.VerifyCredentialResponse\"\x00\x12`\n" +
	"\x10ChangeCredential\x122.authentication.service.v1.ChangeCredentialRequest\x1a\x16.google.protobuf.Empty\"\x00\x12^\n" +
	"\x0fResetCredential\x121.authentication.service.v1.ResetCredentialRequest\x1a\x16.google.protobuf.Empty\"\x00B\xf7\x01\n" +
	"\x1dcom.authentication.service.v1B\x13UserCredentialProtoP\x01Z;kratos-admin/api/gen/go/authentication/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x19Authentication.Service.V1\xca\x02\x19Authentication\\Service\\V1\xe2\x02%Authentication\\Service\\V1\\GPBMetadata\xea\x02\x1bAuthentication::Service::V1b\x06proto3"

var (
	file_authentication_service_v1_user_credential_proto_rawDescOnce sync.Once
	file_authentication_service_v1_user_credential_proto_rawDescData []byte
)

func file_authentication_service_v1_user_credential_proto_rawDescGZIP() []byte {
	file_authentication_service_v1_user_credential_proto_rawDescOnce.Do(func() {
		file_authentication_service_v1_user_credential_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_authentication_service_v1_user_credential_proto_rawDesc), len(file_authentication_service_v1_user_credential_proto_rawDesc)))
	})
	return file_authentication_service_v1_user_credential_proto_rawDescData
}

var file_authentication_service_v1_user_credential_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_authentication_service_v1_user_credential_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_authentication_service_v1_user_credential_proto_goTypes = []any{
	(IdentityType)(0),                            // 0: authentication.service.v1.IdentityType
	(CredentialType)(0),                          // 1: authentication.service.v1.CredentialType
	(UserCredentialStatus)(0),                    // 2: authentication.service.v1.UserCredentialStatus
	(*UserCredential)(nil),                       // 3: authentication.service.v1.UserCredential
	(*ListUserCredentialResponse)(nil),           // 4: authentication.service.v1.ListUserCredentialResponse
	(*UpdateUserCredentialRequest)(nil),          // 5: authentication.service.v1.UpdateUserCredentialRequest
	(*CreateUserCredentialRequest)(nil),          // 6: authentication.service.v1.CreateUserCredentialRequest
	(*DeleteUserCredentialRequest)(nil),          // 7: authentication.service.v1.DeleteUserCredentialRequest
	(*GetUserCredentialRequest)(nil),             // 8: authentication.service.v1.GetUserCredentialRequest
	(*GetUserCredentialByIdentifierRequest)(nil), // 9: authentication.service.v1.GetUserCredentialByIdentifierRequest
	(*VerifyCredentialRequest)(nil),              // 10: authentication.service.v1.VerifyCredentialRequest
	(*VerifyCredentialResponse)(nil),             // 11: authentication.service.v1.VerifyCredentialResponse
	(*ChangeCredentialRequest)(nil),              // 12: authentication.service.v1.ChangeCredentialRequest
	(*ResetCredentialRequest)(nil),               // 13: authentication.service.v1.ResetCredentialRequest
	(*timestamppb.Timestamp)(nil),                // 14: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),                // 15: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),                     // 16: pagination.PagingRequest
	(*emptypb.Empty)(nil),                        // 17: google.protobuf.Empty
}
var file_authentication_service_v1_user_credential_proto_depIdxs = []int32{
	0,  // 0: authentication.service.v1.UserCredential.identity_type:type_name -> authentication.service.v1.IdentityType
	1,  // 1: authentication.service.v1.UserCredential.credential_type:type_name -> authentication.service.v1.CredentialType
	2,  // 2: authentication.service.v1.UserCredential.status:type_name -> authentication.service.v1.UserCredentialStatus
	14, // 3: authentication.service.v1.UserCredential.create_time:type_name -> google.protobuf.Timestamp
	14, // 4: authentication.service.v1.UserCredential.update_time:type_name -> google.protobuf.Timestamp
	14, // 5: authentication.service.v1.UserCredential.delete_time:type_name -> google.protobuf.Timestamp
	3,  // 6: authentication.service.v1.ListUserCredentialResponse.items:type_name -> authentication.service.v1.UserCredential
	3,  // 7: authentication.service.v1.UpdateUserCredentialRequest.data:type_name -> authentication.service.v1.UserCredential
	15, // 8: authentication.service.v1.UpdateUserCredentialRequest.update_mask:type_name -> google.protobuf.FieldMask
	3,  // 9: authentication.service.v1.CreateUserCredentialRequest.data:type_name -> authentication.service.v1.UserCredential
	0,  // 10: authentication.service.v1.GetUserCredentialByIdentifierRequest.identity_type:type_name -> authentication.service.v1.IdentityType
	0,  // 11: authentication.service.v1.VerifyCredentialRequest.identity_type:type_name -> authentication.service.v1.IdentityType
	0,  // 12: authentication.service.v1.ChangeCredentialRequest.identity_type:type_name -> authentication.service.v1.IdentityType
	0,  // 13: authentication.service.v1.ResetCredentialRequest.identity_type:type_name -> authentication.service.v1.IdentityType
	16, // 14: authentication.service.v1.UserCredentialService.List:input_type -> pagination.PagingRequest
	8,  // 15: authentication.service.v1.UserCredentialService.Get:input_type -> authentication.service.v1.GetUserCredentialRequest
	9,  // 16: authentication.service.v1.UserCredentialService.GetByIdentifier:input_type -> authentication.service.v1.GetUserCredentialByIdentifierRequest
	6,  // 17: authentication.service.v1.UserCredentialService.Create:input_type -> authentication.service.v1.CreateUserCredentialRequest
	5,  // 18: authentication.service.v1.UserCredentialService.Update:input_type -> authentication.service.v1.UpdateUserCredentialRequest
	7,  // 19: authentication.service.v1.UserCredentialService.Delete:input_type -> authentication.service.v1.DeleteUserCredentialRequest
	10, // 20: authentication.service.v1.UserCredentialService.VerifyCredential:input_type -> authentication.service.v1.VerifyCredentialRequest
	12, // 21: authentication.service.v1.UserCredentialService.ChangeCredential:input_type -> authentication.service.v1.ChangeCredentialRequest
	13, // 22: authentication.service.v1.UserCredentialService.ResetCredential:input_type -> authentication.service.v1.ResetCredentialRequest
	4,  // 23: authentication.service.v1.UserCredentialService.List:output_type -> authentication.service.v1.ListUserCredentialResponse
	3,  // 24: authentication.service.v1.UserCredentialService.Get:output_type -> authentication.service.v1.UserCredential
	3,  // 25: authentication.service.v1.UserCredentialService.GetByIdentifier:output_type -> authentication.service.v1.UserCredential
	17, // 26: authentication.service.v1.UserCredentialService.Create:output_type -> google.protobuf.Empty
	17, // 27: authentication.service.v1.UserCredentialService.Update:output_type -> google.protobuf.Empty
	17, // 28: authentication.service.v1.UserCredentialService.Delete:output_type -> google.protobuf.Empty
	11, // 29: authentication.service.v1.UserCredentialService.VerifyCredential:output_type -> authentication.service.v1.VerifyCredentialResponse
	17, // 30: authentication.service.v1.UserCredentialService.ChangeCredential:output_type -> google.protobuf.Empty
	17, // 31: authentication.service.v1.UserCredentialService.ResetCredential:output_type -> google.protobuf.Empty
	23, // [23:32] is the sub-list for method output_type
	14, // [14:23] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_authentication_service_v1_user_credential_proto_init() }
func file_authentication_service_v1_user_credential_proto_init() {
	if File_authentication_service_v1_user_credential_proto != nil {
		return
	}
	file_authentication_service_v1_user_credential_proto_msgTypes[0].OneofWrappers = []any{}
	file_authentication_service_v1_user_credential_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_authentication_service_v1_user_credential_proto_rawDesc), len(file_authentication_service_v1_user_credential_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_authentication_service_v1_user_credential_proto_goTypes,
		DependencyIndexes: file_authentication_service_v1_user_credential_proto_depIdxs,
		EnumInfos:         file_authentication_service_v1_user_credential_proto_enumTypes,
		MessageInfos:      file_authentication_service_v1_user_credential_proto_msgTypes,
	}.Build()
	File_authentication_service_v1_user_credential_proto = out.File
	file_authentication_service_v1_user_credential_proto_goTypes = nil
	file_authentication_service_v1_user_credential_proto_depIdxs = nil
}
