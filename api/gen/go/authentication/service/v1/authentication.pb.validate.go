// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: authentication/service/v1/authentication.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	servicev1 "kratos-admin/api/gen/go/user/service/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = servicev1.UserAuthority(0)
)

// Validate checks the field values on LoginRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginRequestMultiError, or
// nil if none found.
func (m *LoginRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GrantType

	if m.ClientId != nil {
		// no validation rules for ClientId
	}

	if m.ClientSecret != nil {
		// no validation rules for ClientSecret
	}

	if m.Scope != nil {
		// no validation rules for Scope
	}

	if m.RedirectUri != nil {
		// no validation rules for RedirectUri
	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if m.Password != nil {
		// no validation rules for Password
	}

	if m.RefreshToken != nil {
		// no validation rules for RefreshToken
	}

	if m.Code != nil {
		// no validation rules for Code
	}

	if len(errors) > 0 {
		return LoginRequestMultiError(errors)
	}

	return nil
}

// LoginRequestMultiError is an error wrapping multiple validation errors
// returned by LoginRequest.ValidateAll() if the designated constraints aren't met.
type LoginRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRequestMultiError) AllErrors() []error { return m }

// LoginRequestValidationError is the validation error returned by
// LoginRequest.Validate if the designated constraints aren't met.
type LoginRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRequestValidationError) ErrorName() string { return "LoginRequestValidationError" }

// Error satisfies the builtin error interface
func (e LoginRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRequestValidationError{}

// Validate checks the field values on LoginResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginResponseMultiError, or
// nil if none found.
func (m *LoginResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessToken

	// no validation rules for RefreshToken

	// no validation rules for TokenType

	if m.ExpiresIn != nil {
		// no validation rules for ExpiresIn
	}

	if m.Scope != nil {
		// no validation rules for Scope
	}

	if len(errors) > 0 {
		return LoginResponseMultiError(errors)
	}

	return nil
}

// LoginResponseMultiError is an error wrapping multiple validation errors
// returned by LoginResponse.ValidateAll() if the designated constraints
// aren't met.
type LoginResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginResponseMultiError) AllErrors() []error { return m }

// LoginResponseValidationError is the validation error returned by
// LoginResponse.Validate if the designated constraints aren't met.
type LoginResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginResponseValidationError) ErrorName() string { return "LoginResponseValidationError" }

// Error satisfies the builtin error interface
func (e LoginResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginResponseValidationError{}

// Validate checks the field values on ValidateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateTokenRequestMultiError, or nil if none found.
func (m *ValidateTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for ClientType

	if len(errors) > 0 {
		return ValidateTokenRequestMultiError(errors)
	}

	return nil
}

// ValidateTokenRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateTokenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateTokenRequestMultiError) AllErrors() []error { return m }

// ValidateTokenRequestValidationError is the validation error returned by
// ValidateTokenRequest.Validate if the designated constraints aren't met.
type ValidateTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateTokenRequestValidationError) ErrorName() string {
	return "ValidateTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateTokenRequestValidationError{}

// Validate checks the field values on ValidateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateTokenResponseMultiError, or nil if none found.
func (m *ValidateTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsValid

	if m.Claim != nil {

		if all {
			switch v := interface{}(m.GetClaim()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ValidateTokenResponseValidationError{
						field:  "Claim",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ValidateTokenResponseValidationError{
						field:  "Claim",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetClaim()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ValidateTokenResponseValidationError{
					field:  "Claim",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ValidateTokenResponseMultiError(errors)
	}

	return nil
}

// ValidateTokenResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateTokenResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateTokenResponseMultiError) AllErrors() []error { return m }

// ValidateTokenResponseValidationError is the validation error returned by
// ValidateTokenResponse.Validate if the designated constraints aren't met.
type ValidateTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateTokenResponseValidationError) ErrorName() string {
	return "ValidateTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateTokenResponseValidationError{}

// Validate checks the field values on RegisterUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterUserRequestMultiError, or nil if none found.
func (m *RegisterUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Username

	// no validation rules for Password

	// no validation rules for TenantCode

	if m.Email != nil {
		// no validation rules for Email
	}

	if len(errors) > 0 {
		return RegisterUserRequestMultiError(errors)
	}

	return nil
}

// RegisterUserRequestMultiError is an error wrapping multiple validation
// errors returned by RegisterUserRequest.ValidateAll() if the designated
// constraints aren't met.
type RegisterUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterUserRequestMultiError) AllErrors() []error { return m }

// RegisterUserRequestValidationError is the validation error returned by
// RegisterUserRequest.Validate if the designated constraints aren't met.
type RegisterUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterUserRequestValidationError) ErrorName() string {
	return "RegisterUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterUserRequestValidationError{}

// Validate checks the field values on RegisterUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterUserResponseMultiError, or nil if none found.
func (m *RegisterUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return RegisterUserResponseMultiError(errors)
	}

	return nil
}

// RegisterUserResponseMultiError is an error wrapping multiple validation
// errors returned by RegisterUserResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterUserResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterUserResponseMultiError) AllErrors() []error { return m }

// RegisterUserResponseValidationError is the validation error returned by
// RegisterUserResponse.Validate if the designated constraints aren't met.
type RegisterUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterUserResponseValidationError) ErrorName() string {
	return "RegisterUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterUserResponseValidationError{}

// Validate checks the field values on UserTokenPayload with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserTokenPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserTokenPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserTokenPayloadMultiError, or nil if none found.
func (m *UserTokenPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *UserTokenPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Authority

	if m.TenantId != nil {
		// no validation rules for TenantId
	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if m.ClientId != nil {
		// no validation rules for ClientId
	}

	if m.RoleId != nil {
		// no validation rules for RoleId
	}

	if m.DeviceId != nil {
		// no validation rules for DeviceId
	}

	if len(errors) > 0 {
		return UserTokenPayloadMultiError(errors)
	}

	return nil
}

// UserTokenPayloadMultiError is an error wrapping multiple validation errors
// returned by UserTokenPayload.ValidateAll() if the designated constraints
// aren't met.
type UserTokenPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserTokenPayloadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserTokenPayloadMultiError) AllErrors() []error { return m }

// UserTokenPayloadValidationError is the validation error returned by
// UserTokenPayload.Validate if the designated constraints aren't met.
type UserTokenPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserTokenPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserTokenPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserTokenPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserTokenPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserTokenPayloadValidationError) ErrorName() string { return "UserTokenPayloadValidationError" }

// Error satisfies the builtin error interface
func (e UserTokenPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserTokenPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserTokenPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserTokenPayloadValidationError{}

// Validate checks the field values on WhoAmIResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WhoAmIResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhoAmIResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WhoAmIResponseMultiError,
// or nil if none found.
func (m *WhoAmIResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *WhoAmIResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Username

	// no validation rules for Authority

	if len(errors) > 0 {
		return WhoAmIResponseMultiError(errors)
	}

	return nil
}

// WhoAmIResponseMultiError is an error wrapping multiple validation errors
// returned by WhoAmIResponse.ValidateAll() if the designated constraints
// aren't met.
type WhoAmIResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhoAmIResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhoAmIResponseMultiError) AllErrors() []error { return m }

// WhoAmIResponseValidationError is the validation error returned by
// WhoAmIResponse.Validate if the designated constraints aren't met.
type WhoAmIResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhoAmIResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhoAmIResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhoAmIResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhoAmIResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhoAmIResponseValidationError) ErrorName() string { return "WhoAmIResponseValidationError" }

// Error satisfies the builtin error interface
func (e WhoAmIResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhoAmIResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhoAmIResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhoAmIResponseValidationError{}

// Validate checks the field values on ChangePasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangePasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePasswordRequestMultiError, or nil if none found.
func (m *ChangePasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Username

	// no validation rules for OldPassword

	// no validation rules for NewPassword

	if len(errors) > 0 {
		return ChangePasswordRequestMultiError(errors)
	}

	return nil
}

// ChangePasswordRequestMultiError is an error wrapping multiple validation
// errors returned by ChangePasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type ChangePasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePasswordRequestMultiError) AllErrors() []error { return m }

// ChangePasswordRequestValidationError is the validation error returned by
// ChangePasswordRequest.Validate if the designated constraints aren't met.
type ChangePasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePasswordRequestValidationError) ErrorName() string {
	return "ChangePasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangePasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePasswordRequestValidationError{}
