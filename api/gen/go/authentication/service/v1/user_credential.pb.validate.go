// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: authentication/service/v1/user_credential.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserCredential with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserCredential) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserCredential with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserCredentialMultiError,
// or nil if none found.
func (m *UserCredential) ValidateAll() error {
	return m.validate(true)
}

func (m *UserCredential) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.UserId != nil {
		// no validation rules for UserId
	}

	if m.TenantId != nil {
		// no validation rules for TenantId
	}

	if m.IdentityType != nil {
		// no validation rules for IdentityType
	}

	if m.Identifier != nil {
		// no validation rules for Identifier
	}

	if m.CredentialType != nil {
		// no validation rules for CredentialType
	}

	if m.Credential != nil {
		// no validation rules for Credential
	}

	if m.IsPrimary != nil {
		// no validation rules for IsPrimary
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.ExtraInfo != nil {
		// no validation rules for ExtraInfo
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserCredentialValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserCredentialValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserCredentialValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserCredentialValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserCredentialValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserCredentialValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserCredentialValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserCredentialValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserCredentialValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserCredentialMultiError(errors)
	}

	return nil
}

// UserCredentialMultiError is an error wrapping multiple validation errors
// returned by UserCredential.ValidateAll() if the designated constraints
// aren't met.
type UserCredentialMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserCredentialMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserCredentialMultiError) AllErrors() []error { return m }

// UserCredentialValidationError is the validation error returned by
// UserCredential.Validate if the designated constraints aren't met.
type UserCredentialValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserCredentialValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserCredentialValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserCredentialValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserCredentialValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserCredentialValidationError) ErrorName() string { return "UserCredentialValidationError" }

// Error satisfies the builtin error interface
func (e UserCredentialValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserCredential.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserCredentialValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserCredentialValidationError{}

// Validate checks the field values on ListUserCredentialResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserCredentialResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserCredentialResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserCredentialResponseMultiError, or nil if none found.
func (m *ListUserCredentialResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserCredentialResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserCredentialResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserCredentialResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserCredentialResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListUserCredentialResponseMultiError(errors)
	}

	return nil
}

// ListUserCredentialResponseMultiError is an error wrapping multiple
// validation errors returned by ListUserCredentialResponse.ValidateAll() if
// the designated constraints aren't met.
type ListUserCredentialResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserCredentialResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserCredentialResponseMultiError) AllErrors() []error { return m }

// ListUserCredentialResponseValidationError is the validation error returned
// by ListUserCredentialResponse.Validate if the designated constraints aren't met.
type ListUserCredentialResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserCredentialResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserCredentialResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserCredentialResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserCredentialResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserCredentialResponseValidationError) ErrorName() string {
	return "ListUserCredentialResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserCredentialResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserCredentialResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserCredentialResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserCredentialResponseValidationError{}

// Validate checks the field values on UpdateUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserCredentialRequestMultiError, or nil if none found.
func (m *UpdateUserCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserCredentialRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserCredentialRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserCredentialRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserCredentialRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserCredentialRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserCredentialRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateUserCredentialRequestMultiError(errors)
	}

	return nil
}

// UpdateUserCredentialRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateUserCredentialRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateUserCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserCredentialRequestMultiError) AllErrors() []error { return m }

// UpdateUserCredentialRequestValidationError is the validation error returned
// by UpdateUserCredentialRequest.Validate if the designated constraints
// aren't met.
type UpdateUserCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserCredentialRequestValidationError) ErrorName() string {
	return "UpdateUserCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserCredentialRequestValidationError{}

// Validate checks the field values on CreateUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserCredentialRequestMultiError, or nil if none found.
func (m *CreateUserCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserCredentialRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserCredentialRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserCredentialRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUserCredentialRequestMultiError(errors)
	}

	return nil
}

// CreateUserCredentialRequestMultiError is an error wrapping multiple
// validation errors returned by CreateUserCredentialRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateUserCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserCredentialRequestMultiError) AllErrors() []error { return m }

// CreateUserCredentialRequestValidationError is the validation error returned
// by CreateUserCredentialRequest.Validate if the designated constraints
// aren't met.
type CreateUserCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserCredentialRequestValidationError) ErrorName() string {
	return "CreateUserCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserCredentialRequestValidationError{}

// Validate checks the field values on DeleteUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserCredentialRequestMultiError, or nil if none found.
func (m *DeleteUserCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteUserCredentialRequestMultiError(errors)
	}

	return nil
}

// DeleteUserCredentialRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteUserCredentialRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteUserCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserCredentialRequestMultiError) AllErrors() []error { return m }

// DeleteUserCredentialRequestValidationError is the validation error returned
// by DeleteUserCredentialRequest.Validate if the designated constraints
// aren't met.
type DeleteUserCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserCredentialRequestValidationError) ErrorName() string {
	return "DeleteUserCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserCredentialRequestValidationError{}

// Validate checks the field values on GetUserCredentialRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserCredentialRequestMultiError, or nil if none found.
func (m *GetUserCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetUserCredentialRequestMultiError(errors)
	}

	return nil
}

// GetUserCredentialRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserCredentialRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCredentialRequestMultiError) AllErrors() []error { return m }

// GetUserCredentialRequestValidationError is the validation error returned by
// GetUserCredentialRequest.Validate if the designated constraints aren't met.
type GetUserCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCredentialRequestValidationError) ErrorName() string {
	return "GetUserCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCredentialRequestValidationError{}

// Validate checks the field values on GetUserCredentialByIdentifierRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetUserCredentialByIdentifierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCredentialByIdentifierRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetUserCredentialByIdentifierRequestMultiError, or nil if none found.
func (m *GetUserCredentialByIdentifierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCredentialByIdentifierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IdentityType

	// no validation rules for Identifier

	if len(errors) > 0 {
		return GetUserCredentialByIdentifierRequestMultiError(errors)
	}

	return nil
}

// GetUserCredentialByIdentifierRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetUserCredentialByIdentifierRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserCredentialByIdentifierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCredentialByIdentifierRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCredentialByIdentifierRequestMultiError) AllErrors() []error { return m }

// GetUserCredentialByIdentifierRequestValidationError is the validation error
// returned by GetUserCredentialByIdentifierRequest.Validate if the designated
// constraints aren't met.
type GetUserCredentialByIdentifierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCredentialByIdentifierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCredentialByIdentifierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCredentialByIdentifierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCredentialByIdentifierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCredentialByIdentifierRequestValidationError) ErrorName() string {
	return "GetUserCredentialByIdentifierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCredentialByIdentifierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCredentialByIdentifierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCredentialByIdentifierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCredentialByIdentifierRequestValidationError{}

// Validate checks the field values on VerifyCredentialRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyCredentialRequestMultiError, or nil if none found.
func (m *VerifyCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IdentityType

	// no validation rules for Identifier

	// no validation rules for Credential

	// no validation rules for NeedDecrypt

	if len(errors) > 0 {
		return VerifyCredentialRequestMultiError(errors)
	}

	return nil
}

// VerifyCredentialRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyCredentialRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyCredentialRequestMultiError) AllErrors() []error { return m }

// VerifyCredentialRequestValidationError is the validation error returned by
// VerifyCredentialRequest.Validate if the designated constraints aren't met.
type VerifyCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyCredentialRequestValidationError) ErrorName() string {
	return "VerifyCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyCredentialRequestValidationError{}

// Validate checks the field values on VerifyCredentialResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyCredentialResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyCredentialResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyCredentialResponseMultiError, or nil if none found.
func (m *VerifyCredentialResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyCredentialResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	if len(errors) > 0 {
		return VerifyCredentialResponseMultiError(errors)
	}

	return nil
}

// VerifyCredentialResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyCredentialResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyCredentialResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyCredentialResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyCredentialResponseMultiError) AllErrors() []error { return m }

// VerifyCredentialResponseValidationError is the validation error returned by
// VerifyCredentialResponse.Validate if the designated constraints aren't met.
type VerifyCredentialResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyCredentialResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyCredentialResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyCredentialResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyCredentialResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyCredentialResponseValidationError) ErrorName() string {
	return "VerifyCredentialResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyCredentialResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyCredentialResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyCredentialResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyCredentialResponseValidationError{}

// Validate checks the field values on ChangeCredentialRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeCredentialRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeCredentialRequestMultiError, or nil if none found.
func (m *ChangeCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IdentityType

	// no validation rules for Identifier

	// no validation rules for OldCredential

	// no validation rules for NewCredential

	// no validation rules for NeedDecrypt

	if len(errors) > 0 {
		return ChangeCredentialRequestMultiError(errors)
	}

	return nil
}

// ChangeCredentialRequestMultiError is an error wrapping multiple validation
// errors returned by ChangeCredentialRequest.ValidateAll() if the designated
// constraints aren't met.
type ChangeCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeCredentialRequestMultiError) AllErrors() []error { return m }

// ChangeCredentialRequestValidationError is the validation error returned by
// ChangeCredentialRequest.Validate if the designated constraints aren't met.
type ChangeCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeCredentialRequestValidationError) ErrorName() string {
	return "ChangeCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeCredentialRequestValidationError{}

// Validate checks the field values on ResetCredentialRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetCredentialRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetCredentialRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetCredentialRequestMultiError, or nil if none found.
func (m *ResetCredentialRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetCredentialRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IdentityType

	// no validation rules for Identifier

	// no validation rules for NewCredential

	// no validation rules for NeedDecrypt

	if len(errors) > 0 {
		return ResetCredentialRequestMultiError(errors)
	}

	return nil
}

// ResetCredentialRequestMultiError is an error wrapping multiple validation
// errors returned by ResetCredentialRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetCredentialRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetCredentialRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetCredentialRequestMultiError) AllErrors() []error { return m }

// ResetCredentialRequestValidationError is the validation error returned by
// ResetCredentialRequest.Validate if the designated constraints aren't met.
type ResetCredentialRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetCredentialRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetCredentialRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetCredentialRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetCredentialRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetCredentialRequestValidationError) ErrorName() string {
	return "ResetCredentialRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetCredentialRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetCredentialRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetCredentialRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetCredentialRequestValidationError{}
