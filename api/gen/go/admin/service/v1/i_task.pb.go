// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_task.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 调度任务类型
type TaskType int32

const (
	TaskType_PERIODIC    TaskType = 0 // 周期性任务
	TaskType_DELAY       TaskType = 1 // 延时任务
	TaskType_WAIT_RESULT TaskType = 2 // 等待结果
)

// Enum value maps for TaskType.
var (
	TaskType_name = map[int32]string{
		0: "PERIODIC",
		1: "DELAY",
		2: "WAIT_RESULT",
	}
	TaskType_value = map[string]int32{
		"PERIODIC":    0,
		"DELAY":       1,
		"WAIT_RESULT": 2,
	}
)

func (x TaskType) Enum() *TaskType {
	p := new(TaskType)
	*p = x
	return p
}

func (x TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_admin_service_v1_i_task_proto_enumTypes[0].Descriptor()
}

func (TaskType) Type() protoreflect.EnumType {
	return &file_admin_service_v1_i_task_proto_enumTypes[0]
}

func (x TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskType.Descriptor instead.
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{0}
}

// 调度任务控制类型
type TaskControlType int32

const (
	TaskControlType_ControlType_Start   TaskControlType = 0 // 启动
	TaskControlType_ControlType_Stop    TaskControlType = 1 // 停止
	TaskControlType_ControlType_Restart TaskControlType = 2 // 重启
)

// Enum value maps for TaskControlType.
var (
	TaskControlType_name = map[int32]string{
		0: "ControlType_Start",
		1: "ControlType_Stop",
		2: "ControlType_Restart",
	}
	TaskControlType_value = map[string]int32{
		"ControlType_Start":   0,
		"ControlType_Stop":    1,
		"ControlType_Restart": 2,
	}
)

func (x TaskControlType) Enum() *TaskControlType {
	p := new(TaskControlType)
	*p = x
	return p
}

func (x TaskControlType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskControlType) Descriptor() protoreflect.EnumDescriptor {
	return file_admin_service_v1_i_task_proto_enumTypes[1].Descriptor()
}

func (TaskControlType) Type() protoreflect.EnumType {
	return &file_admin_service_v1_i_task_proto_enumTypes[1]
}

func (x TaskControlType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskControlType.Descriptor instead.
func (TaskControlType) EnumDescriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{1}
}

// 任务选项
type TaskOption struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RetryCount    *uint32                `protobuf:"varint,1,opt,name=retry_count,json=retryCount,proto3,oneof" json:"retry_count,omitempty"` // 任务最多可以重试的次数
	Timeout       *durationpb.Duration   `protobuf:"bytes,2,opt,name=timeout,proto3,oneof" json:"timeout,omitempty"`                          // 任务超时时间
	Deadline      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=deadline,proto3,oneof" json:"deadline,omitempty"`                        // 任务截止时间
	ProcessIn     *durationpb.Duration   `protobuf:"bytes,4,opt,name=process_in,json=processIn,proto3,oneof" json:"process_in,omitempty"`     // 任务延迟处理时间
	ProcessAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=process_at,json=processAt,proto3,oneof" json:"process_at,omitempty"`     // 任务执行时间点
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskOption) Reset() {
	*x = TaskOption{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskOption) ProtoMessage() {}

func (x *TaskOption) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskOption.ProtoReflect.Descriptor instead.
func (*TaskOption) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{0}
}

func (x *TaskOption) GetRetryCount() uint32 {
	if x != nil && x.RetryCount != nil {
		return *x.RetryCount
	}
	return 0
}

func (x *TaskOption) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *TaskOption) GetDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.Deadline
	}
	return nil
}

func (x *TaskOption) GetProcessIn() *durationpb.Duration {
	if x != nil {
		return x.ProcessIn
	}
	return nil
}

func (x *TaskOption) GetProcessAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ProcessAt
	}
	return nil
}

// 调度任务
type Task struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                     // 任务ID
	Type          *TaskType              `protobuf:"varint,2,opt,name=type,proto3,enum=admin.service.v1.TaskType,oneof" json:"type,omitempty"`  // 任务类型
	TypeName      *string                `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3,oneof" json:"type_name,omitempty"`          // 任务执行类型名
	TaskPayload   *string                `protobuf:"bytes,4,opt,name=task_payload,json=taskPayload,proto3,oneof" json:"task_payload,omitempty"` // 任务数据，以 JSON 格式存储，方便存储不同类型和数量的参数
	CronSpec      *string                `protobuf:"bytes,5,opt,name=cron_spec,json=cronSpec,proto3,oneof" json:"cron_spec,omitempty"`          // cron表达式
	TaskOptions   *TaskOption            `protobuf:"bytes,6,opt,name=task_options,json=taskOptions,proto3,oneof" json:"task_options,omitempty"` // 任务选项
	Enable        *bool                  `protobuf:"varint,10,opt,name=enable,proto3,oneof" json:"enable,omitempty"`                            // 启用/禁用任务
	Remark        *string                `protobuf:"bytes,11,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                             // 备注
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`       // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`       // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`  // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`  // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`  // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{1}
}

func (x *Task) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Task) GetType() TaskType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return TaskType_PERIODIC
}

func (x *Task) GetTypeName() string {
	if x != nil && x.TypeName != nil {
		return *x.TypeName
	}
	return ""
}

func (x *Task) GetTaskPayload() string {
	if x != nil && x.TaskPayload != nil {
		return *x.TaskPayload
	}
	return ""
}

func (x *Task) GetCronSpec() string {
	if x != nil && x.CronSpec != nil {
		return *x.CronSpec
	}
	return ""
}

func (x *Task) GetTaskOptions() *TaskOption {
	if x != nil {
		return x.TaskOptions
	}
	return nil
}

func (x *Task) GetEnable() bool {
	if x != nil && x.Enable != nil {
		return *x.Enable
	}
	return false
}

func (x *Task) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Task) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Task) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Task) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Task) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Task) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询调度任务列表 - 回应
type ListTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Task                `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTaskResponse) Reset() {
	*x = ListTaskResponse{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTaskResponse) ProtoMessage() {}

func (x *ListTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTaskResponse.ProtoReflect.Descriptor instead.
func (*ListTaskResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{2}
}

func (x *ListTaskResponse) GetItems() []*Task {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListTaskResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询调度任务详情 - 请求
type GetTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskRequest) Reset() {
	*x = GetTaskRequest{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRequest) ProtoMessage() {}

func (x *GetTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRequest.ProtoReflect.Descriptor instead.
func (*GetTaskRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{3}
}

func (x *GetTaskRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetTaskByTypeNameRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TypeName      string                 `protobuf:"bytes,1,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"` // 任务执行类型名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskByTypeNameRequest) Reset() {
	*x = GetTaskByTypeNameRequest{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskByTypeNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskByTypeNameRequest) ProtoMessage() {}

func (x *GetTaskByTypeNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskByTypeNameRequest.ProtoReflect.Descriptor instead.
func (*GetTaskByTypeNameRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{4}
}

func (x *GetTaskByTypeNameRequest) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

// 创建调度任务 - 请求
type CreateTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Task                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTaskRequest) Reset() {
	*x = CreateTaskRequest{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskRequest) ProtoMessage() {}

func (x *CreateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateTaskRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{5}
}

func (x *CreateTaskRequest) GetData() *Task {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新调度任务 - 请求
type UpdateTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Task                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTaskRequest) Reset() {
	*x = UpdateTaskRequest{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskRequest) ProtoMessage() {}

func (x *UpdateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskRequest.ProtoReflect.Descriptor instead.
func (*UpdateTaskRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateTaskRequest) GetData() *Task {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateTaskRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateTaskRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除调度任务 - 请求
type DeleteTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTaskRequest) Reset() {
	*x = DeleteTaskRequest{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTaskRequest) ProtoMessage() {}

func (x *DeleteTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTaskRequest.ProtoReflect.Descriptor instead.
func (*DeleteTaskRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteTaskRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 重启调度任务 - 回应
type RestartAllTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int32                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartAllTaskResponse) Reset() {
	*x = RestartAllTaskResponse{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartAllTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartAllTaskResponse) ProtoMessage() {}

func (x *RestartAllTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartAllTaskResponse.ProtoReflect.Descriptor instead.
func (*RestartAllTaskResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{8}
}

func (x *RestartAllTaskResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 控制调度任务 - 请求
type ControlTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ControlType   TaskControlType        `protobuf:"varint,1,opt,name=control_type,json=controlType,proto3,enum=admin.service.v1.TaskControlType" json:"control_type,omitempty"`
	TypeName      string                 `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"` // 任务执行类型名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlTaskRequest) Reset() {
	*x = ControlTaskRequest{}
	mi := &file_admin_service_v1_i_task_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlTaskRequest) ProtoMessage() {}

func (x *ControlTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_task_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlTaskRequest.ProtoReflect.Descriptor instead.
func (*ControlTaskRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_task_proto_rawDescGZIP(), []int{9}
}

func (x *ControlTaskRequest) GetControlType() TaskControlType {
	if x != nil {
		return x.ControlType
	}
	return TaskControlType_ControlType_Start
}

func (x *ControlTaskRequest) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

var File_admin_service_v1_i_task_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_task_proto_rawDesc = "" +
	"\n" +
	"\x1dadmin/service/v1/i_task.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\x98\x04\n" +
	"\n" +
	"TaskOption\x12P\n" +
	"\vretry_count\x18\x01 \x01(\rB*\xe0A\x01\xbaG$\x92\x02!任务最多可以重试的次数H\x00R\n" +
	"retryCount\x88\x01\x01\x12U\n" +
	"\atimeout\x18\x02 \x01(\v2\x19.google.protobuf.DurationB\x1b\xe0A\x01\xbaG\x15\x92\x02\x12任务超时时间H\x01R\atimeout\x88\x01\x01\x12X\n" +
	"\bdeadline\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x1b\xe0A\x01\xbaG\x15\x92\x02\x12任务截止时间H\x02R\bdeadline\x88\x01\x01\x12`\n" +
	"\n" +
	"process_in\x18\x04 \x01(\v2\x19.google.protobuf.DurationB!\xe0A\x01\xbaG\x1b\x92\x02\x18任务延迟处理时间H\x03R\tprocessIn\x88\x01\x01\x12^\n" +
	"\n" +
	"process_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampB\x1e\xe0A\x01\xbaG\x18\x92\x02\x15任务执行时间点H\x04R\tprocessAt\x88\x01\x01B\x0e\n" +
	"\f_retry_countB\n" +
	"\n" +
	"\b_timeoutB\v\n" +
	"\t_deadlineB\r\n" +
	"\v_process_inB\r\n" +
	"\v_process_at\"\x97\n" +
	"\n" +
	"\x04Task\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b任务IDH\x00R\x02id\x88\x01\x01\x12J\n" +
	"\x04type\x18\x02 \x01(\x0e2\x1a.admin.service.v1.TaskTypeB\x15\xe0A\x01\xbaG\x0f\x92\x02\f任务类型H\x01R\x04type\x88\x01\x01\x12\x92\x01\n" +
	"\ttype_name\x18\x03 \x01(\tBp\xe0A\x01\xbaGj\x92\x02g任务执行类型名，例如 \"send_email\"、\"generate_report\" 等，用于区分不同类型的任务H\x02R\btypeName\x88\x01\x01\x12\x82\x01\n" +
	"\ftask_payload\x18\x04 \x01(\tBZ\xe0A\x01\xbaGT\x92\x02Q任务数据，以 JSON 格式存储，方便存储不同类型和数量的参数H\x03R\vtaskPayload\x88\x01\x01\x12\\\n" +
	"\tcron_spec\x18\x05 \x01(\tB:\xe0A\x01\xbaG4\x92\x021cron表达式，用于定义任务的调度时间H\x04R\bcronSpec\x88\x01\x01\x12\xa0\x01\n" +
	"\ftask_options\x18\x06 \x01(\v2\x1c.admin.service.v1.TaskOptionBZ\xe0A\x01\xbaGT\x92\x02Q任务选项，以 JSON 格式存储，方便存储不同类型和数量的选项H\x05R\vtaskOptions\x88\x01\x01\x126\n" +
	"\x06enable\x18\n" +
	" \x01(\bB\x19\xbaG\x16\x92\x02\x13启用/禁用任务H\x06R\x06enable\x88\x01\x01\x12)\n" +
	"\x06remark\x18\v \x01(\tB\f\xbaG\t\x92\x02\x06备注H\aR\x06remark\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\bR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\tR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\n" +
	"R\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\vR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\fR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_typeB\f\n" +
	"\n" +
	"_type_nameB\x0f\n" +
	"\r_task_payloadB\f\n" +
	"\n" +
	"_cron_specB\x0f\n" +
	"\r_task_optionsB\t\n" +
	"\a_enableB\t\n" +
	"\a_remarkB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"V\n" +
	"\x10ListTaskResponse\x12,\n" +
	"\x05items\x18\x01 \x03(\v2\x16.admin.service.v1.TaskR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\" \n" +
	"\x0eGetTaskRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"\xaa\x01\n" +
	"\x18GetTaskByTypeNameRequest\x12\x8d\x01\n" +
	"\ttype_name\x18\x01 \x01(\tBp\xe0A\x01\xbaGj\x92\x02g任务执行类型名，例如 \"send_email\"、\"generate_report\" 等，用于区分不同类型的任务R\btypeName\"?\n" +
	"\x11CreateTaskRequest\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.admin.service.v1.TaskR\x04data\"\xfd\x02\n" +
	"\x11UpdateTaskRequest\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.admin.service.v1.TaskR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"#\n" +
	"\x11DeleteTaskRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\".\n" +
	"\x16RestartAllTaskResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x05R\x05count\"\xea\x01\n" +
	"\x12ControlTaskRequest\x12D\n" +
	"\fcontrol_type\x18\x01 \x01(\x0e2!.admin.service.v1.TaskControlTypeR\vcontrolType\x12\x8d\x01\n" +
	"\ttype_name\x18\x02 \x01(\tBp\xe0A\x01\xbaGj\x92\x02g任务执行类型名，例如 \"send_email\"、\"generate_report\" 等，用于区分不同类型的任务R\btypeName*4\n" +
	"\bTaskType\x12\f\n" +
	"\bPERIODIC\x10\x00\x12\t\n" +
	"\x05DELAY\x10\x01\x12\x0f\n" +
	"\vWAIT_RESULT\x10\x02*W\n" +
	"\x0fTaskControlType\x12\x15\n" +
	"\x11ControlType_Start\x10\x00\x12\x14\n" +
	"\x10ControlType_Stop\x10\x01\x12\x17\n" +
	"\x13ControlType_Restart\x10\x022\xa5\a\n" +
	"\vTaskService\x12^\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a\".admin.service.v1.ListTaskResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/admin/v1/tasks\x12]\n" +
	"\x03Get\x12 .admin.service.v1.GetTaskRequest\x1a\x16.admin.service.v1.Task\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/tasks/{id}\x12a\n" +
	"\x06Create\x12#.admin.service.v1.CreateTaskRequest\x1a\x16.google.protobuf.Empty\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/admin/v1/tasks\x12k\n" +
	"\x06Update\x12#.admin.service.v1.UpdateTaskRequest\x1a\x16.google.protobuf.Empty\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/tasks/{data.id}\x12c\n" +
	"\x06Delete\x12#.admin.service.v1.DeleteTaskRequest\x1a\x16.google.protobuf.Empty\"\x1c\x82\xd3\xe4\x93\x02\x16*\x14/admin/v1/tasks/{id}\x12Y\n" +
	"\x11GetTaskByTypeName\x12*.admin.service.v1.GetTaskByTypeNameRequest\x1a\x16.admin.service.v1.Task\"\x00\x12v\n" +
	"\x0eRestartAllTask\x12\x16.google.protobuf.Empty\x1a(.admin.service.v1.RestartAllTaskResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/admin/v1/tasks:restart\x12^\n" +
	"\vStopAllTask\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/admin/v1/tasks:stop\x12o\n" +
	"\vControlTask\x12$.admin.service.v1.ControlTaskRequest\x1a\x16.google.protobuf.Empty\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/admin/v1/tasks:controlB\xb8\x01\n" +
	"\x14com.admin.service.v1B\n" +
	"ITaskProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_task_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_task_proto_rawDescData []byte
)

func file_admin_service_v1_i_task_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_task_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_task_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_task_proto_rawDesc), len(file_admin_service_v1_i_task_proto_rawDesc)))
	})
	return file_admin_service_v1_i_task_proto_rawDescData
}

var file_admin_service_v1_i_task_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_admin_service_v1_i_task_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_admin_service_v1_i_task_proto_goTypes = []any{
	(TaskType)(0),                    // 0: admin.service.v1.TaskType
	(TaskControlType)(0),             // 1: admin.service.v1.TaskControlType
	(*TaskOption)(nil),               // 2: admin.service.v1.TaskOption
	(*Task)(nil),                     // 3: admin.service.v1.Task
	(*ListTaskResponse)(nil),         // 4: admin.service.v1.ListTaskResponse
	(*GetTaskRequest)(nil),           // 5: admin.service.v1.GetTaskRequest
	(*GetTaskByTypeNameRequest)(nil), // 6: admin.service.v1.GetTaskByTypeNameRequest
	(*CreateTaskRequest)(nil),        // 7: admin.service.v1.CreateTaskRequest
	(*UpdateTaskRequest)(nil),        // 8: admin.service.v1.UpdateTaskRequest
	(*DeleteTaskRequest)(nil),        // 9: admin.service.v1.DeleteTaskRequest
	(*RestartAllTaskResponse)(nil),   // 10: admin.service.v1.RestartAllTaskResponse
	(*ControlTaskRequest)(nil),       // 11: admin.service.v1.ControlTaskRequest
	(*durationpb.Duration)(nil),      // 12: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),    // 13: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),    // 14: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),         // 15: pagination.PagingRequest
	(*emptypb.Empty)(nil),            // 16: google.protobuf.Empty
}
var file_admin_service_v1_i_task_proto_depIdxs = []int32{
	12, // 0: admin.service.v1.TaskOption.timeout:type_name -> google.protobuf.Duration
	13, // 1: admin.service.v1.TaskOption.deadline:type_name -> google.protobuf.Timestamp
	12, // 2: admin.service.v1.TaskOption.process_in:type_name -> google.protobuf.Duration
	13, // 3: admin.service.v1.TaskOption.process_at:type_name -> google.protobuf.Timestamp
	0,  // 4: admin.service.v1.Task.type:type_name -> admin.service.v1.TaskType
	2,  // 5: admin.service.v1.Task.task_options:type_name -> admin.service.v1.TaskOption
	13, // 6: admin.service.v1.Task.create_time:type_name -> google.protobuf.Timestamp
	13, // 7: admin.service.v1.Task.update_time:type_name -> google.protobuf.Timestamp
	13, // 8: admin.service.v1.Task.delete_time:type_name -> google.protobuf.Timestamp
	3,  // 9: admin.service.v1.ListTaskResponse.items:type_name -> admin.service.v1.Task
	3,  // 10: admin.service.v1.CreateTaskRequest.data:type_name -> admin.service.v1.Task
	3,  // 11: admin.service.v1.UpdateTaskRequest.data:type_name -> admin.service.v1.Task
	14, // 12: admin.service.v1.UpdateTaskRequest.update_mask:type_name -> google.protobuf.FieldMask
	1,  // 13: admin.service.v1.ControlTaskRequest.control_type:type_name -> admin.service.v1.TaskControlType
	15, // 14: admin.service.v1.TaskService.List:input_type -> pagination.PagingRequest
	5,  // 15: admin.service.v1.TaskService.Get:input_type -> admin.service.v1.GetTaskRequest
	7,  // 16: admin.service.v1.TaskService.Create:input_type -> admin.service.v1.CreateTaskRequest
	8,  // 17: admin.service.v1.TaskService.Update:input_type -> admin.service.v1.UpdateTaskRequest
	9,  // 18: admin.service.v1.TaskService.Delete:input_type -> admin.service.v1.DeleteTaskRequest
	6,  // 19: admin.service.v1.TaskService.GetTaskByTypeName:input_type -> admin.service.v1.GetTaskByTypeNameRequest
	16, // 20: admin.service.v1.TaskService.RestartAllTask:input_type -> google.protobuf.Empty
	16, // 21: admin.service.v1.TaskService.StopAllTask:input_type -> google.protobuf.Empty
	11, // 22: admin.service.v1.TaskService.ControlTask:input_type -> admin.service.v1.ControlTaskRequest
	4,  // 23: admin.service.v1.TaskService.List:output_type -> admin.service.v1.ListTaskResponse
	3,  // 24: admin.service.v1.TaskService.Get:output_type -> admin.service.v1.Task
	16, // 25: admin.service.v1.TaskService.Create:output_type -> google.protobuf.Empty
	16, // 26: admin.service.v1.TaskService.Update:output_type -> google.protobuf.Empty
	16, // 27: admin.service.v1.TaskService.Delete:output_type -> google.protobuf.Empty
	3,  // 28: admin.service.v1.TaskService.GetTaskByTypeName:output_type -> admin.service.v1.Task
	10, // 29: admin.service.v1.TaskService.RestartAllTask:output_type -> admin.service.v1.RestartAllTaskResponse
	16, // 30: admin.service.v1.TaskService.StopAllTask:output_type -> google.protobuf.Empty
	16, // 31: admin.service.v1.TaskService.ControlTask:output_type -> google.protobuf.Empty
	23, // [23:32] is the sub-list for method output_type
	14, // [14:23] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_task_proto_init() }
func file_admin_service_v1_i_task_proto_init() {
	if File_admin_service_v1_i_task_proto != nil {
		return
	}
	file_admin_service_v1_i_task_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_task_proto_msgTypes[1].OneofWrappers = []any{}
	file_admin_service_v1_i_task_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_task_proto_rawDesc), len(file_admin_service_v1_i_task_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_task_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_task_proto_depIdxs,
		EnumInfos:         file_admin_service_v1_i_task_proto_enumTypes,
		MessageInfos:      file_admin_service_v1_i_task_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_task_proto = out.File
	file_admin_service_v1_i_task_proto_goTypes = nil
	file_admin_service_v1_i_task_proto_depIdxs = nil
}
