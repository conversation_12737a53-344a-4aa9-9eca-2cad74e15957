// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_file.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/file/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationFileServiceCreate = "/admin.service.v1.FileService/Create"
const OperationFileServiceDelete = "/admin.service.v1.FileService/Delete"
const OperationFileServiceGet = "/admin.service.v1.FileService/Get"
const OperationFileServiceList = "/admin.service.v1.FileService/List"
const OperationFileServiceUpdate = "/admin.service.v1.FileService/Update"

type FileServiceHTTPServer interface {
	// Create 创建文件
	Create(context.Context, *v11.CreateFileRequest) (*emptypb.Empty, error)
	// Delete 删除文件
	Delete(context.Context, *v11.DeleteFileRequest) (*emptypb.Empty, error)
	// Get 查询文件详情
	Get(context.Context, *v11.GetFileRequest) (*v11.File, error)
	// List 查询文件列表
	List(context.Context, *v1.PagingRequest) (*v11.ListFileResponse, error)
	// Update 更新文件
	Update(context.Context, *v11.UpdateFileRequest) (*emptypb.Empty, error)
}

func RegisterFileServiceHTTPServer(s *http.Server, srv FileServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/files", _FileService_List6_HTTP_Handler(srv))
	r.GET("/admin/v1/files/{id}", _FileService_Get6_HTTP_Handler(srv))
	r.POST("/admin/v1/files", _FileService_Create4_HTTP_Handler(srv))
	r.PUT("/admin/v1/files/{data.id}", _FileService_Update4_HTTP_Handler(srv))
	r.DELETE("/admin/v1/files/{id}", _FileService_Delete4_HTTP_Handler(srv))
}

func _FileService_List6_HTTP_Handler(srv FileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFileServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListFileResponse)
		return ctx.Result(200, reply)
	}
}

func _FileService_Get6_HTTP_Handler(srv FileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFileServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.File)
		return ctx.Result(200, reply)
	}
}

func _FileService_Create4_HTTP_Handler(srv FileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFileServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreateFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _FileService_Update4_HTTP_Handler(srv FileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdateFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFileServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdateFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _FileService_Delete4_HTTP_Handler(srv FileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFileServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeleteFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type FileServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreateFileRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeleteFileRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetFileRequest, opts ...http.CallOption) (rsp *v11.File, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListFileResponse, err error)
	Update(ctx context.Context, req *v11.UpdateFileRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type FileServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewFileServiceHTTPClient(client *http.Client) FileServiceHTTPClient {
	return &FileServiceHTTPClientImpl{client}
}

func (c *FileServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreateFileRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/files"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFileServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FileServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeleteFileRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/files/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFileServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FileServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetFileRequest, opts ...http.CallOption) (*v11.File, error) {
	var out v11.File
	pattern := "/admin/v1/files/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFileServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FileServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListFileResponse, error) {
	var out v11.ListFileResponse
	pattern := "/admin/v1/files"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFileServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FileServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdateFileRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/files/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFileServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
