// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_role.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/user/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_role_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_role_proto_rawDesc = "" +
	"\n" +
	"\x1dadmin/service/v1/i_role.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1epagination/v1/pagination.proto\x1a\x1auser/service/v1/role.proto2\xfb\x03\n" +
	"\vRoleService\x12]\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a!.user.service.v1.ListRoleResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/admin/v1/roles\x12[\n" +
	"\x03Get\x12\x1f.user.service.v1.GetRoleRequest\x1a\x15.user.service.v1.Role\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/roles/{id}\x12`\n" +
	"\x06Create\x12\".user.service.v1.CreateRoleRequest\x1a\x16.google.protobuf.Empty\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/admin/v1/roles\x12j\n" +
	"\x06Update\x12\".user.service.v1.UpdateRoleRequest\x1a\x16.google.protobuf.Empty\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/roles/{data.id}\x12b\n" +
	"\x06Delete\x12\".user.service.v1.DeleteRoleRequest\x1a\x16.google.protobuf.Empty\"\x1c\x82\xd3\xe4\x93\x02\x16*\x14/admin/v1/roles/{id}B\xb8\x01\n" +
	"\x14com.admin.service.v1B\n" +
	"IRoleProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_role_proto_goTypes = []any{
	(*v1.PagingRequest)(nil),      // 0: pagination.PagingRequest
	(*v11.GetRoleRequest)(nil),    // 1: user.service.v1.GetRoleRequest
	(*v11.CreateRoleRequest)(nil), // 2: user.service.v1.CreateRoleRequest
	(*v11.UpdateRoleRequest)(nil), // 3: user.service.v1.UpdateRoleRequest
	(*v11.DeleteRoleRequest)(nil), // 4: user.service.v1.DeleteRoleRequest
	(*v11.ListRoleResponse)(nil),  // 5: user.service.v1.ListRoleResponse
	(*v11.Role)(nil),              // 6: user.service.v1.Role
	(*emptypb.Empty)(nil),         // 7: google.protobuf.Empty
}
var file_admin_service_v1_i_role_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.RoleService.List:input_type -> pagination.PagingRequest
	1, // 1: admin.service.v1.RoleService.Get:input_type -> user.service.v1.GetRoleRequest
	2, // 2: admin.service.v1.RoleService.Create:input_type -> user.service.v1.CreateRoleRequest
	3, // 3: admin.service.v1.RoleService.Update:input_type -> user.service.v1.UpdateRoleRequest
	4, // 4: admin.service.v1.RoleService.Delete:input_type -> user.service.v1.DeleteRoleRequest
	5, // 5: admin.service.v1.RoleService.List:output_type -> user.service.v1.ListRoleResponse
	6, // 6: admin.service.v1.RoleService.Get:output_type -> user.service.v1.Role
	7, // 7: admin.service.v1.RoleService.Create:output_type -> google.protobuf.Empty
	7, // 8: admin.service.v1.RoleService.Update:output_type -> google.protobuf.Empty
	7, // 9: admin.service.v1.RoleService.Delete:output_type -> google.protobuf.Empty
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_role_proto_init() }
func file_admin_service_v1_i_role_proto_init() {
	if File_admin_service_v1_i_role_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_role_proto_rawDesc), len(file_admin_service_v1_i_role_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_role_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_role_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_role_proto = out.File
	file_admin_service_v1_i_role_proto_goTypes = nil
	file_admin_service_v1_i_role_proto_depIdxs = nil
}
