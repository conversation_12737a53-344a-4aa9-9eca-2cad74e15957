// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_menu.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 菜单类型
type MenuType int32

const (
	MenuType_FOLDER MenuType = 0 // 菜单夹
	MenuType_MENU   MenuType = 1 // 菜单项
	MenuType_BUTTON MenuType = 2 // 按钮
)

// Enum value maps for MenuType.
var (
	MenuType_name = map[int32]string{
		0: "FOLDER",
		1: "MENU",
		2: "BUTTON",
	}
	MenuType_value = map[string]int32{
		"FOLDER": 0,
		"MENU":   1,
		"BUTTON": 2,
	}
)

func (x MenuType) Enum() *MenuType {
	p := new(MenuType)
	*p = x
	return p
}

func (x MenuType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MenuType) Descriptor() protoreflect.EnumDescriptor {
	return file_admin_service_v1_i_menu_proto_enumTypes[0].Descriptor()
}

func (MenuType) Type() protoreflect.EnumType {
	return &file_admin_service_v1_i_menu_proto_enumTypes[0]
}

func (x MenuType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MenuType.Descriptor instead.
func (MenuType) EnumDescriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{0}
}

// 菜单状态
type MenuStatus int32

const (
	MenuStatus_OFF MenuStatus = 0
	MenuStatus_ON  MenuStatus = 1
)

// Enum value maps for MenuStatus.
var (
	MenuStatus_name = map[int32]string{
		0: "OFF",
		1: "ON",
	}
	MenuStatus_value = map[string]int32{
		"OFF": 0,
		"ON":  1,
	}
)

func (x MenuStatus) Enum() *MenuStatus {
	p := new(MenuStatus)
	*p = x
	return p
}

func (x MenuStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MenuStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_admin_service_v1_i_menu_proto_enumTypes[1].Descriptor()
}

func (MenuStatus) Type() protoreflect.EnumType {
	return &file_admin_service_v1_i_menu_proto_enumTypes[1]
}

func (x MenuStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MenuStatus.Descriptor instead.
func (MenuStatus) EnumDescriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{1}
}

// 菜单
type Menu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                          // 菜单ID
	Status        *MenuStatus            `protobuf:"varint,2,opt,name=status,proto3,enum=admin.service.v1.MenuStatus,oneof" json:"status,omitempty"` // 菜单状态
	Type          *MenuType              `protobuf:"varint,3,opt,name=type,proto3,enum=admin.service.v1.MenuType,oneof" json:"type,omitempty"`       // 菜单类型
	Path          *string                `protobuf:"bytes,10,opt,name=path,proto3,oneof" json:"path,omitempty"`                                      // 路由路径
	Redirect      *string                `protobuf:"bytes,11,opt,name=redirect,proto3,oneof" json:"redirect,omitempty"`                              // 重定向地址
	Alias         *string                `protobuf:"bytes,12,opt,name=alias,proto3,oneof" json:"alias,omitempty"`                                    // 路由别名
	Name          *string                `protobuf:"bytes,13,opt,name=name,proto3,oneof" json:"name,omitempty"`                                      // 路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。
	Component     *string                `protobuf:"bytes,14,opt,name=component,proto3,oneof" json:"component,omitempty"`                            // 指向的组件
	Meta          *RouteMeta             `protobuf:"bytes,15,opt,name=meta,proto3,oneof" json:"meta,omitempty"`                                      // 路由元信息
	ParentId      *int32                 `protobuf:"varint,50,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`             // 父节点ID
	Children      []*Menu                `protobuf:"bytes,51,rep,name=children,proto3" json:"children,omitempty"`                                    // 子节点树
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`            // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`            // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`       // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`       // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`       // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Menu) Reset() {
	*x = Menu{}
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Menu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Menu) ProtoMessage() {}

func (x *Menu) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Menu.ProtoReflect.Descriptor instead.
func (*Menu) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{0}
}

func (x *Menu) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Menu) GetStatus() MenuStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return MenuStatus_OFF
}

func (x *Menu) GetType() MenuType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return MenuType_FOLDER
}

func (x *Menu) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *Menu) GetRedirect() string {
	if x != nil && x.Redirect != nil {
		return *x.Redirect
	}
	return ""
}

func (x *Menu) GetAlias() string {
	if x != nil && x.Alias != nil {
		return *x.Alias
	}
	return ""
}

func (x *Menu) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Menu) GetComponent() string {
	if x != nil && x.Component != nil {
		return *x.Component
	}
	return ""
}

func (x *Menu) GetMeta() *RouteMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *Menu) GetParentId() int32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *Menu) GetChildren() []*Menu {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Menu) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Menu) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Menu) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Menu) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Menu) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询菜单列表 - 回应
type ListMenuResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Menu                `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMenuResponse) Reset() {
	*x = ListMenuResponse{}
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMenuResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMenuResponse) ProtoMessage() {}

func (x *ListMenuResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMenuResponse.ProtoReflect.Descriptor instead.
func (*ListMenuResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{1}
}

func (x *ListMenuResponse) GetItems() []*Menu {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListMenuResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询菜单详情 - 请求
type GetMenuRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMenuRequest) Reset() {
	*x = GetMenuRequest{}
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMenuRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMenuRequest) ProtoMessage() {}

func (x *GetMenuRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMenuRequest.ProtoReflect.Descriptor instead.
func (*GetMenuRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{2}
}

func (x *GetMenuRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建菜单 - 请求
type CreateMenuRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Menu                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMenuRequest) Reset() {
	*x = CreateMenuRequest{}
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMenuRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMenuRequest) ProtoMessage() {}

func (x *CreateMenuRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMenuRequest.ProtoReflect.Descriptor instead.
func (*CreateMenuRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{3}
}

func (x *CreateMenuRequest) GetData() *Menu {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新菜单 - 请求
type UpdateMenuRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Menu                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMenuRequest) Reset() {
	*x = UpdateMenuRequest{}
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMenuRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMenuRequest) ProtoMessage() {}

func (x *UpdateMenuRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMenuRequest.ProtoReflect.Descriptor instead.
func (*UpdateMenuRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateMenuRequest) GetData() *Menu {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateMenuRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateMenuRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除菜单 - 请求
type DeleteMenuRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OperatorId    *uint32                `protobuf:"varint,1,opt,name=operator_id,json=operatorId,proto3,oneof" json:"operator_id,omitempty"` // 操作用户ID
	Id            int32                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMenuRequest) Reset() {
	*x = DeleteMenuRequest{}
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMenuRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMenuRequest) ProtoMessage() {}

func (x *DeleteMenuRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_menu_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMenuRequest.ProtoReflect.Descriptor instead.
func (*DeleteMenuRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_menu_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteMenuRequest) GetOperatorId() uint32 {
	if x != nil && x.OperatorId != nil {
		return *x.OperatorId
	}
	return 0
}

func (x *DeleteMenuRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_admin_service_v1_i_menu_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_menu_proto_rawDesc = "" +
	"\n" +
	"\x1dadmin/service/v1/i_menu.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\x1a\x1fadmin/service/v1/i_router.proto\"\xba\n" +
	"\n" +
	"\x04Menu\x12&\n" +
	"\x02id\x18\x01 \x01(\x05B\x11\xe0A\x01\xbaG\v\x92\x02\b菜单IDH\x00R\x02id\x88\x01\x01\x12c\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1c.admin.service.v1.MenuStatusB(\xbaG%\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\f菜单状态H\x01R\x06status\x88\x01\x01\x12R\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1a.admin.service.v1.MenuTypeB\x1d\xbaG\x1a\x8a\x02\b\x1a\x06FOLDER\x92\x02\f菜单类型H\x02R\x04type\x88\x01\x01\x12.\n" +
	"\x04path\x18\n" +
	" \x01(\tB\x15\xe0A\x01\xbaG\x0f\x92\x02\f路由路径H\x03R\x04path\x88\x01\x01\x129\n" +
	"\bredirect\x18\v \x01(\tB\x18\xe0A\x01\xbaG\x12\x92\x02\x0f重定向地址H\x04R\bredirect\x88\x01\x01\x120\n" +
	"\x05alias\x18\f \x01(\tB\x15\xe0A\x01\xbaG\x0f\x92\x02\f路由别名H\x05R\x05alias\x88\x01\x01\x12\x85\x01\n" +
	"\x04name\x18\r \x01(\tBl\xe0A\x01\xbaGf\x92\x02c路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。H\x06R\x04name\x88\x01\x01\x12;\n" +
	"\tcomponent\x18\x0e \x01(\tB\x18\xe0A\x01\xbaG\x12\x92\x02\x0f指向的组件H\aR\tcomponent\x88\x01\x01\x12N\n" +
	"\x04meta\x18\x0f \x01(\v2\x1b.admin.service.v1.RouteMetaB\x18\xe0A\x01\xbaG\x12\x92\x02\x0f路由元信息H\bR\x04meta\x88\x01\x01\x123\n" +
	"\tparent_id\x182 \x01(\x05B\x11\xbaG\x0e\x92\x02\v父节点IDH\tR\bparentId\x88\x01\x01\x12F\n" +
	"\bchildren\x183 \x03(\v2\x16.admin.service.v1.MenuB\x12\xbaG\x0f\x92\x02\f子节点树R\bchildren\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\n" +
	"R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\vR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\fR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\rR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\x0eR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\t\n" +
	"\a_statusB\a\n" +
	"\x05_typeB\a\n" +
	"\x05_pathB\v\n" +
	"\t_redirectB\b\n" +
	"\x06_aliasB\a\n" +
	"\x05_nameB\f\n" +
	"\n" +
	"_componentB\a\n" +
	"\x05_metaB\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"V\n" +
	"\x10ListMenuResponse\x12,\n" +
	"\x05items\x18\x01 \x03(\v2\x16.admin.service.v1.MenuR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\" \n" +
	"\x0eGetMenuRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"?\n" +
	"\x11CreateMenuRequest\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.admin.service.v1.MenuR\x04data\"\xfd\x02\n" +
	"\x11UpdateMenuRequest\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.admin.service.v1.MenuR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"q\n" +
	"\x11DeleteMenuRequest\x12<\n" +
	"\voperator_id\x18\x01 \x01(\rB\x16\xbaG\x13\x18\x01\x92\x02\x0e操作用户IDH\x00R\n" +
	"operatorId\x88\x01\x01\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x05R\x02idB\x0e\n" +
	"\f_operator_id*,\n" +
	"\bMenuType\x12\n" +
	"\n" +
	"\x06FOLDER\x10\x00\x12\b\n" +
	"\x04MENU\x10\x01\x12\n" +
	"\n" +
	"\x06BUTTON\x10\x02*\x1d\n" +
	"\n" +
	"MenuStatus\x12\a\n" +
	"\x03OFF\x10\x00\x12\x06\n" +
	"\x02ON\x10\x012\x81\x04\n" +
	"\vMenuService\x12^\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a\".admin.service.v1.ListMenuResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/admin/v1/menus\x12]\n" +
	"\x03Get\x12 .admin.service.v1.GetMenuRequest\x1a\x16.admin.service.v1.Menu\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/menus/{id}\x12a\n" +
	"\x06Create\x12#.admin.service.v1.CreateMenuRequest\x1a\x16.google.protobuf.Empty\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/admin/v1/menus\x12k\n" +
	"\x06Update\x12#.admin.service.v1.UpdateMenuRequest\x1a\x16.google.protobuf.Empty\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/menus/{data.id}\x12c\n" +
	"\x06Delete\x12#.admin.service.v1.DeleteMenuRequest\x1a\x16.google.protobuf.Empty\"\x1c\x82\xd3\xe4\x93\x02\x16*\x14/admin/v1/menus/{id}B\xb8\x01\n" +
	"\x14com.admin.service.v1B\n" +
	"IMenuProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_menu_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_menu_proto_rawDescData []byte
)

func file_admin_service_v1_i_menu_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_menu_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_menu_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_menu_proto_rawDesc), len(file_admin_service_v1_i_menu_proto_rawDesc)))
	})
	return file_admin_service_v1_i_menu_proto_rawDescData
}

var file_admin_service_v1_i_menu_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_admin_service_v1_i_menu_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_admin_service_v1_i_menu_proto_goTypes = []any{
	(MenuType)(0),                 // 0: admin.service.v1.MenuType
	(MenuStatus)(0),               // 1: admin.service.v1.MenuStatus
	(*Menu)(nil),                  // 2: admin.service.v1.Menu
	(*ListMenuResponse)(nil),      // 3: admin.service.v1.ListMenuResponse
	(*GetMenuRequest)(nil),        // 4: admin.service.v1.GetMenuRequest
	(*CreateMenuRequest)(nil),     // 5: admin.service.v1.CreateMenuRequest
	(*UpdateMenuRequest)(nil),     // 6: admin.service.v1.UpdateMenuRequest
	(*DeleteMenuRequest)(nil),     // 7: admin.service.v1.DeleteMenuRequest
	(*RouteMeta)(nil),             // 8: admin.service.v1.RouteMeta
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil), // 10: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),      // 11: pagination.PagingRequest
	(*emptypb.Empty)(nil),         // 12: google.protobuf.Empty
}
var file_admin_service_v1_i_menu_proto_depIdxs = []int32{
	1,  // 0: admin.service.v1.Menu.status:type_name -> admin.service.v1.MenuStatus
	0,  // 1: admin.service.v1.Menu.type:type_name -> admin.service.v1.MenuType
	8,  // 2: admin.service.v1.Menu.meta:type_name -> admin.service.v1.RouteMeta
	2,  // 3: admin.service.v1.Menu.children:type_name -> admin.service.v1.Menu
	9,  // 4: admin.service.v1.Menu.create_time:type_name -> google.protobuf.Timestamp
	9,  // 5: admin.service.v1.Menu.update_time:type_name -> google.protobuf.Timestamp
	9,  // 6: admin.service.v1.Menu.delete_time:type_name -> google.protobuf.Timestamp
	2,  // 7: admin.service.v1.ListMenuResponse.items:type_name -> admin.service.v1.Menu
	2,  // 8: admin.service.v1.CreateMenuRequest.data:type_name -> admin.service.v1.Menu
	2,  // 9: admin.service.v1.UpdateMenuRequest.data:type_name -> admin.service.v1.Menu
	10, // 10: admin.service.v1.UpdateMenuRequest.update_mask:type_name -> google.protobuf.FieldMask
	11, // 11: admin.service.v1.MenuService.List:input_type -> pagination.PagingRequest
	4,  // 12: admin.service.v1.MenuService.Get:input_type -> admin.service.v1.GetMenuRequest
	5,  // 13: admin.service.v1.MenuService.Create:input_type -> admin.service.v1.CreateMenuRequest
	6,  // 14: admin.service.v1.MenuService.Update:input_type -> admin.service.v1.UpdateMenuRequest
	7,  // 15: admin.service.v1.MenuService.Delete:input_type -> admin.service.v1.DeleteMenuRequest
	3,  // 16: admin.service.v1.MenuService.List:output_type -> admin.service.v1.ListMenuResponse
	2,  // 17: admin.service.v1.MenuService.Get:output_type -> admin.service.v1.Menu
	12, // 18: admin.service.v1.MenuService.Create:output_type -> google.protobuf.Empty
	12, // 19: admin.service.v1.MenuService.Update:output_type -> google.protobuf.Empty
	12, // 20: admin.service.v1.MenuService.Delete:output_type -> google.protobuf.Empty
	16, // [16:21] is the sub-list for method output_type
	11, // [11:16] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_menu_proto_init() }
func file_admin_service_v1_i_menu_proto_init() {
	if File_admin_service_v1_i_menu_proto != nil {
		return
	}
	file_admin_service_v1_i_router_proto_init()
	file_admin_service_v1_i_menu_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_menu_proto_msgTypes[4].OneofWrappers = []any{}
	file_admin_service_v1_i_menu_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_menu_proto_rawDesc), len(file_admin_service_v1_i_menu_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_menu_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_menu_proto_depIdxs,
		EnumInfos:         file_admin_service_v1_i_menu_proto_enumTypes,
		MessageInfos:      file_admin_service_v1_i_menu_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_menu_proto = out.File
	file_admin_service_v1_i_menu_proto_goTypes = nil
	file_admin_service_v1_i_menu_proto_depIdxs = nil
}
