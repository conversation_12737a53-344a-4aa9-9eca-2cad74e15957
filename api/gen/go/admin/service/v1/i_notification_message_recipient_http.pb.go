// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_notification_message_recipient.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationNotificationMessageRecipientServiceCreate = "/admin.service.v1.NotificationMessageRecipientService/Create"
const OperationNotificationMessageRecipientServiceDelete = "/admin.service.v1.NotificationMessageRecipientService/Delete"
const OperationNotificationMessageRecipientServiceGet = "/admin.service.v1.NotificationMessageRecipientService/Get"
const OperationNotificationMessageRecipientServiceList = "/admin.service.v1.NotificationMessageRecipientService/List"
const OperationNotificationMessageRecipientServiceUpdate = "/admin.service.v1.NotificationMessageRecipientService/Update"

type NotificationMessageRecipientServiceHTTPServer interface {
	// Create 创建通知消息接收者
	Create(context.Context, *v11.CreateNotificationMessageRecipientRequest) (*emptypb.Empty, error)
	// Delete 删除通知消息接收者
	Delete(context.Context, *v11.DeleteNotificationMessageRecipientRequest) (*emptypb.Empty, error)
	// Get 查询通知消息接收者详情
	Get(context.Context, *v11.GetNotificationMessageRecipientRequest) (*v11.NotificationMessageRecipient, error)
	// List 查询通知消息接收者列表
	List(context.Context, *v1.PagingRequest) (*v11.ListNotificationMessageRecipientResponse, error)
	// Update 更新通知消息接收者
	Update(context.Context, *v11.UpdateNotificationMessageRecipientRequest) (*emptypb.Empty, error)
}

func RegisterNotificationMessageRecipientServiceHTTPServer(s *http.Server, srv NotificationMessageRecipientServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/notifications:recipients", _NotificationMessageRecipientService_List10_HTTP_Handler(srv))
	r.GET("/admin/v1/notifications:recipients/{id}", _NotificationMessageRecipientService_Get10_HTTP_Handler(srv))
	r.POST("/admin/v1/notifications:recipients", _NotificationMessageRecipientService_Create8_HTTP_Handler(srv))
	r.PUT("/admin/v1/notifications:recipients/{data.id}", _NotificationMessageRecipientService_Update8_HTTP_Handler(srv))
	r.DELETE("/admin/v1/notifications:recipients/{id}", _NotificationMessageRecipientService_Delete8_HTTP_Handler(srv))
}

func _NotificationMessageRecipientService_List10_HTTP_Handler(srv NotificationMessageRecipientServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageRecipientServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListNotificationMessageRecipientResponse)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageRecipientService_Get10_HTTP_Handler(srv NotificationMessageRecipientServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetNotificationMessageRecipientRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageRecipientServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetNotificationMessageRecipientRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.NotificationMessageRecipient)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageRecipientService_Create8_HTTP_Handler(srv NotificationMessageRecipientServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateNotificationMessageRecipientRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageRecipientServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreateNotificationMessageRecipientRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageRecipientService_Update8_HTTP_Handler(srv NotificationMessageRecipientServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdateNotificationMessageRecipientRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageRecipientServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdateNotificationMessageRecipientRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageRecipientService_Delete8_HTTP_Handler(srv NotificationMessageRecipientServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteNotificationMessageRecipientRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageRecipientServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeleteNotificationMessageRecipientRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type NotificationMessageRecipientServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreateNotificationMessageRecipientRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeleteNotificationMessageRecipientRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetNotificationMessageRecipientRequest, opts ...http.CallOption) (rsp *v11.NotificationMessageRecipient, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListNotificationMessageRecipientResponse, err error)
	Update(ctx context.Context, req *v11.UpdateNotificationMessageRecipientRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type NotificationMessageRecipientServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewNotificationMessageRecipientServiceHTTPClient(client *http.Client) NotificationMessageRecipientServiceHTTPClient {
	return &NotificationMessageRecipientServiceHTTPClientImpl{client}
}

func (c *NotificationMessageRecipientServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreateNotificationMessageRecipientRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/notifications:recipients"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNotificationMessageRecipientServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageRecipientServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeleteNotificationMessageRecipientRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/notifications:recipients/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNotificationMessageRecipientServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageRecipientServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetNotificationMessageRecipientRequest, opts ...http.CallOption) (*v11.NotificationMessageRecipient, error) {
	var out v11.NotificationMessageRecipient
	pattern := "/admin/v1/notifications:recipients/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNotificationMessageRecipientServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageRecipientServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListNotificationMessageRecipientResponse, error) {
	var out v11.ListNotificationMessageRecipientResponse
	pattern := "/admin/v1/notifications:recipients"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNotificationMessageRecipientServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageRecipientServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdateNotificationMessageRecipientRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/notifications:recipients/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNotificationMessageRecipientServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
