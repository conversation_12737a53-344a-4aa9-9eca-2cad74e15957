// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_notification_message_category.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	NotificationMessageCategoryService_List_FullMethodName   = "/admin.service.v1.NotificationMessageCategoryService/List"
	NotificationMessageCategoryService_Get_FullMethodName    = "/admin.service.v1.NotificationMessageCategoryService/Get"
	NotificationMessageCategoryService_Create_FullMethodName = "/admin.service.v1.NotificationMessageCategoryService/Create"
	NotificationMessageCategoryService_Update_FullMethodName = "/admin.service.v1.NotificationMessageCategoryService/Update"
	NotificationMessageCategoryService_Delete_FullMethodName = "/admin.service.v1.NotificationMessageCategoryService/Delete"
)

// NotificationMessageCategoryServiceClient is the client API for NotificationMessageCategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 通知消息分类管理服务
type NotificationMessageCategoryServiceClient interface {
	// 查询通知消息分类列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*v11.ListNotificationMessageCategoryResponse, error)
	// 查询通知消息分类详情
	Get(ctx context.Context, in *v11.GetNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*v11.NotificationMessageCategory, error)
	// 创建通知消息分类
	Create(ctx context.Context, in *v11.CreateNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新通知消息分类
	Update(ctx context.Context, in *v11.UpdateNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除通知消息分类
	Delete(ctx context.Context, in *v11.DeleteNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type notificationMessageCategoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationMessageCategoryServiceClient(cc grpc.ClientConnInterface) NotificationMessageCategoryServiceClient {
	return &notificationMessageCategoryServiceClient{cc}
}

func (c *notificationMessageCategoryServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*v11.ListNotificationMessageCategoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.ListNotificationMessageCategoryResponse)
	err := c.cc.Invoke(ctx, NotificationMessageCategoryService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageCategoryServiceClient) Get(ctx context.Context, in *v11.GetNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*v11.NotificationMessageCategory, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.NotificationMessageCategory)
	err := c.cc.Invoke(ctx, NotificationMessageCategoryService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageCategoryServiceClient) Create(ctx context.Context, in *v11.CreateNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NotificationMessageCategoryService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageCategoryServiceClient) Update(ctx context.Context, in *v11.UpdateNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NotificationMessageCategoryService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageCategoryServiceClient) Delete(ctx context.Context, in *v11.DeleteNotificationMessageCategoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NotificationMessageCategoryService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationMessageCategoryServiceServer is the server API for NotificationMessageCategoryService service.
// All implementations must embed UnimplementedNotificationMessageCategoryServiceServer
// for forward compatibility.
//
// 通知消息分类管理服务
type NotificationMessageCategoryServiceServer interface {
	// 查询通知消息分类列表
	List(context.Context, *v1.PagingRequest) (*v11.ListNotificationMessageCategoryResponse, error)
	// 查询通知消息分类详情
	Get(context.Context, *v11.GetNotificationMessageCategoryRequest) (*v11.NotificationMessageCategory, error)
	// 创建通知消息分类
	Create(context.Context, *v11.CreateNotificationMessageCategoryRequest) (*emptypb.Empty, error)
	// 更新通知消息分类
	Update(context.Context, *v11.UpdateNotificationMessageCategoryRequest) (*emptypb.Empty, error)
	// 删除通知消息分类
	Delete(context.Context, *v11.DeleteNotificationMessageCategoryRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedNotificationMessageCategoryServiceServer()
}

// UnimplementedNotificationMessageCategoryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNotificationMessageCategoryServiceServer struct{}

func (UnimplementedNotificationMessageCategoryServiceServer) List(context.Context, *v1.PagingRequest) (*v11.ListNotificationMessageCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedNotificationMessageCategoryServiceServer) Get(context.Context, *v11.GetNotificationMessageCategoryRequest) (*v11.NotificationMessageCategory, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedNotificationMessageCategoryServiceServer) Create(context.Context, *v11.CreateNotificationMessageCategoryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedNotificationMessageCategoryServiceServer) Update(context.Context, *v11.UpdateNotificationMessageCategoryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedNotificationMessageCategoryServiceServer) Delete(context.Context, *v11.DeleteNotificationMessageCategoryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedNotificationMessageCategoryServiceServer) mustEmbedUnimplementedNotificationMessageCategoryServiceServer() {
}
func (UnimplementedNotificationMessageCategoryServiceServer) testEmbeddedByValue() {}

// UnsafeNotificationMessageCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationMessageCategoryServiceServer will
// result in compilation errors.
type UnsafeNotificationMessageCategoryServiceServer interface {
	mustEmbedUnimplementedNotificationMessageCategoryServiceServer()
}

func RegisterNotificationMessageCategoryServiceServer(s grpc.ServiceRegistrar, srv NotificationMessageCategoryServiceServer) {
	// If the following call pancis, it indicates UnimplementedNotificationMessageCategoryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&NotificationMessageCategoryService_ServiceDesc, srv)
}

func _NotificationMessageCategoryService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageCategoryServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageCategoryService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageCategoryServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageCategoryService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.GetNotificationMessageCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageCategoryServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageCategoryService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageCategoryServiceServer).Get(ctx, req.(*v11.GetNotificationMessageCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageCategoryService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.CreateNotificationMessageCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageCategoryServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageCategoryService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageCategoryServiceServer).Create(ctx, req.(*v11.CreateNotificationMessageCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageCategoryService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.UpdateNotificationMessageCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageCategoryServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageCategoryService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageCategoryServiceServer).Update(ctx, req.(*v11.UpdateNotificationMessageCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageCategoryService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.DeleteNotificationMessageCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageCategoryServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageCategoryService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageCategoryServiceServer).Delete(ctx, req.(*v11.DeleteNotificationMessageCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationMessageCategoryService_ServiceDesc is the grpc.ServiceDesc for NotificationMessageCategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationMessageCategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.NotificationMessageCategoryService",
	HandlerType: (*NotificationMessageCategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _NotificationMessageCategoryService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _NotificationMessageCategoryService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _NotificationMessageCategoryService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _NotificationMessageCategoryService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _NotificationMessageCategoryService_Delete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_notification_message_category.proto",
}
