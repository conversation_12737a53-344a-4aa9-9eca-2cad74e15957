// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_position.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/user/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPositionServiceCreate = "/admin.service.v1.PositionService/Create"
const OperationPositionServiceDelete = "/admin.service.v1.PositionService/Delete"
const OperationPositionServiceGet = "/admin.service.v1.PositionService/Get"
const OperationPositionServiceList = "/admin.service.v1.PositionService/List"
const OperationPositionServiceUpdate = "/admin.service.v1.PositionService/Update"

type PositionServiceHTTPServer interface {
	// Create 创建职位
	Create(context.Context, *v11.CreatePositionRequest) (*emptypb.Empty, error)
	// Delete 删除职位
	Delete(context.Context, *v11.DeletePositionRequest) (*emptypb.Empty, error)
	// Get 查询职位详情
	Get(context.Context, *v11.GetPositionRequest) (*v11.Position, error)
	// List 查询职位列表
	List(context.Context, *v1.PagingRequest) (*v11.ListPositionResponse, error)
	// Update 更新职位
	Update(context.Context, *v11.UpdatePositionRequest) (*emptypb.Empty, error)
}

func RegisterPositionServiceHTTPServer(s *http.Server, srv PositionServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/positions", _PositionService_List12_HTTP_Handler(srv))
	r.GET("/admin/v1/positions/{id}", _PositionService_Get12_HTTP_Handler(srv))
	r.POST("/admin/v1/positions", _PositionService_Create10_HTTP_Handler(srv))
	r.PUT("/admin/v1/positions/{data.id}", _PositionService_Update10_HTTP_Handler(srv))
	r.DELETE("/admin/v1/positions/{id}", _PositionService_Delete10_HTTP_Handler(srv))
}

func _PositionService_List12_HTTP_Handler(srv PositionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPositionServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListPositionResponse)
		return ctx.Result(200, reply)
	}
}

func _PositionService_Get12_HTTP_Handler(srv PositionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetPositionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPositionServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetPositionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.Position)
		return ctx.Result(200, reply)
	}
}

func _PositionService_Create10_HTTP_Handler(srv PositionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreatePositionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPositionServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreatePositionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PositionService_Update10_HTTP_Handler(srv PositionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdatePositionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPositionServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdatePositionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PositionService_Delete10_HTTP_Handler(srv PositionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeletePositionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPositionServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeletePositionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type PositionServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreatePositionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeletePositionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetPositionRequest, opts ...http.CallOption) (rsp *v11.Position, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListPositionResponse, err error)
	Update(ctx context.Context, req *v11.UpdatePositionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type PositionServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewPositionServiceHTTPClient(client *http.Client) PositionServiceHTTPClient {
	return &PositionServiceHTTPClientImpl{client}
}

func (c *PositionServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreatePositionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/positions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPositionServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PositionServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeletePositionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/positions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPositionServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PositionServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetPositionRequest, opts ...http.CallOption) (*v11.Position, error) {
	var out v11.Position
	pattern := "/admin/v1/positions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPositionServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PositionServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListPositionResponse, error) {
	var out v11.ListPositionResponse
	pattern := "/admin/v1/positions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPositionServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PositionServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdatePositionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/positions/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPositionServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
