// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_admin_operation_log.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAdminOperationLogServiceGet = "/admin.service.v1.AdminOperationLogService/Get"
const OperationAdminOperationLogServiceList = "/admin.service.v1.AdminOperationLogService/List"

type AdminOperationLogServiceHTTPServer interface {
	// Get 查询后台操作日志详情
	Get(context.Context, *GetAdminOperationLogRequest) (*AdminOperationLog, error)
	// List 查询后台操作日志列表
	List(context.Context, *v1.PagingRequest) (*ListAdminOperationLogResponse, error)
}

func RegisterAdminOperationLogServiceHTTPServer(s *http.Server, srv AdminOperationLogServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/admin_operation_logs", _AdminOperationLogService_List2_HTTP_Handler(srv))
	r.GET("/admin/v1/admin_operation_logs/{id}", _AdminOperationLogService_Get2_HTTP_Handler(srv))
}

func _AdminOperationLogService_List2_HTTP_Handler(srv AdminOperationLogServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminOperationLogServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAdminOperationLogResponse)
		return ctx.Result(200, reply)
	}
}

func _AdminOperationLogService_Get2_HTTP_Handler(srv AdminOperationLogServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAdminOperationLogRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminOperationLogServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetAdminOperationLogRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminOperationLog)
		return ctx.Result(200, reply)
	}
}

type AdminOperationLogServiceHTTPClient interface {
	Get(ctx context.Context, req *GetAdminOperationLogRequest, opts ...http.CallOption) (rsp *AdminOperationLog, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *ListAdminOperationLogResponse, err error)
}

type AdminOperationLogServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAdminOperationLogServiceHTTPClient(client *http.Client) AdminOperationLogServiceHTTPClient {
	return &AdminOperationLogServiceHTTPClientImpl{client}
}

func (c *AdminOperationLogServiceHTTPClientImpl) Get(ctx context.Context, in *GetAdminOperationLogRequest, opts ...http.CallOption) (*AdminOperationLog, error) {
	var out AdminOperationLog
	pattern := "/admin/v1/admin_operation_logs/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminOperationLogServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminOperationLogServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*ListAdminOperationLogResponse, error) {
	var out ListAdminOperationLogResponse
	pattern := "/admin/v1/admin_operation_logs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminOperationLogServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
