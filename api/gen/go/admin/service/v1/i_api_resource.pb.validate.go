// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: admin/service/v1/i_api_resource.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ApiResource with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApiResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApiResource with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ApiResourceMultiError, or
// nil if none found.
func (m *ApiResource) ValidateAll() error {
	return m.validate(true)
}

func (m *ApiResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Operation != nil {
		// no validation rules for Operation
	}

	if m.Path != nil {
		// no validation rules for Path
	}

	if m.Method != nil {
		// no validation rules for Method
	}

	if m.Module != nil {
		// no validation rules for Module
	}

	if m.ModuleDescription != nil {
		// no validation rules for ModuleDescription
	}

	if m.Description != nil {
		// no validation rules for Description
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApiResourceValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApiResourceValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApiResourceValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApiResourceValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApiResourceValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApiResourceValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApiResourceValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApiResourceValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApiResourceValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ApiResourceMultiError(errors)
	}

	return nil
}

// ApiResourceMultiError is an error wrapping multiple validation errors
// returned by ApiResource.ValidateAll() if the designated constraints aren't met.
type ApiResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApiResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApiResourceMultiError) AllErrors() []error { return m }

// ApiResourceValidationError is the validation error returned by
// ApiResource.Validate if the designated constraints aren't met.
type ApiResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApiResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApiResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApiResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApiResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApiResourceValidationError) ErrorName() string { return "ApiResourceValidationError" }

// Error satisfies the builtin error interface
func (e ApiResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApiResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApiResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApiResourceValidationError{}

// Validate checks the field values on ListApiResourceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListApiResourceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListApiResourceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListApiResourceResponseMultiError, or nil if none found.
func (m *ListApiResourceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListApiResourceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListApiResourceResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListApiResourceResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListApiResourceResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListApiResourceResponseMultiError(errors)
	}

	return nil
}

// ListApiResourceResponseMultiError is an error wrapping multiple validation
// errors returned by ListApiResourceResponse.ValidateAll() if the designated
// constraints aren't met.
type ListApiResourceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListApiResourceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListApiResourceResponseMultiError) AllErrors() []error { return m }

// ListApiResourceResponseValidationError is the validation error returned by
// ListApiResourceResponse.Validate if the designated constraints aren't met.
type ListApiResourceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListApiResourceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListApiResourceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListApiResourceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListApiResourceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListApiResourceResponseValidationError) ErrorName() string {
	return "ListApiResourceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListApiResourceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListApiResourceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListApiResourceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListApiResourceResponseValidationError{}

// Validate checks the field values on GetApiResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApiResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApiResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApiResourceRequestMultiError, or nil if none found.
func (m *GetApiResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApiResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetApiResourceRequestMultiError(errors)
	}

	return nil
}

// GetApiResourceRequestMultiError is an error wrapping multiple validation
// errors returned by GetApiResourceRequest.ValidateAll() if the designated
// constraints aren't met.
type GetApiResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApiResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApiResourceRequestMultiError) AllErrors() []error { return m }

// GetApiResourceRequestValidationError is the validation error returned by
// GetApiResourceRequest.Validate if the designated constraints aren't met.
type GetApiResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApiResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApiResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApiResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApiResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApiResourceRequestValidationError) ErrorName() string {
	return "GetApiResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetApiResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApiResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApiResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApiResourceRequestValidationError{}

// Validate checks the field values on CreateApiResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateApiResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateApiResourceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateApiResourceRequestMultiError, or nil if none found.
func (m *CreateApiResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateApiResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateApiResourceRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateApiResourceRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateApiResourceRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateApiResourceRequestMultiError(errors)
	}

	return nil
}

// CreateApiResourceRequestMultiError is an error wrapping multiple validation
// errors returned by CreateApiResourceRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateApiResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateApiResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateApiResourceRequestMultiError) AllErrors() []error { return m }

// CreateApiResourceRequestValidationError is the validation error returned by
// CreateApiResourceRequest.Validate if the designated constraints aren't met.
type CreateApiResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateApiResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateApiResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateApiResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateApiResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateApiResourceRequestValidationError) ErrorName() string {
	return "CreateApiResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateApiResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateApiResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateApiResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateApiResourceRequestValidationError{}

// Validate checks the field values on UpdateApiResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateApiResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApiResourceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateApiResourceRequestMultiError, or nil if none found.
func (m *UpdateApiResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApiResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApiResourceRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApiResourceRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApiResourceRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApiResourceRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApiResourceRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApiResourceRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateApiResourceRequestMultiError(errors)
	}

	return nil
}

// UpdateApiResourceRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateApiResourceRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateApiResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApiResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApiResourceRequestMultiError) AllErrors() []error { return m }

// UpdateApiResourceRequestValidationError is the validation error returned by
// UpdateApiResourceRequest.Validate if the designated constraints aren't met.
type UpdateApiResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApiResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApiResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApiResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApiResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApiResourceRequestValidationError) ErrorName() string {
	return "UpdateApiResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApiResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApiResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApiResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApiResourceRequestValidationError{}

// Validate checks the field values on DeleteApiResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteApiResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteApiResourceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteApiResourceRequestMultiError, or nil if none found.
func (m *DeleteApiResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteApiResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteApiResourceRequestMultiError(errors)
	}

	return nil
}

// DeleteApiResourceRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteApiResourceRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteApiResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteApiResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteApiResourceRequestMultiError) AllErrors() []error { return m }

// DeleteApiResourceRequestValidationError is the validation error returned by
// DeleteApiResourceRequest.Validate if the designated constraints aren't met.
type DeleteApiResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteApiResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteApiResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteApiResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteApiResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteApiResourceRequestValidationError) ErrorName() string {
	return "DeleteApiResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteApiResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteApiResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteApiResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteApiResourceRequestValidationError{}
