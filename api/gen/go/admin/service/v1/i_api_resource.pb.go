// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_api_resource.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// API资源
type ApiResource struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                       // 资源ID
	Operation         *string                `protobuf:"bytes,2,opt,name=operation,proto3,oneof" json:"operation,omitempty"`                                          // 接口操作名
	Path              *string                `protobuf:"bytes,3,opt,name=path,proto3,oneof" json:"path,omitempty"`                                                    // 接口路径
	Method            *string                `protobuf:"bytes,4,opt,name=method,proto3,oneof" json:"method,omitempty"`                                                // 请求方法
	Module            *string                `protobuf:"bytes,5,opt,name=module,proto3,oneof" json:"module,omitempty"`                                                // 所属业务模块
	ModuleDescription *string                `protobuf:"bytes,6,opt,name=module_description,json=moduleDescription,proto3,oneof" json:"module_description,omitempty"` // 模块描述
	Description       *string                `protobuf:"bytes,7,opt,name=description,proto3,oneof" json:"description,omitempty"`                                      // 描述
	CreateBy          *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                         // 创建者ID
	UpdateBy          *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                         // 更新者ID
	CreateTime        *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                    // 创建时间
	UpdateTime        *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                    // 更新时间
	DeleteTime        *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                    // 删除时间
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ApiResource) Reset() {
	*x = ApiResource{}
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiResource) ProtoMessage() {}

func (x *ApiResource) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiResource.ProtoReflect.Descriptor instead.
func (*ApiResource) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_api_resource_proto_rawDescGZIP(), []int{0}
}

func (x *ApiResource) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ApiResource) GetOperation() string {
	if x != nil && x.Operation != nil {
		return *x.Operation
	}
	return ""
}

func (x *ApiResource) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *ApiResource) GetMethod() string {
	if x != nil && x.Method != nil {
		return *x.Method
	}
	return ""
}

func (x *ApiResource) GetModule() string {
	if x != nil && x.Module != nil {
		return *x.Module
	}
	return ""
}

func (x *ApiResource) GetModuleDescription() string {
	if x != nil && x.ModuleDescription != nil {
		return *x.ModuleDescription
	}
	return ""
}

func (x *ApiResource) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ApiResource) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *ApiResource) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *ApiResource) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ApiResource) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ApiResource) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询列表 - 回应
type ListApiResourceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*ApiResource         `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApiResourceResponse) Reset() {
	*x = ListApiResourceResponse{}
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApiResourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApiResourceResponse) ProtoMessage() {}

func (x *ListApiResourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApiResourceResponse.ProtoReflect.Descriptor instead.
func (*ListApiResourceResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_api_resource_proto_rawDescGZIP(), []int{1}
}

func (x *ListApiResourceResponse) GetItems() []*ApiResource {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListApiResourceResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询 - 请求
type GetApiResourceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApiResourceRequest) Reset() {
	*x = GetApiResourceRequest{}
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApiResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApiResourceRequest) ProtoMessage() {}

func (x *GetApiResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApiResourceRequest.ProtoReflect.Descriptor instead.
func (*GetApiResourceRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_api_resource_proto_rawDescGZIP(), []int{2}
}

func (x *GetApiResourceRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建 - 请求
type CreateApiResourceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *ApiResource           `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateApiResourceRequest) Reset() {
	*x = CreateApiResourceRequest{}
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateApiResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateApiResourceRequest) ProtoMessage() {}

func (x *CreateApiResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateApiResourceRequest.ProtoReflect.Descriptor instead.
func (*CreateApiResourceRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_api_resource_proto_rawDescGZIP(), []int{3}
}

func (x *CreateApiResourceRequest) GetData() *ApiResource {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新 - 请求
type UpdateApiResourceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *ApiResource           `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateApiResourceRequest) Reset() {
	*x = UpdateApiResourceRequest{}
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateApiResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApiResourceRequest) ProtoMessage() {}

func (x *UpdateApiResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApiResourceRequest.ProtoReflect.Descriptor instead.
func (*UpdateApiResourceRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_api_resource_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateApiResourceRequest) GetData() *ApiResource {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateApiResourceRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateApiResourceRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除 - 请求
type DeleteApiResourceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteApiResourceRequest) Reset() {
	*x = DeleteApiResourceRequest{}
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteApiResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteApiResourceRequest) ProtoMessage() {}

func (x *DeleteApiResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_api_resource_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteApiResourceRequest.ProtoReflect.Descriptor instead.
func (*DeleteApiResourceRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_api_resource_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteApiResourceRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_admin_service_v1_i_api_resource_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_api_resource_proto_rawDesc = "" +
	"\n" +
	"%admin/service/v1/i_api_resource.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xdb\a\n" +
	"\vApiResource\x12#\n" +
	"\x02id\x18\x01 \x01(\rB\x0e\xbaG\v\x92\x02\b资源IDH\x00R\x02id\x88\x01\x01\x128\n" +
	"\toperation\x18\x02 \x01(\tB\x15\xbaG\x12\x92\x02\x0f接口操作名H\x01R\toperation\x88\x01\x01\x12+\n" +
	"\x04path\x18\x03 \x01(\tB\x12\xbaG\x0f\x92\x02\f接口路径H\x02R\x04path\x88\x01\x01\x12H\n" +
	"\x06method\x18\x04 \x01(\tB+\xbaG(\x92\x02%请求方法（GET/POST/PUT/DELETE）H\x03R\x06method\x88\x01\x01\x12c\n" +
	"\x06module\x18\x05 \x01(\tBF\xbaGC\x92\x02@所属业务模块（如 “用户管理”“支付系统”）H\x04R\x06module\x88\x01\x01\x12F\n" +
	"\x12module_description\x18\x06 \x01(\tB\x12\xbaG\x0f\x92\x02\f模块描述H\x05R\x11moduleDescription\x88\x01\x01\x123\n" +
	"\vdescription\x18\a \x01(\tB\f\xbaG\t\x92\x02\x06描述H\x06R\vdescription\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\aR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\bR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\tR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\n" +
	"R\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\vR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\f\n" +
	"\n" +
	"_operationB\a\n" +
	"\x05_pathB\t\n" +
	"\a_methodB\t\n" +
	"\a_moduleB\x15\n" +
	"\x13_module_descriptionB\x0e\n" +
	"\f_descriptionB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"d\n" +
	"\x17ListApiResourceResponse\x123\n" +
	"\x05items\x18\x01 \x03(\v2\x1d.admin.service.v1.ApiResourceR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"'\n" +
	"\x15GetApiResourceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"M\n" +
	"\x18CreateApiResourceRequest\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.admin.service.v1.ApiResourceR\x04data\"\x8b\x03\n" +
	"\x18UpdateApiResourceRequest\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.admin.service.v1.ApiResourceR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"*\n" +
	"\x18DeleteApiResourceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\xcb\x06\n" +
	"\x12ApiResourceService\x12m\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a).admin.service.v1.ListApiResourceResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/admin/v1/api-resources\x12s\n" +
	"\x03Get\x12'.admin.service.v1.GetApiResourceRequest\x1a\x1d.admin.service.v1.ApiResource\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/admin/v1/api-resources/{id}\x12p\n" +
	"\x06Create\x12*.admin.service.v1.CreateApiResourceRequest\x1a\x16.google.protobuf.Empty\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/admin/v1/api-resources\x12z\n" +
	"\x06Update\x12*.admin.service.v1.UpdateApiResourceRequest\x1a\x16.google.protobuf.Empty\",\x82\xd3\xe4\x93\x02&:\x01*\x1a!/admin/v1/api-resources/{data.id}\x12r\n" +
	"\x06Delete\x12*.admin.service.v1.DeleteApiResourceRequest\x1a\x16.google.protobuf.Empty\"$\x82\xd3\xe4\x93\x02\x1e*\x1c/admin/v1/api-resources/{id}\x12k\n" +
	"\x10SyncApiResources\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/admin/v1/api-resources/sync\x12\x81\x01\n" +
	"\x10GetWalkRouteData\x12\x16.google.protobuf.Empty\x1a).admin.service.v1.ListApiResourceResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/admin/v1/api-resources/walk-routeB\xbf\x01\n" +
	"\x14com.admin.service.v1B\x11IApiResourceProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_api_resource_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_api_resource_proto_rawDescData []byte
)

func file_admin_service_v1_i_api_resource_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_api_resource_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_api_resource_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_api_resource_proto_rawDesc), len(file_admin_service_v1_i_api_resource_proto_rawDesc)))
	})
	return file_admin_service_v1_i_api_resource_proto_rawDescData
}

var file_admin_service_v1_i_api_resource_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_admin_service_v1_i_api_resource_proto_goTypes = []any{
	(*ApiResource)(nil),              // 0: admin.service.v1.ApiResource
	(*ListApiResourceResponse)(nil),  // 1: admin.service.v1.ListApiResourceResponse
	(*GetApiResourceRequest)(nil),    // 2: admin.service.v1.GetApiResourceRequest
	(*CreateApiResourceRequest)(nil), // 3: admin.service.v1.CreateApiResourceRequest
	(*UpdateApiResourceRequest)(nil), // 4: admin.service.v1.UpdateApiResourceRequest
	(*DeleteApiResourceRequest)(nil), // 5: admin.service.v1.DeleteApiResourceRequest
	(*timestamppb.Timestamp)(nil),    // 6: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),    // 7: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),         // 8: pagination.PagingRequest
	(*emptypb.Empty)(nil),            // 9: google.protobuf.Empty
}
var file_admin_service_v1_i_api_resource_proto_depIdxs = []int32{
	6,  // 0: admin.service.v1.ApiResource.create_time:type_name -> google.protobuf.Timestamp
	6,  // 1: admin.service.v1.ApiResource.update_time:type_name -> google.protobuf.Timestamp
	6,  // 2: admin.service.v1.ApiResource.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 3: admin.service.v1.ListApiResourceResponse.items:type_name -> admin.service.v1.ApiResource
	0,  // 4: admin.service.v1.CreateApiResourceRequest.data:type_name -> admin.service.v1.ApiResource
	0,  // 5: admin.service.v1.UpdateApiResourceRequest.data:type_name -> admin.service.v1.ApiResource
	7,  // 6: admin.service.v1.UpdateApiResourceRequest.update_mask:type_name -> google.protobuf.FieldMask
	8,  // 7: admin.service.v1.ApiResourceService.List:input_type -> pagination.PagingRequest
	2,  // 8: admin.service.v1.ApiResourceService.Get:input_type -> admin.service.v1.GetApiResourceRequest
	3,  // 9: admin.service.v1.ApiResourceService.Create:input_type -> admin.service.v1.CreateApiResourceRequest
	4,  // 10: admin.service.v1.ApiResourceService.Update:input_type -> admin.service.v1.UpdateApiResourceRequest
	5,  // 11: admin.service.v1.ApiResourceService.Delete:input_type -> admin.service.v1.DeleteApiResourceRequest
	9,  // 12: admin.service.v1.ApiResourceService.SyncApiResources:input_type -> google.protobuf.Empty
	9,  // 13: admin.service.v1.ApiResourceService.GetWalkRouteData:input_type -> google.protobuf.Empty
	1,  // 14: admin.service.v1.ApiResourceService.List:output_type -> admin.service.v1.ListApiResourceResponse
	0,  // 15: admin.service.v1.ApiResourceService.Get:output_type -> admin.service.v1.ApiResource
	9,  // 16: admin.service.v1.ApiResourceService.Create:output_type -> google.protobuf.Empty
	9,  // 17: admin.service.v1.ApiResourceService.Update:output_type -> google.protobuf.Empty
	9,  // 18: admin.service.v1.ApiResourceService.Delete:output_type -> google.protobuf.Empty
	9,  // 19: admin.service.v1.ApiResourceService.SyncApiResources:output_type -> google.protobuf.Empty
	1,  // 20: admin.service.v1.ApiResourceService.GetWalkRouteData:output_type -> admin.service.v1.ListApiResourceResponse
	14, // [14:21] is the sub-list for method output_type
	7,  // [7:14] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_api_resource_proto_init() }
func file_admin_service_v1_i_api_resource_proto_init() {
	if File_admin_service_v1_i_api_resource_proto != nil {
		return
	}
	file_admin_service_v1_i_api_resource_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_api_resource_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_api_resource_proto_rawDesc), len(file_admin_service_v1_i_api_resource_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_api_resource_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_api_resource_proto_depIdxs,
		MessageInfos:      file_admin_service_v1_i_api_resource_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_api_resource_proto = out.File
	file_admin_service_v1_i_api_resource_proto_goTypes = nil
	file_admin_service_v1_i_api_resource_proto_depIdxs = nil
}
