// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_private_message.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPrivateMessageServiceCreate = "/admin.service.v1.PrivateMessageService/Create"
const OperationPrivateMessageServiceDelete = "/admin.service.v1.PrivateMessageService/Delete"
const OperationPrivateMessageServiceGet = "/admin.service.v1.PrivateMessageService/Get"
const OperationPrivateMessageServiceList = "/admin.service.v1.PrivateMessageService/List"
const OperationPrivateMessageServiceUpdate = "/admin.service.v1.PrivateMessageService/Update"

type PrivateMessageServiceHTTPServer interface {
	// Create 创建私信消息
	Create(context.Context, *v11.CreatePrivateMessageRequest) (*emptypb.Empty, error)
	// Delete 删除私信消息
	Delete(context.Context, *v11.DeletePrivateMessageRequest) (*emptypb.Empty, error)
	// Get 查询私信消息详情
	Get(context.Context, *v11.GetPrivateMessageRequest) (*v11.PrivateMessage, error)
	// List 查询私信消息列表
	List(context.Context, *v1.PagingRequest) (*v11.ListPrivateMessageResponse, error)
	// Update 更新私信消息
	Update(context.Context, *v11.UpdatePrivateMessageRequest) (*emptypb.Empty, error)
}

func RegisterPrivateMessageServiceHTTPServer(s *http.Server, srv PrivateMessageServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/private_messages", _PrivateMessageService_List13_HTTP_Handler(srv))
	r.GET("/admin/v1/private_messages/{id}", _PrivateMessageService_Get13_HTTP_Handler(srv))
	r.POST("/admin/v1/private_messages", _PrivateMessageService_Create11_HTTP_Handler(srv))
	r.PUT("/admin/v1/private_messages/{data.id}", _PrivateMessageService_Update11_HTTP_Handler(srv))
	r.DELETE("/admin/v1/private_messages/{id}", _PrivateMessageService_Delete11_HTTP_Handler(srv))
}

func _PrivateMessageService_List13_HTTP_Handler(srv PrivateMessageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPrivateMessageServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListPrivateMessageResponse)
		return ctx.Result(200, reply)
	}
}

func _PrivateMessageService_Get13_HTTP_Handler(srv PrivateMessageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetPrivateMessageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPrivateMessageServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetPrivateMessageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.PrivateMessage)
		return ctx.Result(200, reply)
	}
}

func _PrivateMessageService_Create11_HTTP_Handler(srv PrivateMessageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreatePrivateMessageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPrivateMessageServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreatePrivateMessageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PrivateMessageService_Update11_HTTP_Handler(srv PrivateMessageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdatePrivateMessageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPrivateMessageServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdatePrivateMessageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _PrivateMessageService_Delete11_HTTP_Handler(srv PrivateMessageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeletePrivateMessageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPrivateMessageServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeletePrivateMessageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type PrivateMessageServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreatePrivateMessageRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeletePrivateMessageRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetPrivateMessageRequest, opts ...http.CallOption) (rsp *v11.PrivateMessage, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListPrivateMessageResponse, err error)
	Update(ctx context.Context, req *v11.UpdatePrivateMessageRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type PrivateMessageServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewPrivateMessageServiceHTTPClient(client *http.Client) PrivateMessageServiceHTTPClient {
	return &PrivateMessageServiceHTTPClientImpl{client}
}

func (c *PrivateMessageServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreatePrivateMessageRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/private_messages"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPrivateMessageServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PrivateMessageServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeletePrivateMessageRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/private_messages/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPrivateMessageServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PrivateMessageServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetPrivateMessageRequest, opts ...http.CallOption) (*v11.PrivateMessage, error) {
	var out v11.PrivateMessage
	pattern := "/admin/v1/private_messages/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPrivateMessageServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PrivateMessageServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListPrivateMessageResponse, error) {
	var out v11.ListPrivateMessageResponse
	pattern := "/admin/v1/private_messages"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPrivateMessageServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PrivateMessageServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdatePrivateMessageRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/private_messages/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPrivateMessageServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
