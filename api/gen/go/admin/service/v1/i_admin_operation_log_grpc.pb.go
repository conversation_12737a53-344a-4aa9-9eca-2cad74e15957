// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_admin_operation_log.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdminOperationLogService_List_FullMethodName = "/admin.service.v1.AdminOperationLogService/List"
	AdminOperationLogService_Get_FullMethodName  = "/admin.service.v1.AdminOperationLogService/Get"
)

// AdminOperationLogServiceClient is the client API for AdminOperationLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 后台操作日志管理服务
type AdminOperationLogServiceClient interface {
	// 查询后台操作日志列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListAdminOperationLogResponse, error)
	// 查询后台操作日志详情
	Get(ctx context.Context, in *GetAdminOperationLogRequest, opts ...grpc.CallOption) (*AdminOperationLog, error)
}

type adminOperationLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminOperationLogServiceClient(cc grpc.ClientConnInterface) AdminOperationLogServiceClient {
	return &adminOperationLogServiceClient{cc}
}

func (c *adminOperationLogServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListAdminOperationLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAdminOperationLogResponse)
	err := c.cc.Invoke(ctx, AdminOperationLogService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminOperationLogServiceClient) Get(ctx context.Context, in *GetAdminOperationLogRequest, opts ...grpc.CallOption) (*AdminOperationLog, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminOperationLog)
	err := c.cc.Invoke(ctx, AdminOperationLogService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminOperationLogServiceServer is the server API for AdminOperationLogService service.
// All implementations must embed UnimplementedAdminOperationLogServiceServer
// for forward compatibility.
//
// 后台操作日志管理服务
type AdminOperationLogServiceServer interface {
	// 查询后台操作日志列表
	List(context.Context, *v1.PagingRequest) (*ListAdminOperationLogResponse, error)
	// 查询后台操作日志详情
	Get(context.Context, *GetAdminOperationLogRequest) (*AdminOperationLog, error)
	mustEmbedUnimplementedAdminOperationLogServiceServer()
}

// UnimplementedAdminOperationLogServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdminOperationLogServiceServer struct{}

func (UnimplementedAdminOperationLogServiceServer) List(context.Context, *v1.PagingRequest) (*ListAdminOperationLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedAdminOperationLogServiceServer) Get(context.Context, *GetAdminOperationLogRequest) (*AdminOperationLog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedAdminOperationLogServiceServer) mustEmbedUnimplementedAdminOperationLogServiceServer() {
}
func (UnimplementedAdminOperationLogServiceServer) testEmbeddedByValue() {}

// UnsafeAdminOperationLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminOperationLogServiceServer will
// result in compilation errors.
type UnsafeAdminOperationLogServiceServer interface {
	mustEmbedUnimplementedAdminOperationLogServiceServer()
}

func RegisterAdminOperationLogServiceServer(s grpc.ServiceRegistrar, srv AdminOperationLogServiceServer) {
	// If the following call pancis, it indicates UnimplementedAdminOperationLogServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdminOperationLogService_ServiceDesc, srv)
}

func _AdminOperationLogService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminOperationLogServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminOperationLogService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminOperationLogServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminOperationLogService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdminOperationLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminOperationLogServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminOperationLogService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminOperationLogServiceServer).Get(ctx, req.(*GetAdminOperationLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminOperationLogService_ServiceDesc is the grpc.ServiceDesc for AdminOperationLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminOperationLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.AdminOperationLogService",
	HandlerType: (*AdminOperationLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _AdminOperationLogService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _AdminOperationLogService_Get_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_admin_operation_log.proto",
}
