// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_dict.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDictServiceCreate = "/admin.service.v1.DictService/Create"
const OperationDictServiceDelete = "/admin.service.v1.DictService/Delete"
const OperationDictServiceGet = "/admin.service.v1.DictService/Get"
const OperationDictServiceList = "/admin.service.v1.DictService/List"
const OperationDictServiceUpdate = "/admin.service.v1.DictService/Update"

type DictServiceHTTPServer interface {
	// Create 创建字典
	Create(context.Context, *CreateDictRequest) (*emptypb.Empty, error)
	// Delete 删除字典
	Delete(context.Context, *DeleteDictRequest) (*emptypb.Empty, error)
	// Get 查询字典详情
	Get(context.Context, *GetDictRequest) (*Dict, error)
	// List 查询字典列表
	List(context.Context, *v1.PagingRequest) (*ListDictResponse, error)
	// Update 更新字典
	Update(context.Context, *UpdateDictRequest) (*emptypb.Empty, error)
}

func RegisterDictServiceHTTPServer(s *http.Server, srv DictServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/dict", _DictService_List5_HTTP_Handler(srv))
	r.GET("/admin/v1/dict/{id}", _DictService_Get5_HTTP_Handler(srv))
	r.POST("/admin/v1/dict", _DictService_Create3_HTTP_Handler(srv))
	r.PUT("/admin/v1/dict/{data.id}", _DictService_Update3_HTTP_Handler(srv))
	r.DELETE("/admin/v1/dict/{id}", _DictService_Delete3_HTTP_Handler(srv))
}

func _DictService_List5_HTTP_Handler(srv DictServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDictServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDictResponse)
		return ctx.Result(200, reply)
	}
}

func _DictService_Get5_HTTP_Handler(srv DictServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDictRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDictServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetDictRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Dict)
		return ctx.Result(200, reply)
	}
}

func _DictService_Create3_HTTP_Handler(srv DictServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDictRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDictServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*CreateDictRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DictService_Update3_HTTP_Handler(srv DictServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDictRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDictServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*UpdateDictRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _DictService_Delete3_HTTP_Handler(srv DictServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDictRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDictServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*DeleteDictRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type DictServiceHTTPClient interface {
	Create(ctx context.Context, req *CreateDictRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *DeleteDictRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *GetDictRequest, opts ...http.CallOption) (rsp *Dict, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *ListDictResponse, err error)
	Update(ctx context.Context, req *UpdateDictRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type DictServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewDictServiceHTTPClient(client *http.Client) DictServiceHTTPClient {
	return &DictServiceHTTPClientImpl{client}
}

func (c *DictServiceHTTPClientImpl) Create(ctx context.Context, in *CreateDictRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/dict"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDictServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DictServiceHTTPClientImpl) Delete(ctx context.Context, in *DeleteDictRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/dict/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDictServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DictServiceHTTPClientImpl) Get(ctx context.Context, in *GetDictRequest, opts ...http.CallOption) (*Dict, error) {
	var out Dict
	pattern := "/admin/v1/dict/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDictServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DictServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*ListDictResponse, error) {
	var out ListDictResponse
	pattern := "/admin/v1/dict"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDictServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DictServiceHTTPClientImpl) Update(ctx context.Context, in *UpdateDictRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/dict/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDictServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
