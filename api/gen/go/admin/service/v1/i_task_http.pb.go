// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_task.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTaskServiceControlTask = "/admin.service.v1.TaskService/ControlTask"
const OperationTaskServiceCreate = "/admin.service.v1.TaskService/Create"
const OperationTaskServiceDelete = "/admin.service.v1.TaskService/Delete"
const OperationTaskServiceGet = "/admin.service.v1.TaskService/Get"
const OperationTaskServiceList = "/admin.service.v1.TaskService/List"
const OperationTaskServiceRestartAllTask = "/admin.service.v1.TaskService/RestartAllTask"
const OperationTaskServiceStopAllTask = "/admin.service.v1.TaskService/StopAllTask"
const OperationTaskServiceUpdate = "/admin.service.v1.TaskService/Update"

type TaskServiceHTTPServer interface {
	// ControlTask 控制调度任务
	ControlTask(context.Context, *ControlTaskRequest) (*emptypb.Empty, error)
	// Create 创建调度任务
	Create(context.Context, *CreateTaskRequest) (*emptypb.Empty, error)
	// Delete 删除调度任务
	Delete(context.Context, *DeleteTaskRequest) (*emptypb.Empty, error)
	// Get 查询调度任务详情
	Get(context.Context, *GetTaskRequest) (*Task, error)
	// List 查询调度任务列表
	List(context.Context, *v1.PagingRequest) (*ListTaskResponse, error)
	// RestartAllTask 重启所有的调度任务
	RestartAllTask(context.Context, *emptypb.Empty) (*RestartAllTaskResponse, error)
	// StopAllTask 停止所有的调度任务
	StopAllTask(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Update 更新调度任务
	Update(context.Context, *UpdateTaskRequest) (*emptypb.Empty, error)
}

func RegisterTaskServiceHTTPServer(s *http.Server, srv TaskServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/tasks", _TaskService_List15_HTTP_Handler(srv))
	r.GET("/admin/v1/tasks/{id}", _TaskService_Get15_HTTP_Handler(srv))
	r.POST("/admin/v1/tasks", _TaskService_Create13_HTTP_Handler(srv))
	r.PUT("/admin/v1/tasks/{data.id}", _TaskService_Update13_HTTP_Handler(srv))
	r.DELETE("/admin/v1/tasks/{id}", _TaskService_Delete13_HTTP_Handler(srv))
	r.POST("/admin/v1/tasks:restart", _TaskService_RestartAllTask0_HTTP_Handler(srv))
	r.POST("/admin/v1/tasks:stop", _TaskService_StopAllTask0_HTTP_Handler(srv))
	r.POST("/admin/v1/tasks:control", _TaskService_ControlTask0_HTTP_Handler(srv))
}

func _TaskService_List15_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTaskResponse)
		return ctx.Result(200, reply)
	}
}

func _TaskService_Get15_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTaskRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Task)
		return ctx.Result(200, reply)
	}
}

func _TaskService_Create13_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*CreateTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _TaskService_Update13_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*UpdateTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _TaskService_Delete13_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteTaskRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*DeleteTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _TaskService_RestartAllTask0_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceRestartAllTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RestartAllTask(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RestartAllTaskResponse)
		return ctx.Result(200, reply)
	}
}

func _TaskService_StopAllTask0_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceStopAllTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StopAllTask(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _TaskService_ControlTask0_HTTP_Handler(srv TaskServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ControlTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTaskServiceControlTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ControlTask(ctx, req.(*ControlTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type TaskServiceHTTPClient interface {
	ControlTask(ctx context.Context, req *ControlTaskRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Create(ctx context.Context, req *CreateTaskRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *DeleteTaskRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *GetTaskRequest, opts ...http.CallOption) (rsp *Task, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *ListTaskResponse, err error)
	RestartAllTask(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *RestartAllTaskResponse, err error)
	StopAllTask(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Update(ctx context.Context, req *UpdateTaskRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type TaskServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewTaskServiceHTTPClient(client *http.Client) TaskServiceHTTPClient {
	return &TaskServiceHTTPClientImpl{client}
}

func (c *TaskServiceHTTPClientImpl) ControlTask(ctx context.Context, in *ControlTaskRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tasks:control"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTaskServiceControlTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) Create(ctx context.Context, in *CreateTaskRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tasks"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTaskServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) Delete(ctx context.Context, in *DeleteTaskRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tasks/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTaskServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) Get(ctx context.Context, in *GetTaskRequest, opts ...http.CallOption) (*Task, error) {
	var out Task
	pattern := "/admin/v1/tasks/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTaskServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*ListTaskResponse, error) {
	var out ListTaskResponse
	pattern := "/admin/v1/tasks"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTaskServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) RestartAllTask(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*RestartAllTaskResponse, error) {
	var out RestartAllTaskResponse
	pattern := "/admin/v1/tasks:restart"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTaskServiceRestartAllTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) StopAllTask(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tasks:stop"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTaskServiceStopAllTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TaskServiceHTTPClientImpl) Update(ctx context.Context, in *UpdateTaskRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tasks/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTaskServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
