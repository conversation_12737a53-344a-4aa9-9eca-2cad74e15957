// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_ueditor.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "kratos-admin/api/gen/go/file/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUEditorServiceUEditorAPI = "/admin.service.v1.UEditorService/UEditorAPI"

type UEditorServiceHTTPServer interface {
	// UEditorAPI UEditor API
	UEditorAPI(context.Context, *v1.UEditorRequest) (*v1.UEditorResponse, error)
}

func RegisterUEditorServiceHTTPServer(s *http.Server, srv UEditorServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/ueditor", _UEditorService_UEditorAPI0_HTTP_Handler(srv))
}

func _UEditorService_UEditorAPI0_HTTP_Handler(srv UEditorServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.UEditorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUEditorServiceUEditorAPI)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UEditorAPI(ctx, req.(*v1.UEditorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UEditorResponse)
		return ctx.Result(200, reply)
	}
}

type UEditorServiceHTTPClient interface {
	UEditorAPI(ctx context.Context, req *v1.UEditorRequest, opts ...http.CallOption) (rsp *v1.UEditorResponse, err error)
}

type UEditorServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewUEditorServiceHTTPClient(client *http.Client) UEditorServiceHTTPClient {
	return &UEditorServiceHTTPClientImpl{client}
}

func (c *UEditorServiceHTTPClientImpl) UEditorAPI(ctx context.Context, in *v1.UEditorRequest, opts ...http.CallOption) (*v1.UEditorResponse, error) {
	var out v1.UEditorResponse
	pattern := "/admin/v1/ueditor"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUEditorServiceUEditorAPI))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
