// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_oss.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "kratos-admin/api/gen/go/file/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationOssServiceOssUploadUrl = "/admin.service.v1.OssService/OssUploadUrl"

type OssServiceHTTPServer interface {
	// OssUploadUrl 获取对象存储（OSS）上传用的预签名链接
	OssUploadUrl(context.Context, *v1.OssUploadUrlRequest) (*v1.OssUploadUrlResponse, error)
}

func RegisterOssServiceHTTPServer(s *http.Server, srv OssServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/admin/v1/file:upload-url", _OssService_OssUploadUrl0_HTTP_Handler(srv))
}

func _OssService_OssUploadUrl0_HTTP_Handler(srv OssServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.OssUploadUrlRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOssServiceOssUploadUrl)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OssUploadUrl(ctx, req.(*v1.OssUploadUrlRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.OssUploadUrlResponse)
		return ctx.Result(200, reply)
	}
}

type OssServiceHTTPClient interface {
	OssUploadUrl(ctx context.Context, req *v1.OssUploadUrlRequest, opts ...http.CallOption) (rsp *v1.OssUploadUrlResponse, err error)
}

type OssServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewOssServiceHTTPClient(client *http.Client) OssServiceHTTPClient {
	return &OssServiceHTTPClientImpl{client}
}

func (c *OssServiceHTTPClientImpl) OssUploadUrl(ctx context.Context, in *v1.OssUploadUrlRequest, opts ...http.CallOption) (*v1.OssUploadUrlResponse, error) {
	var out v1.OssUploadUrlResponse
	pattern := "/admin/v1/file:upload-url"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOssServiceOssUploadUrl))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
