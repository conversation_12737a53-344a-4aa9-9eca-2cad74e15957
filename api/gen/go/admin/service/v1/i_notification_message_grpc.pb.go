// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_notification_message.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	NotificationMessageService_List_FullMethodName   = "/admin.service.v1.NotificationMessageService/List"
	NotificationMessageService_Get_FullMethodName    = "/admin.service.v1.NotificationMessageService/Get"
	NotificationMessageService_Create_FullMethodName = "/admin.service.v1.NotificationMessageService/Create"
	NotificationMessageService_Update_FullMethodName = "/admin.service.v1.NotificationMessageService/Update"
	NotificationMessageService_Delete_FullMethodName = "/admin.service.v1.NotificationMessageService/Delete"
)

// NotificationMessageServiceClient is the client API for NotificationMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 通知消息管理服务
type NotificationMessageServiceClient interface {
	// 查询通知消息列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*v11.ListNotificationMessageResponse, error)
	// 查询通知消息详情
	Get(ctx context.Context, in *v11.GetNotificationMessageRequest, opts ...grpc.CallOption) (*v11.NotificationMessage, error)
	// 创建通知消息
	Create(ctx context.Context, in *v11.CreateNotificationMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新通知消息
	Update(ctx context.Context, in *v11.UpdateNotificationMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除通知消息
	Delete(ctx context.Context, in *v11.DeleteNotificationMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type notificationMessageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationMessageServiceClient(cc grpc.ClientConnInterface) NotificationMessageServiceClient {
	return &notificationMessageServiceClient{cc}
}

func (c *notificationMessageServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*v11.ListNotificationMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.ListNotificationMessageResponse)
	err := c.cc.Invoke(ctx, NotificationMessageService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageServiceClient) Get(ctx context.Context, in *v11.GetNotificationMessageRequest, opts ...grpc.CallOption) (*v11.NotificationMessage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.NotificationMessage)
	err := c.cc.Invoke(ctx, NotificationMessageService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageServiceClient) Create(ctx context.Context, in *v11.CreateNotificationMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NotificationMessageService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageServiceClient) Update(ctx context.Context, in *v11.UpdateNotificationMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NotificationMessageService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationMessageServiceClient) Delete(ctx context.Context, in *v11.DeleteNotificationMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, NotificationMessageService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationMessageServiceServer is the server API for NotificationMessageService service.
// All implementations must embed UnimplementedNotificationMessageServiceServer
// for forward compatibility.
//
// 通知消息管理服务
type NotificationMessageServiceServer interface {
	// 查询通知消息列表
	List(context.Context, *v1.PagingRequest) (*v11.ListNotificationMessageResponse, error)
	// 查询通知消息详情
	Get(context.Context, *v11.GetNotificationMessageRequest) (*v11.NotificationMessage, error)
	// 创建通知消息
	Create(context.Context, *v11.CreateNotificationMessageRequest) (*emptypb.Empty, error)
	// 更新通知消息
	Update(context.Context, *v11.UpdateNotificationMessageRequest) (*emptypb.Empty, error)
	// 删除通知消息
	Delete(context.Context, *v11.DeleteNotificationMessageRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedNotificationMessageServiceServer()
}

// UnimplementedNotificationMessageServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNotificationMessageServiceServer struct{}

func (UnimplementedNotificationMessageServiceServer) List(context.Context, *v1.PagingRequest) (*v11.ListNotificationMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedNotificationMessageServiceServer) Get(context.Context, *v11.GetNotificationMessageRequest) (*v11.NotificationMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedNotificationMessageServiceServer) Create(context.Context, *v11.CreateNotificationMessageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedNotificationMessageServiceServer) Update(context.Context, *v11.UpdateNotificationMessageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedNotificationMessageServiceServer) Delete(context.Context, *v11.DeleteNotificationMessageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedNotificationMessageServiceServer) mustEmbedUnimplementedNotificationMessageServiceServer() {
}
func (UnimplementedNotificationMessageServiceServer) testEmbeddedByValue() {}

// UnsafeNotificationMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationMessageServiceServer will
// result in compilation errors.
type UnsafeNotificationMessageServiceServer interface {
	mustEmbedUnimplementedNotificationMessageServiceServer()
}

func RegisterNotificationMessageServiceServer(s grpc.ServiceRegistrar, srv NotificationMessageServiceServer) {
	// If the following call pancis, it indicates UnimplementedNotificationMessageServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&NotificationMessageService_ServiceDesc, srv)
}

func _NotificationMessageService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.GetNotificationMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageServiceServer).Get(ctx, req.(*v11.GetNotificationMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.CreateNotificationMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageServiceServer).Create(ctx, req.(*v11.CreateNotificationMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.UpdateNotificationMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageServiceServer).Update(ctx, req.(*v11.UpdateNotificationMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationMessageService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.DeleteNotificationMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationMessageServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationMessageService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationMessageServiceServer).Delete(ctx, req.(*v11.DeleteNotificationMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationMessageService_ServiceDesc is the grpc.ServiceDesc for NotificationMessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationMessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.NotificationMessageService",
	HandlerType: (*NotificationMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _NotificationMessageService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _NotificationMessageService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _NotificationMessageService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _NotificationMessageService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _NotificationMessageService_Delete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_notification_message.proto",
}
