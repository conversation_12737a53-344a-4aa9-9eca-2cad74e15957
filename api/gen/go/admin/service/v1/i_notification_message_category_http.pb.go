// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_notification_message_category.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationNotificationMessageCategoryServiceCreate = "/admin.service.v1.NotificationMessageCategoryService/Create"
const OperationNotificationMessageCategoryServiceDelete = "/admin.service.v1.NotificationMessageCategoryService/Delete"
const OperationNotificationMessageCategoryServiceGet = "/admin.service.v1.NotificationMessageCategoryService/Get"
const OperationNotificationMessageCategoryServiceList = "/admin.service.v1.NotificationMessageCategoryService/List"
const OperationNotificationMessageCategoryServiceUpdate = "/admin.service.v1.NotificationMessageCategoryService/Update"

type NotificationMessageCategoryServiceHTTPServer interface {
	// Create 创建通知消息分类
	Create(context.Context, *v11.CreateNotificationMessageCategoryRequest) (*emptypb.Empty, error)
	// Delete 删除通知消息分类
	Delete(context.Context, *v11.DeleteNotificationMessageCategoryRequest) (*emptypb.Empty, error)
	// Get 查询通知消息分类详情
	Get(context.Context, *v11.GetNotificationMessageCategoryRequest) (*v11.NotificationMessageCategory, error)
	// List 查询通知消息分类列表
	List(context.Context, *v1.PagingRequest) (*v11.ListNotificationMessageCategoryResponse, error)
	// Update 更新通知消息分类
	Update(context.Context, *v11.UpdateNotificationMessageCategoryRequest) (*emptypb.Empty, error)
}

func RegisterNotificationMessageCategoryServiceHTTPServer(s *http.Server, srv NotificationMessageCategoryServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/notifications:categories", _NotificationMessageCategoryService_List9_HTTP_Handler(srv))
	r.GET("/admin/v1/notifications:categories/{id}", _NotificationMessageCategoryService_Get9_HTTP_Handler(srv))
	r.POST("/admin/v1/notifications:categories", _NotificationMessageCategoryService_Create7_HTTP_Handler(srv))
	r.PUT("/admin/v1/notifications:categories/{data.id}", _NotificationMessageCategoryService_Update7_HTTP_Handler(srv))
	r.DELETE("/admin/v1/notifications:categories/{id}", _NotificationMessageCategoryService_Delete7_HTTP_Handler(srv))
}

func _NotificationMessageCategoryService_List9_HTTP_Handler(srv NotificationMessageCategoryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageCategoryServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListNotificationMessageCategoryResponse)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageCategoryService_Get9_HTTP_Handler(srv NotificationMessageCategoryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetNotificationMessageCategoryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageCategoryServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetNotificationMessageCategoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.NotificationMessageCategory)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageCategoryService_Create7_HTTP_Handler(srv NotificationMessageCategoryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateNotificationMessageCategoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageCategoryServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreateNotificationMessageCategoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageCategoryService_Update7_HTTP_Handler(srv NotificationMessageCategoryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdateNotificationMessageCategoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageCategoryServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdateNotificationMessageCategoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _NotificationMessageCategoryService_Delete7_HTTP_Handler(srv NotificationMessageCategoryServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteNotificationMessageCategoryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationNotificationMessageCategoryServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeleteNotificationMessageCategoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type NotificationMessageCategoryServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreateNotificationMessageCategoryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeleteNotificationMessageCategoryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetNotificationMessageCategoryRequest, opts ...http.CallOption) (rsp *v11.NotificationMessageCategory, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListNotificationMessageCategoryResponse, err error)
	Update(ctx context.Context, req *v11.UpdateNotificationMessageCategoryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type NotificationMessageCategoryServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewNotificationMessageCategoryServiceHTTPClient(client *http.Client) NotificationMessageCategoryServiceHTTPClient {
	return &NotificationMessageCategoryServiceHTTPClientImpl{client}
}

func (c *NotificationMessageCategoryServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreateNotificationMessageCategoryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/notifications:categories"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNotificationMessageCategoryServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageCategoryServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeleteNotificationMessageCategoryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/notifications:categories/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNotificationMessageCategoryServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageCategoryServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetNotificationMessageCategoryRequest, opts ...http.CallOption) (*v11.NotificationMessageCategory, error) {
	var out v11.NotificationMessageCategory
	pattern := "/admin/v1/notifications:categories/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNotificationMessageCategoryServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageCategoryServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListNotificationMessageCategoryResponse, error) {
	var out v11.ListNotificationMessageCategoryResponse
	pattern := "/admin/v1/notifications:categories"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationNotificationMessageCategoryServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *NotificationMessageCategoryServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdateNotificationMessageCategoryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/notifications:categories/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationNotificationMessageCategoryServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
