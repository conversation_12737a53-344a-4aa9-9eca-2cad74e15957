// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_file.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/file/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_file_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_file_proto_rawDesc = "" +
	"\n" +
	"\x1dadmin/service/v1/i_file.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1epagination/v1/pagination.proto\x1a\x1afile/service/v1/file.proto2\xfb\x03\n" +
	"\vFileService\x12]\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a!.file.service.v1.ListFileResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/admin/v1/files\x12[\n" +
	"\x03Get\x12\x1f.file.service.v1.GetFileRequest\x1a\x15.file.service.v1.File\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/files/{id}\x12`\n" +
	"\x06Create\x12\".file.service.v1.CreateFileRequest\x1a\x16.google.protobuf.Empty\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/admin/v1/files\x12j\n" +
	"\x06Update\x12\".file.service.v1.UpdateFileRequest\x1a\x16.google.protobuf.Empty\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/files/{data.id}\x12b\n" +
	"\x06Delete\x12\".file.service.v1.DeleteFileRequest\x1a\x16.google.protobuf.Empty\"\x1c\x82\xd3\xe4\x93\x02\x16*\x14/admin/v1/files/{id}B\xb8\x01\n" +
	"\x14com.admin.service.v1B\n" +
	"IFileProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_file_proto_goTypes = []any{
	(*v1.PagingRequest)(nil),      // 0: pagination.PagingRequest
	(*v11.GetFileRequest)(nil),    // 1: file.service.v1.GetFileRequest
	(*v11.CreateFileRequest)(nil), // 2: file.service.v1.CreateFileRequest
	(*v11.UpdateFileRequest)(nil), // 3: file.service.v1.UpdateFileRequest
	(*v11.DeleteFileRequest)(nil), // 4: file.service.v1.DeleteFileRequest
	(*v11.ListFileResponse)(nil),  // 5: file.service.v1.ListFileResponse
	(*v11.File)(nil),              // 6: file.service.v1.File
	(*emptypb.Empty)(nil),         // 7: google.protobuf.Empty
}
var file_admin_service_v1_i_file_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.FileService.List:input_type -> pagination.PagingRequest
	1, // 1: admin.service.v1.FileService.Get:input_type -> file.service.v1.GetFileRequest
	2, // 2: admin.service.v1.FileService.Create:input_type -> file.service.v1.CreateFileRequest
	3, // 3: admin.service.v1.FileService.Update:input_type -> file.service.v1.UpdateFileRequest
	4, // 4: admin.service.v1.FileService.Delete:input_type -> file.service.v1.DeleteFileRequest
	5, // 5: admin.service.v1.FileService.List:output_type -> file.service.v1.ListFileResponse
	6, // 6: admin.service.v1.FileService.Get:output_type -> file.service.v1.File
	7, // 7: admin.service.v1.FileService.Create:output_type -> google.protobuf.Empty
	7, // 8: admin.service.v1.FileService.Update:output_type -> google.protobuf.Empty
	7, // 9: admin.service.v1.FileService.Delete:output_type -> google.protobuf.Empty
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_file_proto_init() }
func file_admin_service_v1_i_file_proto_init() {
	if File_admin_service_v1_i_file_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_file_proto_rawDesc), len(file_admin_service_v1_i_file_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_file_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_file_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_file_proto = out.File
	file_admin_service_v1_i_file_proto_goTypes = nil
	file_admin_service_v1_i_file_proto_depIdxs = nil
}
