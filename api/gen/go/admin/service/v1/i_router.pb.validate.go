// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: admin/service/v1/i_router.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RouteItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RouteItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RouteItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RouteItemMultiError, or nil
// if none found.
func (m *RouteItem) ValidateAll() error {
	return m.validate(true)
}

func (m *RouteItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RouteItemValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RouteItemValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RouteItemValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Path != nil {
		// no validation rules for Path
	}

	if m.Redirect != nil {
		// no validation rules for Redirect
	}

	if m.Alias != nil {
		// no validation rules for Alias
	}

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Component != nil {
		// no validation rules for Component
	}

	if m.Meta != nil {

		if all {
			switch v := interface{}(m.GetMeta()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RouteItemValidationError{
						field:  "Meta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RouteItemValidationError{
						field:  "Meta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RouteItemValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RouteItemMultiError(errors)
	}

	return nil
}

// RouteItemMultiError is an error wrapping multiple validation errors returned
// by RouteItem.ValidateAll() if the designated constraints aren't met.
type RouteItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RouteItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RouteItemMultiError) AllErrors() []error { return m }

// RouteItemValidationError is the validation error returned by
// RouteItem.Validate if the designated constraints aren't met.
type RouteItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RouteItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RouteItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RouteItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RouteItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RouteItemValidationError) ErrorName() string { return "RouteItemValidationError" }

// Error satisfies the builtin error interface
func (e RouteItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRouteItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RouteItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RouteItemValidationError{}

// Validate checks the field values on RouteMeta with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RouteMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RouteMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RouteMetaMultiError, or nil
// if none found.
func (m *RouteMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *RouteMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ActiveIcon != nil {
		// no validation rules for ActiveIcon
	}

	if m.ActivePath != nil {
		// no validation rules for ActivePath
	}

	if m.AffixTab != nil {
		// no validation rules for AffixTab
	}

	if m.AffixTabOrder != nil {
		// no validation rules for AffixTabOrder
	}

	if m.Badge != nil {
		// no validation rules for Badge
	}

	if m.BadgeType != nil {
		// no validation rules for BadgeType
	}

	if m.BadgeVariants != nil {
		// no validation rules for BadgeVariants
	}

	if m.HideChildrenInMenu != nil {
		// no validation rules for HideChildrenInMenu
	}

	if m.HideInBreadcrumb != nil {
		// no validation rules for HideInBreadcrumb
	}

	if m.HideInMenu != nil {
		// no validation rules for HideInMenu
	}

	if m.HideInTab != nil {
		// no validation rules for HideInTab
	}

	if m.Icon != nil {
		// no validation rules for Icon
	}

	if m.IframeSrc != nil {
		// no validation rules for IframeSrc
	}

	if m.IgnoreAccess != nil {
		// no validation rules for IgnoreAccess
	}

	if m.KeepAlive != nil {
		// no validation rules for KeepAlive
	}

	if m.Link != nil {
		// no validation rules for Link
	}

	if m.Loaded != nil {
		// no validation rules for Loaded
	}

	if m.MaxNumOfOpenTab != nil {
		// no validation rules for MaxNumOfOpenTab
	}

	if m.MenuVisibleWithForbidden != nil {
		// no validation rules for MenuVisibleWithForbidden
	}

	if m.OpenInNewWindow != nil {
		// no validation rules for OpenInNewWindow
	}

	if m.Order != nil {
		// no validation rules for Order
	}

	if m.Title != nil {
		// no validation rules for Title
	}

	if len(errors) > 0 {
		return RouteMetaMultiError(errors)
	}

	return nil
}

// RouteMetaMultiError is an error wrapping multiple validation errors returned
// by RouteMeta.ValidateAll() if the designated constraints aren't met.
type RouteMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RouteMetaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RouteMetaMultiError) AllErrors() []error { return m }

// RouteMetaValidationError is the validation error returned by
// RouteMeta.Validate if the designated constraints aren't met.
type RouteMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RouteMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RouteMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RouteMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RouteMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RouteMetaValidationError) ErrorName() string { return "RouteMetaValidationError" }

// Error satisfies the builtin error interface
func (e RouteMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRouteMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RouteMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RouteMetaValidationError{}

// Validate checks the field values on ListRouteRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListRouteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRouteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListRouteRequestMultiError, or nil if none found.
func (m *ListRouteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRouteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListRouteRequestMultiError(errors)
	}

	return nil
}

// ListRouteRequestMultiError is an error wrapping multiple validation errors
// returned by ListRouteRequest.ValidateAll() if the designated constraints
// aren't met.
type ListRouteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRouteRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRouteRequestMultiError) AllErrors() []error { return m }

// ListRouteRequestValidationError is the validation error returned by
// ListRouteRequest.Validate if the designated constraints aren't met.
type ListRouteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRouteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRouteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRouteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRouteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRouteRequestValidationError) ErrorName() string { return "ListRouteRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListRouteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRouteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRouteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRouteRequestValidationError{}

// Validate checks the field values on ListRouteResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListRouteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRouteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListRouteResponseMultiError, or nil if none found.
func (m *ListRouteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRouteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRouteResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRouteResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRouteResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListRouteResponseMultiError(errors)
	}

	return nil
}

// ListRouteResponseMultiError is an error wrapping multiple validation errors
// returned by ListRouteResponse.ValidateAll() if the designated constraints
// aren't met.
type ListRouteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRouteResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRouteResponseMultiError) AllErrors() []error { return m }

// ListRouteResponseValidationError is the validation error returned by
// ListRouteResponse.Validate if the designated constraints aren't met.
type ListRouteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRouteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRouteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRouteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRouteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRouteResponseValidationError) ErrorName() string {
	return "ListRouteResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListRouteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRouteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRouteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRouteResponseValidationError{}

// Validate checks the field values on ListPermissionCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPermissionCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPermissionCodeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPermissionCodeRequestMultiError, or nil if none found.
func (m *ListPermissionCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPermissionCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListPermissionCodeRequestMultiError(errors)
	}

	return nil
}

// ListPermissionCodeRequestMultiError is an error wrapping multiple validation
// errors returned by ListPermissionCodeRequest.ValidateAll() if the
// designated constraints aren't met.
type ListPermissionCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPermissionCodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPermissionCodeRequestMultiError) AllErrors() []error { return m }

// ListPermissionCodeRequestValidationError is the validation error returned by
// ListPermissionCodeRequest.Validate if the designated constraints aren't met.
type ListPermissionCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPermissionCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPermissionCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPermissionCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPermissionCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPermissionCodeRequestValidationError) ErrorName() string {
	return "ListPermissionCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPermissionCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPermissionCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPermissionCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPermissionCodeRequestValidationError{}

// Validate checks the field values on ListPermissionCodeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPermissionCodeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPermissionCodeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPermissionCodeResponseMultiError, or nil if none found.
func (m *ListPermissionCodeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPermissionCodeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListPermissionCodeResponseMultiError(errors)
	}

	return nil
}

// ListPermissionCodeResponseMultiError is an error wrapping multiple
// validation errors returned by ListPermissionCodeResponse.ValidateAll() if
// the designated constraints aren't met.
type ListPermissionCodeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPermissionCodeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPermissionCodeResponseMultiError) AllErrors() []error { return m }

// ListPermissionCodeResponseValidationError is the validation error returned
// by ListPermissionCodeResponse.Validate if the designated constraints aren't met.
type ListPermissionCodeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPermissionCodeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPermissionCodeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPermissionCodeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPermissionCodeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPermissionCodeResponseValidationError) ErrorName() string {
	return "ListPermissionCodeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListPermissionCodeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPermissionCodeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPermissionCodeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPermissionCodeResponseValidationError{}
