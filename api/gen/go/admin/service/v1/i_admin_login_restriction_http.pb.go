// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_admin_login_restriction.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAdminLoginRestrictionServiceCreate = "/admin.service.v1.AdminLoginRestrictionService/Create"
const OperationAdminLoginRestrictionServiceDelete = "/admin.service.v1.AdminLoginRestrictionService/Delete"
const OperationAdminLoginRestrictionServiceGet = "/admin.service.v1.AdminLoginRestrictionService/Get"
const OperationAdminLoginRestrictionServiceList = "/admin.service.v1.AdminLoginRestrictionService/List"
const OperationAdminLoginRestrictionServiceUpdate = "/admin.service.v1.AdminLoginRestrictionService/Update"

type AdminLoginRestrictionServiceHTTPServer interface {
	// Create 创建后台登录限制
	Create(context.Context, *CreateAdminLoginRestrictionRequest) (*emptypb.Empty, error)
	// Delete 删除后台登录限制
	Delete(context.Context, *DeleteAdminLoginRestrictionRequest) (*emptypb.Empty, error)
	// Get 查询后台登录限制详情
	Get(context.Context, *GetAdminLoginRestrictionRequest) (*AdminLoginRestriction, error)
	// List 查询后台登录限制列表
	List(context.Context, *v1.PagingRequest) (*ListAdminLoginRestrictionResponse, error)
	// Update 更新后台登录限制
	Update(context.Context, *UpdateAdminLoginRestrictionRequest) (*emptypb.Empty, error)
}

func RegisterAdminLoginRestrictionServiceHTTPServer(s *http.Server, srv AdminLoginRestrictionServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/login-restrictions", _AdminLoginRestrictionService_List1_HTTP_Handler(srv))
	r.GET("/admin/v1/login-restrictions/{id}", _AdminLoginRestrictionService_Get1_HTTP_Handler(srv))
	r.POST("/admin/v1/login-restrictions", _AdminLoginRestrictionService_Create0_HTTP_Handler(srv))
	r.PUT("/admin/v1/login-restrictions/{data.id}", _AdminLoginRestrictionService_Update0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/login-restrictions/{id}", _AdminLoginRestrictionService_Delete0_HTTP_Handler(srv))
}

func _AdminLoginRestrictionService_List1_HTTP_Handler(srv AdminLoginRestrictionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminLoginRestrictionServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAdminLoginRestrictionResponse)
		return ctx.Result(200, reply)
	}
}

func _AdminLoginRestrictionService_Get1_HTTP_Handler(srv AdminLoginRestrictionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAdminLoginRestrictionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminLoginRestrictionServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetAdminLoginRestrictionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminLoginRestriction)
		return ctx.Result(200, reply)
	}
}

func _AdminLoginRestrictionService_Create0_HTTP_Handler(srv AdminLoginRestrictionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAdminLoginRestrictionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminLoginRestrictionServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*CreateAdminLoginRestrictionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _AdminLoginRestrictionService_Update0_HTTP_Handler(srv AdminLoginRestrictionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAdminLoginRestrictionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminLoginRestrictionServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*UpdateAdminLoginRestrictionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _AdminLoginRestrictionService_Delete0_HTTP_Handler(srv AdminLoginRestrictionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAdminLoginRestrictionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminLoginRestrictionServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*DeleteAdminLoginRestrictionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type AdminLoginRestrictionServiceHTTPClient interface {
	Create(ctx context.Context, req *CreateAdminLoginRestrictionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *DeleteAdminLoginRestrictionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *GetAdminLoginRestrictionRequest, opts ...http.CallOption) (rsp *AdminLoginRestriction, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *ListAdminLoginRestrictionResponse, err error)
	Update(ctx context.Context, req *UpdateAdminLoginRestrictionRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type AdminLoginRestrictionServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAdminLoginRestrictionServiceHTTPClient(client *http.Client) AdminLoginRestrictionServiceHTTPClient {
	return &AdminLoginRestrictionServiceHTTPClientImpl{client}
}

func (c *AdminLoginRestrictionServiceHTTPClientImpl) Create(ctx context.Context, in *CreateAdminLoginRestrictionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/login-restrictions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminLoginRestrictionServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminLoginRestrictionServiceHTTPClientImpl) Delete(ctx context.Context, in *DeleteAdminLoginRestrictionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/login-restrictions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminLoginRestrictionServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminLoginRestrictionServiceHTTPClientImpl) Get(ctx context.Context, in *GetAdminLoginRestrictionRequest, opts ...http.CallOption) (*AdminLoginRestriction, error) {
	var out AdminLoginRestriction
	pattern := "/admin/v1/login-restrictions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminLoginRestrictionServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminLoginRestrictionServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*ListAdminLoginRestrictionResponse, error) {
	var out ListAdminLoginRestrictionResponse
	pattern := "/admin/v1/login-restrictions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminLoginRestrictionServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdminLoginRestrictionServiceHTTPClientImpl) Update(ctx context.Context, in *UpdateAdminLoginRestrictionRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/login-restrictions/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdminLoginRestrictionServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
