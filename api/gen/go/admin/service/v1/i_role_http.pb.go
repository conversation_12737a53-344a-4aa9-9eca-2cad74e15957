// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_role.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/user/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRoleServiceCreate = "/admin.service.v1.RoleService/Create"
const OperationRoleServiceDelete = "/admin.service.v1.RoleService/Delete"
const OperationRoleServiceGet = "/admin.service.v1.RoleService/Get"
const OperationRoleServiceList = "/admin.service.v1.RoleService/List"
const OperationRoleServiceUpdate = "/admin.service.v1.RoleService/Update"

type RoleServiceHTTPServer interface {
	// Create 创建角色
	Create(context.Context, *v11.CreateRoleRequest) (*emptypb.Empty, error)
	// Delete 删除角色
	Delete(context.Context, *v11.DeleteRoleRequest) (*emptypb.Empty, error)
	// Get 查询角色详情
	Get(context.Context, *v11.GetRoleRequest) (*v11.Role, error)
	// List 查询角色列表
	List(context.Context, *v1.PagingRequest) (*v11.ListRoleResponse, error)
	// Update 更新角色
	Update(context.Context, *v11.UpdateRoleRequest) (*emptypb.Empty, error)
}

func RegisterRoleServiceHTTPServer(s *http.Server, srv RoleServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/roles", _RoleService_List14_HTTP_Handler(srv))
	r.GET("/admin/v1/roles/{id}", _RoleService_Get14_HTTP_Handler(srv))
	r.POST("/admin/v1/roles", _RoleService_Create12_HTTP_Handler(srv))
	r.PUT("/admin/v1/roles/{data.id}", _RoleService_Update12_HTTP_Handler(srv))
	r.DELETE("/admin/v1/roles/{id}", _RoleService_Delete12_HTTP_Handler(srv))
}

func _RoleService_List14_HTTP_Handler(srv RoleServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRoleServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListRoleResponse)
		return ctx.Result(200, reply)
	}
}

func _RoleService_Get14_HTTP_Handler(srv RoleServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRoleServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.Role)
		return ctx.Result(200, reply)
	}
}

func _RoleService_Create12_HTTP_Handler(srv RoleServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRoleServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _RoleService_Update12_HTTP_Handler(srv RoleServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRoleServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _RoleService_Delete12_HTTP_Handler(srv RoleServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRoleServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeleteRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type RoleServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreateRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeleteRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetRoleRequest, opts ...http.CallOption) (rsp *v11.Role, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListRoleResponse, err error)
	Update(ctx context.Context, req *v11.UpdateRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type RoleServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewRoleServiceHTTPClient(client *http.Client) RoleServiceHTTPClient {
	return &RoleServiceHTTPClientImpl{client}
}

func (c *RoleServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreateRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/roles"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRoleServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RoleServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeleteRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/roles/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRoleServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RoleServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetRoleRequest, opts ...http.CallOption) (*v11.Role, error) {
	var out v11.Role
	pattern := "/admin/v1/roles/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRoleServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RoleServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListRoleResponse, error) {
	var out v11.ListRoleResponse
	pattern := "/admin/v1/roles"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRoleServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RoleServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdateRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/roles/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRoleServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
