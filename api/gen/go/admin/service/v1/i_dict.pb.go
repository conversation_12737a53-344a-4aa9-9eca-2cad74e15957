// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_dict.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 字典
type Dict struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                             // 字典ID
	Category      *string                `protobuf:"bytes,2,opt,name=category,proto3,oneof" json:"category,omitempty"`                                  // 字典分类
	CategoryDesc  *string                `protobuf:"bytes,3,opt,name=category_desc,json=categoryDesc,proto3,oneof" json:"category_desc,omitempty"`      // 字典分类名称
	Key           *string                `protobuf:"bytes,4,opt,name=key,proto3,oneof" json:"key,omitempty"`                                            // 字典键
	Value         *string                `protobuf:"bytes,5,opt,name=value,proto3,oneof" json:"value,omitempty"`                                        // 字典值
	ValueDesc     *string                `protobuf:"bytes,6,opt,name=value_desc,json=valueDesc,proto3,oneof" json:"value_desc,omitempty"`               // 字典值名称
	ValueDataType *string                `protobuf:"bytes,7,opt,name=value_data_type,json=valueDataType,proto3,oneof" json:"value_data_type,omitempty"` // 字典值数据类型
	Status        *string                `protobuf:"bytes,10,opt,name=status,proto3,oneof" json:"status,omitempty"`                                     // 字典状态
	SortId        *int32                 `protobuf:"varint,11,opt,name=sort_id,json=sortId,proto3,oneof" json:"sort_id,omitempty"`                      // 排序编号
	Remark        *string                `protobuf:"bytes,12,opt,name=remark,proto3,oneof" json:"remark,omitempty"`                                     // 备注
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`               // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`               // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`          // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`          // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`          // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dict) Reset() {
	*x = Dict{}
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dict) ProtoMessage() {}

func (x *Dict) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dict.ProtoReflect.Descriptor instead.
func (*Dict) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_dict_proto_rawDescGZIP(), []int{0}
}

func (x *Dict) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Dict) GetCategory() string {
	if x != nil && x.Category != nil {
		return *x.Category
	}
	return ""
}

func (x *Dict) GetCategoryDesc() string {
	if x != nil && x.CategoryDesc != nil {
		return *x.CategoryDesc
	}
	return ""
}

func (x *Dict) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *Dict) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *Dict) GetValueDesc() string {
	if x != nil && x.ValueDesc != nil {
		return *x.ValueDesc
	}
	return ""
}

func (x *Dict) GetValueDataType() string {
	if x != nil && x.ValueDataType != nil {
		return *x.ValueDataType
	}
	return ""
}

func (x *Dict) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *Dict) GetSortId() int32 {
	if x != nil && x.SortId != nil {
		return *x.SortId
	}
	return 0
}

func (x *Dict) GetRemark() string {
	if x != nil && x.Remark != nil {
		return *x.Remark
	}
	return ""
}

func (x *Dict) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *Dict) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *Dict) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Dict) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Dict) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询字典列表 - 回应
type ListDictResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Dict                `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictResponse) Reset() {
	*x = ListDictResponse{}
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictResponse) ProtoMessage() {}

func (x *ListDictResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictResponse.ProtoReflect.Descriptor instead.
func (*ListDictResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_dict_proto_rawDescGZIP(), []int{1}
}

func (x *ListDictResponse) GetItems() []*Dict {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListDictResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询字典详情 - 请求
type GetDictRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictRequest) Reset() {
	*x = GetDictRequest{}
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictRequest) ProtoMessage() {}

func (x *GetDictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictRequest.ProtoReflect.Descriptor instead.
func (*GetDictRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_dict_proto_rawDescGZIP(), []int{2}
}

func (x *GetDictRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建字典 - 请求
type CreateDictRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Dict                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictRequest) Reset() {
	*x = CreateDictRequest{}
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictRequest) ProtoMessage() {}

func (x *CreateDictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictRequest.ProtoReflect.Descriptor instead.
func (*CreateDictRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_dict_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDictRequest) GetData() *Dict {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新字典 - 请求
type UpdateDictRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Dict                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictRequest) Reset() {
	*x = UpdateDictRequest{}
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictRequest) ProtoMessage() {}

func (x *UpdateDictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictRequest.ProtoReflect.Descriptor instead.
func (*UpdateDictRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_dict_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDictRequest) GetData() *Dict {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateDictRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateDictRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除字典 - 请求
type DeleteDictRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictRequest) Reset() {
	*x = DeleteDictRequest{}
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictRequest) ProtoMessage() {}

func (x *DeleteDictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_dict_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictRequest.ProtoReflect.Descriptor instead.
func (*DeleteDictRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_dict_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDictRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_admin_service_v1_i_dict_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_dict_proto_rawDesc = "" +
	"\n" +
	"\x1dadmin/service/v1/i_dict.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xdc\b\n" +
	"\x04Dict\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b字典IDH\x00R\x02id\x88\x01\x01\x123\n" +
	"\bcategory\x18\x02 \x01(\tB\x12\xbaG\x0f\x92\x02\f字典分类H\x01R\bcategory\x88\x01\x01\x12B\n" +
	"\rcategory_desc\x18\x03 \x01(\tB\x18\xbaG\x15\x92\x02\x12字典分类名称H\x02R\fcategoryDesc\x88\x01\x01\x12&\n" +
	"\x03key\x18\x04 \x01(\tB\x0f\xbaG\f\x92\x02\t字典键H\x03R\x03key\x88\x01\x01\x12*\n" +
	"\x05value\x18\x05 \x01(\tB\x0f\xbaG\f\x92\x02\t字典值H\x04R\x05value\x88\x01\x01\x129\n" +
	"\n" +
	"value_desc\x18\x06 \x01(\tB\x15\xbaG\x12\x92\x02\x0f字典值名称H\x05R\tvalueDesc\x88\x01\x01\x12H\n" +
	"\x0fvalue_data_type\x18\a \x01(\tB\x1b\xbaG\x18\x92\x02\x15字典值数据类型H\x06R\rvalueDataType\x88\x01\x01\x12E\n" +
	"\x06status\x18\n" +
	" \x01(\tB(\xbaG%\xc2\x01\x04\x12\x02ON\xc2\x01\x05\x12\x03OFF\x8a\x02\x04\x1a\x02ON\x92\x02\f字典状态H\aR\x06status\x88\x01\x01\x120\n" +
	"\asort_id\x18\v \x01(\x05B\x12\xbaG\x0f\x92\x02\f排序编号H\bR\x06sortId\x88\x01\x01\x12)\n" +
	"\x06remark\x18\f \x01(\tB\f\xbaG\t\x92\x02\x06备注H\tR\x06remark\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\n" +
	"R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\vR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\fR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\rR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\x0eR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\v\n" +
	"\t_categoryB\x10\n" +
	"\x0e_category_descB\x06\n" +
	"\x04_keyB\b\n" +
	"\x06_valueB\r\n" +
	"\v_value_descB\x12\n" +
	"\x10_value_data_typeB\t\n" +
	"\a_statusB\n" +
	"\n" +
	"\b_sort_idB\t\n" +
	"\a_remarkB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"V\n" +
	"\x10ListDictResponse\x12,\n" +
	"\x05items\x18\x01 \x03(\v2\x16.admin.service.v1.DictR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\" \n" +
	"\x0eGetDictRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"?\n" +
	"\x11CreateDictRequest\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.admin.service.v1.DictR\x04data\"\xfd\x02\n" +
	"\x11UpdateDictRequest\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.admin.service.v1.DictR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"#\n" +
	"\x11DeleteDictRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\xfc\x03\n" +
	"\vDictService\x12]\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a\".admin.service.v1.ListDictResponse\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/admin/v1/dict\x12\\\n" +
	"\x03Get\x12 .admin.service.v1.GetDictRequest\x1a\x16.admin.service.v1.Dict\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/admin/v1/dict/{id}\x12`\n" +
	"\x06Create\x12#.admin.service.v1.CreateDictRequest\x1a\x16.google.protobuf.Empty\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\"\x0e/admin/v1/dict\x12j\n" +
	"\x06Update\x12#.admin.service.v1.UpdateDictRequest\x1a\x16.google.protobuf.Empty\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\x1a\x18/admin/v1/dict/{data.id}\x12b\n" +
	"\x06Delete\x12#.admin.service.v1.DeleteDictRequest\x1a\x16.google.protobuf.Empty\"\x1b\x82\xd3\xe4\x93\x02\x15*\x13/admin/v1/dict/{id}B\xb8\x01\n" +
	"\x14com.admin.service.v1B\n" +
	"IDictProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_dict_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_dict_proto_rawDescData []byte
)

func file_admin_service_v1_i_dict_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_dict_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_dict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_dict_proto_rawDesc), len(file_admin_service_v1_i_dict_proto_rawDesc)))
	})
	return file_admin_service_v1_i_dict_proto_rawDescData
}

var file_admin_service_v1_i_dict_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_admin_service_v1_i_dict_proto_goTypes = []any{
	(*Dict)(nil),                  // 0: admin.service.v1.Dict
	(*ListDictResponse)(nil),      // 1: admin.service.v1.ListDictResponse
	(*GetDictRequest)(nil),        // 2: admin.service.v1.GetDictRequest
	(*CreateDictRequest)(nil),     // 3: admin.service.v1.CreateDictRequest
	(*UpdateDictRequest)(nil),     // 4: admin.service.v1.UpdateDictRequest
	(*DeleteDictRequest)(nil),     // 5: admin.service.v1.DeleteDictRequest
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil), // 7: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),      // 8: pagination.PagingRequest
	(*emptypb.Empty)(nil),         // 9: google.protobuf.Empty
}
var file_admin_service_v1_i_dict_proto_depIdxs = []int32{
	6,  // 0: admin.service.v1.Dict.create_time:type_name -> google.protobuf.Timestamp
	6,  // 1: admin.service.v1.Dict.update_time:type_name -> google.protobuf.Timestamp
	6,  // 2: admin.service.v1.Dict.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 3: admin.service.v1.ListDictResponse.items:type_name -> admin.service.v1.Dict
	0,  // 4: admin.service.v1.CreateDictRequest.data:type_name -> admin.service.v1.Dict
	0,  // 5: admin.service.v1.UpdateDictRequest.data:type_name -> admin.service.v1.Dict
	7,  // 6: admin.service.v1.UpdateDictRequest.update_mask:type_name -> google.protobuf.FieldMask
	8,  // 7: admin.service.v1.DictService.List:input_type -> pagination.PagingRequest
	2,  // 8: admin.service.v1.DictService.Get:input_type -> admin.service.v1.GetDictRequest
	3,  // 9: admin.service.v1.DictService.Create:input_type -> admin.service.v1.CreateDictRequest
	4,  // 10: admin.service.v1.DictService.Update:input_type -> admin.service.v1.UpdateDictRequest
	5,  // 11: admin.service.v1.DictService.Delete:input_type -> admin.service.v1.DeleteDictRequest
	1,  // 12: admin.service.v1.DictService.List:output_type -> admin.service.v1.ListDictResponse
	0,  // 13: admin.service.v1.DictService.Get:output_type -> admin.service.v1.Dict
	9,  // 14: admin.service.v1.DictService.Create:output_type -> google.protobuf.Empty
	9,  // 15: admin.service.v1.DictService.Update:output_type -> google.protobuf.Empty
	9,  // 16: admin.service.v1.DictService.Delete:output_type -> google.protobuf.Empty
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_dict_proto_init() }
func file_admin_service_v1_i_dict_proto_init() {
	if File_admin_service_v1_i_dict_proto != nil {
		return
	}
	file_admin_service_v1_i_dict_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_dict_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_dict_proto_rawDesc), len(file_admin_service_v1_i_dict_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_dict_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_dict_proto_depIdxs,
		MessageInfos:      file_admin_service_v1_i_dict_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_dict_proto = out.File
	file_admin_service_v1_i_dict_proto_goTypes = nil
	file_admin_service_v1_i_dict_proto_depIdxs = nil
}
