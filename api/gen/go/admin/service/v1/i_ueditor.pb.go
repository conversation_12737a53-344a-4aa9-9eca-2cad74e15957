// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_ueditor.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	_ "google.golang.org/genproto/googleapis/api/httpbody"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1 "kratos-admin/api/gen/go/file/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_ueditor_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_ueditor_proto_rawDesc = "" +
	"\n" +
	" admin/service/v1/i_ueditor.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x19google/api/httpbody.proto\x1a\x1dfile/service/v1/ueditor.proto2\xf9\x01\n" +
	"\x0eUEditorService\x12j\n" +
	"\n" +
	"UEditorAPI\x12\x1f.file.service.v1.UEditorRequest\x1a .file.service.v1.UEditorResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/admin/v1/ueditor\x12{\n" +
	"\n" +
	"UploadFile\x12%.file.service.v1.UEditorUploadRequest\x1a&.file.service.v1.UEditorUploadResponse\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/admin/v1/ueditor(\x01B\xbb\x01\n" +
	"\x14com.admin.service.v1B\rIUeditorProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_ueditor_proto_goTypes = []any{
	(*v1.UEditorRequest)(nil),        // 0: file.service.v1.UEditorRequest
	(*v1.UEditorUploadRequest)(nil),  // 1: file.service.v1.UEditorUploadRequest
	(*v1.UEditorResponse)(nil),       // 2: file.service.v1.UEditorResponse
	(*v1.UEditorUploadResponse)(nil), // 3: file.service.v1.UEditorUploadResponse
}
var file_admin_service_v1_i_ueditor_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.UEditorService.UEditorAPI:input_type -> file.service.v1.UEditorRequest
	1, // 1: admin.service.v1.UEditorService.UploadFile:input_type -> file.service.v1.UEditorUploadRequest
	2, // 2: admin.service.v1.UEditorService.UEditorAPI:output_type -> file.service.v1.UEditorResponse
	3, // 3: admin.service.v1.UEditorService.UploadFile:output_type -> file.service.v1.UEditorUploadResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_ueditor_proto_init() }
func file_admin_service_v1_i_ueditor_proto_init() {
	if File_admin_service_v1_i_ueditor_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_ueditor_proto_rawDesc), len(file_admin_service_v1_i_ueditor_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_ueditor_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_ueditor_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_ueditor_proto = out.File
	file_admin_service_v1_i_ueditor_proto_goTypes = nil
	file_admin_service_v1_i_ueditor_proto_depIdxs = nil
}
