// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: admin/service/v1/i_admin_operation_log.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AdminOperationLog with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AdminOperationLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminOperationLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminOperationLogMultiError, or nil if none found.
func (m *AdminOperationLog) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminOperationLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.CostTime != nil {

		if all {
			switch v := interface{}(m.GetCostTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdminOperationLogValidationError{
						field:  "CostTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdminOperationLogValidationError{
						field:  "CostTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCostTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdminOperationLogValidationError{
					field:  "CostTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Success != nil {
		// no validation rules for Success
	}

	if m.RequestId != nil {
		// no validation rules for RequestId
	}

	if m.StatusCode != nil {
		// no validation rules for StatusCode
	}

	if m.Reason != nil {
		// no validation rules for Reason
	}

	if m.Location != nil {
		// no validation rules for Location
	}

	if m.Operation != nil {
		// no validation rules for Operation
	}

	if m.Method != nil {
		// no validation rules for Method
	}

	if m.Path != nil {
		// no validation rules for Path
	}

	if m.ApiModule != nil {
		// no validation rules for ApiModule
	}

	if m.ApiDescription != nil {
		// no validation rules for ApiDescription
	}

	if m.Referer != nil {
		// no validation rules for Referer
	}

	if m.RequestUri != nil {
		// no validation rules for RequestUri
	}

	if m.RequestHeader != nil {
		// no validation rules for RequestHeader
	}

	if m.RequestBody != nil {
		// no validation rules for RequestBody
	}

	if m.Response != nil {
		// no validation rules for Response
	}

	if m.UserId != nil {
		// no validation rules for UserId
	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if m.ClientIp != nil {
		// no validation rules for ClientIp
	}

	if m.UserAgent != nil {
		// no validation rules for UserAgent
	}

	if m.BrowserName != nil {
		// no validation rules for BrowserName
	}

	if m.BrowserVersion != nil {
		// no validation rules for BrowserVersion
	}

	if m.ClientId != nil {
		// no validation rules for ClientId
	}

	if m.ClientName != nil {
		// no validation rules for ClientName
	}

	if m.OsName != nil {
		// no validation rules for OsName
	}

	if m.OsVersion != nil {
		// no validation rules for OsVersion
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdminOperationLogValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdminOperationLogValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdminOperationLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AdminOperationLogMultiError(errors)
	}

	return nil
}

// AdminOperationLogMultiError is an error wrapping multiple validation errors
// returned by AdminOperationLog.ValidateAll() if the designated constraints
// aren't met.
type AdminOperationLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminOperationLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminOperationLogMultiError) AllErrors() []error { return m }

// AdminOperationLogValidationError is the validation error returned by
// AdminOperationLog.Validate if the designated constraints aren't met.
type AdminOperationLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminOperationLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminOperationLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminOperationLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminOperationLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminOperationLogValidationError) ErrorName() string {
	return "AdminOperationLogValidationError"
}

// Error satisfies the builtin error interface
func (e AdminOperationLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminOperationLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminOperationLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminOperationLogValidationError{}

// Validate checks the field values on ListAdminOperationLogResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAdminOperationLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAdminOperationLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAdminOperationLogResponseMultiError, or nil if none found.
func (m *ListAdminOperationLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAdminOperationLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAdminOperationLogResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAdminOperationLogResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAdminOperationLogResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAdminOperationLogResponseMultiError(errors)
	}

	return nil
}

// ListAdminOperationLogResponseMultiError is an error wrapping multiple
// validation errors returned by ListAdminOperationLogResponse.ValidateAll()
// if the designated constraints aren't met.
type ListAdminOperationLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAdminOperationLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAdminOperationLogResponseMultiError) AllErrors() []error { return m }

// ListAdminOperationLogResponseValidationError is the validation error
// returned by ListAdminOperationLogResponse.Validate if the designated
// constraints aren't met.
type ListAdminOperationLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAdminOperationLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAdminOperationLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAdminOperationLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAdminOperationLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAdminOperationLogResponseValidationError) ErrorName() string {
	return "ListAdminOperationLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAdminOperationLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAdminOperationLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAdminOperationLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAdminOperationLogResponseValidationError{}

// Validate checks the field values on GetAdminOperationLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAdminOperationLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAdminOperationLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAdminOperationLogRequestMultiError, or nil if none found.
func (m *GetAdminOperationLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAdminOperationLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAdminOperationLogRequestMultiError(errors)
	}

	return nil
}

// GetAdminOperationLogRequestMultiError is an error wrapping multiple
// validation errors returned by GetAdminOperationLogRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAdminOperationLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAdminOperationLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAdminOperationLogRequestMultiError) AllErrors() []error { return m }

// GetAdminOperationLogRequestValidationError is the validation error returned
// by GetAdminOperationLogRequest.Validate if the designated constraints
// aren't met.
type GetAdminOperationLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAdminOperationLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAdminOperationLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAdminOperationLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAdminOperationLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAdminOperationLogRequestValidationError) ErrorName() string {
	return "GetAdminOperationLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAdminOperationLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAdminOperationLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAdminOperationLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAdminOperationLogRequestValidationError{}

// Validate checks the field values on CreateAdminOperationLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAdminOperationLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAdminOperationLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateAdminOperationLogRequestMultiError, or nil if none found.
func (m *CreateAdminOperationLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAdminOperationLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAdminOperationLogRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAdminOperationLogRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAdminOperationLogRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAdminOperationLogRequestMultiError(errors)
	}

	return nil
}

// CreateAdminOperationLogRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAdminOperationLogRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateAdminOperationLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAdminOperationLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAdminOperationLogRequestMultiError) AllErrors() []error { return m }

// CreateAdminOperationLogRequestValidationError is the validation error
// returned by CreateAdminOperationLogRequest.Validate if the designated
// constraints aren't met.
type CreateAdminOperationLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAdminOperationLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAdminOperationLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAdminOperationLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAdminOperationLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAdminOperationLogRequestValidationError) ErrorName() string {
	return "CreateAdminOperationLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAdminOperationLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAdminOperationLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAdminOperationLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAdminOperationLogRequestValidationError{}

// Validate checks the field values on UpdateAdminOperationLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAdminOperationLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAdminOperationLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateAdminOperationLogRequestMultiError, or nil if none found.
func (m *UpdateAdminOperationLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAdminOperationLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAdminOperationLogRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAdminOperationLogRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAdminOperationLogRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAdminOperationLogRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAdminOperationLogRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAdminOperationLogRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateAdminOperationLogRequestMultiError(errors)
	}

	return nil
}

// UpdateAdminOperationLogRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateAdminOperationLogRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateAdminOperationLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAdminOperationLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAdminOperationLogRequestMultiError) AllErrors() []error { return m }

// UpdateAdminOperationLogRequestValidationError is the validation error
// returned by UpdateAdminOperationLogRequest.Validate if the designated
// constraints aren't met.
type UpdateAdminOperationLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAdminOperationLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAdminOperationLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAdminOperationLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAdminOperationLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAdminOperationLogRequestValidationError) ErrorName() string {
	return "UpdateAdminOperationLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAdminOperationLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAdminOperationLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAdminOperationLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAdminOperationLogRequestValidationError{}

// Validate checks the field values on DeleteAdminOperationLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAdminOperationLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAdminOperationLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteAdminOperationLogRequestMultiError, or nil if none found.
func (m *DeleteAdminOperationLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAdminOperationLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAdminOperationLogRequestMultiError(errors)
	}

	return nil
}

// DeleteAdminOperationLogRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteAdminOperationLogRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteAdminOperationLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAdminOperationLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAdminOperationLogRequestMultiError) AllErrors() []error { return m }

// DeleteAdminOperationLogRequestValidationError is the validation error
// returned by DeleteAdminOperationLogRequest.Validate if the designated
// constraints aren't met.
type DeleteAdminOperationLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAdminOperationLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAdminOperationLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAdminOperationLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAdminOperationLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAdminOperationLogRequestValidationError) ErrorName() string {
	return "DeleteAdminOperationLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAdminOperationLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAdminOperationLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAdminOperationLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAdminOperationLogRequestValidationError{}
