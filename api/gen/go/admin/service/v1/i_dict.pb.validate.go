// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: admin/service/v1/i_dict.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Dict with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Dict) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Dict with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DictMultiError, or nil if none found.
func (m *Dict) ValidateAll() error {
	return m.validate(true)
}

func (m *Dict) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Category != nil {
		// no validation rules for Category
	}

	if m.CategoryDesc != nil {
		// no validation rules for CategoryDesc
	}

	if m.Key != nil {
		// no validation rules for Key
	}

	if m.Value != nil {
		// no validation rules for Value
	}

	if m.ValueDesc != nil {
		// no validation rules for ValueDesc
	}

	if m.ValueDataType != nil {
		// no validation rules for ValueDataType
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.SortId != nil {
		// no validation rules for SortId
	}

	if m.Remark != nil {
		// no validation rules for Remark
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DictValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DictValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DictValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DictValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DictValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DictValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DictValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DictValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DictValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DictMultiError(errors)
	}

	return nil
}

// DictMultiError is an error wrapping multiple validation errors returned by
// Dict.ValidateAll() if the designated constraints aren't met.
type DictMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DictMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DictMultiError) AllErrors() []error { return m }

// DictValidationError is the validation error returned by Dict.Validate if the
// designated constraints aren't met.
type DictValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DictValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DictValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DictValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DictValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DictValidationError) ErrorName() string { return "DictValidationError" }

// Error satisfies the builtin error interface
func (e DictValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDict.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DictValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DictValidationError{}

// Validate checks the field values on ListDictResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListDictResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDictResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDictResponseMultiError, or nil if none found.
func (m *ListDictResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDictResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDictResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDictResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDictResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListDictResponseMultiError(errors)
	}

	return nil
}

// ListDictResponseMultiError is an error wrapping multiple validation errors
// returned by ListDictResponse.ValidateAll() if the designated constraints
// aren't met.
type ListDictResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDictResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDictResponseMultiError) AllErrors() []error { return m }

// ListDictResponseValidationError is the validation error returned by
// ListDictResponse.Validate if the designated constraints aren't met.
type ListDictResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDictResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDictResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDictResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDictResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDictResponseValidationError) ErrorName() string { return "ListDictResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListDictResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDictResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDictResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDictResponseValidationError{}

// Validate checks the field values on GetDictRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDictRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDictRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDictRequestMultiError,
// or nil if none found.
func (m *GetDictRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDictRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetDictRequestMultiError(errors)
	}

	return nil
}

// GetDictRequestMultiError is an error wrapping multiple validation errors
// returned by GetDictRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDictRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDictRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDictRequestMultiError) AllErrors() []error { return m }

// GetDictRequestValidationError is the validation error returned by
// GetDictRequest.Validate if the designated constraints aren't met.
type GetDictRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDictRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDictRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDictRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDictRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDictRequestValidationError) ErrorName() string { return "GetDictRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetDictRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDictRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDictRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDictRequestValidationError{}

// Validate checks the field values on CreateDictRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDictRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDictRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDictRequestMultiError, or nil if none found.
func (m *CreateDictRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDictRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDictRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDictRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDictRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDictRequestMultiError(errors)
	}

	return nil
}

// CreateDictRequestMultiError is an error wrapping multiple validation errors
// returned by CreateDictRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateDictRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDictRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDictRequestMultiError) AllErrors() []error { return m }

// CreateDictRequestValidationError is the validation error returned by
// CreateDictRequest.Validate if the designated constraints aren't met.
type CreateDictRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDictRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDictRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDictRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDictRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDictRequestValidationError) ErrorName() string {
	return "CreateDictRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDictRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDictRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDictRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDictRequestValidationError{}

// Validate checks the field values on UpdateDictRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateDictRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDictRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDictRequestMultiError, or nil if none found.
func (m *UpdateDictRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDictRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDictRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDictRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDictRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDictRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDictRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDictRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateDictRequestMultiError(errors)
	}

	return nil
}

// UpdateDictRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateDictRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateDictRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDictRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDictRequestMultiError) AllErrors() []error { return m }

// UpdateDictRequestValidationError is the validation error returned by
// UpdateDictRequest.Validate if the designated constraints aren't met.
type UpdateDictRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDictRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDictRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDictRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDictRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDictRequestValidationError) ErrorName() string {
	return "UpdateDictRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDictRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDictRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDictRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDictRequestValidationError{}

// Validate checks the field values on DeleteDictRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDictRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDictRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDictRequestMultiError, or nil if none found.
func (m *DeleteDictRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDictRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteDictRequestMultiError(errors)
	}

	return nil
}

// DeleteDictRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteDictRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteDictRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDictRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDictRequestMultiError) AllErrors() []error { return m }

// DeleteDictRequestValidationError is the validation error returned by
// DeleteDictRequest.Validate if the designated constraints aren't met.
type DeleteDictRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDictRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDictRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDictRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDictRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDictRequestValidationError) ErrorName() string {
	return "DeleteDictRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDictRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDictRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDictRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDictRequestValidationError{}
