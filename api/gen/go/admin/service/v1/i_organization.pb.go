// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_organization.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/user/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_organization_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_organization_proto_rawDesc = "" +
	"\n" +
	"%admin/service/v1/i_organization.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1epagination/v1/pagination.proto\x1a\"user/service/v1/organization.proto2\xdb\x04\n" +
	"\x13OrganizationService\x12m\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a).user.service.v1.ListOrganizationResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/admin/v1/organizations\x12s\n" +
	"\x03Get\x12'.user.service.v1.GetOrganizationRequest\x1a\x1d.user.service.v1.Organization\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/admin/v1/organizations/{id}\x12p\n" +
	"\x06Create\x12*.user.service.v1.CreateOrganizationRequest\x1a\x16.google.protobuf.Empty\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/admin/v1/organizations\x12z\n" +
	"\x06Update\x12*.user.service.v1.UpdateOrganizationRequest\x1a\x16.google.protobuf.Empty\",\x82\xd3\xe4\x93\x02&:\x01*\x1a!/admin/v1/organizations/{data.id}\x12r\n" +
	"\x06Delete\x12*.user.service.v1.DeleteOrganizationRequest\x1a\x16.google.protobuf.Empty\"$\x82\xd3\xe4\x93\x02\x1e*\x1c/admin/v1/organizations/{id}B\xc0\x01\n" +
	"\x14com.admin.service.v1B\x12IOrganizationProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_organization_proto_goTypes = []any{
	(*v1.PagingRequest)(nil),              // 0: pagination.PagingRequest
	(*v11.GetOrganizationRequest)(nil),    // 1: user.service.v1.GetOrganizationRequest
	(*v11.CreateOrganizationRequest)(nil), // 2: user.service.v1.CreateOrganizationRequest
	(*v11.UpdateOrganizationRequest)(nil), // 3: user.service.v1.UpdateOrganizationRequest
	(*v11.DeleteOrganizationRequest)(nil), // 4: user.service.v1.DeleteOrganizationRequest
	(*v11.ListOrganizationResponse)(nil),  // 5: user.service.v1.ListOrganizationResponse
	(*v11.Organization)(nil),              // 6: user.service.v1.Organization
	(*emptypb.Empty)(nil),                 // 7: google.protobuf.Empty
}
var file_admin_service_v1_i_organization_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.OrganizationService.List:input_type -> pagination.PagingRequest
	1, // 1: admin.service.v1.OrganizationService.Get:input_type -> user.service.v1.GetOrganizationRequest
	2, // 2: admin.service.v1.OrganizationService.Create:input_type -> user.service.v1.CreateOrganizationRequest
	3, // 3: admin.service.v1.OrganizationService.Update:input_type -> user.service.v1.UpdateOrganizationRequest
	4, // 4: admin.service.v1.OrganizationService.Delete:input_type -> user.service.v1.DeleteOrganizationRequest
	5, // 5: admin.service.v1.OrganizationService.List:output_type -> user.service.v1.ListOrganizationResponse
	6, // 6: admin.service.v1.OrganizationService.Get:output_type -> user.service.v1.Organization
	7, // 7: admin.service.v1.OrganizationService.Create:output_type -> google.protobuf.Empty
	7, // 8: admin.service.v1.OrganizationService.Update:output_type -> google.protobuf.Empty
	7, // 9: admin.service.v1.OrganizationService.Delete:output_type -> google.protobuf.Empty
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_organization_proto_init() }
func file_admin_service_v1_i_organization_proto_init() {
	if File_admin_service_v1_i_organization_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_organization_proto_rawDesc), len(file_admin_service_v1_i_organization_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_organization_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_organization_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_organization_proto = out.File
	file_admin_service_v1_i_organization_proto_goTypes = nil
	file_admin_service_v1_i_organization_proto_depIdxs = nil
}
