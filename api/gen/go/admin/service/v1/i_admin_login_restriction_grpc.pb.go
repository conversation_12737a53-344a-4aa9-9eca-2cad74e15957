// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_admin_login_restriction.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdminLoginRestrictionService_List_FullMethodName   = "/admin.service.v1.AdminLoginRestrictionService/List"
	AdminLoginRestrictionService_Get_FullMethodName    = "/admin.service.v1.AdminLoginRestrictionService/Get"
	AdminLoginRestrictionService_Create_FullMethodName = "/admin.service.v1.AdminLoginRestrictionService/Create"
	AdminLoginRestrictionService_Update_FullMethodName = "/admin.service.v1.AdminLoginRestrictionService/Update"
	AdminLoginRestrictionService_Delete_FullMethodName = "/admin.service.v1.AdminLoginRestrictionService/Delete"
)

// AdminLoginRestrictionServiceClient is the client API for AdminLoginRestrictionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 后台登录限制管理服务
type AdminLoginRestrictionServiceClient interface {
	// 查询后台登录限制列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListAdminLoginRestrictionResponse, error)
	// 查询后台登录限制详情
	Get(ctx context.Context, in *GetAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*AdminLoginRestriction, error)
	// 创建后台登录限制
	Create(ctx context.Context, in *CreateAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新后台登录限制
	Update(ctx context.Context, in *UpdateAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除后台登录限制
	Delete(ctx context.Context, in *DeleteAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type adminLoginRestrictionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminLoginRestrictionServiceClient(cc grpc.ClientConnInterface) AdminLoginRestrictionServiceClient {
	return &adminLoginRestrictionServiceClient{cc}
}

func (c *adminLoginRestrictionServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListAdminLoginRestrictionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAdminLoginRestrictionResponse)
	err := c.cc.Invoke(ctx, AdminLoginRestrictionService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminLoginRestrictionServiceClient) Get(ctx context.Context, in *GetAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*AdminLoginRestriction, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminLoginRestriction)
	err := c.cc.Invoke(ctx, AdminLoginRestrictionService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminLoginRestrictionServiceClient) Create(ctx context.Context, in *CreateAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AdminLoginRestrictionService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminLoginRestrictionServiceClient) Update(ctx context.Context, in *UpdateAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AdminLoginRestrictionService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminLoginRestrictionServiceClient) Delete(ctx context.Context, in *DeleteAdminLoginRestrictionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AdminLoginRestrictionService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminLoginRestrictionServiceServer is the server API for AdminLoginRestrictionService service.
// All implementations must embed UnimplementedAdminLoginRestrictionServiceServer
// for forward compatibility.
//
// 后台登录限制管理服务
type AdminLoginRestrictionServiceServer interface {
	// 查询后台登录限制列表
	List(context.Context, *v1.PagingRequest) (*ListAdminLoginRestrictionResponse, error)
	// 查询后台登录限制详情
	Get(context.Context, *GetAdminLoginRestrictionRequest) (*AdminLoginRestriction, error)
	// 创建后台登录限制
	Create(context.Context, *CreateAdminLoginRestrictionRequest) (*emptypb.Empty, error)
	// 更新后台登录限制
	Update(context.Context, *UpdateAdminLoginRestrictionRequest) (*emptypb.Empty, error)
	// 删除后台登录限制
	Delete(context.Context, *DeleteAdminLoginRestrictionRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedAdminLoginRestrictionServiceServer()
}

// UnimplementedAdminLoginRestrictionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdminLoginRestrictionServiceServer struct{}

func (UnimplementedAdminLoginRestrictionServiceServer) List(context.Context, *v1.PagingRequest) (*ListAdminLoginRestrictionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedAdminLoginRestrictionServiceServer) Get(context.Context, *GetAdminLoginRestrictionRequest) (*AdminLoginRestriction, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedAdminLoginRestrictionServiceServer) Create(context.Context, *CreateAdminLoginRestrictionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedAdminLoginRestrictionServiceServer) Update(context.Context, *UpdateAdminLoginRestrictionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedAdminLoginRestrictionServiceServer) Delete(context.Context, *DeleteAdminLoginRestrictionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedAdminLoginRestrictionServiceServer) mustEmbedUnimplementedAdminLoginRestrictionServiceServer() {
}
func (UnimplementedAdminLoginRestrictionServiceServer) testEmbeddedByValue() {}

// UnsafeAdminLoginRestrictionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminLoginRestrictionServiceServer will
// result in compilation errors.
type UnsafeAdminLoginRestrictionServiceServer interface {
	mustEmbedUnimplementedAdminLoginRestrictionServiceServer()
}

func RegisterAdminLoginRestrictionServiceServer(s grpc.ServiceRegistrar, srv AdminLoginRestrictionServiceServer) {
	// If the following call pancis, it indicates UnimplementedAdminLoginRestrictionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdminLoginRestrictionService_ServiceDesc, srv)
}

func _AdminLoginRestrictionService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginRestrictionServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginRestrictionService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginRestrictionServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminLoginRestrictionService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdminLoginRestrictionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginRestrictionServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginRestrictionService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginRestrictionServiceServer).Get(ctx, req.(*GetAdminLoginRestrictionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminLoginRestrictionService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAdminLoginRestrictionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginRestrictionServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginRestrictionService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginRestrictionServiceServer).Create(ctx, req.(*CreateAdminLoginRestrictionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminLoginRestrictionService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAdminLoginRestrictionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginRestrictionServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginRestrictionService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginRestrictionServiceServer).Update(ctx, req.(*UpdateAdminLoginRestrictionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminLoginRestrictionService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAdminLoginRestrictionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginRestrictionServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginRestrictionService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginRestrictionServiceServer).Delete(ctx, req.(*DeleteAdminLoginRestrictionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminLoginRestrictionService_ServiceDesc is the grpc.ServiceDesc for AdminLoginRestrictionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminLoginRestrictionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.AdminLoginRestrictionService",
	HandlerType: (*AdminLoginRestrictionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _AdminLoginRestrictionService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _AdminLoginRestrictionService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _AdminLoginRestrictionService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _AdminLoginRestrictionService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _AdminLoginRestrictionService_Delete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_admin_login_restriction.proto",
}
