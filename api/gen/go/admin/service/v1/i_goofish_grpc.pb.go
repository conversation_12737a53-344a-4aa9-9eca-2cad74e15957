// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_goofish.proto

package servicev1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	v1 "kratos-admin/api/gen/go/goofish/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GoofishApi_GetPlatformInfo_FullMethodName             = "/admin.service.v1.GoofishApi/GetPlatformInfo"
	GoofishApi_GetUserInfo_FullMethodName                 = "/admin.service.v1.GoofishApi/GetUserInfo"
	GoofishApi_GetGoodsList_FullMethodName                = "/admin.service.v1.GoofishApi/GetGoodsList"
	GoofishApi_GetGoodsDetail_FullMethodName              = "/admin.service.v1.GoofishApi/GetGoodsDetail"
	GoofishApi_GetGoodsChangeSubscribeList_FullMethodName = "/admin.service.v1.GoofishApi/GetGoodsChangeSubscribeList"
	GoofishApi_GoodsChangeSubscribe_FullMethodName        = "/admin.service.v1.GoofishApi/GoodsChangeSubscribe"
	GoofishApi_GoodsChangeUnsubscribe_FullMethodName      = "/admin.service.v1.GoofishApi/GoodsChangeUnsubscribe"
	GoofishApi_CreateRechargeOrder_FullMethodName         = "/admin.service.v1.GoofishApi/CreateRechargeOrder"
	GoofishApi_CreateCardOrder_FullMethodName             = "/admin.service.v1.GoofishApi/CreateCardOrder"
	GoofishApi_GetOrderDetail_FullMethodName              = "/admin.service.v1.GoofishApi/GetOrderDetail"
	GoofishApi_GoodsCallback_FullMethodName               = "/admin.service.v1.GoofishApi/GoodsCallback"
	GoofishApi_OrderCallback_FullMethodName               = "/admin.service.v1.GoofishApi/OrderCallback"
)

// GoofishApiClient is the client API for GoofishApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 闲管家虚拟货源标准接口 - goofish API 路由配置，字段与 Apifox 文档保持一致
type GoofishApiClient interface {
	// 查询平台信息
	GetPlatformInfo(ctx context.Context, in *v1.PlatformInfoRequest, opts ...grpc.CallOption) (*v1.PlatformInfoResponse, error)
	// 查询商户信息
	GetUserInfo(ctx context.Context, in *v1.UserInfoRequest, opts ...grpc.CallOption) (*v1.UserInfoResponse, error)
	// 查询商品列表
	GetGoodsList(ctx context.Context, in *v1.GoodsListRequest, opts ...grpc.CallOption) (*v1.GoodsListResponse, error)
	// 查询商品详情
	GetGoodsDetail(ctx context.Context, in *v1.GoodsDetailRequest, opts ...grpc.CallOption) (*v1.GoodsDetailResponse, error)
	// 查询商品订阅列表 - 查询已订阅商品变更通知的商品列表
	GetGoodsChangeSubscribeList(ctx context.Context, in *v1.GoodsChangeSubscribeListRequest, opts ...grpc.CallOption) (*v1.GoodsChangeSubscribeListResponse, error)
	// 订阅商品变更通知 - 订阅货源商品价格、库存、状态变更通知
	GoodsChangeSubscribe(ctx context.Context, in *v1.GoodsChangeSubscribeRequest, opts ...grpc.CallOption) (*v1.GoodsChangeSubscribeResponse, error)
	// 取消商品变更通知 - 取消订阅货源商品价格、库存、状态变更通知
	GoodsChangeUnsubscribe(ctx context.Context, in *v1.GoodsChangeUnsubscribeRequest, opts ...grpc.CallOption) (*v1.GoodsChangeUnsubscribeResponse, error)
	// 创建直充订单
	CreateRechargeOrder(ctx context.Context, in *v1.CreateRechargeOrderRequest, opts ...grpc.CallOption) (*v1.CreateRechargeOrderResponse, error)
	// 创建卡密订单
	CreateCardOrder(ctx context.Context, in *v1.CreateCardOrderRequest, opts ...grpc.CallOption) (*v1.CreateCardOrderResponse, error)
	// 查询订单详情
	GetOrderDetail(ctx context.Context, in *v1.OrderDetailRequest, opts ...grpc.CallOption) (*v1.OrderDetailResponse, error)
	// 商品回调通知 - 注意：实际回调地址在订阅通知时传入
	GoodsCallback(ctx context.Context, in *v1.GoodsCallbackRequest, opts ...grpc.CallOption) (*v1.GoodsCallbackResponse, error)
	// 订单回调通知 - 注意：实际回调地址在创建订单时传入
	OrderCallback(ctx context.Context, in *v1.OrderCallbackRequest, opts ...grpc.CallOption) (*v1.OrderCallbackResponse, error)
}

type goofishApiClient struct {
	cc grpc.ClientConnInterface
}

func NewGoofishApiClient(cc grpc.ClientConnInterface) GoofishApiClient {
	return &goofishApiClient{cc}
}

func (c *goofishApiClient) GetPlatformInfo(ctx context.Context, in *v1.PlatformInfoRequest, opts ...grpc.CallOption) (*v1.PlatformInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.PlatformInfoResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GetPlatformInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GetUserInfo(ctx context.Context, in *v1.UserInfoRequest, opts ...grpc.CallOption) (*v1.UserInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.UserInfoResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GetGoodsList(ctx context.Context, in *v1.GoodsListRequest, opts ...grpc.CallOption) (*v1.GoodsListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GoodsListResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GetGoodsList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GetGoodsDetail(ctx context.Context, in *v1.GoodsDetailRequest, opts ...grpc.CallOption) (*v1.GoodsDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GoodsDetailResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GetGoodsDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GetGoodsChangeSubscribeList(ctx context.Context, in *v1.GoodsChangeSubscribeListRequest, opts ...grpc.CallOption) (*v1.GoodsChangeSubscribeListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GoodsChangeSubscribeListResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GetGoodsChangeSubscribeList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GoodsChangeSubscribe(ctx context.Context, in *v1.GoodsChangeSubscribeRequest, opts ...grpc.CallOption) (*v1.GoodsChangeSubscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GoodsChangeSubscribeResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GoodsChangeSubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GoodsChangeUnsubscribe(ctx context.Context, in *v1.GoodsChangeUnsubscribeRequest, opts ...grpc.CallOption) (*v1.GoodsChangeUnsubscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GoodsChangeUnsubscribeResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GoodsChangeUnsubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) CreateRechargeOrder(ctx context.Context, in *v1.CreateRechargeOrderRequest, opts ...grpc.CallOption) (*v1.CreateRechargeOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.CreateRechargeOrderResponse)
	err := c.cc.Invoke(ctx, GoofishApi_CreateRechargeOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) CreateCardOrder(ctx context.Context, in *v1.CreateCardOrderRequest, opts ...grpc.CallOption) (*v1.CreateCardOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.CreateCardOrderResponse)
	err := c.cc.Invoke(ctx, GoofishApi_CreateCardOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GetOrderDetail(ctx context.Context, in *v1.OrderDetailRequest, opts ...grpc.CallOption) (*v1.OrderDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.OrderDetailResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GetOrderDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) GoodsCallback(ctx context.Context, in *v1.GoodsCallbackRequest, opts ...grpc.CallOption) (*v1.GoodsCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GoodsCallbackResponse)
	err := c.cc.Invoke(ctx, GoofishApi_GoodsCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishApiClient) OrderCallback(ctx context.Context, in *v1.OrderCallbackRequest, opts ...grpc.CallOption) (*v1.OrderCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.OrderCallbackResponse)
	err := c.cc.Invoke(ctx, GoofishApi_OrderCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GoofishApiServer is the server API for GoofishApi service.
// All implementations must embed UnimplementedGoofishApiServer
// for forward compatibility.
//
// 闲管家虚拟货源标准接口 - goofish API 路由配置，字段与 Apifox 文档保持一致
type GoofishApiServer interface {
	// 查询平台信息
	GetPlatformInfo(context.Context, *v1.PlatformInfoRequest) (*v1.PlatformInfoResponse, error)
	// 查询商户信息
	GetUserInfo(context.Context, *v1.UserInfoRequest) (*v1.UserInfoResponse, error)
	// 查询商品列表
	GetGoodsList(context.Context, *v1.GoodsListRequest) (*v1.GoodsListResponse, error)
	// 查询商品详情
	GetGoodsDetail(context.Context, *v1.GoodsDetailRequest) (*v1.GoodsDetailResponse, error)
	// 查询商品订阅列表 - 查询已订阅商品变更通知的商品列表
	GetGoodsChangeSubscribeList(context.Context, *v1.GoodsChangeSubscribeListRequest) (*v1.GoodsChangeSubscribeListResponse, error)
	// 订阅商品变更通知 - 订阅货源商品价格、库存、状态变更通知
	GoodsChangeSubscribe(context.Context, *v1.GoodsChangeSubscribeRequest) (*v1.GoodsChangeSubscribeResponse, error)
	// 取消商品变更通知 - 取消订阅货源商品价格、库存、状态变更通知
	GoodsChangeUnsubscribe(context.Context, *v1.GoodsChangeUnsubscribeRequest) (*v1.GoodsChangeUnsubscribeResponse, error)
	// 创建直充订单
	CreateRechargeOrder(context.Context, *v1.CreateRechargeOrderRequest) (*v1.CreateRechargeOrderResponse, error)
	// 创建卡密订单
	CreateCardOrder(context.Context, *v1.CreateCardOrderRequest) (*v1.CreateCardOrderResponse, error)
	// 查询订单详情
	GetOrderDetail(context.Context, *v1.OrderDetailRequest) (*v1.OrderDetailResponse, error)
	// 商品回调通知 - 注意：实际回调地址在订阅通知时传入
	GoodsCallback(context.Context, *v1.GoodsCallbackRequest) (*v1.GoodsCallbackResponse, error)
	// 订单回调通知 - 注意：实际回调地址在创建订单时传入
	OrderCallback(context.Context, *v1.OrderCallbackRequest) (*v1.OrderCallbackResponse, error)
	mustEmbedUnimplementedGoofishApiServer()
}

// UnimplementedGoofishApiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGoofishApiServer struct{}

func (UnimplementedGoofishApiServer) GetPlatformInfo(context.Context, *v1.PlatformInfoRequest) (*v1.PlatformInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatformInfo not implemented")
}
func (UnimplementedGoofishApiServer) GetUserInfo(context.Context, *v1.UserInfoRequest) (*v1.UserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedGoofishApiServer) GetGoodsList(context.Context, *v1.GoodsListRequest) (*v1.GoodsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsList not implemented")
}
func (UnimplementedGoofishApiServer) GetGoodsDetail(context.Context, *v1.GoodsDetailRequest) (*v1.GoodsDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsDetail not implemented")
}
func (UnimplementedGoofishApiServer) GetGoodsChangeSubscribeList(context.Context, *v1.GoodsChangeSubscribeListRequest) (*v1.GoodsChangeSubscribeListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsChangeSubscribeList not implemented")
}
func (UnimplementedGoofishApiServer) GoodsChangeSubscribe(context.Context, *v1.GoodsChangeSubscribeRequest) (*v1.GoodsChangeSubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsChangeSubscribe not implemented")
}
func (UnimplementedGoofishApiServer) GoodsChangeUnsubscribe(context.Context, *v1.GoodsChangeUnsubscribeRequest) (*v1.GoodsChangeUnsubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsChangeUnsubscribe not implemented")
}
func (UnimplementedGoofishApiServer) CreateRechargeOrder(context.Context, *v1.CreateRechargeOrderRequest) (*v1.CreateRechargeOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRechargeOrder not implemented")
}
func (UnimplementedGoofishApiServer) CreateCardOrder(context.Context, *v1.CreateCardOrderRequest) (*v1.CreateCardOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCardOrder not implemented")
}
func (UnimplementedGoofishApiServer) GetOrderDetail(context.Context, *v1.OrderDetailRequest) (*v1.OrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderDetail not implemented")
}
func (UnimplementedGoofishApiServer) GoodsCallback(context.Context, *v1.GoodsCallbackRequest) (*v1.GoodsCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsCallback not implemented")
}
func (UnimplementedGoofishApiServer) OrderCallback(context.Context, *v1.OrderCallbackRequest) (*v1.OrderCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderCallback not implemented")
}
func (UnimplementedGoofishApiServer) mustEmbedUnimplementedGoofishApiServer() {}
func (UnimplementedGoofishApiServer) testEmbeddedByValue()                    {}

// UnsafeGoofishApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GoofishApiServer will
// result in compilation errors.
type UnsafeGoofishApiServer interface {
	mustEmbedUnimplementedGoofishApiServer()
}

func RegisterGoofishApiServer(s grpc.ServiceRegistrar, srv GoofishApiServer) {
	// If the following call pancis, it indicates UnimplementedGoofishApiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GoofishApi_ServiceDesc, srv)
}

func _GoofishApi_GetPlatformInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PlatformInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GetPlatformInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GetPlatformInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GetPlatformInfo(ctx, req.(*v1.PlatformInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.UserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GetUserInfo(ctx, req.(*v1.UserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GetGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GetGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GetGoodsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GetGoodsList(ctx, req.(*v1.GoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GetGoodsDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GoodsDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GetGoodsDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GetGoodsDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GetGoodsDetail(ctx, req.(*v1.GoodsDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GetGoodsChangeSubscribeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GoodsChangeSubscribeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GetGoodsChangeSubscribeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GetGoodsChangeSubscribeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GetGoodsChangeSubscribeList(ctx, req.(*v1.GoodsChangeSubscribeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GoodsChangeSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GoodsChangeSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GoodsChangeSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GoodsChangeSubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GoodsChangeSubscribe(ctx, req.(*v1.GoodsChangeSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GoodsChangeUnsubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GoodsChangeUnsubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GoodsChangeUnsubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GoodsChangeUnsubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GoodsChangeUnsubscribe(ctx, req.(*v1.GoodsChangeUnsubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_CreateRechargeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.CreateRechargeOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).CreateRechargeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_CreateRechargeOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).CreateRechargeOrder(ctx, req.(*v1.CreateRechargeOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_CreateCardOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.CreateCardOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).CreateCardOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_CreateCardOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).CreateCardOrder(ctx, req.(*v1.CreateCardOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GetOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.OrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GetOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GetOrderDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GetOrderDetail(ctx, req.(*v1.OrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_GoodsCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GoodsCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).GoodsCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_GoodsCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).GoodsCallback(ctx, req.(*v1.GoodsCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishApi_OrderCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.OrderCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishApiServer).OrderCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishApi_OrderCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishApiServer).OrderCallback(ctx, req.(*v1.OrderCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GoofishApi_ServiceDesc is the grpc.ServiceDesc for GoofishApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GoofishApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.GoofishApi",
	HandlerType: (*GoofishApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPlatformInfo",
			Handler:    _GoofishApi_GetPlatformInfo_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _GoofishApi_GetUserInfo_Handler,
		},
		{
			MethodName: "GetGoodsList",
			Handler:    _GoofishApi_GetGoodsList_Handler,
		},
		{
			MethodName: "GetGoodsDetail",
			Handler:    _GoofishApi_GetGoodsDetail_Handler,
		},
		{
			MethodName: "GetGoodsChangeSubscribeList",
			Handler:    _GoofishApi_GetGoodsChangeSubscribeList_Handler,
		},
		{
			MethodName: "GoodsChangeSubscribe",
			Handler:    _GoofishApi_GoodsChangeSubscribe_Handler,
		},
		{
			MethodName: "GoodsChangeUnsubscribe",
			Handler:    _GoofishApi_GoodsChangeUnsubscribe_Handler,
		},
		{
			MethodName: "CreateRechargeOrder",
			Handler:    _GoofishApi_CreateRechargeOrder_Handler,
		},
		{
			MethodName: "CreateCardOrder",
			Handler:    _GoofishApi_CreateCardOrder_Handler,
		},
		{
			MethodName: "GetOrderDetail",
			Handler:    _GoofishApi_GetOrderDetail_Handler,
		},
		{
			MethodName: "GoodsCallback",
			Handler:    _GoofishApi_GoodsCallback_Handler,
		},
		{
			MethodName: "OrderCallback",
			Handler:    _GoofishApi_OrderCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_goofish.proto",
}
