// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_admin_login_log.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdminLoginLogService_List_FullMethodName = "/admin.service.v1.AdminLoginLogService/List"
	AdminLoginLogService_Get_FullMethodName  = "/admin.service.v1.AdminLoginLogService/Get"
)

// AdminLoginLogServiceClient is the client API for AdminLoginLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 后台登录日志管理服务
type AdminLoginLogServiceClient interface {
	// 查询后台登录日志列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListAdminLoginLogResponse, error)
	// 查询后台登录日志详情
	Get(ctx context.Context, in *GetAdminLoginLogRequest, opts ...grpc.CallOption) (*AdminLoginLog, error)
}

type adminLoginLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminLoginLogServiceClient(cc grpc.ClientConnInterface) AdminLoginLogServiceClient {
	return &adminLoginLogServiceClient{cc}
}

func (c *adminLoginLogServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListAdminLoginLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAdminLoginLogResponse)
	err := c.cc.Invoke(ctx, AdminLoginLogService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminLoginLogServiceClient) Get(ctx context.Context, in *GetAdminLoginLogRequest, opts ...grpc.CallOption) (*AdminLoginLog, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminLoginLog)
	err := c.cc.Invoke(ctx, AdminLoginLogService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminLoginLogServiceServer is the server API for AdminLoginLogService service.
// All implementations must embed UnimplementedAdminLoginLogServiceServer
// for forward compatibility.
//
// 后台登录日志管理服务
type AdminLoginLogServiceServer interface {
	// 查询后台登录日志列表
	List(context.Context, *v1.PagingRequest) (*ListAdminLoginLogResponse, error)
	// 查询后台登录日志详情
	Get(context.Context, *GetAdminLoginLogRequest) (*AdminLoginLog, error)
	mustEmbedUnimplementedAdminLoginLogServiceServer()
}

// UnimplementedAdminLoginLogServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdminLoginLogServiceServer struct{}

func (UnimplementedAdminLoginLogServiceServer) List(context.Context, *v1.PagingRequest) (*ListAdminLoginLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedAdminLoginLogServiceServer) Get(context.Context, *GetAdminLoginLogRequest) (*AdminLoginLog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedAdminLoginLogServiceServer) mustEmbedUnimplementedAdminLoginLogServiceServer() {}
func (UnimplementedAdminLoginLogServiceServer) testEmbeddedByValue()                              {}

// UnsafeAdminLoginLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminLoginLogServiceServer will
// result in compilation errors.
type UnsafeAdminLoginLogServiceServer interface {
	mustEmbedUnimplementedAdminLoginLogServiceServer()
}

func RegisterAdminLoginLogServiceServer(s grpc.ServiceRegistrar, srv AdminLoginLogServiceServer) {
	// If the following call pancis, it indicates UnimplementedAdminLoginLogServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdminLoginLogService_ServiceDesc, srv)
}

func _AdminLoginLogService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginLogServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginLogService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginLogServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminLoginLogService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdminLoginLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminLoginLogServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminLoginLogService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminLoginLogServiceServer).Get(ctx, req.(*GetAdminLoginLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminLoginLogService_ServiceDesc is the grpc.ServiceDesc for AdminLoginLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminLoginLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.AdminLoginLogService",
	HandlerType: (*AdminLoginLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _AdminLoginLogService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _AdminLoginLogService_Get_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_admin_login_log.proto",
}
