// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_user_profile.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v1 "kratos-admin/api/gen/go/user/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserProfileServiceGetUser = "/admin.service.v1.UserProfileService/GetUser"
const OperationUserProfileServiceUpdateUser = "/admin.service.v1.UserProfileService/UpdateUser"

type UserProfileServiceHTTPServer interface {
	// GetUser 获取用户资料
	GetUser(context.Context, *emptypb.Empty) (*v1.User, error)
	// UpdateUser 更新用户资料
	UpdateUser(context.Context, *v1.UpdateUserRequest) (*emptypb.Empty, error)
}

func RegisterUserProfileServiceHTTPServer(s *http.Server, srv UserProfileServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/me", _UserProfileService_GetUser0_HTTP_Handler(srv))
	r.PUT("/admin/v1/me", _UserProfileService_UpdateUser0_HTTP_Handler(srv))
}

func _UserProfileService_GetUser0_HTTP_Handler(srv UserProfileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserProfileServiceGetUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUser(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.User)
		return ctx.Result(200, reply)
	}
}

func _UserProfileService_UpdateUser0_HTTP_Handler(srv UserProfileServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.UpdateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserProfileServiceUpdateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUser(ctx, req.(*v1.UpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type UserProfileServiceHTTPClient interface {
	GetUser(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *v1.User, err error)
	UpdateUser(ctx context.Context, req *v1.UpdateUserRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type UserProfileServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewUserProfileServiceHTTPClient(client *http.Client) UserProfileServiceHTTPClient {
	return &UserProfileServiceHTTPClientImpl{client}
}

func (c *UserProfileServiceHTTPClientImpl) GetUser(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*v1.User, error) {
	var out v1.User
	pattern := "/admin/v1/me"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserProfileServiceGetUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserProfileServiceHTTPClientImpl) UpdateUser(ctx context.Context, in *v1.UpdateUserRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/me"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserProfileServiceUpdateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
