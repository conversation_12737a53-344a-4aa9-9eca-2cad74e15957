// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_oss.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1 "kratos-admin/api/gen/go/file/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_oss_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_oss_proto_rawDesc = "" +
	"\n" +
	"\x1cadmin/service/v1/i_oss.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x19file/service/v1/oss.proto2\x9b\x03\n" +
	"\n" +
	"OssService\x12\x81\x01\n" +
	"\fOssUploadUrl\x12$.file.service.v1.OssUploadUrlRequest\x1a%.file.service.v1.OssUploadUrlResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/admin/v1/file:upload-url\x12\x83\x01\n" +
	"\x0ePostUploadFile\x12%.file.service.v1.UploadOssFileRequest\x1a&.file.service.v1.UploadOssFileResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/admin/v1/file:upload(\x01\x12\x82\x01\n" +
	"\rPutUploadFile\x12%.file.service.v1.UploadOssFileRequest\x1a&.file.service.v1.UploadOssFileResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\x1a\x15/admin/v1/file:upload(\x01B\xb7\x01\n" +
	"\x14com.admin.service.v1B\tIOssProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_oss_proto_goTypes = []any{
	(*v1.OssUploadUrlRequest)(nil),   // 0: file.service.v1.OssUploadUrlRequest
	(*v1.UploadOssFileRequest)(nil),  // 1: file.service.v1.UploadOssFileRequest
	(*v1.OssUploadUrlResponse)(nil),  // 2: file.service.v1.OssUploadUrlResponse
	(*v1.UploadOssFileResponse)(nil), // 3: file.service.v1.UploadOssFileResponse
}
var file_admin_service_v1_i_oss_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.OssService.OssUploadUrl:input_type -> file.service.v1.OssUploadUrlRequest
	1, // 1: admin.service.v1.OssService.PostUploadFile:input_type -> file.service.v1.UploadOssFileRequest
	1, // 2: admin.service.v1.OssService.PutUploadFile:input_type -> file.service.v1.UploadOssFileRequest
	2, // 3: admin.service.v1.OssService.OssUploadUrl:output_type -> file.service.v1.OssUploadUrlResponse
	3, // 4: admin.service.v1.OssService.PostUploadFile:output_type -> file.service.v1.UploadOssFileResponse
	3, // 5: admin.service.v1.OssService.PutUploadFile:output_type -> file.service.v1.UploadOssFileResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_oss_proto_init() }
func file_admin_service_v1_i_oss_proto_init() {
	if File_admin_service_v1_i_oss_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_oss_proto_rawDesc), len(file_admin_service_v1_i_oss_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_oss_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_oss_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_oss_proto = out.File
	file_admin_service_v1_i_oss_proto_goTypes = nil
	file_admin_service_v1_i_oss_proto_depIdxs = nil
}
