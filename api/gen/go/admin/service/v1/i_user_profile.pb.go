// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_user_profile.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v1 "kratos-admin/api/gen/go/user/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_user_profile_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_user_profile_proto_rawDesc = "" +
	"\n" +
	"%admin/service/v1/i_user_profile.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1auser/service/v1/user.proto2\xc7\x01\n" +
	"\x12UserProfileService\x12N\n" +
	"\aGetUser\x12\x16.google.protobuf.Empty\x1a\x15.user.service.v1.User\"\x14\x82\xd3\xe4\x93\x02\x0e\x12\f/admin/v1/me\x12a\n" +
	"\n" +
	"UpdateUser\x12\".user.service.v1.UpdateUserRequest\x1a\x16.google.protobuf.Empty\"\x17\x82\xd3\xe4\x93\x02\x11:\x01*\x1a\f/admin/v1/meB\xbf\x01\n" +
	"\x14com.admin.service.v1B\x11IUserProfileProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_user_profile_proto_goTypes = []any{
	(*emptypb.Empty)(nil),        // 0: google.protobuf.Empty
	(*v1.UpdateUserRequest)(nil), // 1: user.service.v1.UpdateUserRequest
	(*v1.User)(nil),              // 2: user.service.v1.User
}
var file_admin_service_v1_i_user_profile_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.UserProfileService.GetUser:input_type -> google.protobuf.Empty
	1, // 1: admin.service.v1.UserProfileService.UpdateUser:input_type -> user.service.v1.UpdateUserRequest
	2, // 2: admin.service.v1.UserProfileService.GetUser:output_type -> user.service.v1.User
	0, // 3: admin.service.v1.UserProfileService.UpdateUser:output_type -> google.protobuf.Empty
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_user_profile_proto_init() }
func file_admin_service_v1_i_user_profile_proto_init() {
	if File_admin_service_v1_i_user_profile_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_user_profile_proto_rawDesc), len(file_admin_service_v1_i_user_profile_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_user_profile_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_user_profile_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_user_profile_proto = out.File
	file_admin_service_v1_i_user_profile_proto_goTypes = nil
	file_admin_service_v1_i_user_profile_proto_depIdxs = nil
}
