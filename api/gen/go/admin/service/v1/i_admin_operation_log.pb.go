// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_admin_operation_log.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 后台操作日志
type AdminOperationLog struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                // 后台操作日志ID
	CostTime       *durationpb.Duration   `protobuf:"bytes,2,opt,name=cost_time,json=costTime,proto3,oneof" json:"cost_time,omitempty"`                     // 操作耗时
	Success        *bool                  `protobuf:"varint,3,opt,name=success,proto3,oneof" json:"success,omitempty"`                                      // 操作是否成功
	RequestId      *string                `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3,oneof" json:"request_id,omitempty"`                  // 请求ID
	StatusCode     *int32                 `protobuf:"varint,5,opt,name=status_code,json=statusCode,proto3,oneof" json:"status_code,omitempty"`              // 状态码
	Reason         *string                `protobuf:"bytes,6,opt,name=reason,proto3,oneof" json:"reason,omitempty"`                                         // 操作失败原因
	Location       *string                `protobuf:"bytes,7,opt,name=location,proto3,oneof" json:"location,omitempty"`                                     // 操作地理位置
	Operation      *string                `protobuf:"bytes,8,opt,name=operation,proto3,oneof" json:"operation,omitempty"`                                   // 操作方法
	Method         *string                `protobuf:"bytes,9,opt,name=method,proto3,oneof" json:"method,omitempty"`                                         // 请求方法
	Path           *string                `protobuf:"bytes,10,opt,name=path,proto3,oneof" json:"path,omitempty"`                                            // 请求路径
	ApiModule      *string                `protobuf:"bytes,11,opt,name=api_module,json=apiModule,proto3,oneof" json:"api_module,omitempty"`                 // API所属模块
	ApiDescription *string                `protobuf:"bytes,12,opt,name=api_description,json=apiDescription,proto3,oneof" json:"api_description,omitempty"`  // API操作描述
	Referer        *string                `protobuf:"bytes,20,opt,name=referer,proto3,oneof" json:"referer,omitempty"`                                      // 请求源
	RequestUri     *string                `protobuf:"bytes,21,opt,name=request_uri,json=requestUri,proto3,oneof" json:"request_uri,omitempty"`              // 请求URI
	RequestHeader  *string                `protobuf:"bytes,50,opt,name=request_header,json=requestHeader,proto3,oneof" json:"request_header,omitempty"`     // 请求头
	RequestBody    *string                `protobuf:"bytes,51,opt,name=request_body,json=requestBody,proto3,oneof" json:"request_body,omitempty"`           // 请求体
	Response       *string                `protobuf:"bytes,52,opt,name=response,proto3,oneof" json:"response,omitempty"`                                    // 响应信息
	UserId         *uint32                `protobuf:"varint,100,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`                        // 操作者用户ID
	Username       *string                `protobuf:"bytes,101,opt,name=username,proto3,oneof" json:"username,omitempty"`                                   // 操作者账号名
	ClientIp       *string                `protobuf:"bytes,102,opt,name=client_ip,json=clientIp,proto3,oneof" json:"client_ip,omitempty"`                   // 操作者IP
	UserAgent      *string                `protobuf:"bytes,200,opt,name=user_agent,json=userAgent,proto3,oneof" json:"user_agent,omitempty"`                // 浏览器的用户代理信息
	BrowserName    *string                `protobuf:"bytes,201,opt,name=browser_name,json=browserName,proto3,oneof" json:"browser_name,omitempty"`          // 浏览器名称
	BrowserVersion *string                `protobuf:"bytes,202,opt,name=browser_version,json=browserVersion,proto3,oneof" json:"browser_version,omitempty"` // 浏览器版本
	ClientId       *string                `protobuf:"bytes,300,opt,name=client_id,json=clientId,proto3,oneof" json:"client_id,omitempty"`                   // 客户端ID
	ClientName     *string                `protobuf:"bytes,301,opt,name=client_name,json=clientName,proto3,oneof" json:"client_name,omitempty"`             // 客户端名称
	OsName         *string                `protobuf:"bytes,302,opt,name=os_name,json=osName,proto3,oneof" json:"os_name,omitempty"`                         // 操作系统名称
	OsVersion      *string                `protobuf:"bytes,303,opt,name=os_version,json=osVersion,proto3,oneof" json:"os_version,omitempty"`                // 操作系统版本
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,500,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`             // 创建时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AdminOperationLog) Reset() {
	*x = AdminOperationLog{}
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminOperationLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminOperationLog) ProtoMessage() {}

func (x *AdminOperationLog) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminOperationLog.ProtoReflect.Descriptor instead.
func (*AdminOperationLog) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP(), []int{0}
}

func (x *AdminOperationLog) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AdminOperationLog) GetCostTime() *durationpb.Duration {
	if x != nil {
		return x.CostTime
	}
	return nil
}

func (x *AdminOperationLog) GetSuccess() bool {
	if x != nil && x.Success != nil {
		return *x.Success
	}
	return false
}

func (x *AdminOperationLog) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *AdminOperationLog) GetStatusCode() int32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *AdminOperationLog) GetReason() string {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return ""
}

func (x *AdminOperationLog) GetLocation() string {
	if x != nil && x.Location != nil {
		return *x.Location
	}
	return ""
}

func (x *AdminOperationLog) GetOperation() string {
	if x != nil && x.Operation != nil {
		return *x.Operation
	}
	return ""
}

func (x *AdminOperationLog) GetMethod() string {
	if x != nil && x.Method != nil {
		return *x.Method
	}
	return ""
}

func (x *AdminOperationLog) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *AdminOperationLog) GetApiModule() string {
	if x != nil && x.ApiModule != nil {
		return *x.ApiModule
	}
	return ""
}

func (x *AdminOperationLog) GetApiDescription() string {
	if x != nil && x.ApiDescription != nil {
		return *x.ApiDescription
	}
	return ""
}

func (x *AdminOperationLog) GetReferer() string {
	if x != nil && x.Referer != nil {
		return *x.Referer
	}
	return ""
}

func (x *AdminOperationLog) GetRequestUri() string {
	if x != nil && x.RequestUri != nil {
		return *x.RequestUri
	}
	return ""
}

func (x *AdminOperationLog) GetRequestHeader() string {
	if x != nil && x.RequestHeader != nil {
		return *x.RequestHeader
	}
	return ""
}

func (x *AdminOperationLog) GetRequestBody() string {
	if x != nil && x.RequestBody != nil {
		return *x.RequestBody
	}
	return ""
}

func (x *AdminOperationLog) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

func (x *AdminOperationLog) GetUserId() uint32 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

func (x *AdminOperationLog) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *AdminOperationLog) GetClientIp() string {
	if x != nil && x.ClientIp != nil {
		return *x.ClientIp
	}
	return ""
}

func (x *AdminOperationLog) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *AdminOperationLog) GetBrowserName() string {
	if x != nil && x.BrowserName != nil {
		return *x.BrowserName
	}
	return ""
}

func (x *AdminOperationLog) GetBrowserVersion() string {
	if x != nil && x.BrowserVersion != nil {
		return *x.BrowserVersion
	}
	return ""
}

func (x *AdminOperationLog) GetClientId() string {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return ""
}

func (x *AdminOperationLog) GetClientName() string {
	if x != nil && x.ClientName != nil {
		return *x.ClientName
	}
	return ""
}

func (x *AdminOperationLog) GetOsName() string {
	if x != nil && x.OsName != nil {
		return *x.OsName
	}
	return ""
}

func (x *AdminOperationLog) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *AdminOperationLog) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// 查询后台操作日志列表 - 回应
type ListAdminOperationLogResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*AdminOperationLog   `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAdminOperationLogResponse) Reset() {
	*x = ListAdminOperationLogResponse{}
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAdminOperationLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAdminOperationLogResponse) ProtoMessage() {}

func (x *ListAdminOperationLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAdminOperationLogResponse.ProtoReflect.Descriptor instead.
func (*ListAdminOperationLogResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP(), []int{1}
}

func (x *ListAdminOperationLogResponse) GetItems() []*AdminOperationLog {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListAdminOperationLogResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询后台操作日志详情 - 请求
type GetAdminOperationLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAdminOperationLogRequest) Reset() {
	*x = GetAdminOperationLogRequest{}
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdminOperationLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdminOperationLogRequest) ProtoMessage() {}

func (x *GetAdminOperationLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdminOperationLogRequest.ProtoReflect.Descriptor instead.
func (*GetAdminOperationLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP(), []int{2}
}

func (x *GetAdminOperationLogRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建后台操作日志 - 请求
type CreateAdminOperationLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *AdminOperationLog     `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAdminOperationLogRequest) Reset() {
	*x = CreateAdminOperationLogRequest{}
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAdminOperationLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdminOperationLogRequest) ProtoMessage() {}

func (x *CreateAdminOperationLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdminOperationLogRequest.ProtoReflect.Descriptor instead.
func (*CreateAdminOperationLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAdminOperationLogRequest) GetData() *AdminOperationLog {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新后台操作日志 - 请求
type UpdateAdminOperationLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *AdminOperationLog     `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAdminOperationLogRequest) Reset() {
	*x = UpdateAdminOperationLogRequest{}
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAdminOperationLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdminOperationLogRequest) ProtoMessage() {}

func (x *UpdateAdminOperationLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdminOperationLogRequest.ProtoReflect.Descriptor instead.
func (*UpdateAdminOperationLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAdminOperationLogRequest) GetData() *AdminOperationLog {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateAdminOperationLogRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateAdminOperationLogRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除后台操作日志 - 请求
type DeleteAdminOperationLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAdminOperationLogRequest) Reset() {
	*x = DeleteAdminOperationLogRequest{}
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAdminOperationLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAdminOperationLogRequest) ProtoMessage() {}

func (x *DeleteAdminOperationLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_operation_log_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAdminOperationLogRequest.ProtoReflect.Descriptor instead.
func (*DeleteAdminOperationLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteAdminOperationLogRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_admin_service_v1_i_admin_operation_log_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_admin_operation_log_proto_rawDesc = "" +
	"\n" +
	",admin/service/v1/i_admin_operation_log.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1epagination/v1/pagination.proto\"\x9e\x10\n" +
	"\x11AdminOperationLog\x122\n" +
	"\x02id\x18\x01 \x01(\rB\x1d\xe0A\x01\xbaG\x17\x92\x02\x14后台操作日志IDH\x00R\x02id\x88\x01\x01\x12O\n" +
	"\tcost_time\x18\x02 \x01(\v2\x19.google.protobuf.DurationB\x12\xbaG\x0f\x92\x02\f操作耗时H\x01R\bcostTime\x88\x01\x01\x127\n" +
	"\asuccess\x18\x03 \x01(\bB\x18\xbaG\x15\x92\x02\x12操作是否成功H\x02R\asuccess\x88\x01\x01\x122\n" +
	"\n" +
	"request_id\x18\x04 \x01(\tB\x0e\xbaG\v\x92\x02\b请求IDH\x03R\trequestId\x88\x01\x01\x125\n" +
	"\vstatus_code\x18\x05 \x01(\x05B\x0f\xbaG\f\x92\x02\t状态码H\x04R\n" +
	"statusCode\x88\x01\x01\x125\n" +
	"\x06reason\x18\x06 \x01(\tB\x18\xbaG\x15\x92\x02\x12操作失败原因H\x05R\x06reason\x88\x01\x01\x129\n" +
	"\blocation\x18\a \x01(\tB\x18\xbaG\x15\x92\x02\x12操作地理位置H\x06R\blocation\x88\x01\x01\x125\n" +
	"\toperation\x18\b \x01(\tB\x12\xbaG\x0f\x92\x02\f操作方法H\aR\toperation\x88\x01\x01\x12/\n" +
	"\x06method\x18\t \x01(\tB\x12\xbaG\x0f\x92\x02\f请求方法H\bR\x06method\x88\x01\x01\x12+\n" +
	"\x04path\x18\n" +
	" \x01(\tB\x12\xbaG\x0f\x92\x02\f请求路径H\tR\x04path\x88\x01\x01\x129\n" +
	"\n" +
	"api_module\x18\v \x01(\tB\x15\xbaG\x12\x92\x02\x0fAPI所属模块H\n" +
	"R\tapiModule\x88\x01\x01\x12C\n" +
	"\x0fapi_description\x18\f \x01(\tB\x15\xbaG\x12\x92\x02\x0fAPI操作描述H\vR\x0eapiDescription\x88\x01\x01\x12.\n" +
	"\areferer\x18\x14 \x01(\tB\x0f\xbaG\f\x92\x02\t请求源H\fR\areferer\x88\x01\x01\x125\n" +
	"\vrequest_uri\x18\x15 \x01(\tB\x0f\xbaG\f\x92\x02\t请求URIH\rR\n" +
	"requestUri\x88\x01\x01\x12;\n" +
	"\x0erequest_header\x182 \x01(\tB\x0f\xbaG\f\x92\x02\t请求头H\x0eR\rrequestHeader\x88\x01\x01\x127\n" +
	"\frequest_body\x183 \x01(\tB\x0f\xbaG\f\x92\x02\t请求体H\x0fR\vrequestBody\x88\x01\x01\x123\n" +
	"\bresponse\x184 \x01(\tB\x12\xbaG\x0f\x92\x02\f响应信息H\x10R\bresponse\x88\x01\x01\x125\n" +
	"\auser_id\x18d \x01(\rB\x17\xbaG\x14\x92\x02\x11操作者用户IDH\x11R\x06userId\x88\x01\x01\x129\n" +
	"\busername\x18e \x01(\tB\x18\xbaG\x15\x92\x02\x12操作者账号名H\x12R\busername\x88\x01\x01\x123\n" +
	"\tclient_ip\x18f \x01(\tB\x11\xbaG\x0e\x92\x02\v操作者IPH\x13R\bclientIp\x88\x01\x01\x12I\n" +
	"\n" +
	"user_agent\x18\xc8\x01 \x01(\tB$\xbaG!\x92\x02\x1e浏览器的用户代理信息H\x14R\tuserAgent\x88\x01\x01\x12>\n" +
	"\fbrowser_name\x18\xc9\x01 \x01(\tB\x15\xbaG\x12\x92\x02\x0f浏览器名称H\x15R\vbrowserName\x88\x01\x01\x12D\n" +
	"\x0fbrowser_version\x18\xca\x01 \x01(\tB\x15\xbaG\x12\x92\x02\x0f浏览器版本H\x16R\x0ebrowserVersion\x88\x01\x01\x124\n" +
	"\tclient_id\x18\xac\x02 \x01(\tB\x11\xbaG\x0e\x92\x02\v客户端IDH\x17R\bclientId\x88\x01\x01\x12<\n" +
	"\vclient_name\x18\xad\x02 \x01(\tB\x15\xbaG\x12\x92\x02\x0f客户端名称H\x18R\n" +
	"clientName\x88\x01\x01\x127\n" +
	"\aos_name\x18\xae\x02 \x01(\tB\x18\xbaG\x15\x92\x02\x12操作系统名称H\x19R\x06osName\x88\x01\x01\x12=\n" +
	"\n" +
	"os_version\x18\xaf\x02 \x01(\tB\x18\xbaG\x15\x92\x02\x12操作系统版本H\x1aR\tosVersion\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xf4\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\x1bR\n" +
	"createTime\x88\x01\x01B\x05\n" +
	"\x03_idB\f\n" +
	"\n" +
	"_cost_timeB\n" +
	"\n" +
	"\b_successB\r\n" +
	"\v_request_idB\x0e\n" +
	"\f_status_codeB\t\n" +
	"\a_reasonB\v\n" +
	"\t_locationB\f\n" +
	"\n" +
	"_operationB\t\n" +
	"\a_methodB\a\n" +
	"\x05_pathB\r\n" +
	"\v_api_moduleB\x12\n" +
	"\x10_api_descriptionB\n" +
	"\n" +
	"\b_refererB\x0e\n" +
	"\f_request_uriB\x11\n" +
	"\x0f_request_headerB\x0f\n" +
	"\r_request_bodyB\v\n" +
	"\t_responseB\n" +
	"\n" +
	"\b_user_idB\v\n" +
	"\t_usernameB\f\n" +
	"\n" +
	"_client_ipB\r\n" +
	"\v_user_agentB\x0f\n" +
	"\r_browser_nameB\x12\n" +
	"\x10_browser_versionB\f\n" +
	"\n" +
	"_client_idB\x0e\n" +
	"\f_client_nameB\n" +
	"\n" +
	"\b_os_nameB\r\n" +
	"\v_os_versionB\x0e\n" +
	"\f_create_time\"p\n" +
	"\x1dListAdminOperationLogResponse\x129\n" +
	"\x05items\x18\x01 \x03(\v2#.admin.service.v1.AdminOperationLogR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"-\n" +
	"\x1bGetAdminOperationLogRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"Y\n" +
	"\x1eCreateAdminOperationLogRequest\x127\n" +
	"\x04data\x18\x01 \x01(\v2#.admin.service.v1.AdminOperationLogR\x04data\"\x97\x03\n" +
	"\x1eUpdateAdminOperationLogRequest\x127\n" +
	"\x04data\x18\x01 \x01(\v2#.admin.service.v1.AdminOperationLogR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"0\n" +
	"\x1eDeleteAdminOperationLogRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\x9f\x02\n" +
	"\x18AdminOperationLogService\x12z\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a/.admin.service.v1.ListAdminOperationLogResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/admin/v1/admin_operation_logs\x12\x86\x01\n" +
	"\x03Get\x12-.admin.service.v1.GetAdminOperationLogRequest\x1a#.admin.service.v1.AdminOperationLog\"+\x82\xd3\xe4\x93\x02%\x12#/admin/v1/admin_operation_logs/{id}B\xc5\x01\n" +
	"\x14com.admin.service.v1B\x17IAdminOperationLogProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_admin_operation_log_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_admin_operation_log_proto_rawDescData []byte
)

func file_admin_service_v1_i_admin_operation_log_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_admin_operation_log_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_admin_operation_log_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_admin_operation_log_proto_rawDesc), len(file_admin_service_v1_i_admin_operation_log_proto_rawDesc)))
	})
	return file_admin_service_v1_i_admin_operation_log_proto_rawDescData
}

var file_admin_service_v1_i_admin_operation_log_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_admin_service_v1_i_admin_operation_log_proto_goTypes = []any{
	(*AdminOperationLog)(nil),              // 0: admin.service.v1.AdminOperationLog
	(*ListAdminOperationLogResponse)(nil),  // 1: admin.service.v1.ListAdminOperationLogResponse
	(*GetAdminOperationLogRequest)(nil),    // 2: admin.service.v1.GetAdminOperationLogRequest
	(*CreateAdminOperationLogRequest)(nil), // 3: admin.service.v1.CreateAdminOperationLogRequest
	(*UpdateAdminOperationLogRequest)(nil), // 4: admin.service.v1.UpdateAdminOperationLogRequest
	(*DeleteAdminOperationLogRequest)(nil), // 5: admin.service.v1.DeleteAdminOperationLogRequest
	(*durationpb.Duration)(nil),            // 6: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),          // 7: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),          // 8: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),               // 9: pagination.PagingRequest
}
var file_admin_service_v1_i_admin_operation_log_proto_depIdxs = []int32{
	6, // 0: admin.service.v1.AdminOperationLog.cost_time:type_name -> google.protobuf.Duration
	7, // 1: admin.service.v1.AdminOperationLog.create_time:type_name -> google.protobuf.Timestamp
	0, // 2: admin.service.v1.ListAdminOperationLogResponse.items:type_name -> admin.service.v1.AdminOperationLog
	0, // 3: admin.service.v1.CreateAdminOperationLogRequest.data:type_name -> admin.service.v1.AdminOperationLog
	0, // 4: admin.service.v1.UpdateAdminOperationLogRequest.data:type_name -> admin.service.v1.AdminOperationLog
	8, // 5: admin.service.v1.UpdateAdminOperationLogRequest.update_mask:type_name -> google.protobuf.FieldMask
	9, // 6: admin.service.v1.AdminOperationLogService.List:input_type -> pagination.PagingRequest
	2, // 7: admin.service.v1.AdminOperationLogService.Get:input_type -> admin.service.v1.GetAdminOperationLogRequest
	1, // 8: admin.service.v1.AdminOperationLogService.List:output_type -> admin.service.v1.ListAdminOperationLogResponse
	0, // 9: admin.service.v1.AdminOperationLogService.Get:output_type -> admin.service.v1.AdminOperationLog
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_admin_operation_log_proto_init() }
func file_admin_service_v1_i_admin_operation_log_proto_init() {
	if File_admin_service_v1_i_admin_operation_log_proto != nil {
		return
	}
	file_admin_service_v1_i_admin_operation_log_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_admin_operation_log_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_admin_operation_log_proto_rawDesc), len(file_admin_service_v1_i_admin_operation_log_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_admin_operation_log_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_admin_operation_log_proto_depIdxs,
		MessageInfos:      file_admin_service_v1_i_admin_operation_log_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_admin_operation_log_proto = out.File
	file_admin_service_v1_i_admin_operation_log_proto_goTypes = nil
	file_admin_service_v1_i_admin_operation_log_proto_depIdxs = nil
}
