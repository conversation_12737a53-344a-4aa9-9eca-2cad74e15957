// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_organization.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/user/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationOrganizationServiceCreate = "/admin.service.v1.OrganizationService/Create"
const OperationOrganizationServiceDelete = "/admin.service.v1.OrganizationService/Delete"
const OperationOrganizationServiceGet = "/admin.service.v1.OrganizationService/Get"
const OperationOrganizationServiceList = "/admin.service.v1.OrganizationService/List"
const OperationOrganizationServiceUpdate = "/admin.service.v1.OrganizationService/Update"

type OrganizationServiceHTTPServer interface {
	// Create 创建组织
	Create(context.Context, *v11.CreateOrganizationRequest) (*emptypb.Empty, error)
	// Delete 删除组织
	Delete(context.Context, *v11.DeleteOrganizationRequest) (*emptypb.Empty, error)
	// Get 查询组织详情
	Get(context.Context, *v11.GetOrganizationRequest) (*v11.Organization, error)
	// List 查询组织列表
	List(context.Context, *v1.PagingRequest) (*v11.ListOrganizationResponse, error)
	// Update 更新组织
	Update(context.Context, *v11.UpdateOrganizationRequest) (*emptypb.Empty, error)
}

func RegisterOrganizationServiceHTTPServer(s *http.Server, srv OrganizationServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/organizations", _OrganizationService_List11_HTTP_Handler(srv))
	r.GET("/admin/v1/organizations/{id}", _OrganizationService_Get11_HTTP_Handler(srv))
	r.POST("/admin/v1/organizations", _OrganizationService_Create9_HTTP_Handler(srv))
	r.PUT("/admin/v1/organizations/{data.id}", _OrganizationService_Update9_HTTP_Handler(srv))
	r.DELETE("/admin/v1/organizations/{id}", _OrganizationService_Delete9_HTTP_Handler(srv))
}

func _OrganizationService_List11_HTTP_Handler(srv OrganizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrganizationServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListOrganizationResponse)
		return ctx.Result(200, reply)
	}
}

func _OrganizationService_Get11_HTTP_Handler(srv OrganizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetOrganizationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrganizationServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetOrganizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.Organization)
		return ctx.Result(200, reply)
	}
}

func _OrganizationService_Create9_HTTP_Handler(srv OrganizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateOrganizationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrganizationServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreateOrganizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _OrganizationService_Update9_HTTP_Handler(srv OrganizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdateOrganizationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrganizationServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdateOrganizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _OrganizationService_Delete9_HTTP_Handler(srv OrganizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteOrganizationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrganizationServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeleteOrganizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type OrganizationServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreateOrganizationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeleteOrganizationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetOrganizationRequest, opts ...http.CallOption) (rsp *v11.Organization, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListOrganizationResponse, err error)
	Update(ctx context.Context, req *v11.UpdateOrganizationRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type OrganizationServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewOrganizationServiceHTTPClient(client *http.Client) OrganizationServiceHTTPClient {
	return &OrganizationServiceHTTPClientImpl{client}
}

func (c *OrganizationServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreateOrganizationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/organizations"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrganizationServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrganizationServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeleteOrganizationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/organizations/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrganizationServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrganizationServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetOrganizationRequest, opts ...http.CallOption) (*v11.Organization, error) {
	var out v11.Organization
	pattern := "/admin/v1/organizations/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrganizationServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrganizationServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListOrganizationResponse, error) {
	var out v11.ListOrganizationResponse
	pattern := "/admin/v1/organizations"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrganizationServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrganizationServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdateOrganizationRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/organizations/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrganizationServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
