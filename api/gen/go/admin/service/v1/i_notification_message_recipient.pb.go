// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_notification_message_recipient.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_notification_message_recipient_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_notification_message_recipient_proto_rawDesc = "" +
	"\n" +
	"7admin/service/v1/i_notification_message_recipient.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1epagination/v1/pagination.proto\x1a@internal_message/service/v1/notification_message_recipient.proto2\xcf\x06\n" +
	"#NotificationMessageRecipientService\x12\x94\x01\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1aE.internal_message.service.v1.ListNotificationMessageRecipientResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/admin/v1/notifications:recipients\x12\xb6\x01\n" +
	"\x03Get\x12C.internal_message.service.v1.GetNotificationMessageRecipientRequest\x1a9.internal_message.service.v1.NotificationMessageRecipient\"/\x82\xd3\xe4\x93\x02)\x12'/admin/v1/notifications:recipients/{id}\x12\x97\x01\n" +
	"\x06Create\x12F.internal_message.service.v1.CreateNotificationMessageRecipientRequest\x1a\x16.google.protobuf.Empty\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/admin/v1/notifications:recipients\x12\xa1\x01\n" +
	"\x06Update\x12F.internal_message.service.v1.UpdateNotificationMessageRecipientRequest\x1a\x16.google.protobuf.Empty\"7\x82\xd3\xe4\x93\x021:\x01*\x1a,/admin/v1/notifications:recipients/{data.id}\x12\x99\x01\n" +
	"\x06Delete\x12F.internal_message.service.v1.DeleteNotificationMessageRecipientRequest\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02)*'/admin/v1/notifications:recipients/{id}B\xd0\x01\n" +
	"\x14com.admin.service.v1B\"INotificationMessageRecipientProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_notification_message_recipient_proto_goTypes = []any{
	(*v1.PagingRequest)(nil),                              // 0: pagination.PagingRequest
	(*v11.GetNotificationMessageRecipientRequest)(nil),    // 1: internal_message.service.v1.GetNotificationMessageRecipientRequest
	(*v11.CreateNotificationMessageRecipientRequest)(nil), // 2: internal_message.service.v1.CreateNotificationMessageRecipientRequest
	(*v11.UpdateNotificationMessageRecipientRequest)(nil), // 3: internal_message.service.v1.UpdateNotificationMessageRecipientRequest
	(*v11.DeleteNotificationMessageRecipientRequest)(nil), // 4: internal_message.service.v1.DeleteNotificationMessageRecipientRequest
	(*v11.ListNotificationMessageRecipientResponse)(nil),  // 5: internal_message.service.v1.ListNotificationMessageRecipientResponse
	(*v11.NotificationMessageRecipient)(nil),              // 6: internal_message.service.v1.NotificationMessageRecipient
	(*emptypb.Empty)(nil),                                 // 7: google.protobuf.Empty
}
var file_admin_service_v1_i_notification_message_recipient_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.NotificationMessageRecipientService.List:input_type -> pagination.PagingRequest
	1, // 1: admin.service.v1.NotificationMessageRecipientService.Get:input_type -> internal_message.service.v1.GetNotificationMessageRecipientRequest
	2, // 2: admin.service.v1.NotificationMessageRecipientService.Create:input_type -> internal_message.service.v1.CreateNotificationMessageRecipientRequest
	3, // 3: admin.service.v1.NotificationMessageRecipientService.Update:input_type -> internal_message.service.v1.UpdateNotificationMessageRecipientRequest
	4, // 4: admin.service.v1.NotificationMessageRecipientService.Delete:input_type -> internal_message.service.v1.DeleteNotificationMessageRecipientRequest
	5, // 5: admin.service.v1.NotificationMessageRecipientService.List:output_type -> internal_message.service.v1.ListNotificationMessageRecipientResponse
	6, // 6: admin.service.v1.NotificationMessageRecipientService.Get:output_type -> internal_message.service.v1.NotificationMessageRecipient
	7, // 7: admin.service.v1.NotificationMessageRecipientService.Create:output_type -> google.protobuf.Empty
	7, // 8: admin.service.v1.NotificationMessageRecipientService.Update:output_type -> google.protobuf.Empty
	7, // 9: admin.service.v1.NotificationMessageRecipientService.Delete:output_type -> google.protobuf.Empty
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_notification_message_recipient_proto_init() }
func file_admin_service_v1_i_notification_message_recipient_proto_init() {
	if File_admin_service_v1_i_notification_message_recipient_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_notification_message_recipient_proto_rawDesc), len(file_admin_service_v1_i_notification_message_recipient_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_notification_message_recipient_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_notification_message_recipient_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_notification_message_recipient_proto = out.File
	file_admin_service_v1_i_notification_message_recipient_proto_goTypes = nil
	file_admin_service_v1_i_notification_message_recipient_proto_depIdxs = nil
}
