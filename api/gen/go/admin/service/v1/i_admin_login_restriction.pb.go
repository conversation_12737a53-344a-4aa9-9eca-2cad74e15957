// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_admin_login_restriction.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 后台登录限制类型
type AdminLoginRestrictionType int32

const (
	AdminLoginRestrictionType_LOGIN_RESTRICTION_TYPE_UNSPECIFIED AdminLoginRestrictionType = 0 // 未知
	AdminLoginRestrictionType_BLACKLIST                          AdminLoginRestrictionType = 1 // 黑名单
	AdminLoginRestrictionType_WHITELIST                          AdminLoginRestrictionType = 2 // 白名单
)

// Enum value maps for AdminLoginRestrictionType.
var (
	AdminLoginRestrictionType_name = map[int32]string{
		0: "LOGIN_RESTRICTION_TYPE_UNSPECIFIED",
		1: "BLACKLIST",
		2: "WHITELIST",
	}
	AdminLoginRestrictionType_value = map[string]int32{
		"LOGIN_RESTRICTION_TYPE_UNSPECIFIED": 0,
		"BLACKLIST":                          1,
		"WHITELIST":                          2,
	}
)

func (x AdminLoginRestrictionType) Enum() *AdminLoginRestrictionType {
	p := new(AdminLoginRestrictionType)
	*p = x
	return p
}

func (x AdminLoginRestrictionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdminLoginRestrictionType) Descriptor() protoreflect.EnumDescriptor {
	return file_admin_service_v1_i_admin_login_restriction_proto_enumTypes[0].Descriptor()
}

func (AdminLoginRestrictionType) Type() protoreflect.EnumType {
	return &file_admin_service_v1_i_admin_login_restriction_proto_enumTypes[0]
}

func (x AdminLoginRestrictionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdminLoginRestrictionType.Descriptor instead.
func (AdminLoginRestrictionType) EnumDescriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{0}
}

// 后台登录限制方式
type AdminLoginRestrictionMethod int32

const (
	AdminLoginRestrictionMethod_LOGIN_RESTRICTION_METHOD_UNSPECIFIED AdminLoginRestrictionMethod = 0 // 未知
	AdminLoginRestrictionMethod_IP                                   AdminLoginRestrictionMethod = 1 // IP地址限制
	AdminLoginRestrictionMethod_MAC                                  AdminLoginRestrictionMethod = 2 // MAC地址限制，绑定设备的MAC地址。
	AdminLoginRestrictionMethod_REGION                               AdminLoginRestrictionMethod = 3 // 地区限制，根据地理位置（如国家、城市）限制登录。
	AdminLoginRestrictionMethod_TIME                                 AdminLoginRestrictionMethod = 4 // 时间限制，限制登录的时间段，例如只允许工作时间登录。
	AdminLoginRestrictionMethod_DEVICE                               AdminLoginRestrictionMethod = 5 // 设备限制，限制登录设备的类型（如PC、手机）或特定设备ID。
)

// Enum value maps for AdminLoginRestrictionMethod.
var (
	AdminLoginRestrictionMethod_name = map[int32]string{
		0: "LOGIN_RESTRICTION_METHOD_UNSPECIFIED",
		1: "IP",
		2: "MAC",
		3: "REGION",
		4: "TIME",
		5: "DEVICE",
	}
	AdminLoginRestrictionMethod_value = map[string]int32{
		"LOGIN_RESTRICTION_METHOD_UNSPECIFIED": 0,
		"IP":                                   1,
		"MAC":                                  2,
		"REGION":                               3,
		"TIME":                                 4,
		"DEVICE":                               5,
	}
)

func (x AdminLoginRestrictionMethod) Enum() *AdminLoginRestrictionMethod {
	p := new(AdminLoginRestrictionMethod)
	*p = x
	return p
}

func (x AdminLoginRestrictionMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdminLoginRestrictionMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_admin_service_v1_i_admin_login_restriction_proto_enumTypes[1].Descriptor()
}

func (AdminLoginRestrictionMethod) Type() protoreflect.EnumType {
	return &file_admin_service_v1_i_admin_login_restriction_proto_enumTypes[1]
}

func (x AdminLoginRestrictionMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdminLoginRestrictionMethod.Descriptor instead.
func (AdminLoginRestrictionMethod) EnumDescriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{1}
}

// 后台登录限制
type AdminLoginRestriction struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Id            *uint32                      `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                           // 后台登录限制ID
	TargetId      *uint32                      `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3,oneof" json:"target_id,omitempty"`                               // 目标用户ID
	Type          *AdminLoginRestrictionType   `protobuf:"varint,3,opt,name=type,proto3,enum=admin.service.v1.AdminLoginRestrictionType,oneof" json:"type,omitempty"`       // 限制类型
	Method        *AdminLoginRestrictionMethod `protobuf:"varint,4,opt,name=method,proto3,enum=admin.service.v1.AdminLoginRestrictionMethod,oneof" json:"method,omitempty"` // 限制方式
	Value         *string                      `protobuf:"bytes,5,opt,name=value,proto3,oneof" json:"value,omitempty"`                                                      // 限制值（如IP地址、MAC地址或地区代码）
	Reason        *string                      `protobuf:"bytes,6,opt,name=reason,proto3,oneof" json:"reason,omitempty"`                                                    // 限制原因
	CreateBy      *uint32                      `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                             // 创建者ID
	UpdateBy      *uint32                      `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                             // 更新者ID
	CreateTime    *timestamppb.Timestamp       `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                        // 创建时间
	UpdateTime    *timestamppb.Timestamp       `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                        // 更新时间
	DeleteTime    *timestamppb.Timestamp       `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                        // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminLoginRestriction) Reset() {
	*x = AdminLoginRestriction{}
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminLoginRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminLoginRestriction) ProtoMessage() {}

func (x *AdminLoginRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminLoginRestriction.ProtoReflect.Descriptor instead.
func (*AdminLoginRestriction) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{0}
}

func (x *AdminLoginRestriction) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AdminLoginRestriction) GetTargetId() uint32 {
	if x != nil && x.TargetId != nil {
		return *x.TargetId
	}
	return 0
}

func (x *AdminLoginRestriction) GetType() AdminLoginRestrictionType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return AdminLoginRestrictionType_LOGIN_RESTRICTION_TYPE_UNSPECIFIED
}

func (x *AdminLoginRestriction) GetMethod() AdminLoginRestrictionMethod {
	if x != nil && x.Method != nil {
		return *x.Method
	}
	return AdminLoginRestrictionMethod_LOGIN_RESTRICTION_METHOD_UNSPECIFIED
}

func (x *AdminLoginRestriction) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *AdminLoginRestriction) GetReason() string {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return ""
}

func (x *AdminLoginRestriction) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *AdminLoginRestriction) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *AdminLoginRestriction) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AdminLoginRestriction) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AdminLoginRestriction) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询后台登录限制列表 - 回应
type ListAdminLoginRestrictionResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Items         []*AdminLoginRestriction `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAdminLoginRestrictionResponse) Reset() {
	*x = ListAdminLoginRestrictionResponse{}
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAdminLoginRestrictionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAdminLoginRestrictionResponse) ProtoMessage() {}

func (x *ListAdminLoginRestrictionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAdminLoginRestrictionResponse.ProtoReflect.Descriptor instead.
func (*ListAdminLoginRestrictionResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{1}
}

func (x *ListAdminLoginRestrictionResponse) GetItems() []*AdminLoginRestriction {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListAdminLoginRestrictionResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询后台登录限制详情 - 请求
type GetAdminLoginRestrictionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAdminLoginRestrictionRequest) Reset() {
	*x = GetAdminLoginRestrictionRequest{}
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdminLoginRestrictionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdminLoginRestrictionRequest) ProtoMessage() {}

func (x *GetAdminLoginRestrictionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdminLoginRestrictionRequest.ProtoReflect.Descriptor instead.
func (*GetAdminLoginRestrictionRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{2}
}

func (x *GetAdminLoginRestrictionRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建后台登录限制 - 请求
type CreateAdminLoginRestrictionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *AdminLoginRestriction `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAdminLoginRestrictionRequest) Reset() {
	*x = CreateAdminLoginRestrictionRequest{}
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAdminLoginRestrictionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdminLoginRestrictionRequest) ProtoMessage() {}

func (x *CreateAdminLoginRestrictionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdminLoginRestrictionRequest.ProtoReflect.Descriptor instead.
func (*CreateAdminLoginRestrictionRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAdminLoginRestrictionRequest) GetData() *AdminLoginRestriction {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新后台登录限制 - 请求
type UpdateAdminLoginRestrictionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *AdminLoginRestriction `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAdminLoginRestrictionRequest) Reset() {
	*x = UpdateAdminLoginRestrictionRequest{}
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAdminLoginRestrictionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdminLoginRestrictionRequest) ProtoMessage() {}

func (x *UpdateAdminLoginRestrictionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdminLoginRestrictionRequest.ProtoReflect.Descriptor instead.
func (*UpdateAdminLoginRestrictionRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAdminLoginRestrictionRequest) GetData() *AdminLoginRestriction {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateAdminLoginRestrictionRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateAdminLoginRestrictionRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除后台登录限制 - 请求
type DeleteAdminLoginRestrictionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAdminLoginRestrictionRequest) Reset() {
	*x = DeleteAdminLoginRestrictionRequest{}
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAdminLoginRestrictionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAdminLoginRestrictionRequest) ProtoMessage() {}

func (x *DeleteAdminLoginRestrictionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAdminLoginRestrictionRequest.ProtoReflect.Descriptor instead.
func (*DeleteAdminLoginRestrictionRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteAdminLoginRestrictionRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_admin_service_v1_i_admin_login_restriction_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_admin_login_restriction_proto_rawDesc = "" +
	"\n" +
	"0admin/service/v1/i_admin_login_restriction.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xbf\a\n" +
	"\x15AdminLoginRestriction\x122\n" +
	"\x02id\x18\x01 \x01(\rB\x1d\xe0A\x01\xbaG\x17\x92\x02\x14后台登录限制IDH\x00R\x02id\x88\x01\x01\x126\n" +
	"\ttarget_id\x18\x02 \x01(\rB\x14\xbaG\x11\x92\x02\x0e目标用户IDH\x01R\btargetId\x88\x01\x01\x12X\n" +
	"\x04type\x18\x03 \x01(\x0e2+.admin.service.v1.AdminLoginRestrictionTypeB\x12\xbaG\x0f\x92\x02\f限制类型H\x02R\x04type\x88\x01\x01\x12^\n" +
	"\x06method\x18\x04 \x01(\x0e2-.admin.service.v1.AdminLoginRestrictionMethodB\x12\xbaG\x0f\x92\x02\f限制方式H\x03R\x06method\x88\x01\x01\x12V\n" +
	"\x05value\x18\x05 \x01(\tB;\xbaG8\x92\x025限制值（如IP地址、MAC地址或地区代码）H\x04R\x05value\x88\x01\x01\x12/\n" +
	"\x06reason\x18\x06 \x01(\tB\x12\xbaG\x0f\x92\x02\f限制原因H\x05R\x06reason\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\x06R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\aR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\bR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\tR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\n" +
	"R\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\f\n" +
	"\n" +
	"_target_idB\a\n" +
	"\x05_typeB\t\n" +
	"\a_methodB\b\n" +
	"\x06_valueB\t\n" +
	"\a_reasonB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"x\n" +
	"!ListAdminLoginRestrictionResponse\x12=\n" +
	"\x05items\x18\x01 \x03(\v2'.admin.service.v1.AdminLoginRestrictionR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"1\n" +
	"\x1fGetAdminLoginRestrictionRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"a\n" +
	"\"CreateAdminLoginRestrictionRequest\x12;\n" +
	"\x04data\x18\x01 \x01(\v2'.admin.service.v1.AdminLoginRestrictionR\x04data\"\x9f\x03\n" +
	"\"UpdateAdminLoginRestrictionRequest\x12;\n" +
	"\x04data\x18\x01 \x01(\v2'.admin.service.v1.AdminLoginRestrictionR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"4\n" +
	"\"DeleteAdminLoginRestrictionRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id*a\n" +
	"\x19AdminLoginRestrictionType\x12&\n" +
	"\"LOGIN_RESTRICTION_TYPE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tBLACKLIST\x10\x01\x12\r\n" +
	"\tWHITELIST\x10\x02*z\n" +
	"\x1bAdminLoginRestrictionMethod\x12(\n" +
	"$LOGIN_RESTRICTION_METHOD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02IP\x10\x01\x12\a\n" +
	"\x03MAC\x10\x02\x12\n" +
	"\n" +
	"\x06REGION\x10\x03\x12\b\n" +
	"\x04TIME\x10\x04\x12\n" +
	"\n" +
	"\x06DEVICE\x10\x052\xbc\x05\n" +
	"\x1cAdminLoginRestrictionService\x12|\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a3.admin.service.v1.ListAdminLoginRestrictionResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/admin/v1/login-restrictions\x12\x8c\x01\n" +
	"\x03Get\x121.admin.service.v1.GetAdminLoginRestrictionRequest\x1a'.admin.service.v1.AdminLoginRestriction\")\x82\xd3\xe4\x93\x02#\x12!/admin/v1/login-restrictions/{id}\x12\x7f\n" +
	"\x06Create\x124.admin.service.v1.CreateAdminLoginRestrictionRequest\x1a\x16.google.protobuf.Empty\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/admin/v1/login-restrictions\x12\x89\x01\n" +
	"\x06Update\x124.admin.service.v1.UpdateAdminLoginRestrictionRequest\x1a\x16.google.protobuf.Empty\"1\x82\xd3\xe4\x93\x02+:\x01*\x1a&/admin/v1/login-restrictions/{data.id}\x12\x81\x01\n" +
	"\x06Delete\x124.admin.service.v1.DeleteAdminLoginRestrictionRequest\x1a\x16.google.protobuf.Empty\")\x82\xd3\xe4\x93\x02#*!/admin/v1/login-restrictions/{id}B\xc9\x01\n" +
	"\x14com.admin.service.v1B\x1bIAdminLoginRestrictionProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_admin_login_restriction_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_admin_login_restriction_proto_rawDescData []byte
)

func file_admin_service_v1_i_admin_login_restriction_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_admin_login_restriction_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_admin_login_restriction_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_admin_login_restriction_proto_rawDesc), len(file_admin_service_v1_i_admin_login_restriction_proto_rawDesc)))
	})
	return file_admin_service_v1_i_admin_login_restriction_proto_rawDescData
}

var file_admin_service_v1_i_admin_login_restriction_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_admin_service_v1_i_admin_login_restriction_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_admin_service_v1_i_admin_login_restriction_proto_goTypes = []any{
	(AdminLoginRestrictionType)(0),             // 0: admin.service.v1.AdminLoginRestrictionType
	(AdminLoginRestrictionMethod)(0),           // 1: admin.service.v1.AdminLoginRestrictionMethod
	(*AdminLoginRestriction)(nil),              // 2: admin.service.v1.AdminLoginRestriction
	(*ListAdminLoginRestrictionResponse)(nil),  // 3: admin.service.v1.ListAdminLoginRestrictionResponse
	(*GetAdminLoginRestrictionRequest)(nil),    // 4: admin.service.v1.GetAdminLoginRestrictionRequest
	(*CreateAdminLoginRestrictionRequest)(nil), // 5: admin.service.v1.CreateAdminLoginRestrictionRequest
	(*UpdateAdminLoginRestrictionRequest)(nil), // 6: admin.service.v1.UpdateAdminLoginRestrictionRequest
	(*DeleteAdminLoginRestrictionRequest)(nil), // 7: admin.service.v1.DeleteAdminLoginRestrictionRequest
	(*timestamppb.Timestamp)(nil),              // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),              // 9: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),                   // 10: pagination.PagingRequest
	(*emptypb.Empty)(nil),                      // 11: google.protobuf.Empty
}
var file_admin_service_v1_i_admin_login_restriction_proto_depIdxs = []int32{
	0,  // 0: admin.service.v1.AdminLoginRestriction.type:type_name -> admin.service.v1.AdminLoginRestrictionType
	1,  // 1: admin.service.v1.AdminLoginRestriction.method:type_name -> admin.service.v1.AdminLoginRestrictionMethod
	8,  // 2: admin.service.v1.AdminLoginRestriction.create_time:type_name -> google.protobuf.Timestamp
	8,  // 3: admin.service.v1.AdminLoginRestriction.update_time:type_name -> google.protobuf.Timestamp
	8,  // 4: admin.service.v1.AdminLoginRestriction.delete_time:type_name -> google.protobuf.Timestamp
	2,  // 5: admin.service.v1.ListAdminLoginRestrictionResponse.items:type_name -> admin.service.v1.AdminLoginRestriction
	2,  // 6: admin.service.v1.CreateAdminLoginRestrictionRequest.data:type_name -> admin.service.v1.AdminLoginRestriction
	2,  // 7: admin.service.v1.UpdateAdminLoginRestrictionRequest.data:type_name -> admin.service.v1.AdminLoginRestriction
	9,  // 8: admin.service.v1.UpdateAdminLoginRestrictionRequest.update_mask:type_name -> google.protobuf.FieldMask
	10, // 9: admin.service.v1.AdminLoginRestrictionService.List:input_type -> pagination.PagingRequest
	4,  // 10: admin.service.v1.AdminLoginRestrictionService.Get:input_type -> admin.service.v1.GetAdminLoginRestrictionRequest
	5,  // 11: admin.service.v1.AdminLoginRestrictionService.Create:input_type -> admin.service.v1.CreateAdminLoginRestrictionRequest
	6,  // 12: admin.service.v1.AdminLoginRestrictionService.Update:input_type -> admin.service.v1.UpdateAdminLoginRestrictionRequest
	7,  // 13: admin.service.v1.AdminLoginRestrictionService.Delete:input_type -> admin.service.v1.DeleteAdminLoginRestrictionRequest
	3,  // 14: admin.service.v1.AdminLoginRestrictionService.List:output_type -> admin.service.v1.ListAdminLoginRestrictionResponse
	2,  // 15: admin.service.v1.AdminLoginRestrictionService.Get:output_type -> admin.service.v1.AdminLoginRestriction
	11, // 16: admin.service.v1.AdminLoginRestrictionService.Create:output_type -> google.protobuf.Empty
	11, // 17: admin.service.v1.AdminLoginRestrictionService.Update:output_type -> google.protobuf.Empty
	11, // 18: admin.service.v1.AdminLoginRestrictionService.Delete:output_type -> google.protobuf.Empty
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_admin_login_restriction_proto_init() }
func file_admin_service_v1_i_admin_login_restriction_proto_init() {
	if File_admin_service_v1_i_admin_login_restriction_proto != nil {
		return
	}
	file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_admin_login_restriction_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_admin_login_restriction_proto_rawDesc), len(file_admin_service_v1_i_admin_login_restriction_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_admin_login_restriction_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_admin_login_restriction_proto_depIdxs,
		EnumInfos:         file_admin_service_v1_i_admin_login_restriction_proto_enumTypes,
		MessageInfos:      file_admin_service_v1_i_admin_login_restriction_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_admin_login_restriction_proto = out.File
	file_admin_service_v1_i_admin_login_restriction_proto_goTypes = nil
	file_admin_service_v1_i_admin_login_restriction_proto_depIdxs = nil
}
