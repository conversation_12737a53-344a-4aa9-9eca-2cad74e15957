// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: admin/service/v1/i_api_resource.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ApiResourceService_List_FullMethodName             = "/admin.service.v1.ApiResourceService/List"
	ApiResourceService_Get_FullMethodName              = "/admin.service.v1.ApiResourceService/Get"
	ApiResourceService_Create_FullMethodName           = "/admin.service.v1.ApiResourceService/Create"
	ApiResourceService_Update_FullMethodName           = "/admin.service.v1.ApiResourceService/Update"
	ApiResourceService_Delete_FullMethodName           = "/admin.service.v1.ApiResourceService/Delete"
	ApiResourceService_SyncApiResources_FullMethodName = "/admin.service.v1.ApiResourceService/SyncApiResources"
	ApiResourceService_GetWalkRouteData_FullMethodName = "/admin.service.v1.ApiResourceService/GetWalkRouteData"
)

// ApiResourceServiceClient is the client API for ApiResourceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// API资源管理服务
type ApiResourceServiceClient interface {
	// 查询API资源列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListApiResourceResponse, error)
	// 查询API资源详情
	Get(ctx context.Context, in *GetApiResourceRequest, opts ...grpc.CallOption) (*ApiResource, error)
	// 创建API资源
	Create(ctx context.Context, in *CreateApiResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新API资源
	Update(ctx context.Context, in *UpdateApiResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除API资源
	Delete(ctx context.Context, in *DeleteApiResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 同步API资源
	SyncApiResources(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 查询路由数据
	GetWalkRouteData(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListApiResourceResponse, error)
}

type apiResourceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApiResourceServiceClient(cc grpc.ClientConnInterface) ApiResourceServiceClient {
	return &apiResourceServiceClient{cc}
}

func (c *apiResourceServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListApiResourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListApiResourceResponse)
	err := c.cc.Invoke(ctx, ApiResourceService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiResourceServiceClient) Get(ctx context.Context, in *GetApiResourceRequest, opts ...grpc.CallOption) (*ApiResource, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApiResource)
	err := c.cc.Invoke(ctx, ApiResourceService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiResourceServiceClient) Create(ctx context.Context, in *CreateApiResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApiResourceService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiResourceServiceClient) Update(ctx context.Context, in *UpdateApiResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApiResourceService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiResourceServiceClient) Delete(ctx context.Context, in *DeleteApiResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApiResourceService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiResourceServiceClient) SyncApiResources(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ApiResourceService_SyncApiResources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiResourceServiceClient) GetWalkRouteData(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListApiResourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListApiResourceResponse)
	err := c.cc.Invoke(ctx, ApiResourceService_GetWalkRouteData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiResourceServiceServer is the server API for ApiResourceService service.
// All implementations must embed UnimplementedApiResourceServiceServer
// for forward compatibility.
//
// API资源管理服务
type ApiResourceServiceServer interface {
	// 查询API资源列表
	List(context.Context, *v1.PagingRequest) (*ListApiResourceResponse, error)
	// 查询API资源详情
	Get(context.Context, *GetApiResourceRequest) (*ApiResource, error)
	// 创建API资源
	Create(context.Context, *CreateApiResourceRequest) (*emptypb.Empty, error)
	// 更新API资源
	Update(context.Context, *UpdateApiResourceRequest) (*emptypb.Empty, error)
	// 删除API资源
	Delete(context.Context, *DeleteApiResourceRequest) (*emptypb.Empty, error)
	// 同步API资源
	SyncApiResources(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// 查询路由数据
	GetWalkRouteData(context.Context, *emptypb.Empty) (*ListApiResourceResponse, error)
	mustEmbedUnimplementedApiResourceServiceServer()
}

// UnimplementedApiResourceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiResourceServiceServer struct{}

func (UnimplementedApiResourceServiceServer) List(context.Context, *v1.PagingRequest) (*ListApiResourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedApiResourceServiceServer) Get(context.Context, *GetApiResourceRequest) (*ApiResource, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedApiResourceServiceServer) Create(context.Context, *CreateApiResourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedApiResourceServiceServer) Update(context.Context, *UpdateApiResourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedApiResourceServiceServer) Delete(context.Context, *DeleteApiResourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedApiResourceServiceServer) SyncApiResources(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncApiResources not implemented")
}
func (UnimplementedApiResourceServiceServer) GetWalkRouteData(context.Context, *emptypb.Empty) (*ListApiResourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWalkRouteData not implemented")
}
func (UnimplementedApiResourceServiceServer) mustEmbedUnimplementedApiResourceServiceServer() {}
func (UnimplementedApiResourceServiceServer) testEmbeddedByValue()                            {}

// UnsafeApiResourceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiResourceServiceServer will
// result in compilation errors.
type UnsafeApiResourceServiceServer interface {
	mustEmbedUnimplementedApiResourceServiceServer()
}

func RegisterApiResourceServiceServer(s grpc.ServiceRegistrar, srv ApiResourceServiceServer) {
	// If the following call pancis, it indicates UnimplementedApiResourceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ApiResourceService_ServiceDesc, srv)
}

func _ApiResourceService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiResourceService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApiResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).Get(ctx, req.(*GetApiResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiResourceService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApiResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).Create(ctx, req.(*CreateApiResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiResourceService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateApiResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).Update(ctx, req.(*UpdateApiResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiResourceService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteApiResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).Delete(ctx, req.(*DeleteApiResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiResourceService_SyncApiResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).SyncApiResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_SyncApiResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).SyncApiResources(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiResourceService_GetWalkRouteData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiResourceServiceServer).GetWalkRouteData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiResourceService_GetWalkRouteData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiResourceServiceServer).GetWalkRouteData(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ApiResourceService_ServiceDesc is the grpc.ServiceDesc for ApiResourceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApiResourceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "admin.service.v1.ApiResourceService",
	HandlerType: (*ApiResourceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _ApiResourceService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _ApiResourceService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _ApiResourceService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _ApiResourceService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _ApiResourceService_Delete_Handler,
		},
		{
			MethodName: "SyncApiResources",
			Handler:    _ApiResourceService_SyncApiResources_Handler,
		},
		{
			MethodName: "GetWalkRouteData",
			Handler:    _ApiResourceService_GetWalkRouteData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "admin/service/v1/i_api_resource.proto",
}
