// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_private_message.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/internal_message/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_private_message_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_private_message_proto_rawDesc = "" +
	"\n" +
	"(admin/service/v1/i_private_message.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1epagination/v1/pagination.proto\x1a1internal_message/service/v1/private_message.proto2\xc4\x05\n" +
	"\x15PrivateMessageService\x12~\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a7.internal_message.service.v1.ListPrivateMessageResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/admin/v1/private_messages\x12\x92\x01\n" +
	"\x03Get\x125.internal_message.service.v1.GetPrivateMessageRequest\x1a+.internal_message.service.v1.PrivateMessage\"'\x82\xd3\xe4\x93\x02!\x12\x1f/admin/v1/private_messages/{id}\x12\x81\x01\n" +
	"\x06Create\x128.internal_message.service.v1.CreatePrivateMessageRequest\x1a\x16.google.protobuf.Empty\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/admin/v1/private_messages\x12\x8b\x01\n" +
	"\x06Update\x128.internal_message.service.v1.UpdatePrivateMessageRequest\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02):\x01*\x1a$/admin/v1/private_messages/{data.id}\x12\x83\x01\n" +
	"\x06Delete\x128.internal_message.service.v1.DeletePrivateMessageRequest\x1a\x16.google.protobuf.Empty\"'\x82\xd3\xe4\x93\x02!*\x1f/admin/v1/private_messages/{id}B\xc2\x01\n" +
	"\x14com.admin.service.v1B\x14IPrivateMessageProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_private_message_proto_goTypes = []any{
	(*v1.PagingRequest)(nil),                // 0: pagination.PagingRequest
	(*v11.GetPrivateMessageRequest)(nil),    // 1: internal_message.service.v1.GetPrivateMessageRequest
	(*v11.CreatePrivateMessageRequest)(nil), // 2: internal_message.service.v1.CreatePrivateMessageRequest
	(*v11.UpdatePrivateMessageRequest)(nil), // 3: internal_message.service.v1.UpdatePrivateMessageRequest
	(*v11.DeletePrivateMessageRequest)(nil), // 4: internal_message.service.v1.DeletePrivateMessageRequest
	(*v11.ListPrivateMessageResponse)(nil),  // 5: internal_message.service.v1.ListPrivateMessageResponse
	(*v11.PrivateMessage)(nil),              // 6: internal_message.service.v1.PrivateMessage
	(*emptypb.Empty)(nil),                   // 7: google.protobuf.Empty
}
var file_admin_service_v1_i_private_message_proto_depIdxs = []int32{
	0, // 0: admin.service.v1.PrivateMessageService.List:input_type -> pagination.PagingRequest
	1, // 1: admin.service.v1.PrivateMessageService.Get:input_type -> internal_message.service.v1.GetPrivateMessageRequest
	2, // 2: admin.service.v1.PrivateMessageService.Create:input_type -> internal_message.service.v1.CreatePrivateMessageRequest
	3, // 3: admin.service.v1.PrivateMessageService.Update:input_type -> internal_message.service.v1.UpdatePrivateMessageRequest
	4, // 4: admin.service.v1.PrivateMessageService.Delete:input_type -> internal_message.service.v1.DeletePrivateMessageRequest
	5, // 5: admin.service.v1.PrivateMessageService.List:output_type -> internal_message.service.v1.ListPrivateMessageResponse
	6, // 6: admin.service.v1.PrivateMessageService.Get:output_type -> internal_message.service.v1.PrivateMessage
	7, // 7: admin.service.v1.PrivateMessageService.Create:output_type -> google.protobuf.Empty
	7, // 8: admin.service.v1.PrivateMessageService.Update:output_type -> google.protobuf.Empty
	7, // 9: admin.service.v1.PrivateMessageService.Delete:output_type -> google.protobuf.Empty
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_private_message_proto_init() }
func file_admin_service_v1_i_private_message_proto_init() {
	if File_admin_service_v1_i_private_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_private_message_proto_rawDesc), len(file_admin_service_v1_i_private_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_private_message_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_private_message_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_private_message_proto = out.File
	file_admin_service_v1_i_private_message_proto_goTypes = nil
	file_admin_service_v1_i_private_message_proto_depIdxs = nil
}
