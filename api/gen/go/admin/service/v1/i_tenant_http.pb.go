// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_tenant.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	v11 "kratos-admin/api/gen/go/user/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTenantServiceCreate = "/admin.service.v1.TenantService/Create"
const OperationTenantServiceDelete = "/admin.service.v1.TenantService/Delete"
const OperationTenantServiceGet = "/admin.service.v1.TenantService/Get"
const OperationTenantServiceList = "/admin.service.v1.TenantService/List"
const OperationTenantServiceUpdate = "/admin.service.v1.TenantService/Update"

type TenantServiceHTTPServer interface {
	// Create 创建租户
	Create(context.Context, *v11.CreateTenantRequest) (*emptypb.Empty, error)
	// Delete 删除租户
	Delete(context.Context, *v11.DeleteTenantRequest) (*emptypb.Empty, error)
	// Get 获取租户数据
	Get(context.Context, *v11.GetTenantRequest) (*v11.Tenant, error)
	// List 获取租户列表
	List(context.Context, *v1.PagingRequest) (*v11.ListTenantResponse, error)
	// Update 更新租户
	Update(context.Context, *v11.UpdateTenantRequest) (*emptypb.Empty, error)
}

func RegisterTenantServiceHTTPServer(s *http.Server, srv TenantServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/tenants", _TenantService_List16_HTTP_Handler(srv))
	r.GET("/admin/v1/tenants/{id}", _TenantService_Get16_HTTP_Handler(srv))
	r.POST("/admin/v1/tenants", _TenantService_Create14_HTTP_Handler(srv))
	r.PUT("/admin/v1/tenants/{data.id}", _TenantService_Update14_HTTP_Handler(srv))
	r.DELETE("/admin/v1/tenants/{id}", _TenantService_Delete14_HTTP_Handler(srv))
}

func _TenantService_List16_HTTP_Handler(srv TenantServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTenantServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListTenantResponse)
		return ctx.Result(200, reply)
	}
}

func _TenantService_Get16_HTTP_Handler(srv TenantServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetTenantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTenantServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*v11.GetTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.Tenant)
		return ctx.Result(200, reply)
	}
}

func _TenantService_Create14_HTTP_Handler(srv TenantServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateTenantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTenantServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*v11.CreateTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _TenantService_Update14_HTTP_Handler(srv TenantServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UpdateTenantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTenantServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*v11.UpdateTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _TenantService_Delete14_HTTP_Handler(srv TenantServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteTenantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTenantServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*v11.DeleteTenantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type TenantServiceHTTPClient interface {
	Create(ctx context.Context, req *v11.CreateTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *v11.DeleteTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *v11.GetTenantRequest, opts ...http.CallOption) (rsp *v11.Tenant, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *v11.ListTenantResponse, err error)
	Update(ctx context.Context, req *v11.UpdateTenantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type TenantServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewTenantServiceHTTPClient(client *http.Client) TenantServiceHTTPClient {
	return &TenantServiceHTTPClientImpl{client}
}

func (c *TenantServiceHTTPClientImpl) Create(ctx context.Context, in *v11.CreateTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tenants"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTenantServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TenantServiceHTTPClientImpl) Delete(ctx context.Context, in *v11.DeleteTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tenants/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTenantServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TenantServiceHTTPClientImpl) Get(ctx context.Context, in *v11.GetTenantRequest, opts ...http.CallOption) (*v11.Tenant, error) {
	var out v11.Tenant
	pattern := "/admin/v1/tenants/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTenantServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TenantServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*v11.ListTenantResponse, error) {
	var out v11.ListTenantResponse
	pattern := "/admin/v1/tenants"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTenantServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TenantServiceHTTPClientImpl) Update(ctx context.Context, in *v11.UpdateTenantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/tenants/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTenantServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
