// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: admin/service/v1/i_admin_login_restriction.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AdminLoginRestriction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdminLoginRestriction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminLoginRestriction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminLoginRestrictionMultiError, or nil if none found.
func (m *AdminLoginRestriction) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminLoginRestriction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.TargetId != nil {
		// no validation rules for TargetId
	}

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.Method != nil {
		// no validation rules for Method
	}

	if m.Value != nil {
		// no validation rules for Value
	}

	if m.Reason != nil {
		// no validation rules for Reason
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdminLoginRestrictionValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdminLoginRestrictionValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdminLoginRestrictionValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdminLoginRestrictionValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdminLoginRestrictionValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdminLoginRestrictionValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdminLoginRestrictionValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdminLoginRestrictionValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdminLoginRestrictionValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AdminLoginRestrictionMultiError(errors)
	}

	return nil
}

// AdminLoginRestrictionMultiError is an error wrapping multiple validation
// errors returned by AdminLoginRestriction.ValidateAll() if the designated
// constraints aren't met.
type AdminLoginRestrictionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminLoginRestrictionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminLoginRestrictionMultiError) AllErrors() []error { return m }

// AdminLoginRestrictionValidationError is the validation error returned by
// AdminLoginRestriction.Validate if the designated constraints aren't met.
type AdminLoginRestrictionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminLoginRestrictionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminLoginRestrictionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminLoginRestrictionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminLoginRestrictionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminLoginRestrictionValidationError) ErrorName() string {
	return "AdminLoginRestrictionValidationError"
}

// Error satisfies the builtin error interface
func (e AdminLoginRestrictionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminLoginRestriction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminLoginRestrictionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminLoginRestrictionValidationError{}

// Validate checks the field values on ListAdminLoginRestrictionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListAdminLoginRestrictionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAdminLoginRestrictionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListAdminLoginRestrictionResponseMultiError, or nil if none found.
func (m *ListAdminLoginRestrictionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAdminLoginRestrictionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAdminLoginRestrictionResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAdminLoginRestrictionResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAdminLoginRestrictionResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAdminLoginRestrictionResponseMultiError(errors)
	}

	return nil
}

// ListAdminLoginRestrictionResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListAdminLoginRestrictionResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAdminLoginRestrictionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAdminLoginRestrictionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAdminLoginRestrictionResponseMultiError) AllErrors() []error { return m }

// ListAdminLoginRestrictionResponseValidationError is the validation error
// returned by ListAdminLoginRestrictionResponse.Validate if the designated
// constraints aren't met.
type ListAdminLoginRestrictionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAdminLoginRestrictionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAdminLoginRestrictionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAdminLoginRestrictionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAdminLoginRestrictionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAdminLoginRestrictionResponseValidationError) ErrorName() string {
	return "ListAdminLoginRestrictionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAdminLoginRestrictionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAdminLoginRestrictionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAdminLoginRestrictionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAdminLoginRestrictionResponseValidationError{}

// Validate checks the field values on GetAdminLoginRestrictionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAdminLoginRestrictionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAdminLoginRestrictionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAdminLoginRestrictionRequestMultiError, or nil if none found.
func (m *GetAdminLoginRestrictionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAdminLoginRestrictionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAdminLoginRestrictionRequestMultiError(errors)
	}

	return nil
}

// GetAdminLoginRestrictionRequestMultiError is an error wrapping multiple
// validation errors returned by GetAdminLoginRestrictionRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAdminLoginRestrictionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAdminLoginRestrictionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAdminLoginRestrictionRequestMultiError) AllErrors() []error { return m }

// GetAdminLoginRestrictionRequestValidationError is the validation error
// returned by GetAdminLoginRestrictionRequest.Validate if the designated
// constraints aren't met.
type GetAdminLoginRestrictionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAdminLoginRestrictionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAdminLoginRestrictionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAdminLoginRestrictionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAdminLoginRestrictionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAdminLoginRestrictionRequestValidationError) ErrorName() string {
	return "GetAdminLoginRestrictionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAdminLoginRestrictionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAdminLoginRestrictionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAdminLoginRestrictionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAdminLoginRestrictionRequestValidationError{}

// Validate checks the field values on CreateAdminLoginRestrictionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateAdminLoginRestrictionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAdminLoginRestrictionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateAdminLoginRestrictionRequestMultiError, or nil if none found.
func (m *CreateAdminLoginRestrictionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAdminLoginRestrictionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAdminLoginRestrictionRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAdminLoginRestrictionRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAdminLoginRestrictionRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAdminLoginRestrictionRequestMultiError(errors)
	}

	return nil
}

// CreateAdminLoginRestrictionRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateAdminLoginRestrictionRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAdminLoginRestrictionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAdminLoginRestrictionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAdminLoginRestrictionRequestMultiError) AllErrors() []error { return m }

// CreateAdminLoginRestrictionRequestValidationError is the validation error
// returned by CreateAdminLoginRestrictionRequest.Validate if the designated
// constraints aren't met.
type CreateAdminLoginRestrictionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAdminLoginRestrictionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAdminLoginRestrictionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAdminLoginRestrictionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAdminLoginRestrictionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAdminLoginRestrictionRequestValidationError) ErrorName() string {
	return "CreateAdminLoginRestrictionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAdminLoginRestrictionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAdminLoginRestrictionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAdminLoginRestrictionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAdminLoginRestrictionRequestValidationError{}

// Validate checks the field values on UpdateAdminLoginRestrictionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateAdminLoginRestrictionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAdminLoginRestrictionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateAdminLoginRestrictionRequestMultiError, or nil if none found.
func (m *UpdateAdminLoginRestrictionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAdminLoginRestrictionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAdminLoginRestrictionRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAdminLoginRestrictionRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAdminLoginRestrictionRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAdminLoginRestrictionRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAdminLoginRestrictionRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAdminLoginRestrictionRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateAdminLoginRestrictionRequestMultiError(errors)
	}

	return nil
}

// UpdateAdminLoginRestrictionRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateAdminLoginRestrictionRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAdminLoginRestrictionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAdminLoginRestrictionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAdminLoginRestrictionRequestMultiError) AllErrors() []error { return m }

// UpdateAdminLoginRestrictionRequestValidationError is the validation error
// returned by UpdateAdminLoginRestrictionRequest.Validate if the designated
// constraints aren't met.
type UpdateAdminLoginRestrictionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAdminLoginRestrictionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAdminLoginRestrictionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAdminLoginRestrictionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAdminLoginRestrictionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAdminLoginRestrictionRequestValidationError) ErrorName() string {
	return "UpdateAdminLoginRestrictionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAdminLoginRestrictionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAdminLoginRestrictionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAdminLoginRestrictionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAdminLoginRestrictionRequestValidationError{}

// Validate checks the field values on DeleteAdminLoginRestrictionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteAdminLoginRestrictionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAdminLoginRestrictionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteAdminLoginRestrictionRequestMultiError, or nil if none found.
func (m *DeleteAdminLoginRestrictionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAdminLoginRestrictionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAdminLoginRestrictionRequestMultiError(errors)
	}

	return nil
}

// DeleteAdminLoginRestrictionRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteAdminLoginRestrictionRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAdminLoginRestrictionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAdminLoginRestrictionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAdminLoginRestrictionRequestMultiError) AllErrors() []error { return m }

// DeleteAdminLoginRestrictionRequestValidationError is the validation error
// returned by DeleteAdminLoginRestrictionRequest.Validate if the designated
// constraints aren't met.
type DeleteAdminLoginRestrictionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAdminLoginRestrictionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAdminLoginRestrictionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAdminLoginRestrictionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAdminLoginRestrictionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAdminLoginRestrictionRequestValidationError) ErrorName() string {
	return "DeleteAdminLoginRestrictionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAdminLoginRestrictionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAdminLoginRestrictionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAdminLoginRestrictionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAdminLoginRestrictionRequestValidationError{}
