// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_admin_login_log.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 后台登录日志
type AdminLoginLog struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                // 后台登录日志ID
	LoginIp        *string                `protobuf:"bytes,2,opt,name=login_ip,json=loginIp,proto3,oneof" json:"login_ip,omitempty"`                        // 登录IP地址
	LoginMac       *string                `protobuf:"bytes,3,opt,name=login_mac,json=loginMac,proto3,oneof" json:"login_mac,omitempty"`                     // 登录MAC地址
	LoginTime      *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=login_time,json=loginTime,proto3,oneof" json:"login_time,omitempty"`                  // 登录时间
	StatusCode     *int32                 `protobuf:"varint,5,opt,name=status_code,json=statusCode,proto3,oneof" json:"status_code,omitempty"`              // 状态码
	Success        *bool                  `protobuf:"varint,6,opt,name=success,proto3,oneof" json:"success,omitempty"`                                      // 登录是否成功
	Reason         *string                `protobuf:"bytes,7,opt,name=reason,proto3,oneof" json:"reason,omitempty"`                                         // 登录失败原因
	Location       *string                `protobuf:"bytes,8,opt,name=location,proto3,oneof" json:"location,omitempty"`                                     // 登录地理位置
	UserAgent      *string                `protobuf:"bytes,100,opt,name=user_agent,json=userAgent,proto3,oneof" json:"user_agent,omitempty"`                // 浏览器的用户代理信息
	BrowserName    *string                `protobuf:"bytes,101,opt,name=browser_name,json=browserName,proto3,oneof" json:"browser_name,omitempty"`          // 浏览器名称
	BrowserVersion *string                `protobuf:"bytes,102,opt,name=browser_version,json=browserVersion,proto3,oneof" json:"browser_version,omitempty"` // 浏览器版本
	ClientId       *string                `protobuf:"bytes,200,opt,name=client_id,json=clientId,proto3,oneof" json:"client_id,omitempty"`                   // 客户端ID
	ClientName     *string                `protobuf:"bytes,201,opt,name=client_name,json=clientName,proto3,oneof" json:"client_name,omitempty"`             // 客户端名称
	OsName         *string                `protobuf:"bytes,202,opt,name=os_name,json=osName,proto3,oneof" json:"os_name,omitempty"`                         // 操作系统名称
	OsVersion      *string                `protobuf:"bytes,203,opt,name=os_version,json=osVersion,proto3,oneof" json:"os_version,omitempty"`                // 操作系统版本
	UserId         *uint32                `protobuf:"varint,300,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`                        // 操作者用户ID
	Username       *string                `protobuf:"bytes,301,opt,name=username,proto3,oneof" json:"username,omitempty"`                                   // 操作者账号名
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,400,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`             // 创建时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AdminLoginLog) Reset() {
	*x = AdminLoginLog{}
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminLoginLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminLoginLog) ProtoMessage() {}

func (x *AdminLoginLog) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminLoginLog.ProtoReflect.Descriptor instead.
func (*AdminLoginLog) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP(), []int{0}
}

func (x *AdminLoginLog) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AdminLoginLog) GetLoginIp() string {
	if x != nil && x.LoginIp != nil {
		return *x.LoginIp
	}
	return ""
}

func (x *AdminLoginLog) GetLoginMac() string {
	if x != nil && x.LoginMac != nil {
		return *x.LoginMac
	}
	return ""
}

func (x *AdminLoginLog) GetLoginTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

func (x *AdminLoginLog) GetStatusCode() int32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *AdminLoginLog) GetSuccess() bool {
	if x != nil && x.Success != nil {
		return *x.Success
	}
	return false
}

func (x *AdminLoginLog) GetReason() string {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return ""
}

func (x *AdminLoginLog) GetLocation() string {
	if x != nil && x.Location != nil {
		return *x.Location
	}
	return ""
}

func (x *AdminLoginLog) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *AdminLoginLog) GetBrowserName() string {
	if x != nil && x.BrowserName != nil {
		return *x.BrowserName
	}
	return ""
}

func (x *AdminLoginLog) GetBrowserVersion() string {
	if x != nil && x.BrowserVersion != nil {
		return *x.BrowserVersion
	}
	return ""
}

func (x *AdminLoginLog) GetClientId() string {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return ""
}

func (x *AdminLoginLog) GetClientName() string {
	if x != nil && x.ClientName != nil {
		return *x.ClientName
	}
	return ""
}

func (x *AdminLoginLog) GetOsName() string {
	if x != nil && x.OsName != nil {
		return *x.OsName
	}
	return ""
}

func (x *AdminLoginLog) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *AdminLoginLog) GetUserId() uint32 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

func (x *AdminLoginLog) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *AdminLoginLog) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// 查询后台登录日志列表 - 回应
type ListAdminLoginLogResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*AdminLoginLog       `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAdminLoginLogResponse) Reset() {
	*x = ListAdminLoginLogResponse{}
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAdminLoginLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAdminLoginLogResponse) ProtoMessage() {}

func (x *ListAdminLoginLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAdminLoginLogResponse.ProtoReflect.Descriptor instead.
func (*ListAdminLoginLogResponse) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP(), []int{1}
}

func (x *ListAdminLoginLogResponse) GetItems() []*AdminLoginLog {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListAdminLoginLogResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询后台登录日志详情 - 请求
type GetAdminLoginLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAdminLoginLogRequest) Reset() {
	*x = GetAdminLoginLogRequest{}
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdminLoginLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdminLoginLogRequest) ProtoMessage() {}

func (x *GetAdminLoginLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdminLoginLogRequest.ProtoReflect.Descriptor instead.
func (*GetAdminLoginLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP(), []int{2}
}

func (x *GetAdminLoginLogRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建后台登录日志 - 请求
type CreateAdminLoginLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *AdminLoginLog         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAdminLoginLogRequest) Reset() {
	*x = CreateAdminLoginLogRequest{}
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAdminLoginLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdminLoginLogRequest) ProtoMessage() {}

func (x *CreateAdminLoginLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdminLoginLogRequest.ProtoReflect.Descriptor instead.
func (*CreateAdminLoginLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAdminLoginLogRequest) GetData() *AdminLoginLog {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新后台登录日志 - 请求
type UpdateAdminLoginLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *AdminLoginLog         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAdminLoginLogRequest) Reset() {
	*x = UpdateAdminLoginLogRequest{}
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAdminLoginLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdminLoginLogRequest) ProtoMessage() {}

func (x *UpdateAdminLoginLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdminLoginLogRequest.ProtoReflect.Descriptor instead.
func (*UpdateAdminLoginLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAdminLoginLogRequest) GetData() *AdminLoginLog {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateAdminLoginLogRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateAdminLoginLogRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除后台登录日志 - 请求
type DeleteAdminLoginLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAdminLoginLogRequest) Reset() {
	*x = DeleteAdminLoginLogRequest{}
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAdminLoginLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAdminLoginLogRequest) ProtoMessage() {}

func (x *DeleteAdminLoginLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_admin_service_v1_i_admin_login_log_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAdminLoginLogRequest.ProtoReflect.Descriptor instead.
func (*DeleteAdminLoginLogRequest) Descriptor() ([]byte, []int) {
	return file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteAdminLoginLogRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_admin_service_v1_i_admin_login_log_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_admin_login_log_proto_rawDesc = "" +
	"\n" +
	"(admin/service/v1/i_admin_login_log.proto\x12\x10admin.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xe8\n" +
	"\n" +
	"\rAdminLoginLog\x122\n" +
	"\x02id\x18\x01 \x01(\rB\x1d\xe0A\x01\xbaG\x17\x92\x02\x14后台登录日志IDH\x00R\x02id\x88\x01\x01\x124\n" +
	"\blogin_ip\x18\x02 \x01(\tB\x14\xbaG\x11\x92\x02\x0e登录IP地址H\x01R\aloginIp\x88\x01\x01\x127\n" +
	"\tlogin_mac\x18\x03 \x01(\tB\x15\xbaG\x12\x92\x02\x0f登录MAC地址H\x02R\bloginMac\x88\x01\x01\x12R\n" +
	"\n" +
	"login_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f登录时间H\x03R\tloginTime\x88\x01\x01\x125\n" +
	"\vstatus_code\x18\x05 \x01(\x05B\x0f\xbaG\f\x92\x02\t状态码H\x04R\n" +
	"statusCode\x88\x01\x01\x127\n" +
	"\asuccess\x18\x06 \x01(\bB\x18\xbaG\x15\x92\x02\x12登录是否成功H\x05R\asuccess\x88\x01\x01\x125\n" +
	"\x06reason\x18\a \x01(\tB\x18\xbaG\x15\x92\x02\x12登录失败原因H\x06R\x06reason\x88\x01\x01\x129\n" +
	"\blocation\x18\b \x01(\tB\x18\xbaG\x15\x92\x02\x12登录地理位置H\aR\blocation\x88\x01\x01\x12H\n" +
	"\n" +
	"user_agent\x18d \x01(\tB$\xbaG!\x92\x02\x1e浏览器的用户代理信息H\bR\tuserAgent\x88\x01\x01\x12=\n" +
	"\fbrowser_name\x18e \x01(\tB\x15\xbaG\x12\x92\x02\x0f浏览器名称H\tR\vbrowserName\x88\x01\x01\x12C\n" +
	"\x0fbrowser_version\x18f \x01(\tB\x15\xbaG\x12\x92\x02\x0f浏览器版本H\n" +
	"R\x0ebrowserVersion\x88\x01\x01\x124\n" +
	"\tclient_id\x18\xc8\x01 \x01(\tB\x11\xbaG\x0e\x92\x02\v客户端IDH\vR\bclientId\x88\x01\x01\x12<\n" +
	"\vclient_name\x18\xc9\x01 \x01(\tB\x15\xbaG\x12\x92\x02\x0f客户端名称H\fR\n" +
	"clientName\x88\x01\x01\x127\n" +
	"\aos_name\x18\xca\x01 \x01(\tB\x18\xbaG\x15\x92\x02\x12操作系统名称H\rR\x06osName\x88\x01\x01\x12=\n" +
	"\n" +
	"os_version\x18\xcb\x01 \x01(\tB\x18\xbaG\x15\x92\x02\x12操作系统版本H\x0eR\tosVersion\x88\x01\x01\x126\n" +
	"\auser_id\x18\xac\x02 \x01(\rB\x17\xbaG\x14\x92\x02\x11操作者用户IDH\x0fR\x06userId\x88\x01\x01\x12:\n" +
	"\busername\x18\xad\x02 \x01(\tB\x18\xbaG\x15\x92\x02\x12操作者账号名H\x10R\busername\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\x90\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\x11R\n" +
	"createTime\x88\x01\x01B\x05\n" +
	"\x03_idB\v\n" +
	"\t_login_ipB\f\n" +
	"\n" +
	"_login_macB\r\n" +
	"\v_login_timeB\x0e\n" +
	"\f_status_codeB\n" +
	"\n" +
	"\b_successB\t\n" +
	"\a_reasonB\v\n" +
	"\t_locationB\r\n" +
	"\v_user_agentB\x0f\n" +
	"\r_browser_nameB\x12\n" +
	"\x10_browser_versionB\f\n" +
	"\n" +
	"_client_idB\x0e\n" +
	"\f_client_nameB\n" +
	"\n" +
	"\b_os_nameB\r\n" +
	"\v_os_versionB\n" +
	"\n" +
	"\b_user_idB\v\n" +
	"\t_usernameB\x0e\n" +
	"\f_create_time\"h\n" +
	"\x19ListAdminLoginLogResponse\x125\n" +
	"\x05items\x18\x01 \x03(\v2\x1f.admin.service.v1.AdminLoginLogR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\")\n" +
	"\x17GetAdminLoginLogRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"Q\n" +
	"\x1aCreateAdminLoginLogRequest\x123\n" +
	"\x04data\x18\x01 \x01(\v2\x1f.admin.service.v1.AdminLoginLogR\x04data\"\x8f\x03\n" +
	"\x1aUpdateAdminLoginLogRequest\x123\n" +
	"\x04data\x18\x01 \x01(\v2\x1f.admin.service.v1.AdminLoginLogR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\",\n" +
	"\x1aDeleteAdminLoginLogRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\x86\x02\n" +
	"\x14AdminLoginLogService\x12r\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a+.admin.service.v1.ListAdminLoginLogResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/admin/v1/admin_login_logs\x12z\n" +
	"\x03Get\x12).admin.service.v1.GetAdminLoginLogRequest\x1a\x1f.admin.service.v1.AdminLoginLog\"'\x82\xd3\xe4\x93\x02!\x12\x1f/admin/v1/admin_login_logs/{id}B\xc1\x01\n" +
	"\x14com.admin.service.v1B\x13IAdminLoginLogProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var (
	file_admin_service_v1_i_admin_login_log_proto_rawDescOnce sync.Once
	file_admin_service_v1_i_admin_login_log_proto_rawDescData []byte
)

func file_admin_service_v1_i_admin_login_log_proto_rawDescGZIP() []byte {
	file_admin_service_v1_i_admin_login_log_proto_rawDescOnce.Do(func() {
		file_admin_service_v1_i_admin_login_log_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_admin_login_log_proto_rawDesc), len(file_admin_service_v1_i_admin_login_log_proto_rawDesc)))
	})
	return file_admin_service_v1_i_admin_login_log_proto_rawDescData
}

var file_admin_service_v1_i_admin_login_log_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_admin_service_v1_i_admin_login_log_proto_goTypes = []any{
	(*AdminLoginLog)(nil),              // 0: admin.service.v1.AdminLoginLog
	(*ListAdminLoginLogResponse)(nil),  // 1: admin.service.v1.ListAdminLoginLogResponse
	(*GetAdminLoginLogRequest)(nil),    // 2: admin.service.v1.GetAdminLoginLogRequest
	(*CreateAdminLoginLogRequest)(nil), // 3: admin.service.v1.CreateAdminLoginLogRequest
	(*UpdateAdminLoginLogRequest)(nil), // 4: admin.service.v1.UpdateAdminLoginLogRequest
	(*DeleteAdminLoginLogRequest)(nil), // 5: admin.service.v1.DeleteAdminLoginLogRequest
	(*timestamppb.Timestamp)(nil),      // 6: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),      // 7: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),           // 8: pagination.PagingRequest
}
var file_admin_service_v1_i_admin_login_log_proto_depIdxs = []int32{
	6, // 0: admin.service.v1.AdminLoginLog.login_time:type_name -> google.protobuf.Timestamp
	6, // 1: admin.service.v1.AdminLoginLog.create_time:type_name -> google.protobuf.Timestamp
	0, // 2: admin.service.v1.ListAdminLoginLogResponse.items:type_name -> admin.service.v1.AdminLoginLog
	0, // 3: admin.service.v1.CreateAdminLoginLogRequest.data:type_name -> admin.service.v1.AdminLoginLog
	0, // 4: admin.service.v1.UpdateAdminLoginLogRequest.data:type_name -> admin.service.v1.AdminLoginLog
	7, // 5: admin.service.v1.UpdateAdminLoginLogRequest.update_mask:type_name -> google.protobuf.FieldMask
	8, // 6: admin.service.v1.AdminLoginLogService.List:input_type -> pagination.PagingRequest
	2, // 7: admin.service.v1.AdminLoginLogService.Get:input_type -> admin.service.v1.GetAdminLoginLogRequest
	1, // 8: admin.service.v1.AdminLoginLogService.List:output_type -> admin.service.v1.ListAdminLoginLogResponse
	0, // 9: admin.service.v1.AdminLoginLogService.Get:output_type -> admin.service.v1.AdminLoginLog
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_admin_login_log_proto_init() }
func file_admin_service_v1_i_admin_login_log_proto_init() {
	if File_admin_service_v1_i_admin_login_log_proto != nil {
		return
	}
	file_admin_service_v1_i_admin_login_log_proto_msgTypes[0].OneofWrappers = []any{}
	file_admin_service_v1_i_admin_login_log_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_admin_login_log_proto_rawDesc), len(file_admin_service_v1_i_admin_login_log_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_admin_login_log_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_admin_login_log_proto_depIdxs,
		MessageInfos:      file_admin_service_v1_i_admin_login_log_proto_msgTypes,
	}.Build()
	File_admin_service_v1_i_admin_login_log_proto = out.File
	file_admin_service_v1_i_admin_login_log_proto_goTypes = nil
	file_admin_service_v1_i_admin_login_log_proto_depIdxs = nil
}
