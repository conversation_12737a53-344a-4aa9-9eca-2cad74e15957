// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_api_resource.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationApiResourceServiceCreate = "/admin.service.v1.ApiResourceService/Create"
const OperationApiResourceServiceDelete = "/admin.service.v1.ApiResourceService/Delete"
const OperationApiResourceServiceGet = "/admin.service.v1.ApiResourceService/Get"
const OperationApiResourceServiceGetWalkRouteData = "/admin.service.v1.ApiResourceService/GetWalkRouteData"
const OperationApiResourceServiceList = "/admin.service.v1.ApiResourceService/List"
const OperationApiResourceServiceSyncApiResources = "/admin.service.v1.ApiResourceService/SyncApiResources"
const OperationApiResourceServiceUpdate = "/admin.service.v1.ApiResourceService/Update"

type ApiResourceServiceHTTPServer interface {
	// Create 创建API资源
	Create(context.Context, *CreateApiResourceRequest) (*emptypb.Empty, error)
	// Delete 删除API资源
	Delete(context.Context, *DeleteApiResourceRequest) (*emptypb.Empty, error)
	// Get 查询API资源详情
	Get(context.Context, *GetApiResourceRequest) (*ApiResource, error)
	// GetWalkRouteData 查询路由数据
	GetWalkRouteData(context.Context, *emptypb.Empty) (*ListApiResourceResponse, error)
	// List 查询API资源列表
	List(context.Context, *v1.PagingRequest) (*ListApiResourceResponse, error)
	// SyncApiResources 同步API资源
	SyncApiResources(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Update 更新API资源
	Update(context.Context, *UpdateApiResourceRequest) (*emptypb.Empty, error)
}

func RegisterApiResourceServiceHTTPServer(s *http.Server, srv ApiResourceServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/api-resources", _ApiResourceService_List3_HTTP_Handler(srv))
	r.GET("/admin/v1/api-resources/{id}", _ApiResourceService_Get3_HTTP_Handler(srv))
	r.POST("/admin/v1/api-resources", _ApiResourceService_Create1_HTTP_Handler(srv))
	r.PUT("/admin/v1/api-resources/{data.id}", _ApiResourceService_Update1_HTTP_Handler(srv))
	r.DELETE("/admin/v1/api-resources/{id}", _ApiResourceService_Delete1_HTTP_Handler(srv))
	r.POST("/admin/v1/api-resources/sync", _ApiResourceService_SyncApiResources0_HTTP_Handler(srv))
	r.GET("/admin/v1/api-resources/walk-route", _ApiResourceService_GetWalkRouteData0_HTTP_Handler(srv))
}

func _ApiResourceService_List3_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PagingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*v1.PagingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApiResourceResponse)
		return ctx.Result(200, reply)
	}
}

func _ApiResourceService_Get3_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetApiResourceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetApiResourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ApiResource)
		return ctx.Result(200, reply)
	}
}

func _ApiResourceService_Create1_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateApiResourceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*CreateApiResourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApiResourceService_Update1_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateApiResourceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*UpdateApiResourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApiResourceService_Delete1_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteApiResourceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*DeleteApiResourceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApiResourceService_SyncApiResources0_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceSyncApiResources)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncApiResources(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ApiResourceService_GetWalkRouteData0_HTTP_Handler(srv ApiResourceServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApiResourceServiceGetWalkRouteData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWalkRouteData(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListApiResourceResponse)
		return ctx.Result(200, reply)
	}
}

type ApiResourceServiceHTTPClient interface {
	Create(ctx context.Context, req *CreateApiResourceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Delete(ctx context.Context, req *DeleteApiResourceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *GetApiResourceRequest, opts ...http.CallOption) (rsp *ApiResource, err error)
	GetWalkRouteData(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *ListApiResourceResponse, err error)
	List(ctx context.Context, req *v1.PagingRequest, opts ...http.CallOption) (rsp *ListApiResourceResponse, err error)
	SyncApiResources(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Update(ctx context.Context, req *UpdateApiResourceRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type ApiResourceServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewApiResourceServiceHTTPClient(client *http.Client) ApiResourceServiceHTTPClient {
	return &ApiResourceServiceHTTPClientImpl{client}
}

func (c *ApiResourceServiceHTTPClientImpl) Create(ctx context.Context, in *CreateApiResourceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/api-resources"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApiResourceServiceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApiResourceServiceHTTPClientImpl) Delete(ctx context.Context, in *DeleteApiResourceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/api-resources/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApiResourceServiceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApiResourceServiceHTTPClientImpl) Get(ctx context.Context, in *GetApiResourceRequest, opts ...http.CallOption) (*ApiResource, error) {
	var out ApiResource
	pattern := "/admin/v1/api-resources/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApiResourceServiceGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApiResourceServiceHTTPClientImpl) GetWalkRouteData(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*ListApiResourceResponse, error) {
	var out ListApiResourceResponse
	pattern := "/admin/v1/api-resources/walk-route"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApiResourceServiceGetWalkRouteData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApiResourceServiceHTTPClientImpl) List(ctx context.Context, in *v1.PagingRequest, opts ...http.CallOption) (*ListApiResourceResponse, error) {
	var out ListApiResourceResponse
	pattern := "/admin/v1/api-resources"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationApiResourceServiceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApiResourceServiceHTTPClientImpl) SyncApiResources(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/api-resources/sync"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApiResourceServiceSyncApiResources))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApiResourceServiceHTTPClientImpl) Update(ctx context.Context, in *UpdateApiResourceRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/api-resources/{data.id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApiResourceServiceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
