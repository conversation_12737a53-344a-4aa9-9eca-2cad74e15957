// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: file/service/v1/oss.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 前端上传文件所用的HTTP方法
type UploadMethod int32

const (
	UploadMethod_Put  UploadMethod = 0
	UploadMethod_Post UploadMethod = 1
)

// Enum value maps for UploadMethod.
var (
	UploadMethod_name = map[int32]string{
		0: "Put",
		1: "Post",
	}
	UploadMethod_value = map[string]int32{
		"Put":  0,
		"Post": 1,
	}
)

func (x UploadMethod) Enum() *UploadMethod {
	p := new(UploadMethod)
	*p = x
	return p
}

func (x UploadMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_file_service_v1_oss_proto_enumTypes[0].Descriptor()
}

func (UploadMethod) Type() protoreflect.EnumType {
	return &file_file_service_v1_oss_proto_enumTypes[0]
}

func (x UploadMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadMethod.Descriptor instead.
func (UploadMethod) EnumDescriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{0}
}

// 获取对象存储上传链接 - 请求
type OssUploadUrlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Method        UploadMethod           `protobuf:"varint,1,opt,name=method,proto3,enum=file.service.v1.UploadMethod" json:"method,omitempty"` // 上传文件所用的HTTP方法
	ContentType   *string                `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3,oneof" json:"content_type,omitempty"` // 文件的MIME类型
	BucketName    *string                `protobuf:"bytes,3,opt,name=bucket_name,json=bucketName,proto3,oneof" json:"bucket_name,omitempty"`    // 文件桶名称，如果不填写，将会根据文件名或者MIME类型进行自动解析。
	FilePath      *string                `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3,oneof" json:"file_path,omitempty"`          // 远端的文件路径，可以不填写。
	FileName      *string                `protobuf:"bytes,5,opt,name=file_name,json=fileName,proto3,oneof" json:"file_name,omitempty"`          // 文件名，如果不填写，则会生成UUID，有同名文件也会改为UUID。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OssUploadUrlRequest) Reset() {
	*x = OssUploadUrlRequest{}
	mi := &file_file_service_v1_oss_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OssUploadUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OssUploadUrlRequest) ProtoMessage() {}

func (x *OssUploadUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OssUploadUrlRequest.ProtoReflect.Descriptor instead.
func (*OssUploadUrlRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{0}
}

func (x *OssUploadUrlRequest) GetMethod() UploadMethod {
	if x != nil {
		return x.Method
	}
	return UploadMethod_Put
}

func (x *OssUploadUrlRequest) GetContentType() string {
	if x != nil && x.ContentType != nil {
		return *x.ContentType
	}
	return ""
}

func (x *OssUploadUrlRequest) GetBucketName() string {
	if x != nil && x.BucketName != nil {
		return *x.BucketName
	}
	return ""
}

func (x *OssUploadUrlRequest) GetFilePath() string {
	if x != nil && x.FilePath != nil {
		return *x.FilePath
	}
	return ""
}

func (x *OssUploadUrlRequest) GetFileName() string {
	if x != nil && x.FileName != nil {
		return *x.FileName
	}
	return ""
}

// 获取对象存储上传链接 - 回应
type OssUploadUrlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UploadUrl     string                 `protobuf:"bytes,1,opt,name=upload_url,json=uploadUrl,proto3" json:"upload_url,omitempty"`          // 文件的上传链接，默认1个小时的过期时间。
	DownloadUrl   string                 `protobuf:"bytes,2,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`    // 文件的下载链接
	BucketName    *string                `protobuf:"bytes,3,opt,name=bucket_name,json=bucketName,proto3,oneof" json:"bucket_name,omitempty"` // 文件桶名称
	ObjectName    string                 `protobuf:"bytes,4,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`       // 文件名
	FormData      map[string]string      `protobuf:"bytes,5,rep,name=form_data,json=formData,proto3" json:"form_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OssUploadUrlResponse) Reset() {
	*x = OssUploadUrlResponse{}
	mi := &file_file_service_v1_oss_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OssUploadUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OssUploadUrlResponse) ProtoMessage() {}

func (x *OssUploadUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OssUploadUrlResponse.ProtoReflect.Descriptor instead.
func (*OssUploadUrlResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{1}
}

func (x *OssUploadUrlResponse) GetUploadUrl() string {
	if x != nil {
		return x.UploadUrl
	}
	return ""
}

func (x *OssUploadUrlResponse) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *OssUploadUrlResponse) GetBucketName() string {
	if x != nil && x.BucketName != nil {
		return *x.BucketName
	}
	return ""
}

func (x *OssUploadUrlResponse) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *OssUploadUrlResponse) GetFormData() map[string]string {
	if x != nil {
		return x.FormData
	}
	return nil
}

type GetDownloadUrlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDownloadUrlRequest) Reset() {
	*x = GetDownloadUrlRequest{}
	mi := &file_file_service_v1_oss_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDownloadUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadUrlRequest) ProtoMessage() {}

func (x *GetDownloadUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadUrlRequest.ProtoReflect.Descriptor instead.
func (*GetDownloadUrlRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{2}
}

type GetDownloadUrlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDownloadUrlResponse) Reset() {
	*x = GetDownloadUrlResponse{}
	mi := &file_file_service_v1_oss_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDownloadUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadUrlResponse) ProtoMessage() {}

func (x *GetDownloadUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadUrlResponse.ProtoReflect.Descriptor instead.
func (*GetDownloadUrlResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{3}
}

type ListOssFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BucketName    *string                `protobuf:"bytes,1,opt,name=bucket_name,json=bucketName,proto3,oneof" json:"bucket_name,omitempty"` // 文件桶名称
	Folder        *string                `protobuf:"bytes,2,opt,name=folder,proto3,oneof" json:"folder,omitempty"`                           // 文件夹名称
	Recursive     *bool                  `protobuf:"varint,3,opt,name=recursive,proto3,oneof" json:"recursive,omitempty"`                    // 是否递归文件夹
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOssFileRequest) Reset() {
	*x = ListOssFileRequest{}
	mi := &file_file_service_v1_oss_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOssFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOssFileRequest) ProtoMessage() {}

func (x *ListOssFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOssFileRequest.ProtoReflect.Descriptor instead.
func (*ListOssFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{4}
}

func (x *ListOssFileRequest) GetBucketName() string {
	if x != nil && x.BucketName != nil {
		return *x.BucketName
	}
	return ""
}

func (x *ListOssFileRequest) GetFolder() string {
	if x != nil && x.Folder != nil {
		return *x.Folder
	}
	return ""
}

func (x *ListOssFileRequest) GetRecursive() bool {
	if x != nil && x.Recursive != nil {
		return *x.Recursive
	}
	return false
}

type ListOssFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Files         []string               `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOssFileResponse) Reset() {
	*x = ListOssFileResponse{}
	mi := &file_file_service_v1_oss_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOssFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOssFileResponse) ProtoMessage() {}

func (x *ListOssFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOssFileResponse.ProtoReflect.Descriptor instead.
func (*ListOssFileResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{5}
}

func (x *ListOssFileResponse) GetFiles() []string {
	if x != nil {
		return x.Files
	}
	return nil
}

type DeleteOssFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BucketName    *string                `protobuf:"bytes,1,opt,name=bucket_name,json=bucketName,proto3,oneof" json:"bucket_name,omitempty"` // 文件桶名称
	ObjectName    *string                `protobuf:"bytes,2,opt,name=object_name,json=objectName,proto3,oneof" json:"object_name,omitempty"` // 文件名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteOssFileRequest) Reset() {
	*x = DeleteOssFileRequest{}
	mi := &file_file_service_v1_oss_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteOssFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOssFileRequest) ProtoMessage() {}

func (x *DeleteOssFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOssFileRequest.ProtoReflect.Descriptor instead.
func (*DeleteOssFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteOssFileRequest) GetBucketName() string {
	if x != nil && x.BucketName != nil {
		return *x.BucketName
	}
	return ""
}

func (x *DeleteOssFileRequest) GetObjectName() string {
	if x != nil && x.ObjectName != nil {
		return *x.ObjectName
	}
	return ""
}

type DeleteOssFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteOssFileResponse) Reset() {
	*x = DeleteOssFileResponse{}
	mi := &file_file_service_v1_oss_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteOssFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOssFileResponse) ProtoMessage() {}

func (x *DeleteOssFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOssFileResponse.ProtoReflect.Descriptor instead.
func (*DeleteOssFileResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{7}
}

type UploadOssFileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BucketName     *string                `protobuf:"bytes,1,opt,name=bucket_name,json=bucketName,proto3,oneof" json:"bucket_name,omitempty"`               // 文件桶名称
	ObjectName     *string                `protobuf:"bytes,2,opt,name=object_name,json=objectName,proto3,oneof" json:"object_name,omitempty"`               // 文件名
	File           []byte                 `protobuf:"bytes,3,opt,name=file,proto3,oneof" json:"file,omitempty"`                                             // 文件内容
	SourceFileName *string                `protobuf:"bytes,4,opt,name=source_file_name,json=sourceFileName,proto3,oneof" json:"source_file_name,omitempty"` // 原文件文件名
	Mime           *string                `protobuf:"bytes,5,opt,name=mime,proto3,oneof" json:"mime,omitempty"`                                             // 文件的MIME类型
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UploadOssFileRequest) Reset() {
	*x = UploadOssFileRequest{}
	mi := &file_file_service_v1_oss_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadOssFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadOssFileRequest) ProtoMessage() {}

func (x *UploadOssFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadOssFileRequest.ProtoReflect.Descriptor instead.
func (*UploadOssFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{8}
}

func (x *UploadOssFileRequest) GetBucketName() string {
	if x != nil && x.BucketName != nil {
		return *x.BucketName
	}
	return ""
}

func (x *UploadOssFileRequest) GetObjectName() string {
	if x != nil && x.ObjectName != nil {
		return *x.ObjectName
	}
	return ""
}

func (x *UploadOssFileRequest) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *UploadOssFileRequest) GetSourceFileName() string {
	if x != nil && x.SourceFileName != nil {
		return *x.SourceFileName
	}
	return ""
}

func (x *UploadOssFileRequest) GetMime() string {
	if x != nil && x.Mime != nil {
		return *x.Mime
	}
	return ""
}

type UploadOssFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadOssFileResponse) Reset() {
	*x = UploadOssFileResponse{}
	mi := &file_file_service_v1_oss_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadOssFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadOssFileResponse) ProtoMessage() {}

func (x *UploadOssFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_oss_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadOssFileResponse.ProtoReflect.Descriptor instead.
func (*UploadOssFileResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_oss_proto_rawDescGZIP(), []int{9}
}

func (x *UploadOssFileResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_file_service_v1_oss_proto protoreflect.FileDescriptor

const file_file_service_v1_oss_proto_rawDesc = "" +
	"\n" +
	"\x19file/service/v1/oss.proto\x12\x0ffile.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\"\xdb\x04\n" +
	"\x13OssUploadUrlRequest\x12o\n" +
	"\x06method\x18\x01 \x01(\x0e2\x1d.file.service.v1.UploadMethodB8\xbaG5\x92\x022上传文件所用的HTTP方法，支持POST和PUTR\x06method\x12A\n" +
	"\fcontent_type\x18\x02 \x01(\tB\x19\xbaG\x16\x92\x02\x13文件的MIME类型H\x00R\vcontentType\x88\x01\x01\x12\x87\x01\n" +
	"\vbucket_name\x18\x03 \x01(\tBa\xbaG^\x92\x02[文件桶名称，如果不填写，将会根据文件名或者MIME类型进行自动解析H\x01R\n" +
	"bucketName\x88\x01\x01\x12O\n" +
	"\tfile_path\x18\x04 \x01(\tB-\xbaG*\x92\x02'远端的文件路径，可以不填写H\x02R\bfilePath\x88\x01\x01\x12x\n" +
	"\tfile_name\x18\x05 \x01(\tBV\xbaGS\x92\x02P文件名，如果不填写，则会生成UUID，有同名文件也会改为UUIDH\x03R\bfileName\x88\x01\x01B\x0f\n" +
	"\r_content_typeB\x0e\n" +
	"\f_bucket_nameB\f\n" +
	"\n" +
	"_file_pathB\f\n" +
	"\n" +
	"_file_name\"\xf3\x03\n" +
	"\x14OssUploadUrlResponse\x12\\\n" +
	"\n" +
	"upload_url\x18\x01 \x01(\tB=\xbaG:\x92\x027文件的上传链接，默认1个小时的过期时间R\tuploadUrl\x12>\n" +
	"\fdownload_url\x18\x02 \x01(\tB\x1b\xbaG\x18\x92\x02\x15文件的下载链接R\vdownloadUrl\x12;\n" +
	"\vbucket_name\x18\x03 \x01(\tB\x15\xbaG\x12\x92\x02\x0f文件桶名称H\x00R\n" +
	"bucketName\x88\x01\x01\x120\n" +
	"\vobject_name\x18\x04 \x01(\tB\x0f\xbaG\f\x92\x02\t文件名R\n" +
	"objectName\x12\x80\x01\n" +
	"\tform_data\x18\x05 \x03(\v23.file.service.v1.OssUploadUrlResponse.FormDataEntryB.\xbaG+\x92\x02(表单数据，使用POST方法时填写R\bformData\x1a;\n" +
	"\rFormDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\x0e\n" +
	"\f_bucket_name\"\x17\n" +
	"\x15GetDownloadUrlRequest\"\x18\n" +
	"\x16GetDownloadUrlResponse\"\xee\x01\n" +
	"\x12ListOssFileRequest\x12;\n" +
	"\vbucket_name\x18\x01 \x01(\tB\x15\xbaG\x12\x92\x02\x0f文件桶名称H\x00R\n" +
	"bucketName\x88\x01\x01\x122\n" +
	"\x06folder\x18\x02 \x01(\tB\x15\xbaG\x12\x92\x02\x0f文件夹名称H\x01R\x06folder\x88\x01\x01\x12>\n" +
	"\trecursive\x18\x03 \x01(\bB\x1b\xbaG\x18\x92\x02\x15是否递归文件夹H\x02R\trecursive\x88\x01\x01B\x0e\n" +
	"\f_bucket_nameB\t\n" +
	"\a_folderB\f\n" +
	"\n" +
	"_recursive\"?\n" +
	"\x13ListOssFileResponse\x12(\n" +
	"\x05files\x18\x01 \x03(\tB\x12\xbaG\x0f\x92\x02\f文件列表R\x05files\"\xaa\x01\n" +
	"\x14DeleteOssFileRequest\x12;\n" +
	"\vbucket_name\x18\x01 \x01(\tB\x15\xbaG\x12\x92\x02\x0f文件桶名称H\x00R\n" +
	"bucketName\x88\x01\x01\x125\n" +
	"\vobject_name\x18\x02 \x01(\tB\x0f\xbaG\f\x92\x02\t文件名H\x01R\n" +
	"objectName\x88\x01\x01B\x0e\n" +
	"\f_bucket_nameB\x0e\n" +
	"\f_object_name\"\x17\n" +
	"\x15DeleteOssFileResponse\"\xfb\x02\n" +
	"\x14UploadOssFileRequest\x12;\n" +
	"\vbucket_name\x18\x01 \x01(\tB\x15\xbaG\x12\x92\x02\x0f文件桶名称H\x00R\n" +
	"bucketName\x88\x01\x01\x125\n" +
	"\vobject_name\x18\x02 \x01(\tB\x0f\xbaG\f\x92\x02\t文件名H\x01R\n" +
	"objectName\x88\x01\x01\x12+\n" +
	"\x04file\x18\x03 \x01(\fB\x12\xbaG\x0f\x92\x02\f文件内容H\x02R\x04file\x88\x01\x01\x12G\n" +
	"\x10source_file_name\x18\x04 \x01(\tB\x18\xbaG\x15\x92\x02\x12原文件文件名H\x03R\x0esourceFileName\x88\x01\x01\x122\n" +
	"\x04mime\x18\x05 \x01(\tB\x19\xbaG\x16\x92\x02\x13文件的MIME类型H\x04R\x04mime\x88\x01\x01B\x0e\n" +
	"\f_bucket_nameB\x0e\n" +
	"\f_object_nameB\a\n" +
	"\x05_fileB\x13\n" +
	"\x11_source_file_nameB\a\n" +
	"\x05_mime\")\n" +
	"\x15UploadOssFileResponse\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url*!\n" +
	"\fUploadMethod\x12\a\n" +
	"\x03Put\x10\x00\x12\b\n" +
	"\x04Post\x10\x012\xf0\x03\n" +
	"\n" +
	"OssService\x12]\n" +
	"\fOssUploadUrl\x12$.file.service.v1.OssUploadUrlRequest\x1a%.file.service.v1.OssUploadUrlResponse\"\x00\x12c\n" +
	"\x0eGetDownloadUrl\x12&.file.service.v1.GetDownloadUrlRequest\x1a'.file.service.v1.GetDownloadUrlResponse\"\x00\x12Z\n" +
	"\vListOssFile\x12#.file.service.v1.ListOssFileRequest\x1a$.file.service.v1.ListOssFileResponse\"\x00\x12`\n" +
	"\rDeleteOssFile\x12%.file.service.v1.DeleteOssFileRequest\x1a&.file.service.v1.DeleteOssFileResponse\"\x00\x12`\n" +
	"\rUploadOssFile\x12%.file.service.v1.UploadOssFileRequest\x1a&.file.service.v1.UploadOssFileResponse\"\x00B\xb0\x01\n" +
	"\x13com.file.service.v1B\bOssProtoP\x01Z1kratos-admin/api/gen/go/file/service/v1;servicev1\xa2\x02\x03FSX\xaa\x02\x0fFile.Service.V1\xca\x02\x0fFile\\Service\\V1\xe2\x02\x1bFile\\Service\\V1\\GPBMetadata\xea\x02\x11File::Service::V1b\x06proto3"

var (
	file_file_service_v1_oss_proto_rawDescOnce sync.Once
	file_file_service_v1_oss_proto_rawDescData []byte
)

func file_file_service_v1_oss_proto_rawDescGZIP() []byte {
	file_file_service_v1_oss_proto_rawDescOnce.Do(func() {
		file_file_service_v1_oss_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_file_service_v1_oss_proto_rawDesc), len(file_file_service_v1_oss_proto_rawDesc)))
	})
	return file_file_service_v1_oss_proto_rawDescData
}

var file_file_service_v1_oss_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_file_service_v1_oss_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_file_service_v1_oss_proto_goTypes = []any{
	(UploadMethod)(0),              // 0: file.service.v1.UploadMethod
	(*OssUploadUrlRequest)(nil),    // 1: file.service.v1.OssUploadUrlRequest
	(*OssUploadUrlResponse)(nil),   // 2: file.service.v1.OssUploadUrlResponse
	(*GetDownloadUrlRequest)(nil),  // 3: file.service.v1.GetDownloadUrlRequest
	(*GetDownloadUrlResponse)(nil), // 4: file.service.v1.GetDownloadUrlResponse
	(*ListOssFileRequest)(nil),     // 5: file.service.v1.ListOssFileRequest
	(*ListOssFileResponse)(nil),    // 6: file.service.v1.ListOssFileResponse
	(*DeleteOssFileRequest)(nil),   // 7: file.service.v1.DeleteOssFileRequest
	(*DeleteOssFileResponse)(nil),  // 8: file.service.v1.DeleteOssFileResponse
	(*UploadOssFileRequest)(nil),   // 9: file.service.v1.UploadOssFileRequest
	(*UploadOssFileResponse)(nil),  // 10: file.service.v1.UploadOssFileResponse
	nil,                            // 11: file.service.v1.OssUploadUrlResponse.FormDataEntry
}
var file_file_service_v1_oss_proto_depIdxs = []int32{
	0,  // 0: file.service.v1.OssUploadUrlRequest.method:type_name -> file.service.v1.UploadMethod
	11, // 1: file.service.v1.OssUploadUrlResponse.form_data:type_name -> file.service.v1.OssUploadUrlResponse.FormDataEntry
	1,  // 2: file.service.v1.OssService.OssUploadUrl:input_type -> file.service.v1.OssUploadUrlRequest
	3,  // 3: file.service.v1.OssService.GetDownloadUrl:input_type -> file.service.v1.GetDownloadUrlRequest
	5,  // 4: file.service.v1.OssService.ListOssFile:input_type -> file.service.v1.ListOssFileRequest
	7,  // 5: file.service.v1.OssService.DeleteOssFile:input_type -> file.service.v1.DeleteOssFileRequest
	9,  // 6: file.service.v1.OssService.UploadOssFile:input_type -> file.service.v1.UploadOssFileRequest
	2,  // 7: file.service.v1.OssService.OssUploadUrl:output_type -> file.service.v1.OssUploadUrlResponse
	4,  // 8: file.service.v1.OssService.GetDownloadUrl:output_type -> file.service.v1.GetDownloadUrlResponse
	6,  // 9: file.service.v1.OssService.ListOssFile:output_type -> file.service.v1.ListOssFileResponse
	8,  // 10: file.service.v1.OssService.DeleteOssFile:output_type -> file.service.v1.DeleteOssFileResponse
	10, // 11: file.service.v1.OssService.UploadOssFile:output_type -> file.service.v1.UploadOssFileResponse
	7,  // [7:12] is the sub-list for method output_type
	2,  // [2:7] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_file_service_v1_oss_proto_init() }
func file_file_service_v1_oss_proto_init() {
	if File_file_service_v1_oss_proto != nil {
		return
	}
	file_file_service_v1_oss_proto_msgTypes[0].OneofWrappers = []any{}
	file_file_service_v1_oss_proto_msgTypes[1].OneofWrappers = []any{}
	file_file_service_v1_oss_proto_msgTypes[4].OneofWrappers = []any{}
	file_file_service_v1_oss_proto_msgTypes[6].OneofWrappers = []any{}
	file_file_service_v1_oss_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_file_service_v1_oss_proto_rawDesc), len(file_file_service_v1_oss_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_file_service_v1_oss_proto_goTypes,
		DependencyIndexes: file_file_service_v1_oss_proto_depIdxs,
		EnumInfos:         file_file_service_v1_oss_proto_enumTypes,
		MessageInfos:      file_file_service_v1_oss_proto_msgTypes,
	}.Build()
	File_file_service_v1_oss_proto = out.File
	file_file_service_v1_oss_proto_goTypes = nil
	file_file_service_v1_oss_proto_depIdxs = nil
}
