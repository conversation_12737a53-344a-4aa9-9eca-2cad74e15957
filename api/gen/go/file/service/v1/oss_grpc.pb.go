// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: file/service/v1/oss.proto

package servicev1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OssService_OssUploadUrl_FullMethodName   = "/file.service.v1.OssService/OssUploadUrl"
	OssService_GetDownloadUrl_FullMethodName = "/file.service.v1.OssService/GetDownloadUrl"
	OssService_ListOssFile_FullMethodName    = "/file.service.v1.OssService/ListOssFile"
	OssService_DeleteOssFile_FullMethodName  = "/file.service.v1.OssService/DeleteOssFile"
	OssService_UploadOssFile_FullMethodName  = "/file.service.v1.OssService/UploadOssFile"
)

// OssServiceClient is the client API for OssService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// OSS服务
type OssServiceClient interface {
	// 获取对象存储（OSS）上传链接
	OssUploadUrl(ctx context.Context, in *OssUploadUrlRequest, opts ...grpc.CallOption) (*OssUploadUrlResponse, error)
	// 获取对象存储（OSS）下载链接
	GetDownloadUrl(ctx context.Context, in *GetDownloadUrlRequest, opts ...grpc.CallOption) (*GetDownloadUrlResponse, error)
	// 获取文件夹下面的文件列表
	ListOssFile(ctx context.Context, in *ListOssFileRequest, opts ...grpc.CallOption) (*ListOssFileResponse, error)
	// 删除一个文件
	DeleteOssFile(ctx context.Context, in *DeleteOssFileRequest, opts ...grpc.CallOption) (*DeleteOssFileResponse, error)
	// 上传文件
	UploadOssFile(ctx context.Context, in *UploadOssFileRequest, opts ...grpc.CallOption) (*UploadOssFileResponse, error)
}

type ossServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOssServiceClient(cc grpc.ClientConnInterface) OssServiceClient {
	return &ossServiceClient{cc}
}

func (c *ossServiceClient) OssUploadUrl(ctx context.Context, in *OssUploadUrlRequest, opts ...grpc.CallOption) (*OssUploadUrlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OssUploadUrlResponse)
	err := c.cc.Invoke(ctx, OssService_OssUploadUrl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ossServiceClient) GetDownloadUrl(ctx context.Context, in *GetDownloadUrlRequest, opts ...grpc.CallOption) (*GetDownloadUrlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDownloadUrlResponse)
	err := c.cc.Invoke(ctx, OssService_GetDownloadUrl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ossServiceClient) ListOssFile(ctx context.Context, in *ListOssFileRequest, opts ...grpc.CallOption) (*ListOssFileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListOssFileResponse)
	err := c.cc.Invoke(ctx, OssService_ListOssFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ossServiceClient) DeleteOssFile(ctx context.Context, in *DeleteOssFileRequest, opts ...grpc.CallOption) (*DeleteOssFileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteOssFileResponse)
	err := c.cc.Invoke(ctx, OssService_DeleteOssFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ossServiceClient) UploadOssFile(ctx context.Context, in *UploadOssFileRequest, opts ...grpc.CallOption) (*UploadOssFileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadOssFileResponse)
	err := c.cc.Invoke(ctx, OssService_UploadOssFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OssServiceServer is the server API for OssService service.
// All implementations must embed UnimplementedOssServiceServer
// for forward compatibility.
//
// OSS服务
type OssServiceServer interface {
	// 获取对象存储（OSS）上传链接
	OssUploadUrl(context.Context, *OssUploadUrlRequest) (*OssUploadUrlResponse, error)
	// 获取对象存储（OSS）下载链接
	GetDownloadUrl(context.Context, *GetDownloadUrlRequest) (*GetDownloadUrlResponse, error)
	// 获取文件夹下面的文件列表
	ListOssFile(context.Context, *ListOssFileRequest) (*ListOssFileResponse, error)
	// 删除一个文件
	DeleteOssFile(context.Context, *DeleteOssFileRequest) (*DeleteOssFileResponse, error)
	// 上传文件
	UploadOssFile(context.Context, *UploadOssFileRequest) (*UploadOssFileResponse, error)
	mustEmbedUnimplementedOssServiceServer()
}

// UnimplementedOssServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOssServiceServer struct{}

func (UnimplementedOssServiceServer) OssUploadUrl(context.Context, *OssUploadUrlRequest) (*OssUploadUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OssUploadUrl not implemented")
}
func (UnimplementedOssServiceServer) GetDownloadUrl(context.Context, *GetDownloadUrlRequest) (*GetDownloadUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDownloadUrl not implemented")
}
func (UnimplementedOssServiceServer) ListOssFile(context.Context, *ListOssFileRequest) (*ListOssFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOssFile not implemented")
}
func (UnimplementedOssServiceServer) DeleteOssFile(context.Context, *DeleteOssFileRequest) (*DeleteOssFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOssFile not implemented")
}
func (UnimplementedOssServiceServer) UploadOssFile(context.Context, *UploadOssFileRequest) (*UploadOssFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadOssFile not implemented")
}
func (UnimplementedOssServiceServer) mustEmbedUnimplementedOssServiceServer() {}
func (UnimplementedOssServiceServer) testEmbeddedByValue()                    {}

// UnsafeOssServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OssServiceServer will
// result in compilation errors.
type UnsafeOssServiceServer interface {
	mustEmbedUnimplementedOssServiceServer()
}

func RegisterOssServiceServer(s grpc.ServiceRegistrar, srv OssServiceServer) {
	// If the following call pancis, it indicates UnimplementedOssServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OssService_ServiceDesc, srv)
}

func _OssService_OssUploadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OssUploadUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OssServiceServer).OssUploadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OssService_OssUploadUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OssServiceServer).OssUploadUrl(ctx, req.(*OssUploadUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OssService_GetDownloadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDownloadUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OssServiceServer).GetDownloadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OssService_GetDownloadUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OssServiceServer).GetDownloadUrl(ctx, req.(*GetDownloadUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OssService_ListOssFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOssFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OssServiceServer).ListOssFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OssService_ListOssFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OssServiceServer).ListOssFile(ctx, req.(*ListOssFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OssService_DeleteOssFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteOssFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OssServiceServer).DeleteOssFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OssService_DeleteOssFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OssServiceServer).DeleteOssFile(ctx, req.(*DeleteOssFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OssService_UploadOssFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadOssFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OssServiceServer).UploadOssFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OssService_UploadOssFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OssServiceServer).UploadOssFile(ctx, req.(*UploadOssFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OssService_ServiceDesc is the grpc.ServiceDesc for OssService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OssService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "file.service.v1.OssService",
	HandlerType: (*OssServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OssUploadUrl",
			Handler:    _OssService_OssUploadUrl_Handler,
		},
		{
			MethodName: "GetDownloadUrl",
			Handler:    _OssService_GetDownloadUrl_Handler,
		},
		{
			MethodName: "ListOssFile",
			Handler:    _OssService_ListOssFile_Handler,
		},
		{
			MethodName: "DeleteOssFile",
			Handler:    _OssService_DeleteOssFile_Handler,
		},
		{
			MethodName: "UploadOssFile",
			Handler:    _OssService_UploadOssFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "file/service/v1/oss.proto",
}
