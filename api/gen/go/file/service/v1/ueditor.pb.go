// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: file/service/v1/ueditor.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	_ "google.golang.org/genproto/googleapis/api/httpbody"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 动作
type UEditorAction int32

const (
	UEditorAction_config       UEditorAction = 0  // 配置
	UEditorAction_listFile     UEditorAction = 1  // 文件
	UEditorAction_listImage    UEditorAction = 2  // 图片
	UEditorAction_uploadFile   UEditorAction = 10 // 文件
	UEditorAction_uploadImage  UEditorAction = 11 // 图片
	UEditorAction_uploadVideo  UEditorAction = 12 // 视频
	UEditorAction_uploadScrawl UEditorAction = 13 // 涂鸦图片
	UEditorAction_catchImage   UEditorAction = 14 // 抓取远程图片
)

// Enum value maps for UEditorAction.
var (
	UEditorAction_name = map[int32]string{
		0:  "config",
		1:  "listFile",
		2:  "listImage",
		10: "uploadFile",
		11: "uploadImage",
		12: "uploadVideo",
		13: "uploadScrawl",
		14: "catchImage",
	}
	UEditorAction_value = map[string]int32{
		"config":       0,
		"listFile":     1,
		"listImage":    2,
		"uploadFile":   10,
		"uploadImage":  11,
		"uploadVideo":  12,
		"uploadScrawl": 13,
		"catchImage":   14,
	}
)

func (x UEditorAction) Enum() *UEditorAction {
	p := new(UEditorAction)
	*p = x
	return p
}

func (x UEditorAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UEditorAction) Descriptor() protoreflect.EnumDescriptor {
	return file_file_service_v1_ueditor_proto_enumTypes[0].Descriptor()
}

func (UEditorAction) Type() protoreflect.EnumType {
	return &file_file_service_v1_ueditor_proto_enumTypes[0]
}

func (x UEditorAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UEditorAction.Descriptor instead.
func (UEditorAction) EnumDescriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{0}
}

type UEditorRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        string                 `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Encode        string                 `protobuf:"bytes,2,opt,name=encode,proto3" json:"encode,omitempty"`
	Start         int32                  `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`
	Size          int32                  `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UEditorRequest) Reset() {
	*x = UEditorRequest{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorRequest) ProtoMessage() {}

func (x *UEditorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorRequest.ProtoReflect.Descriptor instead.
func (*UEditorRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{0}
}

func (x *UEditorRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *UEditorRequest) GetEncode() string {
	if x != nil {
		return x.Encode
	}
	return ""
}

func (x *UEditorRequest) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *UEditorRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type UEditorResponse struct {
	state                   protoimpl.MessageState         `protogen:"open.v1"`
	ImageActionName         *string                        `protobuf:"bytes,1,opt,name=imageActionName,proto3,oneof" json:"imageActionName,omitempty"`                  // 执行上传图片的action名称
	ImageFieldName          *string                        `protobuf:"bytes,2,opt,name=imageFieldName,proto3,oneof" json:"imageFieldName,omitempty"`                    // 提交的图片表单名称
	ImageMaxSize            *int64                         `protobuf:"varint,3,opt,name=imageMaxSize,proto3,oneof" json:"imageMaxSize,omitempty"`                       // 上传大小限制，单位B
	ImageAllowFiles         []string                       `protobuf:"bytes,4,rep,name=imageAllowFiles,proto3" json:"imageAllowFiles,omitempty"`                        // 上传图片格式显示
	ImageCompressEnable     *bool                          `protobuf:"varint,5,opt,name=imageCompressEnable,proto3,oneof" json:"imageCompressEnable,omitempty"`         // 是否压缩图片,默认是true
	ImageCompressBorder     *int64                         `protobuf:"varint,6,opt,name=imageCompressBorder,proto3,oneof" json:"imageCompressBorder,omitempty"`         // 图片压缩最长边限制
	ImageInsertAlign        *string                        `protobuf:"bytes,7,opt,name=imageInsertAlign,proto3,oneof" json:"imageInsertAlign,omitempty"`                // 插入的图片浮动方式
	ImageUrlPrefix          *string                        `protobuf:"bytes,8,opt,name=imageUrlPrefix,proto3,oneof" json:"imageUrlPrefix,omitempty"`                    // 图片访问路径前缀
	ImagePathFormat         *string                        `protobuf:"bytes,9,opt,name=imagePathFormat,proto3,oneof" json:"imagePathFormat,omitempty"`                  // 上传保存路径,可以自定义保存路径和文件名格式
	ScrawlActionName        *string                        `protobuf:"bytes,10,opt,name=scrawlActionName,proto3,oneof" json:"scrawlActionName,omitempty"`               // 执行上传涂鸦的action名称
	ScrawlFieldName         *string                        `protobuf:"bytes,11,opt,name=scrawlFieldName,proto3,oneof" json:"scrawlFieldName,omitempty"`                 // 提交的图片表单名称
	ScrawlMaxSize           *int64                         `protobuf:"varint,12,opt,name=scrawlMaxSize,proto3,oneof" json:"scrawlMaxSize,omitempty"`                    // 上传大小限制，单位B
	ScrawlUrlPrefix         *string                        `protobuf:"bytes,13,opt,name=scrawlUrlPrefix,proto3,oneof" json:"scrawlUrlPrefix,omitempty"`                 // 图片访问路径前缀
	ScrawlInsertAlign       *string                        `protobuf:"bytes,14,opt,name=scrawlInsertAlign,proto3,oneof" json:"scrawlInsertAlign,omitempty"`             //
	ScrawlPathFormat        *string                        `protobuf:"bytes,15,opt,name=scrawlPathFormat,proto3,oneof" json:"scrawlPathFormat,omitempty"`               // 上传保存路径,可以自定义保存路径和文件名格式
	SnapscreenActionName    *string                        `protobuf:"bytes,20,opt,name=snapscreenActionName,proto3,oneof" json:"snapscreenActionName,omitempty"`       // 执行上传截图的action名称
	SnapscreenUrlPrefix     *string                        `protobuf:"bytes,21,opt,name=snapscreenUrlPrefix,proto3,oneof" json:"snapscreenUrlPrefix,omitempty"`         // 图片访问路径前缀
	SnapscreenInsertAlign   *string                        `protobuf:"bytes,22,opt,name=snapscreenInsertAlign,proto3,oneof" json:"snapscreenInsertAlign,omitempty"`     // 插入的图片浮动方式
	SnapscreenPathFormat    *string                        `protobuf:"bytes,23,opt,name=snapscreenPathFormat,proto3,oneof" json:"snapscreenPathFormat,omitempty"`       // 上传保存路径,可以自定义保存路径和文件名格式
	CatcherActionName       *string                        `protobuf:"bytes,30,opt,name=catcherActionName,proto3,oneof" json:"catcherActionName,omitempty"`             // 执行抓取远程图片的action名称
	CatcherFieldName        *string                        `protobuf:"bytes,31,opt,name=catcherFieldName,proto3,oneof" json:"catcherFieldName,omitempty"`               // 提交的图片列表表单名称
	CatcherLocalDomain      []string                       `protobuf:"bytes,32,rep,name=catcherLocalDomain,proto3" json:"catcherLocalDomain,omitempty"`                 //
	CatcherUrlPrefix        *string                        `protobuf:"bytes,33,opt,name=catcherUrlPrefix,proto3,oneof" json:"catcherUrlPrefix,omitempty"`               // 图片访问路径前缀
	CatcherMaxSize          *int64                         `protobuf:"varint,34,opt,name=catcherMaxSize,proto3,oneof" json:"catcherMaxSize,omitempty"`                  // 上传大小限制，单位B
	CatcherAllowFiles       []string                       `protobuf:"bytes,35,rep,name=catcherAllowFiles,proto3" json:"catcherAllowFiles,omitempty"`                   // 列出的文件类型
	CatcherPathFormat       *string                        `protobuf:"bytes,36,opt,name=catcherPathFormat,proto3,oneof" json:"catcherPathFormat,omitempty"`             // 上传保存路径,可以自定义保存路径和文件名格式
	VideoActionName         *string                        `protobuf:"bytes,40,opt,name=videoActionName,proto3,oneof" json:"videoActionName,omitempty"`                 // 执行上传视频的action名称
	VideoFieldName          *string                        `protobuf:"bytes,41,opt,name=videoFieldName,proto3,oneof" json:"videoFieldName,omitempty"`                   // 提交的视频表单名称
	VideoUrlPrefix          *string                        `protobuf:"bytes,42,opt,name=videoUrlPrefix,proto3,oneof" json:"videoUrlPrefix,omitempty"`                   // 视频访问路径前缀
	VideoMaxSize            *int64                         `protobuf:"varint,43,opt,name=videoMaxSize,proto3,oneof" json:"videoMaxSize,omitempty"`                      // 上传大小限制，单位B，默认100MB
	VideoAllowFiles         []string                       `protobuf:"bytes,44,rep,name=videoAllowFiles,proto3" json:"videoAllowFiles,omitempty"`                       // 列出的文件类型
	VideoPathFormat         *string                        `protobuf:"bytes,45,opt,name=videoPathFormat,proto3,oneof" json:"videoPathFormat,omitempty"`                 // 上传保存路径,可以自定义保存路径和文件名格式
	FileActionName          *string                        `protobuf:"bytes,50,opt,name=fileActionName,proto3,oneof" json:"fileActionName,omitempty"`                   // 执行上传视频的action名称
	FileFieldName           *string                        `protobuf:"bytes,51,opt,name=fileFieldName,proto3,oneof" json:"fileFieldName,omitempty"`                     // 提交的文件表单名称
	FileUrlPrefix           *string                        `protobuf:"bytes,52,opt,name=fileUrlPrefix,proto3,oneof" json:"fileUrlPrefix,omitempty"`                     // 文件访问路径前缀
	FileMaxSize             *int64                         `protobuf:"varint,53,opt,name=fileMaxSize,proto3,oneof" json:"fileMaxSize,omitempty"`                        // 上传大小限制，单位B，默认50MB
	FileAllowFiles          []string                       `protobuf:"bytes,54,rep,name=fileAllowFiles,proto3" json:"fileAllowFiles,omitempty"`                         // 列出的文件类型
	FilePathFormat          *string                        `protobuf:"bytes,55,opt,name=filePathFormat,proto3,oneof" json:"filePathFormat,omitempty"`                   // 上传保存路径,可以自定义保存路径和文件名格式
	ImageManagerActionName  *string                        `protobuf:"bytes,60,opt,name=imageManagerActionName,proto3,oneof" json:"imageManagerActionName,omitempty"`   // 图片列表配置
	ImageManagerListSize    *int64                         `protobuf:"varint,61,opt,name=imageManagerListSize,proto3,oneof" json:"imageManagerListSize,omitempty"`      // 每次列出文件数量
	ImageManagerUrlPrefix   *string                        `protobuf:"bytes,62,opt,name=imageManagerUrlPrefix,proto3,oneof" json:"imageManagerUrlPrefix,omitempty"`     // 图片访问路径前缀
	ImageManagerInsertAlign *string                        `protobuf:"bytes,63,opt,name=imageManagerInsertAlign,proto3,oneof" json:"imageManagerInsertAlign,omitempty"` // 插入的图片浮动方式，默认值：none
	ImageManagerAllowFiles  []string                       `protobuf:"bytes,64,rep,name=imageManagerAllowFiles,proto3" json:"imageManagerAllowFiles,omitempty"`         // 列出的文件类型
	ImageManagerListPath    *string                        `protobuf:"bytes,65,opt,name=imageManagerListPath,proto3,oneof" json:"imageManagerListPath,omitempty"`       // 指定要列出图片的目录
	FileManagerActionName   *string                        `protobuf:"bytes,70,opt,name=fileManagerActionName,proto3,oneof" json:"fileManagerActionName,omitempty"`     // 文件列表配置
	FileManagerUrlPrefix    *string                        `protobuf:"bytes,71,opt,name=fileManagerUrlPrefix,proto3,oneof" json:"fileManagerUrlPrefix,omitempty"`       // 指定要列出文件的目录
	FileManagerListSize     *int64                         `protobuf:"varint,72,opt,name=fileManagerListSize,proto3,oneof" json:"fileManagerListSize,omitempty"`        // 每次列出文件数量
	FileManagerAllowFiles   []string                       `protobuf:"bytes,73,rep,name=fileManagerAllowFiles,proto3" json:"fileManagerAllowFiles,omitempty"`           // 列出的文件类型
	FileManagerListPath     *string                        `protobuf:"bytes,74,opt,name=FileManagerListPath,proto3,oneof" json:"FileManagerListPath,omitempty"`         // 列出的文件类型
	FormulaConfig           *UEditorResponse_FormulaConfig `protobuf:"bytes,80,opt,name=formulaConfig,proto3,oneof" json:"formulaConfig,omitempty"`                     // 公式配置
	State                   *string                        `protobuf:"bytes,100,opt,name=state,proto3,oneof" json:"state,omitempty"`                                    // 上传状态，上传成功时必须返回"SUCCESS"
	Start                   *int32                         `protobuf:"varint,101,opt,name=start,proto3,oneof" json:"start,omitempty"`
	Total                   *int32                         `protobuf:"varint,102,opt,name=total,proto3,oneof" json:"total,omitempty"`
	List                    []*UEditorResponse_Item        `protobuf:"bytes,103,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *UEditorResponse) Reset() {
	*x = UEditorResponse{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorResponse) ProtoMessage() {}

func (x *UEditorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorResponse.ProtoReflect.Descriptor instead.
func (*UEditorResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{1}
}

func (x *UEditorResponse) GetImageActionName() string {
	if x != nil && x.ImageActionName != nil {
		return *x.ImageActionName
	}
	return ""
}

func (x *UEditorResponse) GetImageFieldName() string {
	if x != nil && x.ImageFieldName != nil {
		return *x.ImageFieldName
	}
	return ""
}

func (x *UEditorResponse) GetImageMaxSize() int64 {
	if x != nil && x.ImageMaxSize != nil {
		return *x.ImageMaxSize
	}
	return 0
}

func (x *UEditorResponse) GetImageAllowFiles() []string {
	if x != nil {
		return x.ImageAllowFiles
	}
	return nil
}

func (x *UEditorResponse) GetImageCompressEnable() bool {
	if x != nil && x.ImageCompressEnable != nil {
		return *x.ImageCompressEnable
	}
	return false
}

func (x *UEditorResponse) GetImageCompressBorder() int64 {
	if x != nil && x.ImageCompressBorder != nil {
		return *x.ImageCompressBorder
	}
	return 0
}

func (x *UEditorResponse) GetImageInsertAlign() string {
	if x != nil && x.ImageInsertAlign != nil {
		return *x.ImageInsertAlign
	}
	return ""
}

func (x *UEditorResponse) GetImageUrlPrefix() string {
	if x != nil && x.ImageUrlPrefix != nil {
		return *x.ImageUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetImagePathFormat() string {
	if x != nil && x.ImagePathFormat != nil {
		return *x.ImagePathFormat
	}
	return ""
}

func (x *UEditorResponse) GetScrawlActionName() string {
	if x != nil && x.ScrawlActionName != nil {
		return *x.ScrawlActionName
	}
	return ""
}

func (x *UEditorResponse) GetScrawlFieldName() string {
	if x != nil && x.ScrawlFieldName != nil {
		return *x.ScrawlFieldName
	}
	return ""
}

func (x *UEditorResponse) GetScrawlMaxSize() int64 {
	if x != nil && x.ScrawlMaxSize != nil {
		return *x.ScrawlMaxSize
	}
	return 0
}

func (x *UEditorResponse) GetScrawlUrlPrefix() string {
	if x != nil && x.ScrawlUrlPrefix != nil {
		return *x.ScrawlUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetScrawlInsertAlign() string {
	if x != nil && x.ScrawlInsertAlign != nil {
		return *x.ScrawlInsertAlign
	}
	return ""
}

func (x *UEditorResponse) GetScrawlPathFormat() string {
	if x != nil && x.ScrawlPathFormat != nil {
		return *x.ScrawlPathFormat
	}
	return ""
}

func (x *UEditorResponse) GetSnapscreenActionName() string {
	if x != nil && x.SnapscreenActionName != nil {
		return *x.SnapscreenActionName
	}
	return ""
}

func (x *UEditorResponse) GetSnapscreenUrlPrefix() string {
	if x != nil && x.SnapscreenUrlPrefix != nil {
		return *x.SnapscreenUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetSnapscreenInsertAlign() string {
	if x != nil && x.SnapscreenInsertAlign != nil {
		return *x.SnapscreenInsertAlign
	}
	return ""
}

func (x *UEditorResponse) GetSnapscreenPathFormat() string {
	if x != nil && x.SnapscreenPathFormat != nil {
		return *x.SnapscreenPathFormat
	}
	return ""
}

func (x *UEditorResponse) GetCatcherActionName() string {
	if x != nil && x.CatcherActionName != nil {
		return *x.CatcherActionName
	}
	return ""
}

func (x *UEditorResponse) GetCatcherFieldName() string {
	if x != nil && x.CatcherFieldName != nil {
		return *x.CatcherFieldName
	}
	return ""
}

func (x *UEditorResponse) GetCatcherLocalDomain() []string {
	if x != nil {
		return x.CatcherLocalDomain
	}
	return nil
}

func (x *UEditorResponse) GetCatcherUrlPrefix() string {
	if x != nil && x.CatcherUrlPrefix != nil {
		return *x.CatcherUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetCatcherMaxSize() int64 {
	if x != nil && x.CatcherMaxSize != nil {
		return *x.CatcherMaxSize
	}
	return 0
}

func (x *UEditorResponse) GetCatcherAllowFiles() []string {
	if x != nil {
		return x.CatcherAllowFiles
	}
	return nil
}

func (x *UEditorResponse) GetCatcherPathFormat() string {
	if x != nil && x.CatcherPathFormat != nil {
		return *x.CatcherPathFormat
	}
	return ""
}

func (x *UEditorResponse) GetVideoActionName() string {
	if x != nil && x.VideoActionName != nil {
		return *x.VideoActionName
	}
	return ""
}

func (x *UEditorResponse) GetVideoFieldName() string {
	if x != nil && x.VideoFieldName != nil {
		return *x.VideoFieldName
	}
	return ""
}

func (x *UEditorResponse) GetVideoUrlPrefix() string {
	if x != nil && x.VideoUrlPrefix != nil {
		return *x.VideoUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetVideoMaxSize() int64 {
	if x != nil && x.VideoMaxSize != nil {
		return *x.VideoMaxSize
	}
	return 0
}

func (x *UEditorResponse) GetVideoAllowFiles() []string {
	if x != nil {
		return x.VideoAllowFiles
	}
	return nil
}

func (x *UEditorResponse) GetVideoPathFormat() string {
	if x != nil && x.VideoPathFormat != nil {
		return *x.VideoPathFormat
	}
	return ""
}

func (x *UEditorResponse) GetFileActionName() string {
	if x != nil && x.FileActionName != nil {
		return *x.FileActionName
	}
	return ""
}

func (x *UEditorResponse) GetFileFieldName() string {
	if x != nil && x.FileFieldName != nil {
		return *x.FileFieldName
	}
	return ""
}

func (x *UEditorResponse) GetFileUrlPrefix() string {
	if x != nil && x.FileUrlPrefix != nil {
		return *x.FileUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetFileMaxSize() int64 {
	if x != nil && x.FileMaxSize != nil {
		return *x.FileMaxSize
	}
	return 0
}

func (x *UEditorResponse) GetFileAllowFiles() []string {
	if x != nil {
		return x.FileAllowFiles
	}
	return nil
}

func (x *UEditorResponse) GetFilePathFormat() string {
	if x != nil && x.FilePathFormat != nil {
		return *x.FilePathFormat
	}
	return ""
}

func (x *UEditorResponse) GetImageManagerActionName() string {
	if x != nil && x.ImageManagerActionName != nil {
		return *x.ImageManagerActionName
	}
	return ""
}

func (x *UEditorResponse) GetImageManagerListSize() int64 {
	if x != nil && x.ImageManagerListSize != nil {
		return *x.ImageManagerListSize
	}
	return 0
}

func (x *UEditorResponse) GetImageManagerUrlPrefix() string {
	if x != nil && x.ImageManagerUrlPrefix != nil {
		return *x.ImageManagerUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetImageManagerInsertAlign() string {
	if x != nil && x.ImageManagerInsertAlign != nil {
		return *x.ImageManagerInsertAlign
	}
	return ""
}

func (x *UEditorResponse) GetImageManagerAllowFiles() []string {
	if x != nil {
		return x.ImageManagerAllowFiles
	}
	return nil
}

func (x *UEditorResponse) GetImageManagerListPath() string {
	if x != nil && x.ImageManagerListPath != nil {
		return *x.ImageManagerListPath
	}
	return ""
}

func (x *UEditorResponse) GetFileManagerActionName() string {
	if x != nil && x.FileManagerActionName != nil {
		return *x.FileManagerActionName
	}
	return ""
}

func (x *UEditorResponse) GetFileManagerUrlPrefix() string {
	if x != nil && x.FileManagerUrlPrefix != nil {
		return *x.FileManagerUrlPrefix
	}
	return ""
}

func (x *UEditorResponse) GetFileManagerListSize() int64 {
	if x != nil && x.FileManagerListSize != nil {
		return *x.FileManagerListSize
	}
	return 0
}

func (x *UEditorResponse) GetFileManagerAllowFiles() []string {
	if x != nil {
		return x.FileManagerAllowFiles
	}
	return nil
}

func (x *UEditorResponse) GetFileManagerListPath() string {
	if x != nil && x.FileManagerListPath != nil {
		return *x.FileManagerListPath
	}
	return ""
}

func (x *UEditorResponse) GetFormulaConfig() *UEditorResponse_FormulaConfig {
	if x != nil {
		return x.FormulaConfig
	}
	return nil
}

func (x *UEditorResponse) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *UEditorResponse) GetStart() int32 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *UEditorResponse) GetTotal() int32 {
	if x != nil && x.Total != nil {
		return *x.Total
	}
	return 0
}

func (x *UEditorResponse) GetList() []*UEditorResponse_Item {
	if x != nil {
		return x.List
	}
	return nil
}

type UEditorUploadRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Action         *string                `protobuf:"bytes,1,opt,name=action,proto3,oneof" json:"action,omitempty"`                                         // 动作
	File           []byte                 `protobuf:"bytes,2,opt,name=file,proto3,oneof" json:"file,omitempty"`                                             // 文件内容
	SourceFileName *string                `protobuf:"bytes,3,opt,name=source_file_name,json=sourceFileName,proto3,oneof" json:"source_file_name,omitempty"` // 原文件文件名
	Mime           *string                `protobuf:"bytes,4,opt,name=mime,proto3,oneof" json:"mime,omitempty"`                                             // 文件的MIME类型
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UEditorUploadRequest) Reset() {
	*x = UEditorUploadRequest{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorUploadRequest) ProtoMessage() {}

func (x *UEditorUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorUploadRequest.ProtoReflect.Descriptor instead.
func (*UEditorUploadRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{2}
}

func (x *UEditorUploadRequest) GetAction() string {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return ""
}

func (x *UEditorUploadRequest) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *UEditorUploadRequest) GetSourceFileName() string {
	if x != nil && x.SourceFileName != nil {
		return *x.SourceFileName
	}
	return ""
}

func (x *UEditorUploadRequest) GetMime() string {
	if x != nil && x.Mime != nil {
		return *x.Mime
	}
	return ""
}

type UEditorUploadResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	State         *string                       `protobuf:"bytes,1,opt,name=state,proto3,oneof" json:"state,omitempty"`       // 上传状态，上传成功时必须返回"SUCCESS"
	Url           *string                       `protobuf:"bytes,2,opt,name=url,proto3,oneof" json:"url,omitempty"`           // 返回的地址
	Title         *string                       `protobuf:"bytes,3,opt,name=title,proto3,oneof" json:"title,omitempty"`       // 新文件名
	Original      *string                       `protobuf:"bytes,4,opt,name=original,proto3,oneof" json:"original,omitempty"` // 原始文件名
	Type          *string                       `protobuf:"bytes,5,opt,name=type,proto3,oneof" json:"type,omitempty"`         // 文件类型
	Size          *int32                        `protobuf:"varint,6,opt,name=size,proto3,oneof" json:"size,omitempty"`        // 文件大小
	List          []*UEditorUploadResponse_Item `protobuf:"bytes,10,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UEditorUploadResponse) Reset() {
	*x = UEditorUploadResponse{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorUploadResponse) ProtoMessage() {}

func (x *UEditorUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorUploadResponse.ProtoReflect.Descriptor instead.
func (*UEditorUploadResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{3}
}

func (x *UEditorUploadResponse) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *UEditorUploadResponse) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *UEditorUploadResponse) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *UEditorUploadResponse) GetOriginal() string {
	if x != nil && x.Original != nil {
		return *x.Original
	}
	return ""
}

func (x *UEditorUploadResponse) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *UEditorUploadResponse) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *UEditorUploadResponse) GetList() []*UEditorUploadResponse_Item {
	if x != nil {
		return x.List
	}
	return nil
}

type UEditorListResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	State         string                      `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Start         int32                       `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	Total         int32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List          []*UEditorListResponse_Item `protobuf:"bytes,10,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UEditorListResponse) Reset() {
	*x = UEditorListResponse{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorListResponse) ProtoMessage() {}

func (x *UEditorListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorListResponse.ProtoReflect.Descriptor instead.
func (*UEditorListResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{4}
}

func (x *UEditorListResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *UEditorListResponse) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *UEditorListResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *UEditorListResponse) GetList() []*UEditorListResponse_Item {
	if x != nil {
		return x.List
	}
	return nil
}

type UEditorResponse_FormulaConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ImageUrlTemplate string                 `protobuf:"bytes,1,opt,name=imageUrlTemplate,proto3" json:"imageUrlTemplate,omitempty"` // 公式渲染的路径
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UEditorResponse_FormulaConfig) Reset() {
	*x = UEditorResponse_FormulaConfig{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorResponse_FormulaConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorResponse_FormulaConfig) ProtoMessage() {}

func (x *UEditorResponse_FormulaConfig) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorResponse_FormulaConfig.ProtoReflect.Descriptor instead.
func (*UEditorResponse_FormulaConfig) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{1, 0}
}

func (x *UEditorResponse_FormulaConfig) GetImageUrlTemplate() string {
	if x != nil {
		return x.ImageUrlTemplate
	}
	return ""
}

type UEditorResponse_Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Mtime         int64                  `protobuf:"varint,2,opt,name=mtime,proto3" json:"mtime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UEditorResponse_Item) Reset() {
	*x = UEditorResponse_Item{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorResponse_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorResponse_Item) ProtoMessage() {}

func (x *UEditorResponse_Item) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorResponse_Item.ProtoReflect.Descriptor instead.
func (*UEditorResponse_Item) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{1, 1}
}

func (x *UEditorResponse_Item) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UEditorResponse_Item) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

type UEditorUploadResponse_Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	State         string                 `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`             // 上传状态，上传成功时必须返回"SUCCESS"
	Url           *string                `protobuf:"bytes,2,opt,name=url,proto3,oneof" json:"url,omitempty"`           // 返回的地址
	Title         *string                `protobuf:"bytes,3,opt,name=title,proto3,oneof" json:"title,omitempty"`       // 新文件名
	Original      *string                `protobuf:"bytes,4,opt,name=original,proto3,oneof" json:"original,omitempty"` // 原始文件名
	Type          *string                `protobuf:"bytes,5,opt,name=type,proto3,oneof" json:"type,omitempty"`         // 文件类型
	Size          *int32                 `protobuf:"varint,6,opt,name=size,proto3,oneof" json:"size,omitempty"`        // 文件大小
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UEditorUploadResponse_Item) Reset() {
	*x = UEditorUploadResponse_Item{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorUploadResponse_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorUploadResponse_Item) ProtoMessage() {}

func (x *UEditorUploadResponse_Item) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorUploadResponse_Item.ProtoReflect.Descriptor instead.
func (*UEditorUploadResponse_Item) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{3, 0}
}

func (x *UEditorUploadResponse_Item) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *UEditorUploadResponse_Item) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *UEditorUploadResponse_Item) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *UEditorUploadResponse_Item) GetOriginal() string {
	if x != nil && x.Original != nil {
		return *x.Original
	}
	return ""
}

func (x *UEditorUploadResponse_Item) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *UEditorUploadResponse_Item) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

type UEditorListResponse_Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Mtime         int64                  `protobuf:"varint,2,opt,name=mtime,proto3" json:"mtime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UEditorListResponse_Item) Reset() {
	*x = UEditorListResponse_Item{}
	mi := &file_file_service_v1_ueditor_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UEditorListResponse_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UEditorListResponse_Item) ProtoMessage() {}

func (x *UEditorListResponse_Item) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_ueditor_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UEditorListResponse_Item.ProtoReflect.Descriptor instead.
func (*UEditorListResponse_Item) Descriptor() ([]byte, []int) {
	return file_file_service_v1_ueditor_proto_rawDescGZIP(), []int{4, 0}
}

func (x *UEditorListResponse_Item) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UEditorListResponse_Item) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

var File_file_service_v1_ueditor_proto protoreflect.FileDescriptor

const file_file_service_v1_ueditor_proto_rawDesc = "" +
	"\n" +
	"\x1dfile/service/v1/ueditor.proto\x12\x0ffile.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x19google/api/httpbody.proto\"j\n" +
	"\x0eUEditorRequest\x12\x16\n" +
	"\x06action\x18\x01 \x01(\tR\x06action\x12\x16\n" +
	"\x06encode\x18\x02 \x01(\tR\x06encode\x12\x14\n" +
	"\x05start\x18\x03 \x01(\x05R\x05start\x12\x12\n" +
	"\x04size\x18\x04 \x01(\x05R\x04size\"\xa1\x1d\n" +
	"\x0fUEditorResponse\x12-\n" +
	"\x0fimageActionName\x18\x01 \x01(\tH\x00R\x0fimageActionName\x88\x01\x01\x12+\n" +
	"\x0eimageFieldName\x18\x02 \x01(\tH\x01R\x0eimageFieldName\x88\x01\x01\x12'\n" +
	"\fimageMaxSize\x18\x03 \x01(\x03H\x02R\fimageMaxSize\x88\x01\x01\x12(\n" +
	"\x0fimageAllowFiles\x18\x04 \x03(\tR\x0fimageAllowFiles\x125\n" +
	"\x13imageCompressEnable\x18\x05 \x01(\bH\x03R\x13imageCompressEnable\x88\x01\x01\x125\n" +
	"\x13imageCompressBorder\x18\x06 \x01(\x03H\x04R\x13imageCompressBorder\x88\x01\x01\x12/\n" +
	"\x10imageInsertAlign\x18\a \x01(\tH\x05R\x10imageInsertAlign\x88\x01\x01\x12+\n" +
	"\x0eimageUrlPrefix\x18\b \x01(\tH\x06R\x0eimageUrlPrefix\x88\x01\x01\x12-\n" +
	"\x0fimagePathFormat\x18\t \x01(\tH\aR\x0fimagePathFormat\x88\x01\x01\x12/\n" +
	"\x10scrawlActionName\x18\n" +
	" \x01(\tH\bR\x10scrawlActionName\x88\x01\x01\x12-\n" +
	"\x0fscrawlFieldName\x18\v \x01(\tH\tR\x0fscrawlFieldName\x88\x01\x01\x12)\n" +
	"\rscrawlMaxSize\x18\f \x01(\x03H\n" +
	"R\rscrawlMaxSize\x88\x01\x01\x12-\n" +
	"\x0fscrawlUrlPrefix\x18\r \x01(\tH\vR\x0fscrawlUrlPrefix\x88\x01\x01\x121\n" +
	"\x11scrawlInsertAlign\x18\x0e \x01(\tH\fR\x11scrawlInsertAlign\x88\x01\x01\x12/\n" +
	"\x10scrawlPathFormat\x18\x0f \x01(\tH\rR\x10scrawlPathFormat\x88\x01\x01\x127\n" +
	"\x14snapscreenActionName\x18\x14 \x01(\tH\x0eR\x14snapscreenActionName\x88\x01\x01\x125\n" +
	"\x13snapscreenUrlPrefix\x18\x15 \x01(\tH\x0fR\x13snapscreenUrlPrefix\x88\x01\x01\x129\n" +
	"\x15snapscreenInsertAlign\x18\x16 \x01(\tH\x10R\x15snapscreenInsertAlign\x88\x01\x01\x127\n" +
	"\x14snapscreenPathFormat\x18\x17 \x01(\tH\x11R\x14snapscreenPathFormat\x88\x01\x01\x121\n" +
	"\x11catcherActionName\x18\x1e \x01(\tH\x12R\x11catcherActionName\x88\x01\x01\x12/\n" +
	"\x10catcherFieldName\x18\x1f \x01(\tH\x13R\x10catcherFieldName\x88\x01\x01\x12.\n" +
	"\x12catcherLocalDomain\x18  \x03(\tR\x12catcherLocalDomain\x12/\n" +
	"\x10catcherUrlPrefix\x18! \x01(\tH\x14R\x10catcherUrlPrefix\x88\x01\x01\x12+\n" +
	"\x0ecatcherMaxSize\x18\" \x01(\x03H\x15R\x0ecatcherMaxSize\x88\x01\x01\x12,\n" +
	"\x11catcherAllowFiles\x18# \x03(\tR\x11catcherAllowFiles\x121\n" +
	"\x11catcherPathFormat\x18$ \x01(\tH\x16R\x11catcherPathFormat\x88\x01\x01\x12-\n" +
	"\x0fvideoActionName\x18( \x01(\tH\x17R\x0fvideoActionName\x88\x01\x01\x12+\n" +
	"\x0evideoFieldName\x18) \x01(\tH\x18R\x0evideoFieldName\x88\x01\x01\x12+\n" +
	"\x0evideoUrlPrefix\x18* \x01(\tH\x19R\x0evideoUrlPrefix\x88\x01\x01\x12'\n" +
	"\fvideoMaxSize\x18+ \x01(\x03H\x1aR\fvideoMaxSize\x88\x01\x01\x12(\n" +
	"\x0fvideoAllowFiles\x18, \x03(\tR\x0fvideoAllowFiles\x12-\n" +
	"\x0fvideoPathFormat\x18- \x01(\tH\x1bR\x0fvideoPathFormat\x88\x01\x01\x12+\n" +
	"\x0efileActionName\x182 \x01(\tH\x1cR\x0efileActionName\x88\x01\x01\x12)\n" +
	"\rfileFieldName\x183 \x01(\tH\x1dR\rfileFieldName\x88\x01\x01\x12)\n" +
	"\rfileUrlPrefix\x184 \x01(\tH\x1eR\rfileUrlPrefix\x88\x01\x01\x12%\n" +
	"\vfileMaxSize\x185 \x01(\x03H\x1fR\vfileMaxSize\x88\x01\x01\x12&\n" +
	"\x0efileAllowFiles\x186 \x03(\tR\x0efileAllowFiles\x12+\n" +
	"\x0efilePathFormat\x187 \x01(\tH R\x0efilePathFormat\x88\x01\x01\x12;\n" +
	"\x16imageManagerActionName\x18< \x01(\tH!R\x16imageManagerActionName\x88\x01\x01\x127\n" +
	"\x14imageManagerListSize\x18= \x01(\x03H\"R\x14imageManagerListSize\x88\x01\x01\x129\n" +
	"\x15imageManagerUrlPrefix\x18> \x01(\tH#R\x15imageManagerUrlPrefix\x88\x01\x01\x12=\n" +
	"\x17imageManagerInsertAlign\x18? \x01(\tH$R\x17imageManagerInsertAlign\x88\x01\x01\x126\n" +
	"\x16imageManagerAllowFiles\x18@ \x03(\tR\x16imageManagerAllowFiles\x127\n" +
	"\x14imageManagerListPath\x18A \x01(\tH%R\x14imageManagerListPath\x88\x01\x01\x129\n" +
	"\x15fileManagerActionName\x18F \x01(\tH&R\x15fileManagerActionName\x88\x01\x01\x127\n" +
	"\x14fileManagerUrlPrefix\x18G \x01(\tH'R\x14fileManagerUrlPrefix\x88\x01\x01\x125\n" +
	"\x13fileManagerListSize\x18H \x01(\x03H(R\x13fileManagerListSize\x88\x01\x01\x124\n" +
	"\x15fileManagerAllowFiles\x18I \x03(\tR\x15fileManagerAllowFiles\x125\n" +
	"\x13FileManagerListPath\x18J \x01(\tH)R\x13FileManagerListPath\x88\x01\x01\x12Y\n" +
	"\rformulaConfig\x18P \x01(\v2..file.service.v1.UEditorResponse.FormulaConfigH*R\rformulaConfig\x88\x01\x01\x12\x19\n" +
	"\x05state\x18d \x01(\tH+R\x05state\x88\x01\x01\x12\x19\n" +
	"\x05start\x18e \x01(\x05H,R\x05start\x88\x01\x01\x12\x19\n" +
	"\x05total\x18f \x01(\x05H-R\x05total\x88\x01\x01\x129\n" +
	"\x04list\x18g \x03(\v2%.file.service.v1.UEditorResponse.ItemR\x04list\x1a;\n" +
	"\rFormulaConfig\x12*\n" +
	"\x10imageUrlTemplate\x18\x01 \x01(\tR\x10imageUrlTemplate\x1a.\n" +
	"\x04Item\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x14\n" +
	"\x05mtime\x18\x02 \x01(\x03R\x05mtimeB\x12\n" +
	"\x10_imageActionNameB\x11\n" +
	"\x0f_imageFieldNameB\x0f\n" +
	"\r_imageMaxSizeB\x16\n" +
	"\x14_imageCompressEnableB\x16\n" +
	"\x14_imageCompressBorderB\x13\n" +
	"\x11_imageInsertAlignB\x11\n" +
	"\x0f_imageUrlPrefixB\x12\n" +
	"\x10_imagePathFormatB\x13\n" +
	"\x11_scrawlActionNameB\x12\n" +
	"\x10_scrawlFieldNameB\x10\n" +
	"\x0e_scrawlMaxSizeB\x12\n" +
	"\x10_scrawlUrlPrefixB\x14\n" +
	"\x12_scrawlInsertAlignB\x13\n" +
	"\x11_scrawlPathFormatB\x17\n" +
	"\x15_snapscreenActionNameB\x16\n" +
	"\x14_snapscreenUrlPrefixB\x18\n" +
	"\x16_snapscreenInsertAlignB\x17\n" +
	"\x15_snapscreenPathFormatB\x14\n" +
	"\x12_catcherActionNameB\x13\n" +
	"\x11_catcherFieldNameB\x13\n" +
	"\x11_catcherUrlPrefixB\x11\n" +
	"\x0f_catcherMaxSizeB\x14\n" +
	"\x12_catcherPathFormatB\x12\n" +
	"\x10_videoActionNameB\x11\n" +
	"\x0f_videoFieldNameB\x11\n" +
	"\x0f_videoUrlPrefixB\x0f\n" +
	"\r_videoMaxSizeB\x12\n" +
	"\x10_videoPathFormatB\x11\n" +
	"\x0f_fileActionNameB\x10\n" +
	"\x0e_fileFieldNameB\x10\n" +
	"\x0e_fileUrlPrefixB\x0e\n" +
	"\f_fileMaxSizeB\x11\n" +
	"\x0f_filePathFormatB\x19\n" +
	"\x17_imageManagerActionNameB\x17\n" +
	"\x15_imageManagerListSizeB\x18\n" +
	"\x16_imageManagerUrlPrefixB\x1a\n" +
	"\x18_imageManagerInsertAlignB\x17\n" +
	"\x15_imageManagerListPathB\x18\n" +
	"\x16_fileManagerActionNameB\x17\n" +
	"\x15_fileManagerUrlPrefixB\x16\n" +
	"\x14_fileManagerListSizeB\x16\n" +
	"\x14_FileManagerListPathB\x10\n" +
	"\x0e_formulaConfigB\b\n" +
	"\x06_stateB\b\n" +
	"\x06_startB\b\n" +
	"\x06_total\"\x9d\x02\n" +
	"\x14UEditorUploadRequest\x12)\n" +
	"\x06action\x18\x01 \x01(\tB\f\xbaG\t\x92\x02\x06动作H\x00R\x06action\x88\x01\x01\x12+\n" +
	"\x04file\x18\x02 \x01(\fB\x12\xbaG\x0f\x92\x02\f文件内容H\x01R\x04file\x88\x01\x01\x12G\n" +
	"\x10source_file_name\x18\x03 \x01(\tB\x18\xbaG\x15\x92\x02\x12原文件文件名H\x02R\x0esourceFileName\x88\x01\x01\x122\n" +
	"\x04mime\x18\x04 \x01(\tB\x19\xbaG\x16\x92\x02\x13文件的MIME类型H\x03R\x04mime\x88\x01\x01B\t\n" +
	"\a_actionB\a\n" +
	"\x05_fileB\x13\n" +
	"\x11_source_file_nameB\a\n" +
	"\x05_mime\"\x88\x04\n" +
	"\x15UEditorUploadResponse\x12\x19\n" +
	"\x05state\x18\x01 \x01(\tH\x00R\x05state\x88\x01\x01\x12\x15\n" +
	"\x03url\x18\x02 \x01(\tH\x01R\x03url\x88\x01\x01\x12\x19\n" +
	"\x05title\x18\x03 \x01(\tH\x02R\x05title\x88\x01\x01\x12\x1f\n" +
	"\boriginal\x18\x04 \x01(\tH\x03R\boriginal\x88\x01\x01\x12\x17\n" +
	"\x04type\x18\x05 \x01(\tH\x04R\x04type\x88\x01\x01\x12\x17\n" +
	"\x04size\x18\x06 \x01(\x05H\x05R\x04size\x88\x01\x01\x12?\n" +
	"\x04list\x18\n" +
	" \x03(\v2+.file.service.v1.UEditorUploadResponse.ItemR\x04list\x1a\xd2\x01\n" +
	"\x04Item\x12\x14\n" +
	"\x05state\x18\x01 \x01(\tR\x05state\x12\x15\n" +
	"\x03url\x18\x02 \x01(\tH\x00R\x03url\x88\x01\x01\x12\x19\n" +
	"\x05title\x18\x03 \x01(\tH\x01R\x05title\x88\x01\x01\x12\x1f\n" +
	"\boriginal\x18\x04 \x01(\tH\x02R\boriginal\x88\x01\x01\x12\x17\n" +
	"\x04type\x18\x05 \x01(\tH\x03R\x04type\x88\x01\x01\x12\x17\n" +
	"\x04size\x18\x06 \x01(\x05H\x04R\x04size\x88\x01\x01B\x06\n" +
	"\x04_urlB\b\n" +
	"\x06_titleB\v\n" +
	"\t_originalB\a\n" +
	"\x05_typeB\a\n" +
	"\x05_sizeB\b\n" +
	"\x06_stateB\x06\n" +
	"\x04_urlB\b\n" +
	"\x06_titleB\v\n" +
	"\t_originalB\a\n" +
	"\x05_typeB\a\n" +
	"\x05_size\"\xc6\x01\n" +
	"\x13UEditorListResponse\x12\x14\n" +
	"\x05state\x18\x01 \x01(\tR\x05state\x12\x14\n" +
	"\x05start\x18\x02 \x01(\x05R\x05start\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12=\n" +
	"\x04list\x18\n" +
	" \x03(\v2).file.service.v1.UEditorListResponse.ItemR\x04list\x1a.\n" +
	"\x04Item\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x14\n" +
	"\x05mtime\x18\x02 \x01(\x03R\x05mtime*\x8c\x01\n" +
	"\rUEditorAction\x12\n" +
	"\n" +
	"\x06config\x10\x00\x12\f\n" +
	"\blistFile\x10\x01\x12\r\n" +
	"\tlistImage\x10\x02\x12\x0e\n" +
	"\n" +
	"uploadFile\x10\n" +
	"\x12\x0f\n" +
	"\vuploadImage\x10\v\x12\x0f\n" +
	"\vuploadVideo\x10\f\x12\x10\n" +
	"\fuploadScrawl\x10\r\x12\x0e\n" +
	"\n" +
	"catchImage\x10\x0e2\xc2\x01\n" +
	"\x0eUEditorService\x12Q\n" +
	"\n" +
	"UEditorAPI\x12\x1f.file.service.v1.UEditorRequest\x1a .file.service.v1.UEditorResponse\"\x00\x12]\n" +
	"\n" +
	"UploadFile\x12%.file.service.v1.UEditorUploadRequest\x1a&.file.service.v1.UEditorUploadResponse\"\x00B\xb4\x01\n" +
	"\x13com.file.service.v1B\fUeditorProtoP\x01Z1kratos-admin/api/gen/go/file/service/v1;servicev1\xa2\x02\x03FSX\xaa\x02\x0fFile.Service.V1\xca\x02\x0fFile\\Service\\V1\xe2\x02\x1bFile\\Service\\V1\\GPBMetadata\xea\x02\x11File::Service::V1b\x06proto3"

var (
	file_file_service_v1_ueditor_proto_rawDescOnce sync.Once
	file_file_service_v1_ueditor_proto_rawDescData []byte
)

func file_file_service_v1_ueditor_proto_rawDescGZIP() []byte {
	file_file_service_v1_ueditor_proto_rawDescOnce.Do(func() {
		file_file_service_v1_ueditor_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_file_service_v1_ueditor_proto_rawDesc), len(file_file_service_v1_ueditor_proto_rawDesc)))
	})
	return file_file_service_v1_ueditor_proto_rawDescData
}

var file_file_service_v1_ueditor_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_file_service_v1_ueditor_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_file_service_v1_ueditor_proto_goTypes = []any{
	(UEditorAction)(0),                    // 0: file.service.v1.UEditorAction
	(*UEditorRequest)(nil),                // 1: file.service.v1.UEditorRequest
	(*UEditorResponse)(nil),               // 2: file.service.v1.UEditorResponse
	(*UEditorUploadRequest)(nil),          // 3: file.service.v1.UEditorUploadRequest
	(*UEditorUploadResponse)(nil),         // 4: file.service.v1.UEditorUploadResponse
	(*UEditorListResponse)(nil),           // 5: file.service.v1.UEditorListResponse
	(*UEditorResponse_FormulaConfig)(nil), // 6: file.service.v1.UEditorResponse.FormulaConfig
	(*UEditorResponse_Item)(nil),          // 7: file.service.v1.UEditorResponse.Item
	(*UEditorUploadResponse_Item)(nil),    // 8: file.service.v1.UEditorUploadResponse.Item
	(*UEditorListResponse_Item)(nil),      // 9: file.service.v1.UEditorListResponse.Item
}
var file_file_service_v1_ueditor_proto_depIdxs = []int32{
	6, // 0: file.service.v1.UEditorResponse.formulaConfig:type_name -> file.service.v1.UEditorResponse.FormulaConfig
	7, // 1: file.service.v1.UEditorResponse.list:type_name -> file.service.v1.UEditorResponse.Item
	8, // 2: file.service.v1.UEditorUploadResponse.list:type_name -> file.service.v1.UEditorUploadResponse.Item
	9, // 3: file.service.v1.UEditorListResponse.list:type_name -> file.service.v1.UEditorListResponse.Item
	1, // 4: file.service.v1.UEditorService.UEditorAPI:input_type -> file.service.v1.UEditorRequest
	3, // 5: file.service.v1.UEditorService.UploadFile:input_type -> file.service.v1.UEditorUploadRequest
	2, // 6: file.service.v1.UEditorService.UEditorAPI:output_type -> file.service.v1.UEditorResponse
	4, // 7: file.service.v1.UEditorService.UploadFile:output_type -> file.service.v1.UEditorUploadResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_file_service_v1_ueditor_proto_init() }
func file_file_service_v1_ueditor_proto_init() {
	if File_file_service_v1_ueditor_proto != nil {
		return
	}
	file_file_service_v1_ueditor_proto_msgTypes[1].OneofWrappers = []any{}
	file_file_service_v1_ueditor_proto_msgTypes[2].OneofWrappers = []any{}
	file_file_service_v1_ueditor_proto_msgTypes[3].OneofWrappers = []any{}
	file_file_service_v1_ueditor_proto_msgTypes[7].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_file_service_v1_ueditor_proto_rawDesc), len(file_file_service_v1_ueditor_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_file_service_v1_ueditor_proto_goTypes,
		DependencyIndexes: file_file_service_v1_ueditor_proto_depIdxs,
		EnumInfos:         file_file_service_v1_ueditor_proto_enumTypes,
		MessageInfos:      file_file_service_v1_ueditor_proto_msgTypes,
	}.Build()
	File_file_service_v1_ueditor_proto = out.File
	file_file_service_v1_ueditor_proto_goTypes = nil
	file_file_service_v1_ueditor_proto_depIdxs = nil
}
