// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: file/service/v1/file_error.proto

package servicev1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FileErrorReason 是文件系统错误定义
type FileErrorReason int32

const (
	// 400
	FileErrorReason_BAD_REQUEST FileErrorReason = 0 // 错误请求
	// 401
	FileErrorReason_UNAUTHORIZED FileErrorReason = 100 // 未授权
	// 402
	FileErrorReason_PAYMENT_REQUIRED FileErrorReason = 200 // 需要支付
	// 403
	FileErrorReason_FORBIDDEN FileErrorReason = 300 // 禁止访问
	// 404
	FileErrorReason_NOT_FOUND      FileErrorReason = 400 // 找不到资源
	FileErrorReason_FILE_NOT_FOUND FileErrorReason = 401 // 文件不存在
	// 405
	FileErrorReason_METHOD_NOT_ALLOWED FileErrorReason = 500 // 方法不允许
	// 406
	FileErrorReason_NOT_ACCEPTABLE FileErrorReason = 600 // 不可接受的请求
	// 407
	FileErrorReason_PROXY_AUTHENTICATION_REQUIRED FileErrorReason = 700 // 代理身份验证需要
	// 408
	FileErrorReason_REQUEST_TIMEOUT FileErrorReason = 800 // 请求超时
	// 409
	FileErrorReason_CONFLICT FileErrorReason = 900 // 冲突
	// 410
	FileErrorReason_GONE FileErrorReason = 1000 // 已删除
	// 411
	FileErrorReason_LENGTH_REQUIRED FileErrorReason = 1010 // 需要Content-Length
	// 412
	FileErrorReason_PRECONDITION_FAILED FileErrorReason = 1020 // 前置条件失败
	// 413
	FileErrorReason_PAYLOAD_TOO_LARGE FileErrorReason = 1030 // 负载过大
	FileErrorReason_FILE_TOO_LARGE    FileErrorReason = 1031 // 文件过大
	// 414
	FileErrorReason_URI_TOO_LONG FileErrorReason = 1040 // URI过长
	// 415
	FileErrorReason_UNSUPPORTED_MEDIA_TYPE FileErrorReason = 1050 // 不支持的媒体类型
	// 416
	FileErrorReason_RANGE_NOT_SATISFIABLE FileErrorReason = 1060 // 请求范围无法满足
	// 417
	FileErrorReason_EXPECTATION_FAILED FileErrorReason = 1070 // 期望失败
	// 418
	FileErrorReason_IM_A_TEAPOT FileErrorReason = 1080 // 我是茶壶 (RFC 2324)
	// 421
	FileErrorReason_MISDIRECTED_REQUEST FileErrorReason = 1090 // 错误的请求
	// 422
	FileErrorReason_UNPROCESSABLE_ENTITY FileErrorReason = 1100 // 不可处理的实体
	// 423
	FileErrorReason_LOCKED FileErrorReason = 1110 // 已锁定
	// 424
	FileErrorReason_FAILED_DEPENDENCY FileErrorReason = 1120 // 依赖失败
	// 425
	FileErrorReason_TOO_EARLY FileErrorReason = 1130 // 请求过早
	// 426
	FileErrorReason_UPGRADE_REQUIRED FileErrorReason = 1140 // 需要升级
	// 428
	FileErrorReason_PRECONDITION_REQUIRED FileErrorReason = 1150 // 需要前置条件
	// 429
	FileErrorReason_TOO_MANY_REQUESTS FileErrorReason = 1160 // 请求过多
	// 431
	FileErrorReason_REQUEST_HEADER_FIELDS_TOO_LARGE FileErrorReason = 1170 // 请求头字段过大
	// 451
	FileErrorReason_UNAVAILABLE_FOR_LEGAL_REASONS FileErrorReason = 1180 // 因法律原因不可用
	// 500
	FileErrorReason_INTERNAL_SERVER_ERROR FileErrorReason = 2000 // 内部服务器错误
	FileErrorReason_UPLOAD_FAILED         FileErrorReason = 2001
	FileErrorReason_DOWNLOAD_FAILED       FileErrorReason = 2002
	// 501
	FileErrorReason_NOT_IMPLEMENTED FileErrorReason = 2100 // 未实现
	// 502
	FileErrorReason_BAD_GATEWAY FileErrorReason = 2200 // 错误网关
	// 503
	FileErrorReason_SERVICE_UNAVAILABLE FileErrorReason = 2300 // 服务不可用
	// 504
	FileErrorReason_GATEWAY_TIMEOUT FileErrorReason = 2400 // 网关超时
	// 505
	FileErrorReason_HTTP_VERSION_NOT_SUPPORTED FileErrorReason = 2500 // HTTP版本不支持
	// 506
	FileErrorReason_VARIANT_ALSO_NEGOTIATES FileErrorReason = 2600 // 变体也协商
	// 507
	FileErrorReason_INSUFFICIENT_STORAGE FileErrorReason = 2700 // 存储空间不足
	// 508
	FileErrorReason_LOOP_DETECTED FileErrorReason = 2800 // 检测到循环
	// 510
	FileErrorReason_NOT_EXTENDED FileErrorReason = 2900 // 未扩展
	// 511
	FileErrorReason_NETWORK_AUTHENTICATION_REQUIRED FileErrorReason = 3000 // 需要网络认证
	// 598
	FileErrorReason_NETWORK_READ_TIMEOUT_ERROR FileErrorReason = 3100 // 网络读取超时
	// 599
	FileErrorReason_NETWORK_CONNECT_TIMEOUT_ERROR FileErrorReason = 3200 // 网络连接超时
)

// Enum value maps for FileErrorReason.
var (
	FileErrorReason_name = map[int32]string{
		0:    "BAD_REQUEST",
		100:  "UNAUTHORIZED",
		200:  "PAYMENT_REQUIRED",
		300:  "FORBIDDEN",
		400:  "NOT_FOUND",
		401:  "FILE_NOT_FOUND",
		500:  "METHOD_NOT_ALLOWED",
		600:  "NOT_ACCEPTABLE",
		700:  "PROXY_AUTHENTICATION_REQUIRED",
		800:  "REQUEST_TIMEOUT",
		900:  "CONFLICT",
		1000: "GONE",
		1010: "LENGTH_REQUIRED",
		1020: "PRECONDITION_FAILED",
		1030: "PAYLOAD_TOO_LARGE",
		1031: "FILE_TOO_LARGE",
		1040: "URI_TOO_LONG",
		1050: "UNSUPPORTED_MEDIA_TYPE",
		1060: "RANGE_NOT_SATISFIABLE",
		1070: "EXPECTATION_FAILED",
		1080: "IM_A_TEAPOT",
		1090: "MISDIRECTED_REQUEST",
		1100: "UNPROCESSABLE_ENTITY",
		1110: "LOCKED",
		1120: "FAILED_DEPENDENCY",
		1130: "TOO_EARLY",
		1140: "UPGRADE_REQUIRED",
		1150: "PRECONDITION_REQUIRED",
		1160: "TOO_MANY_REQUESTS",
		1170: "REQUEST_HEADER_FIELDS_TOO_LARGE",
		1180: "UNAVAILABLE_FOR_LEGAL_REASONS",
		2000: "INTERNAL_SERVER_ERROR",
		2001: "UPLOAD_FAILED",
		2002: "DOWNLOAD_FAILED",
		2100: "NOT_IMPLEMENTED",
		2200: "BAD_GATEWAY",
		2300: "SERVICE_UNAVAILABLE",
		2400: "GATEWAY_TIMEOUT",
		2500: "HTTP_VERSION_NOT_SUPPORTED",
		2600: "VARIANT_ALSO_NEGOTIATES",
		2700: "INSUFFICIENT_STORAGE",
		2800: "LOOP_DETECTED",
		2900: "NOT_EXTENDED",
		3000: "NETWORK_AUTHENTICATION_REQUIRED",
		3100: "NETWORK_READ_TIMEOUT_ERROR",
		3200: "NETWORK_CONNECT_TIMEOUT_ERROR",
	}
	FileErrorReason_value = map[string]int32{
		"BAD_REQUEST":                     0,
		"UNAUTHORIZED":                    100,
		"PAYMENT_REQUIRED":                200,
		"FORBIDDEN":                       300,
		"NOT_FOUND":                       400,
		"FILE_NOT_FOUND":                  401,
		"METHOD_NOT_ALLOWED":              500,
		"NOT_ACCEPTABLE":                  600,
		"PROXY_AUTHENTICATION_REQUIRED":   700,
		"REQUEST_TIMEOUT":                 800,
		"CONFLICT":                        900,
		"GONE":                            1000,
		"LENGTH_REQUIRED":                 1010,
		"PRECONDITION_FAILED":             1020,
		"PAYLOAD_TOO_LARGE":               1030,
		"FILE_TOO_LARGE":                  1031,
		"URI_TOO_LONG":                    1040,
		"UNSUPPORTED_MEDIA_TYPE":          1050,
		"RANGE_NOT_SATISFIABLE":           1060,
		"EXPECTATION_FAILED":              1070,
		"IM_A_TEAPOT":                     1080,
		"MISDIRECTED_REQUEST":             1090,
		"UNPROCESSABLE_ENTITY":            1100,
		"LOCKED":                          1110,
		"FAILED_DEPENDENCY":               1120,
		"TOO_EARLY":                       1130,
		"UPGRADE_REQUIRED":                1140,
		"PRECONDITION_REQUIRED":           1150,
		"TOO_MANY_REQUESTS":               1160,
		"REQUEST_HEADER_FIELDS_TOO_LARGE": 1170,
		"UNAVAILABLE_FOR_LEGAL_REASONS":   1180,
		"INTERNAL_SERVER_ERROR":           2000,
		"UPLOAD_FAILED":                   2001,
		"DOWNLOAD_FAILED":                 2002,
		"NOT_IMPLEMENTED":                 2100,
		"BAD_GATEWAY":                     2200,
		"SERVICE_UNAVAILABLE":             2300,
		"GATEWAY_TIMEOUT":                 2400,
		"HTTP_VERSION_NOT_SUPPORTED":      2500,
		"VARIANT_ALSO_NEGOTIATES":         2600,
		"INSUFFICIENT_STORAGE":            2700,
		"LOOP_DETECTED":                   2800,
		"NOT_EXTENDED":                    2900,
		"NETWORK_AUTHENTICATION_REQUIRED": 3000,
		"NETWORK_READ_TIMEOUT_ERROR":      3100,
		"NETWORK_CONNECT_TIMEOUT_ERROR":   3200,
	}
)

func (x FileErrorReason) Enum() *FileErrorReason {
	p := new(FileErrorReason)
	*p = x
	return p
}

func (x FileErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_file_service_v1_file_error_proto_enumTypes[0].Descriptor()
}

func (FileErrorReason) Type() protoreflect.EnumType {
	return &file_file_service_v1_file_error_proto_enumTypes[0]
}

func (x FileErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileErrorReason.Descriptor instead.
func (FileErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_file_service_v1_file_error_proto_rawDescGZIP(), []int{0}
}

var File_file_service_v1_file_error_proto protoreflect.FileDescriptor

const file_file_service_v1_file_error_proto_rawDesc = "" +
	"\n" +
	" file/service/v1/file_error.proto\x12\x0ffile.service.v1\x1a\x13errors/errors.proto*\xf9\n" +
	"\n" +
	"\x0fFileErrorReason\x12\x15\n" +
	"\vBAD_REQUEST\x10\x00\x1a\x04\xa8E\x90\x03\x12\x16\n" +
	"\fUNAUTHORIZED\x10d\x1a\x04\xa8E\x91\x03\x12\x1b\n" +
	"\x10PAYMENT_REQUIRED\x10\xc8\x01\x1a\x04\xa8E\x92\x03\x12\x14\n" +
	"\tFORBIDDEN\x10\xac\x02\x1a\x04\xa8E\x93\x03\x12\x14\n" +
	"\tNOT_FOUND\x10\x90\x03\x1a\x04\xa8E\x94\x03\x12\x19\n" +
	"\x0eFILE_NOT_FOUND\x10\x91\x03\x1a\x04\xa8E\x94\x03\x12\x1d\n" +
	"\x12METHOD_NOT_ALLOWED\x10\xf4\x03\x1a\x04\xa8E\x95\x03\x12\x19\n" +
	"\x0eNOT_ACCEPTABLE\x10\xd8\x04\x1a\x04\xa8E\x96\x03\x12(\n" +
	"\x1dPROXY_AUTHENTICATION_REQUIRED\x10\xbc\x05\x1a\x04\xa8E\x97\x03\x12\x1a\n" +
	"\x0fREQUEST_TIMEOUT\x10\xa0\x06\x1a\x04\xa8E\x98\x03\x12\x13\n" +
	"\bCONFLICT\x10\x84\a\x1a\x04\xa8E\x99\x03\x12\x0f\n" +
	"\x04GONE\x10\xe8\a\x1a\x04\xa8E\x9a\x03\x12\x1a\n" +
	"\x0fLENGTH_REQUIRED\x10\xf2\a\x1a\x04\xa8E\x9b\x03\x12\x1e\n" +
	"\x13PRECONDITION_FAILED\x10\xfc\a\x1a\x04\xa8E\x9c\x03\x12\x1c\n" +
	"\x11PAYLOAD_TOO_LARGE\x10\x86\b\x1a\x04\xa8E\x9d\x03\x12\x19\n" +
	"\x0eFILE_TOO_LARGE\x10\x87\b\x1a\x04\xa8E\x9d\x03\x12\x17\n" +
	"\fURI_TOO_LONG\x10\x90\b\x1a\x04\xa8E\x9e\x03\x12!\n" +
	"\x16UNSUPPORTED_MEDIA_TYPE\x10\x9a\b\x1a\x04\xa8E\x9f\x03\x12 \n" +
	"\x15RANGE_NOT_SATISFIABLE\x10\xa4\b\x1a\x04\xa8E\xa0\x03\x12\x1d\n" +
	"\x12EXPECTATION_FAILED\x10\xae\b\x1a\x04\xa8E\xa1\x03\x12\x16\n" +
	"\vIM_A_TEAPOT\x10\xb8\b\x1a\x04\xa8E\xa2\x03\x12\x1e\n" +
	"\x13MISDIRECTED_REQUEST\x10\xc2\b\x1a\x04\xa8E\xa5\x03\x12\x1f\n" +
	"\x14UNPROCESSABLE_ENTITY\x10\xcc\b\x1a\x04\xa8E\xa6\x03\x12\x11\n" +
	"\x06LOCKED\x10\xd6\b\x1a\x04\xa8E\xa7\x03\x12\x1c\n" +
	"\x11FAILED_DEPENDENCY\x10\xe0\b\x1a\x04\xa8E\xa8\x03\x12\x14\n" +
	"\tTOO_EARLY\x10\xea\b\x1a\x04\xa8E\xa9\x03\x12\x1b\n" +
	"\x10UPGRADE_REQUIRED\x10\xf4\b\x1a\x04\xa8E\xaa\x03\x12 \n" +
	"\x15PRECONDITION_REQUIRED\x10\xfe\b\x1a\x04\xa8E\xac\x03\x12\x1c\n" +
	"\x11TOO_MANY_REQUESTS\x10\x88\t\x1a\x04\xa8E\xad\x03\x12*\n" +
	"\x1fREQUEST_HEADER_FIELDS_TOO_LARGE\x10\x92\t\x1a\x04\xa8E\xaf\x03\x12(\n" +
	"\x1dUNAVAILABLE_FOR_LEGAL_REASONS\x10\x9c\t\x1a\x04\xa8E\xc3\x03\x12 \n" +
	"\x15INTERNAL_SERVER_ERROR\x10\xd0\x0f\x1a\x04\xa8E\xf4\x03\x12\x18\n" +
	"\rUPLOAD_FAILED\x10\xd1\x0f\x1a\x04\xa8E\xf4\x03\x12\x1a\n" +
	"\x0fDOWNLOAD_FAILED\x10\xd2\x0f\x1a\x04\xa8E\xf4\x03\x12\x1a\n" +
	"\x0fNOT_IMPLEMENTED\x10\xb4\x10\x1a\x04\xa8E\xf5\x03\x12\x16\n" +
	"\vBAD_GATEWAY\x10\x98\x11\x1a\x04\xa8E\xf6\x03\x12\x1e\n" +
	"\x13SERVICE_UNAVAILABLE\x10\xfc\x11\x1a\x04\xa8E\xf7\x03\x12\x1a\n" +
	"\x0fGATEWAY_TIMEOUT\x10\xe0\x12\x1a\x04\xa8E\xf8\x03\x12%\n" +
	"\x1aHTTP_VERSION_NOT_SUPPORTED\x10\xc4\x13\x1a\x04\xa8E\xf9\x03\x12\"\n" +
	"\x17VARIANT_ALSO_NEGOTIATES\x10\xa8\x14\x1a\x04\xa8E\xfa\x03\x12\x1f\n" +
	"\x14INSUFFICIENT_STORAGE\x10\x8c\x15\x1a\x04\xa8E\xfb\x03\x12\x18\n" +
	"\rLOOP_DETECTED\x10\xf0\x15\x1a\x04\xa8E\xfc\x03\x12\x17\n" +
	"\fNOT_EXTENDED\x10\xd4\x16\x1a\x04\xa8E\xfe\x03\x12*\n" +
	"\x1fNETWORK_AUTHENTICATION_REQUIRED\x10\xb8\x17\x1a\x04\xa8E\xff\x03\x12%\n" +
	"\x1aNETWORK_READ_TIMEOUT_ERROR\x10\x9c\x18\x1a\x04\xa8E\xd6\x04\x12(\n" +
	"\x1dNETWORK_CONNECT_TIMEOUT_ERROR\x10\x80\x19\x1a\x04\xa8E\xd7\x04\x1a\x04\xa0E\xf4\x03B\xb6\x01\n" +
	"\x13com.file.service.v1B\x0eFileErrorProtoP\x01Z1kratos-admin/api/gen/go/file/service/v1;servicev1\xa2\x02\x03FSX\xaa\x02\x0fFile.Service.V1\xca\x02\x0fFile\\Service\\V1\xe2\x02\x1bFile\\Service\\V1\\GPBMetadata\xea\x02\x11File::Service::V1b\x06proto3"

var (
	file_file_service_v1_file_error_proto_rawDescOnce sync.Once
	file_file_service_v1_file_error_proto_rawDescData []byte
)

func file_file_service_v1_file_error_proto_rawDescGZIP() []byte {
	file_file_service_v1_file_error_proto_rawDescOnce.Do(func() {
		file_file_service_v1_file_error_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_file_service_v1_file_error_proto_rawDesc), len(file_file_service_v1_file_error_proto_rawDesc)))
	})
	return file_file_service_v1_file_error_proto_rawDescData
}

var file_file_service_v1_file_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_file_service_v1_file_error_proto_goTypes = []any{
	(FileErrorReason)(0), // 0: file.service.v1.FileErrorReason
}
var file_file_service_v1_file_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_file_service_v1_file_error_proto_init() }
func file_file_service_v1_file_error_proto_init() {
	if File_file_service_v1_file_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_file_service_v1_file_error_proto_rawDesc), len(file_file_service_v1_file_error_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_file_service_v1_file_error_proto_goTypes,
		DependencyIndexes: file_file_service_v1_file_error_proto_depIdxs,
		EnumInfos:         file_file_service_v1_file_error_proto_enumTypes,
	}.Build()
	File_file_service_v1_file_error_proto = out.File
	file_file_service_v1_file_error_proto_goTypes = nil
	file_file_service_v1_file_error_proto_depIdxs = nil
}
