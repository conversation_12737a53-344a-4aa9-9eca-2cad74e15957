// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: file/service/v1/file.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on File with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *File) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on File with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FileMultiError, or nil if none found.
func (m *File) ValidateAll() error {
	return m.validate(true)
}

func (m *File) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Provider != nil {
		// no validation rules for Provider
	}

	if m.BucketName != nil {
		// no validation rules for BucketName
	}

	if m.FileDirectory != nil {
		// no validation rules for FileDirectory
	}

	if m.FileGuid != nil {
		// no validation rules for FileGuid
	}

	if m.SaveFileName != nil {
		// no validation rules for SaveFileName
	}

	if m.FileName != nil {
		// no validation rules for FileName
	}

	if m.Extension != nil {
		// no validation rules for Extension
	}

	if m.Size != nil {
		// no validation rules for Size
	}

	if m.SizeFormat != nil {
		// no validation rules for SizeFormat
	}

	if m.LinkUrl != nil {
		// no validation rules for LinkUrl
	}

	if m.Md5 != nil {
		// no validation rules for Md5
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FileMultiError(errors)
	}

	return nil
}

// FileMultiError is an error wrapping multiple validation errors returned by
// File.ValidateAll() if the designated constraints aren't met.
type FileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileMultiError) AllErrors() []error { return m }

// FileValidationError is the validation error returned by File.Validate if the
// designated constraints aren't met.
type FileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileValidationError) ErrorName() string { return "FileValidationError" }

// Error satisfies the builtin error interface
func (e FileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileValidationError{}

// Validate checks the field values on ListFileResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFileResponseMultiError, or nil if none found.
func (m *ListFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFileResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFileResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFileResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListFileResponseMultiError(errors)
	}

	return nil
}

// ListFileResponseMultiError is an error wrapping multiple validation errors
// returned by ListFileResponse.ValidateAll() if the designated constraints
// aren't met.
type ListFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFileResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFileResponseMultiError) AllErrors() []error { return m }

// ListFileResponseValidationError is the validation error returned by
// ListFileResponse.Validate if the designated constraints aren't met.
type ListFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFileResponseValidationError) ErrorName() string { return "ListFileResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFileResponseValidationError{}

// Validate checks the field values on GetFileRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFileRequestMultiError,
// or nil if none found.
func (m *GetFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetFileRequestMultiError(errors)
	}

	return nil
}

// GetFileRequestMultiError is an error wrapping multiple validation errors
// returned by GetFileRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileRequestMultiError) AllErrors() []error { return m }

// GetFileRequestValidationError is the validation error returned by
// GetFileRequest.Validate if the designated constraints aren't met.
type GetFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileRequestValidationError) ErrorName() string { return "GetFileRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileRequestValidationError{}

// Validate checks the field values on CreateFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFileRequestMultiError, or nil if none found.
func (m *CreateFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFileRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFileRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFileRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateFileRequestMultiError(errors)
	}

	return nil
}

// CreateFileRequestMultiError is an error wrapping multiple validation errors
// returned by CreateFileRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFileRequestMultiError) AllErrors() []error { return m }

// CreateFileRequestValidationError is the validation error returned by
// CreateFileRequest.Validate if the designated constraints aren't met.
type CreateFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFileRequestValidationError) ErrorName() string {
	return "CreateFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFileRequestValidationError{}

// Validate checks the field values on UpdateFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFileRequestMultiError, or nil if none found.
func (m *UpdateFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFileRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFileRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFileRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFileRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFileRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFileRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateFileRequestMultiError(errors)
	}

	return nil
}

// UpdateFileRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateFileRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFileRequestMultiError) AllErrors() []error { return m }

// UpdateFileRequestValidationError is the validation error returned by
// UpdateFileRequest.Validate if the designated constraints aren't met.
type UpdateFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFileRequestValidationError) ErrorName() string {
	return "UpdateFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFileRequestValidationError{}

// Validate checks the field values on DeleteFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteFileRequestMultiError, or nil if none found.
func (m *DeleteFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteFileRequestMultiError(errors)
	}

	return nil
}

// DeleteFileRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteFileRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFileRequestMultiError) AllErrors() []error { return m }

// DeleteFileRequestValidationError is the validation error returned by
// DeleteFileRequest.Validate if the designated constraints aren't met.
type DeleteFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFileRequestValidationError) ErrorName() string {
	return "DeleteFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFileRequestValidationError{}
