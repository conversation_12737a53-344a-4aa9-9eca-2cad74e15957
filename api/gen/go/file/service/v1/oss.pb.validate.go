// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: file/service/v1/oss.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on OssUploadUrlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OssUploadUrlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OssUploadUrlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OssUploadUrlRequestMultiError, or nil if none found.
func (m *OssUploadUrlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OssUploadUrlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Method

	if m.ContentType != nil {
		// no validation rules for ContentType
	}

	if m.BucketName != nil {
		// no validation rules for BucketName
	}

	if m.FilePath != nil {
		// no validation rules for FilePath
	}

	if m.FileName != nil {
		// no validation rules for FileName
	}

	if len(errors) > 0 {
		return OssUploadUrlRequestMultiError(errors)
	}

	return nil
}

// OssUploadUrlRequestMultiError is an error wrapping multiple validation
// errors returned by OssUploadUrlRequest.ValidateAll() if the designated
// constraints aren't met.
type OssUploadUrlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OssUploadUrlRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OssUploadUrlRequestMultiError) AllErrors() []error { return m }

// OssUploadUrlRequestValidationError is the validation error returned by
// OssUploadUrlRequest.Validate if the designated constraints aren't met.
type OssUploadUrlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OssUploadUrlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OssUploadUrlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OssUploadUrlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OssUploadUrlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OssUploadUrlRequestValidationError) ErrorName() string {
	return "OssUploadUrlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OssUploadUrlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOssUploadUrlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OssUploadUrlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OssUploadUrlRequestValidationError{}

// Validate checks the field values on OssUploadUrlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OssUploadUrlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OssUploadUrlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OssUploadUrlResponseMultiError, or nil if none found.
func (m *OssUploadUrlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OssUploadUrlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UploadUrl

	// no validation rules for DownloadUrl

	// no validation rules for ObjectName

	// no validation rules for FormData

	if m.BucketName != nil {
		// no validation rules for BucketName
	}

	if len(errors) > 0 {
		return OssUploadUrlResponseMultiError(errors)
	}

	return nil
}

// OssUploadUrlResponseMultiError is an error wrapping multiple validation
// errors returned by OssUploadUrlResponse.ValidateAll() if the designated
// constraints aren't met.
type OssUploadUrlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OssUploadUrlResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OssUploadUrlResponseMultiError) AllErrors() []error { return m }

// OssUploadUrlResponseValidationError is the validation error returned by
// OssUploadUrlResponse.Validate if the designated constraints aren't met.
type OssUploadUrlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OssUploadUrlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OssUploadUrlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OssUploadUrlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OssUploadUrlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OssUploadUrlResponseValidationError) ErrorName() string {
	return "OssUploadUrlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OssUploadUrlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOssUploadUrlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OssUploadUrlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OssUploadUrlResponseValidationError{}

// Validate checks the field values on GetDownloadUrlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDownloadUrlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDownloadUrlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDownloadUrlRequestMultiError, or nil if none found.
func (m *GetDownloadUrlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDownloadUrlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetDownloadUrlRequestMultiError(errors)
	}

	return nil
}

// GetDownloadUrlRequestMultiError is an error wrapping multiple validation
// errors returned by GetDownloadUrlRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDownloadUrlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDownloadUrlRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDownloadUrlRequestMultiError) AllErrors() []error { return m }

// GetDownloadUrlRequestValidationError is the validation error returned by
// GetDownloadUrlRequest.Validate if the designated constraints aren't met.
type GetDownloadUrlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDownloadUrlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDownloadUrlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDownloadUrlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDownloadUrlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDownloadUrlRequestValidationError) ErrorName() string {
	return "GetDownloadUrlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDownloadUrlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDownloadUrlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDownloadUrlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDownloadUrlRequestValidationError{}

// Validate checks the field values on GetDownloadUrlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDownloadUrlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDownloadUrlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDownloadUrlResponseMultiError, or nil if none found.
func (m *GetDownloadUrlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDownloadUrlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetDownloadUrlResponseMultiError(errors)
	}

	return nil
}

// GetDownloadUrlResponseMultiError is an error wrapping multiple validation
// errors returned by GetDownloadUrlResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDownloadUrlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDownloadUrlResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDownloadUrlResponseMultiError) AllErrors() []error { return m }

// GetDownloadUrlResponseValidationError is the validation error returned by
// GetDownloadUrlResponse.Validate if the designated constraints aren't met.
type GetDownloadUrlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDownloadUrlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDownloadUrlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDownloadUrlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDownloadUrlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDownloadUrlResponseValidationError) ErrorName() string {
	return "GetDownloadUrlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDownloadUrlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDownloadUrlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDownloadUrlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDownloadUrlResponseValidationError{}

// Validate checks the field values on ListOssFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOssFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOssFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOssFileRequestMultiError, or nil if none found.
func (m *ListOssFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOssFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.BucketName != nil {
		// no validation rules for BucketName
	}

	if m.Folder != nil {
		// no validation rules for Folder
	}

	if m.Recursive != nil {
		// no validation rules for Recursive
	}

	if len(errors) > 0 {
		return ListOssFileRequestMultiError(errors)
	}

	return nil
}

// ListOssFileRequestMultiError is an error wrapping multiple validation errors
// returned by ListOssFileRequest.ValidateAll() if the designated constraints
// aren't met.
type ListOssFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOssFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOssFileRequestMultiError) AllErrors() []error { return m }

// ListOssFileRequestValidationError is the validation error returned by
// ListOssFileRequest.Validate if the designated constraints aren't met.
type ListOssFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOssFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOssFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOssFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOssFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOssFileRequestValidationError) ErrorName() string {
	return "ListOssFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListOssFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOssFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOssFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOssFileRequestValidationError{}

// Validate checks the field values on ListOssFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOssFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOssFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOssFileResponseMultiError, or nil if none found.
func (m *ListOssFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOssFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListOssFileResponseMultiError(errors)
	}

	return nil
}

// ListOssFileResponseMultiError is an error wrapping multiple validation
// errors returned by ListOssFileResponse.ValidateAll() if the designated
// constraints aren't met.
type ListOssFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOssFileResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOssFileResponseMultiError) AllErrors() []error { return m }

// ListOssFileResponseValidationError is the validation error returned by
// ListOssFileResponse.Validate if the designated constraints aren't met.
type ListOssFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOssFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOssFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOssFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOssFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOssFileResponseValidationError) ErrorName() string {
	return "ListOssFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListOssFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOssFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOssFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOssFileResponseValidationError{}

// Validate checks the field values on DeleteOssFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOssFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOssFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOssFileRequestMultiError, or nil if none found.
func (m *DeleteOssFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOssFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.BucketName != nil {
		// no validation rules for BucketName
	}

	if m.ObjectName != nil {
		// no validation rules for ObjectName
	}

	if len(errors) > 0 {
		return DeleteOssFileRequestMultiError(errors)
	}

	return nil
}

// DeleteOssFileRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteOssFileRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteOssFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOssFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOssFileRequestMultiError) AllErrors() []error { return m }

// DeleteOssFileRequestValidationError is the validation error returned by
// DeleteOssFileRequest.Validate if the designated constraints aren't met.
type DeleteOssFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOssFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOssFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOssFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOssFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOssFileRequestValidationError) ErrorName() string {
	return "DeleteOssFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOssFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOssFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOssFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOssFileRequestValidationError{}

// Validate checks the field values on DeleteOssFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOssFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOssFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOssFileResponseMultiError, or nil if none found.
func (m *DeleteOssFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOssFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteOssFileResponseMultiError(errors)
	}

	return nil
}

// DeleteOssFileResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteOssFileResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteOssFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOssFileResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOssFileResponseMultiError) AllErrors() []error { return m }

// DeleteOssFileResponseValidationError is the validation error returned by
// DeleteOssFileResponse.Validate if the designated constraints aren't met.
type DeleteOssFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOssFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOssFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOssFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOssFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOssFileResponseValidationError) ErrorName() string {
	return "DeleteOssFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOssFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOssFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOssFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOssFileResponseValidationError{}

// Validate checks the field values on UploadOssFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadOssFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadOssFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadOssFileRequestMultiError, or nil if none found.
func (m *UploadOssFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadOssFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.BucketName != nil {
		// no validation rules for BucketName
	}

	if m.ObjectName != nil {
		// no validation rules for ObjectName
	}

	if m.File != nil {
		// no validation rules for File
	}

	if m.SourceFileName != nil {
		// no validation rules for SourceFileName
	}

	if m.Mime != nil {
		// no validation rules for Mime
	}

	if len(errors) > 0 {
		return UploadOssFileRequestMultiError(errors)
	}

	return nil
}

// UploadOssFileRequestMultiError is an error wrapping multiple validation
// errors returned by UploadOssFileRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadOssFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadOssFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadOssFileRequestMultiError) AllErrors() []error { return m }

// UploadOssFileRequestValidationError is the validation error returned by
// UploadOssFileRequest.Validate if the designated constraints aren't met.
type UploadOssFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadOssFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadOssFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadOssFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadOssFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadOssFileRequestValidationError) ErrorName() string {
	return "UploadOssFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadOssFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadOssFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadOssFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadOssFileRequestValidationError{}

// Validate checks the field values on UploadOssFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadOssFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadOssFileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadOssFileResponseMultiError, or nil if none found.
func (m *UploadOssFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadOssFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return UploadOssFileResponseMultiError(errors)
	}

	return nil
}

// UploadOssFileResponseMultiError is an error wrapping multiple validation
// errors returned by UploadOssFileResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadOssFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadOssFileResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadOssFileResponseMultiError) AllErrors() []error { return m }

// UploadOssFileResponseValidationError is the validation error returned by
// UploadOssFileResponse.Validate if the designated constraints aren't met.
type UploadOssFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadOssFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadOssFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadOssFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadOssFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadOssFileResponseValidationError) ErrorName() string {
	return "UploadOssFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadOssFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadOssFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadOssFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadOssFileResponseValidationError{}
