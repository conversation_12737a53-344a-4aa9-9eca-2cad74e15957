// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: file/service/v1/file.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OSS供应商
type OSSProvider int32

const (
	OSSProvider_MINIO   OSSProvider = 0
	OSSProvider_ALIYUN  OSSProvider = 1
	OSSProvider_AWS     OSSProvider = 2
	OSSProvider_AZURE   OSSProvider = 3
	OSSProvider_BAIDU   OSSProvider = 4
	OSSProvider_QINIU   OSSProvider = 5
	OSSProvider_TENCENT OSSProvider = 6
	OSSProvider_GOOGLE  OSSProvider = 7
	OSSProvider_HUAWEI  OSSProvider = 8
	OSSProvider_QCLOUD  OSSProvider = 9
	OSSProvider_LOCAL   OSSProvider = 10
)

// Enum value maps for OSSProvider.
var (
	OSSProvider_name = map[int32]string{
		0:  "MINIO",
		1:  "ALIYUN",
		2:  "AWS",
		3:  "AZURE",
		4:  "BAIDU",
		5:  "QINIU",
		6:  "TENCENT",
		7:  "GOOGLE",
		8:  "HUAWEI",
		9:  "QCLOUD",
		10: "LOCAL",
	}
	OSSProvider_value = map[string]int32{
		"MINIO":   0,
		"ALIYUN":  1,
		"AWS":     2,
		"AZURE":   3,
		"BAIDU":   4,
		"QINIU":   5,
		"TENCENT": 6,
		"GOOGLE":  7,
		"HUAWEI":  8,
		"QCLOUD":  9,
		"LOCAL":   10,
	}
)

func (x OSSProvider) Enum() *OSSProvider {
	p := new(OSSProvider)
	*p = x
	return p
}

func (x OSSProvider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OSSProvider) Descriptor() protoreflect.EnumDescriptor {
	return file_file_service_v1_file_proto_enumTypes[0].Descriptor()
}

func (OSSProvider) Type() protoreflect.EnumType {
	return &file_file_service_v1_file_proto_enumTypes[0]
}

func (x OSSProvider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OSSProvider.Descriptor instead.
func (OSSProvider) EnumDescriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{0}
}

// 文件
type File struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                              // 文件ID
	Provider      *OSSProvider           `protobuf:"varint,2,opt,name=provider,proto3,enum=file.service.v1.OSSProvider,oneof" json:"provider,omitempty"` // OSS供应商
	BucketName    *string                `protobuf:"bytes,3,opt,name=bucket_name,json=bucketName,proto3,oneof" json:"bucket_name,omitempty"`             // 存储桶名称
	FileDirectory *string                `protobuf:"bytes,4,opt,name=file_directory,json=fileDirectory,proto3,oneof" json:"file_directory,omitempty"`    // 文件目录
	FileGuid      *string                `protobuf:"bytes,5,opt,name=file_guid,json=fileGuid,proto3,oneof" json:"file_guid,omitempty"`                   // 文件Guid
	SaveFileName  *string                `protobuf:"bytes,6,opt,name=save_file_name,json=saveFileName,proto3,oneof" json:"save_file_name,omitempty"`     // 保存文件名
	FileName      *string                `protobuf:"bytes,7,opt,name=file_name,json=fileName,proto3,oneof" json:"file_name,omitempty"`                   // 文件名
	Extension     *string                `protobuf:"bytes,8,opt,name=extension,proto3,oneof" json:"extension,omitempty"`                                 // 文件扩展名
	Size          *uint64                `protobuf:"varint,9,opt,name=size,proto3,oneof" json:"size,omitempty"`                                          // 文件字节长度
	SizeFormat    *string                `protobuf:"bytes,10,opt,name=size_format,json=sizeFormat,proto3,oneof" json:"size_format,omitempty"`            // 文件大小格式化
	LinkUrl       *string                `protobuf:"bytes,11,opt,name=link_url,json=linkUrl,proto3,oneof" json:"link_url,omitempty"`                     // 链接地址
	Md5           *string                `protobuf:"bytes,12,opt,name=md5,proto3,oneof" json:"md5,omitempty"`                                            // md5码，防止上传重复文件
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`           // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`           // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`           // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *File) Reset() {
	*x = File{}
	mi := &file_file_service_v1_file_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_file_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{0}
}

func (x *File) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *File) GetProvider() OSSProvider {
	if x != nil && x.Provider != nil {
		return *x.Provider
	}
	return OSSProvider_MINIO
}

func (x *File) GetBucketName() string {
	if x != nil && x.BucketName != nil {
		return *x.BucketName
	}
	return ""
}

func (x *File) GetFileDirectory() string {
	if x != nil && x.FileDirectory != nil {
		return *x.FileDirectory
	}
	return ""
}

func (x *File) GetFileGuid() string {
	if x != nil && x.FileGuid != nil {
		return *x.FileGuid
	}
	return ""
}

func (x *File) GetSaveFileName() string {
	if x != nil && x.SaveFileName != nil {
		return *x.SaveFileName
	}
	return ""
}

func (x *File) GetFileName() string {
	if x != nil && x.FileName != nil {
		return *x.FileName
	}
	return ""
}

func (x *File) GetExtension() string {
	if x != nil && x.Extension != nil {
		return *x.Extension
	}
	return ""
}

func (x *File) GetSize() uint64 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *File) GetSizeFormat() string {
	if x != nil && x.SizeFormat != nil {
		return *x.SizeFormat
	}
	return ""
}

func (x *File) GetLinkUrl() string {
	if x != nil && x.LinkUrl != nil {
		return *x.LinkUrl
	}
	return ""
}

func (x *File) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

func (x *File) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *File) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *File) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *File) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *File) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询列表 - 回应
type ListFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*File                `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFileResponse) Reset() {
	*x = ListFileResponse{}
	mi := &file_file_service_v1_file_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFileResponse) ProtoMessage() {}

func (x *ListFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_file_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFileResponse.ProtoReflect.Descriptor instead.
func (*ListFileResponse) Descriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{1}
}

func (x *ListFileResponse) GetItems() []*File {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListFileResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询 - 请求
type GetFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFileRequest) Reset() {
	*x = GetFileRequest{}
	mi := &file_file_service_v1_file_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileRequest) ProtoMessage() {}

func (x *GetFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_file_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileRequest.ProtoReflect.Descriptor instead.
func (*GetFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{2}
}

func (x *GetFileRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建 - 请求
type CreateFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *File                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFileRequest) Reset() {
	*x = CreateFileRequest{}
	mi := &file_file_service_v1_file_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFileRequest) ProtoMessage() {}

func (x *CreateFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_file_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFileRequest.ProtoReflect.Descriptor instead.
func (*CreateFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{3}
}

func (x *CreateFileRequest) GetData() *File {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新 - 请求
type UpdateFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *File                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFileRequest) Reset() {
	*x = UpdateFileRequest{}
	mi := &file_file_service_v1_file_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileRequest) ProtoMessage() {}

func (x *UpdateFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_file_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileRequest.ProtoReflect.Descriptor instead.
func (*UpdateFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateFileRequest) GetData() *File {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateFileRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateFileRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除 - 请求
type DeleteFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFileRequest) Reset() {
	*x = DeleteFileRequest{}
	mi := &file_file_service_v1_file_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFileRequest) ProtoMessage() {}

func (x *DeleteFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_service_v1_file_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFileRequest.ProtoReflect.Descriptor instead.
func (*DeleteFileRequest) Descriptor() ([]byte, []int) {
	return file_file_service_v1_file_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteFileRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_file_service_v1_file_proto protoreflect.FileDescriptor

const file_file_service_v1_file_proto_rawDesc = "" +
	"\n" +
	"\x1afile/service/v1/file.proto\x12\x0ffile.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\xa8\n" +
	"\n" +
	"\x04File\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b文件IDH\x00R\x02id\x88\x01\x01\x12Q\n" +
	"\bprovider\x18\x02 \x01(\x0e2\x1c.file.service.v1.OSSProviderB\x12\xbaG\x0f\x92\x02\fOSS供应商H\x01R\bprovider\x88\x01\x01\x12;\n" +
	"\vbucket_name\x18\x03 \x01(\tB\x15\xbaG\x12\x92\x02\x0f存储桶名称H\x02R\n" +
	"bucketName\x88\x01\x01\x12>\n" +
	"\x0efile_directory\x18\x04 \x01(\tB\x12\xbaG\x0f\x92\x02\f文件目录H\x03R\rfileDirectory\x88\x01\x01\x122\n" +
	"\tfile_guid\x18\x05 \x01(\tB\x10\xbaG\r\x92\x02\n" +
	"文件GuidH\x04R\bfileGuid\x88\x01\x01\x12@\n" +
	"\x0esave_file_name\x18\x06 \x01(\tB\x15\xbaG\x12\x92\x02\x0f保存文件名H\x05R\fsaveFileName\x88\x01\x01\x121\n" +
	"\tfile_name\x18\a \x01(\tB\x0f\xbaG\f\x92\x02\t文件名H\x06R\bfileName\x88\x01\x01\x128\n" +
	"\textension\x18\b \x01(\tB\x15\xbaG\x12\x92\x02\x0f文件扩展名H\aR\textension\x88\x01\x01\x121\n" +
	"\x04size\x18\t \x01(\x04B\x18\xbaG\x15\x92\x02\x12文件字节长度H\bR\x04size\x88\x01\x01\x12A\n" +
	"\vsize_format\x18\n" +
	" \x01(\tB\x1b\xbaG\x18\x92\x02\x15文件大小格式化H\tR\n" +
	"sizeFormat\x88\x01\x01\x122\n" +
	"\blink_url\x18\v \x01(\tB\x12\xbaG\x0f\x92\x02\f链接地址H\n" +
	"R\alinkUrl\x88\x01\x01\x12>\n" +
	"\x03md5\x18\f \x01(\tB'\xbaG$\x92\x02!md5码，防止上传重复文件H\vR\x03md5\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\fR\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\rR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\x0eR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\x0fR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\x10R\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\v\n" +
	"\t_providerB\x0e\n" +
	"\f_bucket_nameB\x11\n" +
	"\x0f_file_directoryB\f\n" +
	"\n" +
	"_file_guidB\x11\n" +
	"\x0f_save_file_nameB\f\n" +
	"\n" +
	"_file_nameB\f\n" +
	"\n" +
	"_extensionB\a\n" +
	"\x05_sizeB\x0e\n" +
	"\f_size_formatB\v\n" +
	"\t_link_urlB\x06\n" +
	"\x04_md5B\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"U\n" +
	"\x10ListFileResponse\x12+\n" +
	"\x05items\x18\x01 \x03(\v2\x15.file.service.v1.FileR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\" \n" +
	"\x0eGetFileRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\">\n" +
	"\x11CreateFileRequest\x12)\n" +
	"\x04data\x18\x01 \x01(\v2\x15.file.service.v1.FileR\x04data\"\xfc\x02\n" +
	"\x11UpdateFileRequest\x12)\n" +
	"\x04data\x18\x01 \x01(\v2\x15.file.service.v1.FileR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"#\n" +
	"\x11DeleteFileRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id*\x8a\x01\n" +
	"\vOSSProvider\x12\t\n" +
	"\x05MINIO\x10\x00\x12\n" +
	"\n" +
	"\x06ALIYUN\x10\x01\x12\a\n" +
	"\x03AWS\x10\x02\x12\t\n" +
	"\x05AZURE\x10\x03\x12\t\n" +
	"\x05BAIDU\x10\x04\x12\t\n" +
	"\x05QINIU\x10\x05\x12\v\n" +
	"\aTENCENT\x10\x06\x12\n" +
	"\n" +
	"\x06GOOGLE\x10\a\x12\n" +
	"\n" +
	"\x06HUAWEI\x10\b\x12\n" +
	"\n" +
	"\x06QCLOUD\x10\t\x12\t\n" +
	"\x05LOCAL\x10\n" +
	"2\xee\x02\n" +
	"\vFileService\x12F\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a!.file.service.v1.ListFileResponse\"\x00\x12?\n" +
	"\x03Get\x12\x1f.file.service.v1.GetFileRequest\x1a\x15.file.service.v1.File\"\x00\x12F\n" +
	"\x06Create\x12\".file.service.v1.CreateFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x12F\n" +
	"\x06Update\x12\".file.service.v1.UpdateFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x12F\n" +
	"\x06Delete\x12\".file.service.v1.DeleteFileRequest\x1a\x16.google.protobuf.Empty\"\x00B\xb1\x01\n" +
	"\x13com.file.service.v1B\tFileProtoP\x01Z1kratos-admin/api/gen/go/file/service/v1;servicev1\xa2\x02\x03FSX\xaa\x02\x0fFile.Service.V1\xca\x02\x0fFile\\Service\\V1\xe2\x02\x1bFile\\Service\\V1\\GPBMetadata\xea\x02\x11File::Service::V1b\x06proto3"

var (
	file_file_service_v1_file_proto_rawDescOnce sync.Once
	file_file_service_v1_file_proto_rawDescData []byte
)

func file_file_service_v1_file_proto_rawDescGZIP() []byte {
	file_file_service_v1_file_proto_rawDescOnce.Do(func() {
		file_file_service_v1_file_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_file_service_v1_file_proto_rawDesc), len(file_file_service_v1_file_proto_rawDesc)))
	})
	return file_file_service_v1_file_proto_rawDescData
}

var file_file_service_v1_file_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_file_service_v1_file_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_file_service_v1_file_proto_goTypes = []any{
	(OSSProvider)(0),              // 0: file.service.v1.OSSProvider
	(*File)(nil),                  // 1: file.service.v1.File
	(*ListFileResponse)(nil),      // 2: file.service.v1.ListFileResponse
	(*GetFileRequest)(nil),        // 3: file.service.v1.GetFileRequest
	(*CreateFileRequest)(nil),     // 4: file.service.v1.CreateFileRequest
	(*UpdateFileRequest)(nil),     // 5: file.service.v1.UpdateFileRequest
	(*DeleteFileRequest)(nil),     // 6: file.service.v1.DeleteFileRequest
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil), // 8: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),      // 9: pagination.PagingRequest
	(*emptypb.Empty)(nil),         // 10: google.protobuf.Empty
}
var file_file_service_v1_file_proto_depIdxs = []int32{
	0,  // 0: file.service.v1.File.provider:type_name -> file.service.v1.OSSProvider
	7,  // 1: file.service.v1.File.create_time:type_name -> google.protobuf.Timestamp
	7,  // 2: file.service.v1.File.update_time:type_name -> google.protobuf.Timestamp
	7,  // 3: file.service.v1.File.delete_time:type_name -> google.protobuf.Timestamp
	1,  // 4: file.service.v1.ListFileResponse.items:type_name -> file.service.v1.File
	1,  // 5: file.service.v1.CreateFileRequest.data:type_name -> file.service.v1.File
	1,  // 6: file.service.v1.UpdateFileRequest.data:type_name -> file.service.v1.File
	8,  // 7: file.service.v1.UpdateFileRequest.update_mask:type_name -> google.protobuf.FieldMask
	9,  // 8: file.service.v1.FileService.List:input_type -> pagination.PagingRequest
	3,  // 9: file.service.v1.FileService.Get:input_type -> file.service.v1.GetFileRequest
	4,  // 10: file.service.v1.FileService.Create:input_type -> file.service.v1.CreateFileRequest
	5,  // 11: file.service.v1.FileService.Update:input_type -> file.service.v1.UpdateFileRequest
	6,  // 12: file.service.v1.FileService.Delete:input_type -> file.service.v1.DeleteFileRequest
	2,  // 13: file.service.v1.FileService.List:output_type -> file.service.v1.ListFileResponse
	1,  // 14: file.service.v1.FileService.Get:output_type -> file.service.v1.File
	10, // 15: file.service.v1.FileService.Create:output_type -> google.protobuf.Empty
	10, // 16: file.service.v1.FileService.Update:output_type -> google.protobuf.Empty
	10, // 17: file.service.v1.FileService.Delete:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_file_service_v1_file_proto_init() }
func file_file_service_v1_file_proto_init() {
	if File_file_service_v1_file_proto != nil {
		return
	}
	file_file_service_v1_file_proto_msgTypes[0].OneofWrappers = []any{}
	file_file_service_v1_file_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_file_service_v1_file_proto_rawDesc), len(file_file_service_v1_file_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_file_service_v1_file_proto_goTypes,
		DependencyIndexes: file_file_service_v1_file_proto_depIdxs,
		EnumInfos:         file_file_service_v1_file_proto_enumTypes,
		MessageInfos:      file_file_service_v1_file_proto_msgTypes,
	}.Build()
	File_file_service_v1_file_proto = out.File
	file_file_service_v1_file_proto_goTypes = nil
	file_file_service_v1_file_proto_depIdxs = nil
}
