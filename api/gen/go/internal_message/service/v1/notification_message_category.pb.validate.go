// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: internal_message/service/v1/notification_message_category.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NotificationMessageCategory with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NotificationMessageCategory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotificationMessageCategory with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NotificationMessageCategoryMultiError, or nil if none found.
func (m *NotificationMessageCategory) ValidateAll() error {
	return m.validate(true)
}

func (m *NotificationMessageCategory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageCategoryValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Code != nil {
		// no validation rules for Code
	}

	if m.SortId != nil {
		// no validation rules for SortId
	}

	if m.Enable != nil {
		// no validation rules for Enable
	}

	if m.ParentId != nil {
		// no validation rules for ParentId
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageCategoryValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageCategoryValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageCategoryValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageCategoryValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NotificationMessageCategoryMultiError(errors)
	}

	return nil
}

// NotificationMessageCategoryMultiError is an error wrapping multiple
// validation errors returned by NotificationMessageCategory.ValidateAll() if
// the designated constraints aren't met.
type NotificationMessageCategoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotificationMessageCategoryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotificationMessageCategoryMultiError) AllErrors() []error { return m }

// NotificationMessageCategoryValidationError is the validation error returned
// by NotificationMessageCategory.Validate if the designated constraints
// aren't met.
type NotificationMessageCategoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotificationMessageCategoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotificationMessageCategoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotificationMessageCategoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotificationMessageCategoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotificationMessageCategoryValidationError) ErrorName() string {
	return "NotificationMessageCategoryValidationError"
}

// Error satisfies the builtin error interface
func (e NotificationMessageCategoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotificationMessageCategory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotificationMessageCategoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotificationMessageCategoryValidationError{}

// Validate checks the field values on ListNotificationMessageCategoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListNotificationMessageCategoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListNotificationMessageCategoryResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListNotificationMessageCategoryResponseMultiError, or nil if none found.
func (m *ListNotificationMessageCategoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNotificationMessageCategoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNotificationMessageCategoryResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNotificationMessageCategoryResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNotificationMessageCategoryResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListNotificationMessageCategoryResponseMultiError(errors)
	}

	return nil
}

// ListNotificationMessageCategoryResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListNotificationMessageCategoryResponse.ValidateAll() if the designated
// constraints aren't met.
type ListNotificationMessageCategoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNotificationMessageCategoryResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNotificationMessageCategoryResponseMultiError) AllErrors() []error { return m }

// ListNotificationMessageCategoryResponseValidationError is the validation
// error returned by ListNotificationMessageCategoryResponse.Validate if the
// designated constraints aren't met.
type ListNotificationMessageCategoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNotificationMessageCategoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNotificationMessageCategoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNotificationMessageCategoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNotificationMessageCategoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNotificationMessageCategoryResponseValidationError) ErrorName() string {
	return "ListNotificationMessageCategoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNotificationMessageCategoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNotificationMessageCategoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNotificationMessageCategoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNotificationMessageCategoryResponseValidationError{}

// Validate checks the field values on GetNotificationMessageCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetNotificationMessageCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNotificationMessageCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNotificationMessageCategoryRequestMultiError, or nil if none found.
func (m *GetNotificationMessageCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNotificationMessageCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetNotificationMessageCategoryRequestMultiError(errors)
	}

	return nil
}

// GetNotificationMessageCategoryRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetNotificationMessageCategoryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNotificationMessageCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNotificationMessageCategoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNotificationMessageCategoryRequestMultiError) AllErrors() []error { return m }

// GetNotificationMessageCategoryRequestValidationError is the validation error
// returned by GetNotificationMessageCategoryRequest.Validate if the
// designated constraints aren't met.
type GetNotificationMessageCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNotificationMessageCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNotificationMessageCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNotificationMessageCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNotificationMessageCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNotificationMessageCategoryRequestValidationError) ErrorName() string {
	return "GetNotificationMessageCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNotificationMessageCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNotificationMessageCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNotificationMessageCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNotificationMessageCategoryRequestValidationError{}

// Validate checks the field values on CreateNotificationMessageCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateNotificationMessageCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateNotificationMessageCategoryRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateNotificationMessageCategoryRequestMultiError, or nil if none found.
func (m *CreateNotificationMessageCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNotificationMessageCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNotificationMessageCategoryRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNotificationMessageCategoryRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNotificationMessageCategoryRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNotificationMessageCategoryRequestMultiError(errors)
	}

	return nil
}

// CreateNotificationMessageCategoryRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateNotificationMessageCategoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNotificationMessageCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNotificationMessageCategoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNotificationMessageCategoryRequestMultiError) AllErrors() []error { return m }

// CreateNotificationMessageCategoryRequestValidationError is the validation
// error returned by CreateNotificationMessageCategoryRequest.Validate if the
// designated constraints aren't met.
type CreateNotificationMessageCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNotificationMessageCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNotificationMessageCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNotificationMessageCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNotificationMessageCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNotificationMessageCategoryRequestValidationError) ErrorName() string {
	return "CreateNotificationMessageCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNotificationMessageCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNotificationMessageCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNotificationMessageCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNotificationMessageCategoryRequestValidationError{}

// Validate checks the field values on UpdateNotificationMessageCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateNotificationMessageCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateNotificationMessageCategoryRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateNotificationMessageCategoryRequestMultiError, or nil if none found.
func (m *UpdateNotificationMessageCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNotificationMessageCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotificationMessageCategoryRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotificationMessageCategoryRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotificationMessageCategoryRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotificationMessageCategoryRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotificationMessageCategoryRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotificationMessageCategoryRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateNotificationMessageCategoryRequestMultiError(errors)
	}

	return nil
}

// UpdateNotificationMessageCategoryRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateNotificationMessageCategoryRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNotificationMessageCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNotificationMessageCategoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNotificationMessageCategoryRequestMultiError) AllErrors() []error { return m }

// UpdateNotificationMessageCategoryRequestValidationError is the validation
// error returned by UpdateNotificationMessageCategoryRequest.Validate if the
// designated constraints aren't met.
type UpdateNotificationMessageCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNotificationMessageCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNotificationMessageCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNotificationMessageCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNotificationMessageCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNotificationMessageCategoryRequestValidationError) ErrorName() string {
	return "UpdateNotificationMessageCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNotificationMessageCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNotificationMessageCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNotificationMessageCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNotificationMessageCategoryRequestValidationError{}

// Validate checks the field values on DeleteNotificationMessageCategoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeleteNotificationMessageCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteNotificationMessageCategoryRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DeleteNotificationMessageCategoryRequestMultiError, or nil if none found.
func (m *DeleteNotificationMessageCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteNotificationMessageCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteNotificationMessageCategoryRequestMultiError(errors)
	}

	return nil
}

// DeleteNotificationMessageCategoryRequestMultiError is an error wrapping
// multiple validation errors returned by
// DeleteNotificationMessageCategoryRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteNotificationMessageCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteNotificationMessageCategoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteNotificationMessageCategoryRequestMultiError) AllErrors() []error { return m }

// DeleteNotificationMessageCategoryRequestValidationError is the validation
// error returned by DeleteNotificationMessageCategoryRequest.Validate if the
// designated constraints aren't met.
type DeleteNotificationMessageCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteNotificationMessageCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteNotificationMessageCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteNotificationMessageCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteNotificationMessageCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteNotificationMessageCategoryRequestValidationError) ErrorName() string {
	return "DeleteNotificationMessageCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteNotificationMessageCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteNotificationMessageCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteNotificationMessageCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteNotificationMessageCategoryRequestValidationError{}
