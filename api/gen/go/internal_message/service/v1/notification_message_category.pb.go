// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: internal_message/service/v1/notification_message_category.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通知消息分类
type NotificationMessageCategory struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Id            *uint32                        `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                    // 分类ID
	Name          *string                        `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`                                 // 名称
	Code          *string                        `protobuf:"bytes,3,opt,name=code,proto3,oneof" json:"code,omitempty"`                                 // 编码
	SortId        *int32                         `protobuf:"varint,4,opt,name=sort_id,json=sortId,proto3,oneof" json:"sort_id,omitempty"`              // 排序编号
	Enable        *bool                          `protobuf:"varint,5,opt,name=enable,proto3,oneof" json:"enable,omitempty"`                            // 是否启用
	ParentId      *uint32                        `protobuf:"varint,50,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`       // 父节点ID
	Children      []*NotificationMessageCategory `protobuf:"bytes,51,rep,name=children,proto3" json:"children,omitempty"`                              // 子节点树
	CreateBy      *uint32                        `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`      // 创建者ID
	UpdateBy      *uint32                        `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`      // 更新者ID
	CreateTime    *timestamppb.Timestamp         `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"` // 创建时间
	UpdateTime    *timestamppb.Timestamp         `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"` // 更新时间
	DeleteTime    *timestamppb.Timestamp         `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"` // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationMessageCategory) Reset() {
	*x = NotificationMessageCategory{}
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationMessageCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationMessageCategory) ProtoMessage() {}

func (x *NotificationMessageCategory) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationMessageCategory.ProtoReflect.Descriptor instead.
func (*NotificationMessageCategory) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP(), []int{0}
}

func (x *NotificationMessageCategory) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NotificationMessageCategory) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *NotificationMessageCategory) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *NotificationMessageCategory) GetSortId() int32 {
	if x != nil && x.SortId != nil {
		return *x.SortId
	}
	return 0
}

func (x *NotificationMessageCategory) GetEnable() bool {
	if x != nil && x.Enable != nil {
		return *x.Enable
	}
	return false
}

func (x *NotificationMessageCategory) GetParentId() uint32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *NotificationMessageCategory) GetChildren() []*NotificationMessageCategory {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *NotificationMessageCategory) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *NotificationMessageCategory) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *NotificationMessageCategory) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *NotificationMessageCategory) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *NotificationMessageCategory) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询通知消息分类列表 - 回应
type ListNotificationMessageCategoryResponse struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Items         []*NotificationMessageCategory `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationMessageCategoryResponse) Reset() {
	*x = ListNotificationMessageCategoryResponse{}
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationMessageCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationMessageCategoryResponse) ProtoMessage() {}

func (x *ListNotificationMessageCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationMessageCategoryResponse.ProtoReflect.Descriptor instead.
func (*ListNotificationMessageCategoryResponse) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP(), []int{1}
}

func (x *ListNotificationMessageCategoryResponse) GetItems() []*NotificationMessageCategory {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListNotificationMessageCategoryResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询通知消息分类详情 - 请求
type GetNotificationMessageCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationMessageCategoryRequest) Reset() {
	*x = GetNotificationMessageCategoryRequest{}
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationMessageCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationMessageCategoryRequest) ProtoMessage() {}

func (x *GetNotificationMessageCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationMessageCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationMessageCategoryRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP(), []int{2}
}

func (x *GetNotificationMessageCategoryRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建通知消息分类 - 请求
type CreateNotificationMessageCategoryRequest struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Data          *NotificationMessageCategory `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotificationMessageCategoryRequest) Reset() {
	*x = CreateNotificationMessageCategoryRequest{}
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotificationMessageCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotificationMessageCategoryRequest) ProtoMessage() {}

func (x *CreateNotificationMessageCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotificationMessageCategoryRequest.ProtoReflect.Descriptor instead.
func (*CreateNotificationMessageCategoryRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP(), []int{3}
}

func (x *CreateNotificationMessageCategoryRequest) GetData() *NotificationMessageCategory {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新通知消息分类 - 请求
type UpdateNotificationMessageCategoryRequest struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Data          *NotificationMessageCategory `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask       `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                        `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotificationMessageCategoryRequest) Reset() {
	*x = UpdateNotificationMessageCategoryRequest{}
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationMessageCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationMessageCategoryRequest) ProtoMessage() {}

func (x *UpdateNotificationMessageCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationMessageCategoryRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotificationMessageCategoryRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateNotificationMessageCategoryRequest) GetData() *NotificationMessageCategory {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateNotificationMessageCategoryRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateNotificationMessageCategoryRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除通知消息分类 - 请求
type DeleteNotificationMessageCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationMessageCategoryRequest) Reset() {
	*x = DeleteNotificationMessageCategoryRequest{}
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationMessageCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationMessageCategoryRequest) ProtoMessage() {}

func (x *DeleteNotificationMessageCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_category_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationMessageCategoryRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationMessageCategoryRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteNotificationMessageCategoryRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_internal_message_service_v1_notification_message_category_proto protoreflect.FileDescriptor

const file_internal_message_service_v1_notification_message_category_proto_rawDesc = "" +
	"\n" +
	"?internal_message/service/v1/notification_message_category.proto\x12\x1binternal_message.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\"\x94\a\n" +
	"\x1bNotificationMessageCategory\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b分类IDH\x00R\x02id\x88\x01\x01\x12(\n" +
	"\x04name\x18\x02 \x01(\tB\x0f\xe0A\x01\xbaG\t\x92\x02\x06名称H\x01R\x04name\x88\x01\x01\x12(\n" +
	"\x04code\x18\x03 \x01(\tB\x0f\xe0A\x01\xbaG\t\x92\x02\x06编码H\x02R\x04code\x88\x01\x01\x120\n" +
	"\asort_id\x18\x04 \x01(\x05B\x12\xbaG\x0f\x92\x02\f排序编号H\x03R\x06sortId\x88\x01\x01\x12/\n" +
	"\x06enable\x18\x05 \x01(\bB\x12\xbaG\x0f\x92\x02\f是否启用H\x04R\x06enable\x88\x01\x01\x123\n" +
	"\tparent_id\x182 \x01(\rB\x11\xbaG\x0e\x92\x02\v父节点IDH\x05R\bparentId\x88\x01\x01\x12h\n" +
	"\bchildren\x183 \x03(\v28.internal_message.service.v1.NotificationMessageCategoryB\x12\xbaG\x0f\x92\x02\f子节点树R\bchildren\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\x06R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\aR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\bR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\tR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\n" +
	"R\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\a\n" +
	"\x05_nameB\a\n" +
	"\x05_codeB\n" +
	"\n" +
	"\b_sort_idB\t\n" +
	"\a_enableB\f\n" +
	"\n" +
	"_parent_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"\x8f\x01\n" +
	"'ListNotificationMessageCategoryResponse\x12N\n" +
	"\x05items\x18\x01 \x03(\v28.internal_message.service.v1.NotificationMessageCategoryR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"7\n" +
	"%GetNotificationMessageCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"x\n" +
	"(CreateNotificationMessageCategoryRequest\x12L\n" +
	"\x04data\x18\x01 \x01(\v28.internal_message.service.v1.NotificationMessageCategoryR\x04data\"\xb6\x03\n" +
	"(UpdateNotificationMessageCategoryRequest\x12L\n" +
	"\x04data\x18\x01 \x01(\v28.internal_message.service.v1.NotificationMessageCategoryR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\":\n" +
	"(DeleteNotificationMessageCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\xd8\x04\n" +
	"\"NotificationMessageCategoryService\x12i\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1aD.internal_message.service.v1.ListNotificationMessageCategoryResponse\"\x00\x12\x85\x01\n" +
	"\x03Get\x12B.internal_message.service.v1.GetNotificationMessageCategoryRequest\x1a8.internal_message.service.v1.NotificationMessageCategory\"\x00\x12i\n" +
	"\x06Create\x12E.internal_message.service.v1.CreateNotificationMessageCategoryRequest\x1a\x16.google.protobuf.Empty\"\x00\x12i\n" +
	"\x06Update\x12E.internal_message.service.v1.UpdateNotificationMessageCategoryRequest\x1a\x16.google.protobuf.Empty\"\x00\x12i\n" +
	"\x06Delete\x12E.internal_message.service.v1.DeleteNotificationMessageCategoryRequest\x1a\x16.google.protobuf.Empty\"\x00B\x8c\x02\n" +
	"\x1fcom.internal_message.service.v1B NotificationMessageCategoryProtoP\x01Z=kratos-admin/api/gen/go/internal_message/service/v1;servicev1\xa2\x02\x03ISX\xaa\x02\x1aInternalMessage.Service.V1\xca\x02\x1aInternalMessage\\Service\\V1\xe2\x02&InternalMessage\\Service\\V1\\GPBMetadata\xea\x02\x1cInternalMessage::Service::V1b\x06proto3"

var (
	file_internal_message_service_v1_notification_message_category_proto_rawDescOnce sync.Once
	file_internal_message_service_v1_notification_message_category_proto_rawDescData []byte
)

func file_internal_message_service_v1_notification_message_category_proto_rawDescGZIP() []byte {
	file_internal_message_service_v1_notification_message_category_proto_rawDescOnce.Do(func() {
		file_internal_message_service_v1_notification_message_category_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_notification_message_category_proto_rawDesc), len(file_internal_message_service_v1_notification_message_category_proto_rawDesc)))
	})
	return file_internal_message_service_v1_notification_message_category_proto_rawDescData
}

var file_internal_message_service_v1_notification_message_category_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_internal_message_service_v1_notification_message_category_proto_goTypes = []any{
	(*NotificationMessageCategory)(nil),              // 0: internal_message.service.v1.NotificationMessageCategory
	(*ListNotificationMessageCategoryResponse)(nil),  // 1: internal_message.service.v1.ListNotificationMessageCategoryResponse
	(*GetNotificationMessageCategoryRequest)(nil),    // 2: internal_message.service.v1.GetNotificationMessageCategoryRequest
	(*CreateNotificationMessageCategoryRequest)(nil), // 3: internal_message.service.v1.CreateNotificationMessageCategoryRequest
	(*UpdateNotificationMessageCategoryRequest)(nil), // 4: internal_message.service.v1.UpdateNotificationMessageCategoryRequest
	(*DeleteNotificationMessageCategoryRequest)(nil), // 5: internal_message.service.v1.DeleteNotificationMessageCategoryRequest
	(*timestamppb.Timestamp)(nil),                    // 6: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),                    // 7: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),                         // 8: pagination.PagingRequest
	(*emptypb.Empty)(nil),                            // 9: google.protobuf.Empty
}
var file_internal_message_service_v1_notification_message_category_proto_depIdxs = []int32{
	0,  // 0: internal_message.service.v1.NotificationMessageCategory.children:type_name -> internal_message.service.v1.NotificationMessageCategory
	6,  // 1: internal_message.service.v1.NotificationMessageCategory.create_time:type_name -> google.protobuf.Timestamp
	6,  // 2: internal_message.service.v1.NotificationMessageCategory.update_time:type_name -> google.protobuf.Timestamp
	6,  // 3: internal_message.service.v1.NotificationMessageCategory.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: internal_message.service.v1.ListNotificationMessageCategoryResponse.items:type_name -> internal_message.service.v1.NotificationMessageCategory
	0,  // 5: internal_message.service.v1.CreateNotificationMessageCategoryRequest.data:type_name -> internal_message.service.v1.NotificationMessageCategory
	0,  // 6: internal_message.service.v1.UpdateNotificationMessageCategoryRequest.data:type_name -> internal_message.service.v1.NotificationMessageCategory
	7,  // 7: internal_message.service.v1.UpdateNotificationMessageCategoryRequest.update_mask:type_name -> google.protobuf.FieldMask
	8,  // 8: internal_message.service.v1.NotificationMessageCategoryService.List:input_type -> pagination.PagingRequest
	2,  // 9: internal_message.service.v1.NotificationMessageCategoryService.Get:input_type -> internal_message.service.v1.GetNotificationMessageCategoryRequest
	3,  // 10: internal_message.service.v1.NotificationMessageCategoryService.Create:input_type -> internal_message.service.v1.CreateNotificationMessageCategoryRequest
	4,  // 11: internal_message.service.v1.NotificationMessageCategoryService.Update:input_type -> internal_message.service.v1.UpdateNotificationMessageCategoryRequest
	5,  // 12: internal_message.service.v1.NotificationMessageCategoryService.Delete:input_type -> internal_message.service.v1.DeleteNotificationMessageCategoryRequest
	1,  // 13: internal_message.service.v1.NotificationMessageCategoryService.List:output_type -> internal_message.service.v1.ListNotificationMessageCategoryResponse
	0,  // 14: internal_message.service.v1.NotificationMessageCategoryService.Get:output_type -> internal_message.service.v1.NotificationMessageCategory
	9,  // 15: internal_message.service.v1.NotificationMessageCategoryService.Create:output_type -> google.protobuf.Empty
	9,  // 16: internal_message.service.v1.NotificationMessageCategoryService.Update:output_type -> google.protobuf.Empty
	9,  // 17: internal_message.service.v1.NotificationMessageCategoryService.Delete:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_internal_message_service_v1_notification_message_category_proto_init() }
func file_internal_message_service_v1_notification_message_category_proto_init() {
	if File_internal_message_service_v1_notification_message_category_proto != nil {
		return
	}
	file_internal_message_service_v1_notification_message_category_proto_msgTypes[0].OneofWrappers = []any{}
	file_internal_message_service_v1_notification_message_category_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_notification_message_category_proto_rawDesc), len(file_internal_message_service_v1_notification_message_category_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_message_service_v1_notification_message_category_proto_goTypes,
		DependencyIndexes: file_internal_message_service_v1_notification_message_category_proto_depIdxs,
		MessageInfos:      file_internal_message_service_v1_notification_message_category_proto_msgTypes,
	}.Build()
	File_internal_message_service_v1_notification_message_category_proto = out.File
	file_internal_message_service_v1_notification_message_category_proto_goTypes = nil
	file_internal_message_service_v1_notification_message_category_proto_depIdxs = nil
}
