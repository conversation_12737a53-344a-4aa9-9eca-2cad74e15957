// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: internal_message/service/v1/notification_message_recipient.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NotificationMessageRecipient with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NotificationMessageRecipient) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotificationMessageRecipient with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NotificationMessageRecipientMultiError, or nil if none found.
func (m *NotificationMessageRecipient) ValidateAll() error {
	return m.validate(true)
}

func (m *NotificationMessageRecipient) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.MessageId != nil {
		// no validation rules for MessageId
	}

	if m.RecipientId != nil {
		// no validation rules for RecipientId
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageRecipientValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageRecipientValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageRecipientValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageRecipientValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageRecipientValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageRecipientValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageRecipientValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageRecipientValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageRecipientValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NotificationMessageRecipientMultiError(errors)
	}

	return nil
}

// NotificationMessageRecipientMultiError is an error wrapping multiple
// validation errors returned by NotificationMessageRecipient.ValidateAll() if
// the designated constraints aren't met.
type NotificationMessageRecipientMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotificationMessageRecipientMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotificationMessageRecipientMultiError) AllErrors() []error { return m }

// NotificationMessageRecipientValidationError is the validation error returned
// by NotificationMessageRecipient.Validate if the designated constraints
// aren't met.
type NotificationMessageRecipientValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotificationMessageRecipientValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotificationMessageRecipientValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotificationMessageRecipientValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotificationMessageRecipientValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotificationMessageRecipientValidationError) ErrorName() string {
	return "NotificationMessageRecipientValidationError"
}

// Error satisfies the builtin error interface
func (e NotificationMessageRecipientValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotificationMessageRecipient.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotificationMessageRecipientValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotificationMessageRecipientValidationError{}

// Validate checks the field values on ListNotificationMessageRecipientResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListNotificationMessageRecipientResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListNotificationMessageRecipientResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListNotificationMessageRecipientResponseMultiError, or nil if none found.
func (m *ListNotificationMessageRecipientResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNotificationMessageRecipientResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNotificationMessageRecipientResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNotificationMessageRecipientResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNotificationMessageRecipientResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListNotificationMessageRecipientResponseMultiError(errors)
	}

	return nil
}

// ListNotificationMessageRecipientResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListNotificationMessageRecipientResponse.ValidateAll() if the designated
// constraints aren't met.
type ListNotificationMessageRecipientResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNotificationMessageRecipientResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNotificationMessageRecipientResponseMultiError) AllErrors() []error { return m }

// ListNotificationMessageRecipientResponseValidationError is the validation
// error returned by ListNotificationMessageRecipientResponse.Validate if the
// designated constraints aren't met.
type ListNotificationMessageRecipientResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNotificationMessageRecipientResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNotificationMessageRecipientResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNotificationMessageRecipientResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNotificationMessageRecipientResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNotificationMessageRecipientResponseValidationError) ErrorName() string {
	return "ListNotificationMessageRecipientResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNotificationMessageRecipientResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNotificationMessageRecipientResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNotificationMessageRecipientResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNotificationMessageRecipientResponseValidationError{}

// Validate checks the field values on GetNotificationMessageRecipientRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetNotificationMessageRecipientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNotificationMessageRecipientRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetNotificationMessageRecipientRequestMultiError, or nil if none found.
func (m *GetNotificationMessageRecipientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNotificationMessageRecipientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetNotificationMessageRecipientRequestMultiError(errors)
	}

	return nil
}

// GetNotificationMessageRecipientRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetNotificationMessageRecipientRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNotificationMessageRecipientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNotificationMessageRecipientRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNotificationMessageRecipientRequestMultiError) AllErrors() []error { return m }

// GetNotificationMessageRecipientRequestValidationError is the validation
// error returned by GetNotificationMessageRecipientRequest.Validate if the
// designated constraints aren't met.
type GetNotificationMessageRecipientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNotificationMessageRecipientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNotificationMessageRecipientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNotificationMessageRecipientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNotificationMessageRecipientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNotificationMessageRecipientRequestValidationError) ErrorName() string {
	return "GetNotificationMessageRecipientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNotificationMessageRecipientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNotificationMessageRecipientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNotificationMessageRecipientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNotificationMessageRecipientRequestValidationError{}

// Validate checks the field values on
// CreateNotificationMessageRecipientRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateNotificationMessageRecipientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateNotificationMessageRecipientRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateNotificationMessageRecipientRequestMultiError, or nil if none found.
func (m *CreateNotificationMessageRecipientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNotificationMessageRecipientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNotificationMessageRecipientRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNotificationMessageRecipientRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNotificationMessageRecipientRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNotificationMessageRecipientRequestMultiError(errors)
	}

	return nil
}

// CreateNotificationMessageRecipientRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateNotificationMessageRecipientRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNotificationMessageRecipientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNotificationMessageRecipientRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNotificationMessageRecipientRequestMultiError) AllErrors() []error { return m }

// CreateNotificationMessageRecipientRequestValidationError is the validation
// error returned by CreateNotificationMessageRecipientRequest.Validate if the
// designated constraints aren't met.
type CreateNotificationMessageRecipientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNotificationMessageRecipientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNotificationMessageRecipientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNotificationMessageRecipientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNotificationMessageRecipientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNotificationMessageRecipientRequestValidationError) ErrorName() string {
	return "CreateNotificationMessageRecipientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNotificationMessageRecipientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNotificationMessageRecipientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNotificationMessageRecipientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNotificationMessageRecipientRequestValidationError{}

// Validate checks the field values on
// UpdateNotificationMessageRecipientRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateNotificationMessageRecipientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateNotificationMessageRecipientRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateNotificationMessageRecipientRequestMultiError, or nil if none found.
func (m *UpdateNotificationMessageRecipientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNotificationMessageRecipientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotificationMessageRecipientRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotificationMessageRecipientRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotificationMessageRecipientRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotificationMessageRecipientRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotificationMessageRecipientRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotificationMessageRecipientRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateNotificationMessageRecipientRequestMultiError(errors)
	}

	return nil
}

// UpdateNotificationMessageRecipientRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateNotificationMessageRecipientRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNotificationMessageRecipientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNotificationMessageRecipientRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNotificationMessageRecipientRequestMultiError) AllErrors() []error { return m }

// UpdateNotificationMessageRecipientRequestValidationError is the validation
// error returned by UpdateNotificationMessageRecipientRequest.Validate if the
// designated constraints aren't met.
type UpdateNotificationMessageRecipientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNotificationMessageRecipientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNotificationMessageRecipientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNotificationMessageRecipientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNotificationMessageRecipientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNotificationMessageRecipientRequestValidationError) ErrorName() string {
	return "UpdateNotificationMessageRecipientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNotificationMessageRecipientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNotificationMessageRecipientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNotificationMessageRecipientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNotificationMessageRecipientRequestValidationError{}

// Validate checks the field values on
// DeleteNotificationMessageRecipientRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteNotificationMessageRecipientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteNotificationMessageRecipientRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DeleteNotificationMessageRecipientRequestMultiError, or nil if none found.
func (m *DeleteNotificationMessageRecipientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteNotificationMessageRecipientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteNotificationMessageRecipientRequestMultiError(errors)
	}

	return nil
}

// DeleteNotificationMessageRecipientRequestMultiError is an error wrapping
// multiple validation errors returned by
// DeleteNotificationMessageRecipientRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteNotificationMessageRecipientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteNotificationMessageRecipientRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteNotificationMessageRecipientRequestMultiError) AllErrors() []error { return m }

// DeleteNotificationMessageRecipientRequestValidationError is the validation
// error returned by DeleteNotificationMessageRecipientRequest.Validate if the
// designated constraints aren't met.
type DeleteNotificationMessageRecipientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteNotificationMessageRecipientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteNotificationMessageRecipientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteNotificationMessageRecipientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteNotificationMessageRecipientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteNotificationMessageRecipientRequestValidationError) ErrorName() string {
	return "DeleteNotificationMessageRecipientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteNotificationMessageRecipientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteNotificationMessageRecipientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteNotificationMessageRecipientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteNotificationMessageRecipientRequestValidationError{}
