// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: internal_message/service/v1/notification_message_recipient.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通知消息接收者
type NotificationMessageRecipient struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                        // 记录ID
	MessageId     *uint32                `protobuf:"varint,2,opt,name=message_id,json=messageId,proto3,oneof" json:"message_id,omitempty"`                         // 群发消息ID
	RecipientId   *uint32                `protobuf:"varint,3,opt,name=recipient_id,json=recipientId,proto3,oneof" json:"recipient_id,omitempty"`                   // 接收者用户ID
	Status        *MessageStatus         `protobuf:"varint,4,opt,name=status,proto3,enum=internal_message.service.v1.MessageStatus,oneof" json:"status,omitempty"` // 消息状态
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                          // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                          // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                     // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                     // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                     // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationMessageRecipient) Reset() {
	*x = NotificationMessageRecipient{}
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationMessageRecipient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationMessageRecipient) ProtoMessage() {}

func (x *NotificationMessageRecipient) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationMessageRecipient.ProtoReflect.Descriptor instead.
func (*NotificationMessageRecipient) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP(), []int{0}
}

func (x *NotificationMessageRecipient) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NotificationMessageRecipient) GetMessageId() uint32 {
	if x != nil && x.MessageId != nil {
		return *x.MessageId
	}
	return 0
}

func (x *NotificationMessageRecipient) GetRecipientId() uint32 {
	if x != nil && x.RecipientId != nil {
		return *x.RecipientId
	}
	return 0
}

func (x *NotificationMessageRecipient) GetStatus() MessageStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return MessageStatus_MessageStatus_Unknown
}

func (x *NotificationMessageRecipient) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *NotificationMessageRecipient) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *NotificationMessageRecipient) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *NotificationMessageRecipient) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *NotificationMessageRecipient) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询通知消息接收者列表 - 回应
type ListNotificationMessageRecipientResponse struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Items         []*NotificationMessageRecipient `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationMessageRecipientResponse) Reset() {
	*x = ListNotificationMessageRecipientResponse{}
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationMessageRecipientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationMessageRecipientResponse) ProtoMessage() {}

func (x *ListNotificationMessageRecipientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationMessageRecipientResponse.ProtoReflect.Descriptor instead.
func (*ListNotificationMessageRecipientResponse) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP(), []int{1}
}

func (x *ListNotificationMessageRecipientResponse) GetItems() []*NotificationMessageRecipient {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListNotificationMessageRecipientResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询通知消息接收者详情 - 请求
type GetNotificationMessageRecipientRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationMessageRecipientRequest) Reset() {
	*x = GetNotificationMessageRecipientRequest{}
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationMessageRecipientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationMessageRecipientRequest) ProtoMessage() {}

func (x *GetNotificationMessageRecipientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationMessageRecipientRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationMessageRecipientRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP(), []int{2}
}

func (x *GetNotificationMessageRecipientRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建通知消息接收者 - 请求
type CreateNotificationMessageRecipientRequest struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Data          *NotificationMessageRecipient `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotificationMessageRecipientRequest) Reset() {
	*x = CreateNotificationMessageRecipientRequest{}
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotificationMessageRecipientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotificationMessageRecipientRequest) ProtoMessage() {}

func (x *CreateNotificationMessageRecipientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotificationMessageRecipientRequest.ProtoReflect.Descriptor instead.
func (*CreateNotificationMessageRecipientRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP(), []int{3}
}

func (x *CreateNotificationMessageRecipientRequest) GetData() *NotificationMessageRecipient {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新通知消息接收者 - 请求
type UpdateNotificationMessageRecipientRequest struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Data          *NotificationMessageRecipient `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask        `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                         `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotificationMessageRecipientRequest) Reset() {
	*x = UpdateNotificationMessageRecipientRequest{}
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationMessageRecipientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationMessageRecipientRequest) ProtoMessage() {}

func (x *UpdateNotificationMessageRecipientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationMessageRecipientRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotificationMessageRecipientRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateNotificationMessageRecipientRequest) GetData() *NotificationMessageRecipient {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateNotificationMessageRecipientRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateNotificationMessageRecipientRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除通知消息接收者 - 请求
type DeleteNotificationMessageRecipientRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationMessageRecipientRequest) Reset() {
	*x = DeleteNotificationMessageRecipientRequest{}
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationMessageRecipientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationMessageRecipientRequest) ProtoMessage() {}

func (x *DeleteNotificationMessageRecipientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationMessageRecipientRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationMessageRecipientRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteNotificationMessageRecipientRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_internal_message_service_v1_notification_message_recipient_proto protoreflect.FileDescriptor

const file_internal_message_service_v1_notification_message_recipient_proto_rawDesc = "" +
	"\n" +
	"@internal_message/service/v1/notification_message_recipient.proto\x12\x1binternal_message.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\x1a)internal_message/service/v1/message.proto\"\x91\x06\n" +
	"\x1cNotificationMessageRecipient\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b记录IDH\x00R\x02id\x88\x01\x01\x12;\n" +
	"\n" +
	"message_id\x18\x02 \x01(\rB\x17\xe0A\x01\xbaG\x11\x92\x02\x0e群发消息IDH\x01R\tmessageId\x88\x01\x01\x12B\n" +
	"\frecipient_id\x18\x03 \x01(\rB\x1a\xe0A\x01\xbaG\x14\x92\x02\x11接收者用户IDH\x02R\vrecipientId\x88\x01\x01\x12[\n" +
	"\x06status\x18\x04 \x01(\x0e2*.internal_message.service.v1.MessageStatusB\x12\xbaG\x0f\x92\x02\f消息状态H\x03R\x06status\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\x04R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\x05R\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\x06R\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\aR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\bR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\r\n" +
	"\v_message_idB\x0f\n" +
	"\r_recipient_idB\t\n" +
	"\a_statusB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"\x91\x01\n" +
	"(ListNotificationMessageRecipientResponse\x12O\n" +
	"\x05items\x18\x01 \x03(\v29.internal_message.service.v1.NotificationMessageRecipientR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"8\n" +
	"&GetNotificationMessageRecipientRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"z\n" +
	")CreateNotificationMessageRecipientRequest\x12M\n" +
	"\x04data\x18\x01 \x01(\v29.internal_message.service.v1.NotificationMessageRecipientR\x04data\"\xb8\x03\n" +
	")UpdateNotificationMessageRecipientRequest\x12M\n" +
	"\x04data\x18\x01 \x01(\v29.internal_message.service.v1.NotificationMessageRecipientR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\";\n" +
	")DeleteNotificationMessageRecipientRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\xdf\x04\n" +
	"#NotificationMessageRecipientService\x12j\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1aE.internal_message.service.v1.ListNotificationMessageRecipientResponse\"\x00\x12\x87\x01\n" +
	"\x03Get\x12C.internal_message.service.v1.GetNotificationMessageRecipientRequest\x1a9.internal_message.service.v1.NotificationMessageRecipient\"\x00\x12j\n" +
	"\x06Create\x12F.internal_message.service.v1.CreateNotificationMessageRecipientRequest\x1a\x16.google.protobuf.Empty\"\x00\x12j\n" +
	"\x06Update\x12F.internal_message.service.v1.UpdateNotificationMessageRecipientRequest\x1a\x16.google.protobuf.Empty\"\x00\x12j\n" +
	"\x06Delete\x12F.internal_message.service.v1.DeleteNotificationMessageRecipientRequest\x1a\x16.google.protobuf.Empty\"\x00B\x8d\x02\n" +
	"\x1fcom.internal_message.service.v1B!NotificationMessageRecipientProtoP\x01Z=kratos-admin/api/gen/go/internal_message/service/v1;servicev1\xa2\x02\x03ISX\xaa\x02\x1aInternalMessage.Service.V1\xca\x02\x1aInternalMessage\\Service\\V1\xe2\x02&InternalMessage\\Service\\V1\\GPBMetadata\xea\x02\x1cInternalMessage::Service::V1b\x06proto3"

var (
	file_internal_message_service_v1_notification_message_recipient_proto_rawDescOnce sync.Once
	file_internal_message_service_v1_notification_message_recipient_proto_rawDescData []byte
)

func file_internal_message_service_v1_notification_message_recipient_proto_rawDescGZIP() []byte {
	file_internal_message_service_v1_notification_message_recipient_proto_rawDescOnce.Do(func() {
		file_internal_message_service_v1_notification_message_recipient_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_notification_message_recipient_proto_rawDesc), len(file_internal_message_service_v1_notification_message_recipient_proto_rawDesc)))
	})
	return file_internal_message_service_v1_notification_message_recipient_proto_rawDescData
}

var file_internal_message_service_v1_notification_message_recipient_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_internal_message_service_v1_notification_message_recipient_proto_goTypes = []any{
	(*NotificationMessageRecipient)(nil),              // 0: internal_message.service.v1.NotificationMessageRecipient
	(*ListNotificationMessageRecipientResponse)(nil),  // 1: internal_message.service.v1.ListNotificationMessageRecipientResponse
	(*GetNotificationMessageRecipientRequest)(nil),    // 2: internal_message.service.v1.GetNotificationMessageRecipientRequest
	(*CreateNotificationMessageRecipientRequest)(nil), // 3: internal_message.service.v1.CreateNotificationMessageRecipientRequest
	(*UpdateNotificationMessageRecipientRequest)(nil), // 4: internal_message.service.v1.UpdateNotificationMessageRecipientRequest
	(*DeleteNotificationMessageRecipientRequest)(nil), // 5: internal_message.service.v1.DeleteNotificationMessageRecipientRequest
	(MessageStatus)(0),            // 6: internal_message.service.v1.MessageStatus
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil), // 8: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),      // 9: pagination.PagingRequest
	(*emptypb.Empty)(nil),         // 10: google.protobuf.Empty
}
var file_internal_message_service_v1_notification_message_recipient_proto_depIdxs = []int32{
	6,  // 0: internal_message.service.v1.NotificationMessageRecipient.status:type_name -> internal_message.service.v1.MessageStatus
	7,  // 1: internal_message.service.v1.NotificationMessageRecipient.create_time:type_name -> google.protobuf.Timestamp
	7,  // 2: internal_message.service.v1.NotificationMessageRecipient.update_time:type_name -> google.protobuf.Timestamp
	7,  // 3: internal_message.service.v1.NotificationMessageRecipient.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: internal_message.service.v1.ListNotificationMessageRecipientResponse.items:type_name -> internal_message.service.v1.NotificationMessageRecipient
	0,  // 5: internal_message.service.v1.CreateNotificationMessageRecipientRequest.data:type_name -> internal_message.service.v1.NotificationMessageRecipient
	0,  // 6: internal_message.service.v1.UpdateNotificationMessageRecipientRequest.data:type_name -> internal_message.service.v1.NotificationMessageRecipient
	8,  // 7: internal_message.service.v1.UpdateNotificationMessageRecipientRequest.update_mask:type_name -> google.protobuf.FieldMask
	9,  // 8: internal_message.service.v1.NotificationMessageRecipientService.List:input_type -> pagination.PagingRequest
	2,  // 9: internal_message.service.v1.NotificationMessageRecipientService.Get:input_type -> internal_message.service.v1.GetNotificationMessageRecipientRequest
	3,  // 10: internal_message.service.v1.NotificationMessageRecipientService.Create:input_type -> internal_message.service.v1.CreateNotificationMessageRecipientRequest
	4,  // 11: internal_message.service.v1.NotificationMessageRecipientService.Update:input_type -> internal_message.service.v1.UpdateNotificationMessageRecipientRequest
	5,  // 12: internal_message.service.v1.NotificationMessageRecipientService.Delete:input_type -> internal_message.service.v1.DeleteNotificationMessageRecipientRequest
	1,  // 13: internal_message.service.v1.NotificationMessageRecipientService.List:output_type -> internal_message.service.v1.ListNotificationMessageRecipientResponse
	0,  // 14: internal_message.service.v1.NotificationMessageRecipientService.Get:output_type -> internal_message.service.v1.NotificationMessageRecipient
	10, // 15: internal_message.service.v1.NotificationMessageRecipientService.Create:output_type -> google.protobuf.Empty
	10, // 16: internal_message.service.v1.NotificationMessageRecipientService.Update:output_type -> google.protobuf.Empty
	10, // 17: internal_message.service.v1.NotificationMessageRecipientService.Delete:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_internal_message_service_v1_notification_message_recipient_proto_init() }
func file_internal_message_service_v1_notification_message_recipient_proto_init() {
	if File_internal_message_service_v1_notification_message_recipient_proto != nil {
		return
	}
	file_internal_message_service_v1_message_proto_init()
	file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[0].OneofWrappers = []any{}
	file_internal_message_service_v1_notification_message_recipient_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_notification_message_recipient_proto_rawDesc), len(file_internal_message_service_v1_notification_message_recipient_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_message_service_v1_notification_message_recipient_proto_goTypes,
		DependencyIndexes: file_internal_message_service_v1_notification_message_recipient_proto_depIdxs,
		MessageInfos:      file_internal_message_service_v1_notification_message_recipient_proto_msgTypes,
	}.Build()
	File_internal_message_service_v1_notification_message_recipient_proto = out.File
	file_internal_message_service_v1_notification_message_recipient_proto_goTypes = nil
	file_internal_message_service_v1_notification_message_recipient_proto_depIdxs = nil
}
