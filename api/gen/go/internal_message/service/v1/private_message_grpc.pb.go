// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: internal_message/service/v1/private_message.proto

package servicev1

import (
	context "context"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PrivateMessageService_List_FullMethodName   = "/internal_message.service.v1.PrivateMessageService/List"
	PrivateMessageService_Get_FullMethodName    = "/internal_message.service.v1.PrivateMessageService/Get"
	PrivateMessageService_Create_FullMethodName = "/internal_message.service.v1.PrivateMessageService/Create"
	PrivateMessageService_Update_FullMethodName = "/internal_message.service.v1.PrivateMessageService/Update"
	PrivateMessageService_Delete_FullMethodName = "/internal_message.service.v1.PrivateMessageService/Delete"
)

// PrivateMessageServiceClient is the client API for PrivateMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 私信消息服务
type PrivateMessageServiceClient interface {
	// 查询私信消息列表
	List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListPrivateMessageResponse, error)
	// 查询私信消息详情
	Get(ctx context.Context, in *GetPrivateMessageRequest, opts ...grpc.CallOption) (*PrivateMessage, error)
	// 创建私信消息
	Create(ctx context.Context, in *CreatePrivateMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新私信消息
	Update(ctx context.Context, in *UpdatePrivateMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除私信消息
	Delete(ctx context.Context, in *DeletePrivateMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type privateMessageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPrivateMessageServiceClient(cc grpc.ClientConnInterface) PrivateMessageServiceClient {
	return &privateMessageServiceClient{cc}
}

func (c *privateMessageServiceClient) List(ctx context.Context, in *v1.PagingRequest, opts ...grpc.CallOption) (*ListPrivateMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPrivateMessageResponse)
	err := c.cc.Invoke(ctx, PrivateMessageService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privateMessageServiceClient) Get(ctx context.Context, in *GetPrivateMessageRequest, opts ...grpc.CallOption) (*PrivateMessage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrivateMessage)
	err := c.cc.Invoke(ctx, PrivateMessageService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privateMessageServiceClient) Create(ctx context.Context, in *CreatePrivateMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PrivateMessageService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privateMessageServiceClient) Update(ctx context.Context, in *UpdatePrivateMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PrivateMessageService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privateMessageServiceClient) Delete(ctx context.Context, in *DeletePrivateMessageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PrivateMessageService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrivateMessageServiceServer is the server API for PrivateMessageService service.
// All implementations must embed UnimplementedPrivateMessageServiceServer
// for forward compatibility.
//
// 私信消息服务
type PrivateMessageServiceServer interface {
	// 查询私信消息列表
	List(context.Context, *v1.PagingRequest) (*ListPrivateMessageResponse, error)
	// 查询私信消息详情
	Get(context.Context, *GetPrivateMessageRequest) (*PrivateMessage, error)
	// 创建私信消息
	Create(context.Context, *CreatePrivateMessageRequest) (*emptypb.Empty, error)
	// 更新私信消息
	Update(context.Context, *UpdatePrivateMessageRequest) (*emptypb.Empty, error)
	// 删除私信消息
	Delete(context.Context, *DeletePrivateMessageRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedPrivateMessageServiceServer()
}

// UnimplementedPrivateMessageServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPrivateMessageServiceServer struct{}

func (UnimplementedPrivateMessageServiceServer) List(context.Context, *v1.PagingRequest) (*ListPrivateMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedPrivateMessageServiceServer) Get(context.Context, *GetPrivateMessageRequest) (*PrivateMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedPrivateMessageServiceServer) Create(context.Context, *CreatePrivateMessageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedPrivateMessageServiceServer) Update(context.Context, *UpdatePrivateMessageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedPrivateMessageServiceServer) Delete(context.Context, *DeletePrivateMessageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedPrivateMessageServiceServer) mustEmbedUnimplementedPrivateMessageServiceServer() {}
func (UnimplementedPrivateMessageServiceServer) testEmbeddedByValue()                               {}

// UnsafePrivateMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrivateMessageServiceServer will
// result in compilation errors.
type UnsafePrivateMessageServiceServer interface {
	mustEmbedUnimplementedPrivateMessageServiceServer()
}

func RegisterPrivateMessageServiceServer(s grpc.ServiceRegistrar, srv PrivateMessageServiceServer) {
	// If the following call pancis, it indicates UnimplementedPrivateMessageServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PrivateMessageService_ServiceDesc, srv)
}

func _PrivateMessageService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PagingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivateMessageServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrivateMessageService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivateMessageServiceServer).List(ctx, req.(*v1.PagingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrivateMessageService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrivateMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivateMessageServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrivateMessageService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivateMessageServiceServer).Get(ctx, req.(*GetPrivateMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrivateMessageService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePrivateMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivateMessageServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrivateMessageService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivateMessageServiceServer).Create(ctx, req.(*CreatePrivateMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrivateMessageService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePrivateMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivateMessageServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrivateMessageService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivateMessageServiceServer).Update(ctx, req.(*UpdatePrivateMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrivateMessageService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePrivateMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivateMessageServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrivateMessageService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivateMessageServiceServer).Delete(ctx, req.(*DeletePrivateMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PrivateMessageService_ServiceDesc is the grpc.ServiceDesc for PrivateMessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PrivateMessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "internal_message.service.v1.PrivateMessageService",
	HandlerType: (*PrivateMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _PrivateMessageService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _PrivateMessageService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _PrivateMessageService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrivateMessageService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrivateMessageService_Delete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "internal_message/service/v1/private_message.proto",
}
