// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: internal_message/service/v1/notification_message.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通知消息
type NotificationMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                        // 消息ID
	Subject       *string                `protobuf:"bytes,2,opt,name=subject,proto3,oneof" json:"subject,omitempty"`                                               // 主题
	Content       *string                `protobuf:"bytes,3,opt,name=content,proto3,oneof" json:"content,omitempty"`                                               // 内容
	Status        *MessageStatus         `protobuf:"varint,4,opt,name=status,proto3,enum=internal_message.service.v1.MessageStatus,oneof" json:"status,omitempty"` // 消息状态
	CategoryId    *uint32                `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`                      // 分类ID
	CategoryName  *string                `protobuf:"bytes,6,opt,name=category_name,json=categoryName,proto3,oneof" json:"category_name,omitempty"`                 // 分类名称
	CreateBy      *uint32                `protobuf:"varint,100,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`                          // 创建者ID
	UpdateBy      *uint32                `protobuf:"varint,101,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`                          // 更新者ID
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                     // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                     // 更新时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                     // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationMessage) Reset() {
	*x = NotificationMessage{}
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationMessage) ProtoMessage() {}

func (x *NotificationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationMessage.ProtoReflect.Descriptor instead.
func (*NotificationMessage) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_proto_rawDescGZIP(), []int{0}
}

func (x *NotificationMessage) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NotificationMessage) GetSubject() string {
	if x != nil && x.Subject != nil {
		return *x.Subject
	}
	return ""
}

func (x *NotificationMessage) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *NotificationMessage) GetStatus() MessageStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return MessageStatus_MessageStatus_Unknown
}

func (x *NotificationMessage) GetCategoryId() uint32 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *NotificationMessage) GetCategoryName() string {
	if x != nil && x.CategoryName != nil {
		return *x.CategoryName
	}
	return ""
}

func (x *NotificationMessage) GetCreateBy() uint32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *NotificationMessage) GetUpdateBy() uint32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *NotificationMessage) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *NotificationMessage) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *NotificationMessage) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询通知消息列表 - 回应
type ListNotificationMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*NotificationMessage `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationMessageResponse) Reset() {
	*x = ListNotificationMessageResponse{}
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationMessageResponse) ProtoMessage() {}

func (x *ListNotificationMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationMessageResponse.ProtoReflect.Descriptor instead.
func (*ListNotificationMessageResponse) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_proto_rawDescGZIP(), []int{1}
}

func (x *ListNotificationMessageResponse) GetItems() []*NotificationMessage {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListNotificationMessageResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询通知消息详情 - 请求
type GetNotificationMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationMessageRequest) Reset() {
	*x = GetNotificationMessageRequest{}
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationMessageRequest) ProtoMessage() {}

func (x *GetNotificationMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationMessageRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_proto_rawDescGZIP(), []int{2}
}

func (x *GetNotificationMessageRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建通知消息 - 请求
type CreateNotificationMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *NotificationMessage   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotificationMessageRequest) Reset() {
	*x = CreateNotificationMessageRequest{}
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotificationMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotificationMessageRequest) ProtoMessage() {}

func (x *CreateNotificationMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotificationMessageRequest.ProtoReflect.Descriptor instead.
func (*CreateNotificationMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_proto_rawDescGZIP(), []int{3}
}

func (x *CreateNotificationMessageRequest) GetData() *NotificationMessage {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新通知消息 - 请求
type UpdateNotificationMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *NotificationMessage   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotificationMessageRequest) Reset() {
	*x = UpdateNotificationMessageRequest{}
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationMessageRequest) ProtoMessage() {}

func (x *UpdateNotificationMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationMessageRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotificationMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateNotificationMessageRequest) GetData() *NotificationMessage {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateNotificationMessageRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdateNotificationMessageRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除通知消息 - 请求
type DeleteNotificationMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationMessageRequest) Reset() {
	*x = DeleteNotificationMessageRequest{}
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationMessageRequest) ProtoMessage() {}

func (x *DeleteNotificationMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_notification_message_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationMessageRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_notification_message_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteNotificationMessageRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_internal_message_service_v1_notification_message_proto protoreflect.FileDescriptor

const file_internal_message_service_v1_notification_message_proto_rawDesc = "" +
	"\n" +
	"6internal_message/service/v1/notification_message.proto\x12\x1binternal_message.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\x1a)internal_message/service/v1/message.proto\"\xef\x06\n" +
	"\x13NotificationMessage\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b消息IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\asubject\x18\x02 \x01(\tB\f\xbaG\t\x92\x02\x06主题H\x01R\asubject\x88\x01\x01\x12+\n" +
	"\acontent\x18\x03 \x01(\tB\f\xbaG\t\x92\x02\x06内容H\x02R\acontent\x88\x01\x01\x12[\n" +
	"\x06status\x18\x04 \x01(\x0e2*.internal_message.service.v1.MessageStatusB\x12\xbaG\x0f\x92\x02\f消息状态H\x03R\x06status\x88\x01\x01\x124\n" +
	"\vcategory_id\x18\x05 \x01(\rB\x0e\xbaG\v\x92\x02\b分类IDH\x04R\n" +
	"categoryId\x88\x01\x01\x12<\n" +
	"\rcategory_name\x18\x06 \x01(\tB\x12\xbaG\x0f\x92\x02\f分类名称H\x05R\fcategoryName\x88\x01\x01\x123\n" +
	"\tcreate_by\x18d \x01(\rB\x11\xbaG\x0e\x92\x02\v创建者IDH\x06R\bcreateBy\x88\x01\x01\x123\n" +
	"\tupdate_by\x18e \x01(\rB\x11\xbaG\x0e\x92\x02\v更新者IDH\aR\bupdateBy\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\bR\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\tR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\n" +
	"R\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\n" +
	"\n" +
	"\b_subjectB\n" +
	"\n" +
	"\b_contentB\t\n" +
	"\a_statusB\x0e\n" +
	"\f_category_idB\x10\n" +
	"\x0e_category_nameB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"\x7f\n" +
	"\x1fListNotificationMessageResponse\x12F\n" +
	"\x05items\x18\x01 \x03(\v20.internal_message.service.v1.NotificationMessageR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"/\n" +
	"\x1dGetNotificationMessageRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"h\n" +
	" CreateNotificationMessageRequest\x12D\n" +
	"\x04data\x18\x01 \x01(\v20.internal_message.service.v1.NotificationMessageR\x04data\"\xa6\x03\n" +
	" UpdateNotificationMessageRequest\x12D\n" +
	"\x04data\x18\x01 \x01(\v20.internal_message.service.v1.NotificationMessageR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"2\n" +
	" DeleteNotificationMessageRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\x9f\x04\n" +
	"\x1aNotificationMessageService\x12a\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a<.internal_message.service.v1.ListNotificationMessageResponse\"\x00\x12u\n" +
	"\x03Get\x12:.internal_message.service.v1.GetNotificationMessageRequest\x1a0.internal_message.service.v1.NotificationMessage\"\x00\x12a\n" +
	"\x06Create\x12=.internal_message.service.v1.CreateNotificationMessageRequest\x1a\x16.google.protobuf.Empty\"\x00\x12a\n" +
	"\x06Update\x12=.internal_message.service.v1.UpdateNotificationMessageRequest\x1a\x16.google.protobuf.Empty\"\x00\x12a\n" +
	"\x06Delete\x12=.internal_message.service.v1.DeleteNotificationMessageRequest\x1a\x16.google.protobuf.Empty\"\x00B\x84\x02\n" +
	"\x1fcom.internal_message.service.v1B\x18NotificationMessageProtoP\x01Z=kratos-admin/api/gen/go/internal_message/service/v1;servicev1\xa2\x02\x03ISX\xaa\x02\x1aInternalMessage.Service.V1\xca\x02\x1aInternalMessage\\Service\\V1\xe2\x02&InternalMessage\\Service\\V1\\GPBMetadata\xea\x02\x1cInternalMessage::Service::V1b\x06proto3"

var (
	file_internal_message_service_v1_notification_message_proto_rawDescOnce sync.Once
	file_internal_message_service_v1_notification_message_proto_rawDescData []byte
)

func file_internal_message_service_v1_notification_message_proto_rawDescGZIP() []byte {
	file_internal_message_service_v1_notification_message_proto_rawDescOnce.Do(func() {
		file_internal_message_service_v1_notification_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_notification_message_proto_rawDesc), len(file_internal_message_service_v1_notification_message_proto_rawDesc)))
	})
	return file_internal_message_service_v1_notification_message_proto_rawDescData
}

var file_internal_message_service_v1_notification_message_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_internal_message_service_v1_notification_message_proto_goTypes = []any{
	(*NotificationMessage)(nil),              // 0: internal_message.service.v1.NotificationMessage
	(*ListNotificationMessageResponse)(nil),  // 1: internal_message.service.v1.ListNotificationMessageResponse
	(*GetNotificationMessageRequest)(nil),    // 2: internal_message.service.v1.GetNotificationMessageRequest
	(*CreateNotificationMessageRequest)(nil), // 3: internal_message.service.v1.CreateNotificationMessageRequest
	(*UpdateNotificationMessageRequest)(nil), // 4: internal_message.service.v1.UpdateNotificationMessageRequest
	(*DeleteNotificationMessageRequest)(nil), // 5: internal_message.service.v1.DeleteNotificationMessageRequest
	(MessageStatus)(0),                       // 6: internal_message.service.v1.MessageStatus
	(*timestamppb.Timestamp)(nil),            // 7: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),            // 8: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),                 // 9: pagination.PagingRequest
	(*emptypb.Empty)(nil),                    // 10: google.protobuf.Empty
}
var file_internal_message_service_v1_notification_message_proto_depIdxs = []int32{
	6,  // 0: internal_message.service.v1.NotificationMessage.status:type_name -> internal_message.service.v1.MessageStatus
	7,  // 1: internal_message.service.v1.NotificationMessage.create_time:type_name -> google.protobuf.Timestamp
	7,  // 2: internal_message.service.v1.NotificationMessage.update_time:type_name -> google.protobuf.Timestamp
	7,  // 3: internal_message.service.v1.NotificationMessage.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: internal_message.service.v1.ListNotificationMessageResponse.items:type_name -> internal_message.service.v1.NotificationMessage
	0,  // 5: internal_message.service.v1.CreateNotificationMessageRequest.data:type_name -> internal_message.service.v1.NotificationMessage
	0,  // 6: internal_message.service.v1.UpdateNotificationMessageRequest.data:type_name -> internal_message.service.v1.NotificationMessage
	8,  // 7: internal_message.service.v1.UpdateNotificationMessageRequest.update_mask:type_name -> google.protobuf.FieldMask
	9,  // 8: internal_message.service.v1.NotificationMessageService.List:input_type -> pagination.PagingRequest
	2,  // 9: internal_message.service.v1.NotificationMessageService.Get:input_type -> internal_message.service.v1.GetNotificationMessageRequest
	3,  // 10: internal_message.service.v1.NotificationMessageService.Create:input_type -> internal_message.service.v1.CreateNotificationMessageRequest
	4,  // 11: internal_message.service.v1.NotificationMessageService.Update:input_type -> internal_message.service.v1.UpdateNotificationMessageRequest
	5,  // 12: internal_message.service.v1.NotificationMessageService.Delete:input_type -> internal_message.service.v1.DeleteNotificationMessageRequest
	1,  // 13: internal_message.service.v1.NotificationMessageService.List:output_type -> internal_message.service.v1.ListNotificationMessageResponse
	0,  // 14: internal_message.service.v1.NotificationMessageService.Get:output_type -> internal_message.service.v1.NotificationMessage
	10, // 15: internal_message.service.v1.NotificationMessageService.Create:output_type -> google.protobuf.Empty
	10, // 16: internal_message.service.v1.NotificationMessageService.Update:output_type -> google.protobuf.Empty
	10, // 17: internal_message.service.v1.NotificationMessageService.Delete:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_internal_message_service_v1_notification_message_proto_init() }
func file_internal_message_service_v1_notification_message_proto_init() {
	if File_internal_message_service_v1_notification_message_proto != nil {
		return
	}
	file_internal_message_service_v1_message_proto_init()
	file_internal_message_service_v1_notification_message_proto_msgTypes[0].OneofWrappers = []any{}
	file_internal_message_service_v1_notification_message_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_notification_message_proto_rawDesc), len(file_internal_message_service_v1_notification_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_message_service_v1_notification_message_proto_goTypes,
		DependencyIndexes: file_internal_message_service_v1_notification_message_proto_depIdxs,
		MessageInfos:      file_internal_message_service_v1_notification_message_proto_msgTypes,
	}.Build()
	File_internal_message_service_v1_notification_message_proto = out.File
	file_internal_message_service_v1_notification_message_proto_goTypes = nil
	file_internal_message_service_v1_notification_message_proto_depIdxs = nil
}
