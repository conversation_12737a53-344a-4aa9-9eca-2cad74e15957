// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: internal_message/service/v1/private_message.proto

package servicev1

import (
	_ "github.com/google/gnostic/openapiv3"
	v1 "github.com/tx7do/kratos-bootstrap/api/gen/go/pagination/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 私信消息
type PrivateMessage struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *uint32                `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`                                                        // 消息ID
	Subject        *string                `protobuf:"bytes,2,opt,name=subject,proto3,oneof" json:"subject,omitempty"`                                               // 主题
	Content        *string                `protobuf:"bytes,3,opt,name=content,proto3,oneof" json:"content,omitempty"`                                               // 内容
	Status         *MessageStatus         `protobuf:"varint,4,opt,name=status,proto3,enum=internal_message.service.v1.MessageStatus,oneof" json:"status,omitempty"` // 消息状态
	SenderId       *uint32                `protobuf:"varint,10,opt,name=sender_id,json=senderId,proto3,oneof" json:"sender_id,omitempty"`                           // 发送者用户ID
	SenderName     *string                `protobuf:"bytes,11,opt,name=sender_name,json=senderName,proto3,oneof" json:"sender_name,omitempty"`                      // 发送者用户名称
	SenderAvatar   *string                `protobuf:"bytes,12,opt,name=sender_avatar,json=senderAvatar,proto3,oneof" json:"sender_avatar,omitempty"`                // 发送者用户头像
	ReceiverId     *uint32                `protobuf:"varint,20,opt,name=receiver_id,json=receiverId,proto3,oneof" json:"receiver_id,omitempty"`                     // 接收者用户ID
	ReceiverName   *string                `protobuf:"bytes,21,opt,name=receiver_name,json=receiverName,proto3,oneof" json:"receiver_name,omitempty"`                // 接收者用户名称
	ReceiverAvatar *string                `protobuf:"bytes,22,opt,name=receiver_avatar,json=receiverAvatar,proto3,oneof" json:"receiver_avatar,omitempty"`          // 接收者用户头像
	CreateTime     *timestamppb.Timestamp `protobuf:"bytes,200,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`                     // 创建时间
	UpdateTime     *timestamppb.Timestamp `protobuf:"bytes,201,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                     // 更新时间
	DeleteTime     *timestamppb.Timestamp `protobuf:"bytes,202,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`                     // 删除时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PrivateMessage) Reset() {
	*x = PrivateMessage{}
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivateMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateMessage) ProtoMessage() {}

func (x *PrivateMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateMessage.ProtoReflect.Descriptor instead.
func (*PrivateMessage) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_private_message_proto_rawDescGZIP(), []int{0}
}

func (x *PrivateMessage) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *PrivateMessage) GetSubject() string {
	if x != nil && x.Subject != nil {
		return *x.Subject
	}
	return ""
}

func (x *PrivateMessage) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *PrivateMessage) GetStatus() MessageStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return MessageStatus_MessageStatus_Unknown
}

func (x *PrivateMessage) GetSenderId() uint32 {
	if x != nil && x.SenderId != nil {
		return *x.SenderId
	}
	return 0
}

func (x *PrivateMessage) GetSenderName() string {
	if x != nil && x.SenderName != nil {
		return *x.SenderName
	}
	return ""
}

func (x *PrivateMessage) GetSenderAvatar() string {
	if x != nil && x.SenderAvatar != nil {
		return *x.SenderAvatar
	}
	return ""
}

func (x *PrivateMessage) GetReceiverId() uint32 {
	if x != nil && x.ReceiverId != nil {
		return *x.ReceiverId
	}
	return 0
}

func (x *PrivateMessage) GetReceiverName() string {
	if x != nil && x.ReceiverName != nil {
		return *x.ReceiverName
	}
	return ""
}

func (x *PrivateMessage) GetReceiverAvatar() string {
	if x != nil && x.ReceiverAvatar != nil {
		return *x.ReceiverAvatar
	}
	return ""
}

func (x *PrivateMessage) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PrivateMessage) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *PrivateMessage) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// 查询私信消息列表 - 回应
type ListPrivateMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*PrivateMessage      `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPrivateMessageResponse) Reset() {
	*x = ListPrivateMessageResponse{}
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPrivateMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPrivateMessageResponse) ProtoMessage() {}

func (x *ListPrivateMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPrivateMessageResponse.ProtoReflect.Descriptor instead.
func (*ListPrivateMessageResponse) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_private_message_proto_rawDescGZIP(), []int{1}
}

func (x *ListPrivateMessageResponse) GetItems() []*PrivateMessage {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListPrivateMessageResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 查询私信消息详情 - 请求
type GetPrivateMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrivateMessageRequest) Reset() {
	*x = GetPrivateMessageRequest{}
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrivateMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrivateMessageRequest) ProtoMessage() {}

func (x *GetPrivateMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrivateMessageRequest.ProtoReflect.Descriptor instead.
func (*GetPrivateMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_private_message_proto_rawDescGZIP(), []int{2}
}

func (x *GetPrivateMessageRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 创建私信消息 - 请求
type CreatePrivateMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *PrivateMessage        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePrivateMessageRequest) Reset() {
	*x = CreatePrivateMessageRequest{}
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePrivateMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePrivateMessageRequest) ProtoMessage() {}

func (x *CreatePrivateMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePrivateMessageRequest.ProtoReflect.Descriptor instead.
func (*CreatePrivateMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_private_message_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePrivateMessageRequest) GetData() *PrivateMessage {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新私信消息 - 请求
type UpdatePrivateMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *PrivateMessage        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`              // 要更新的字段列表
	AllowMissing  *bool                  `protobuf:"varint,3,opt,name=allow_missing,json=allowMissing,proto3,oneof" json:"allow_missing,omitempty"` // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePrivateMessageRequest) Reset() {
	*x = UpdatePrivateMessageRequest{}
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePrivateMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePrivateMessageRequest) ProtoMessage() {}

func (x *UpdatePrivateMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePrivateMessageRequest.ProtoReflect.Descriptor instead.
func (*UpdatePrivateMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_private_message_proto_rawDescGZIP(), []int{4}
}

func (x *UpdatePrivateMessageRequest) GetData() *PrivateMessage {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdatePrivateMessageRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

func (x *UpdatePrivateMessageRequest) GetAllowMissing() bool {
	if x != nil && x.AllowMissing != nil {
		return *x.AllowMissing
	}
	return false
}

// 删除私信消息 - 请求
type DeletePrivateMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePrivateMessageRequest) Reset() {
	*x = DeletePrivateMessageRequest{}
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePrivateMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePrivateMessageRequest) ProtoMessage() {}

func (x *DeletePrivateMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_message_service_v1_private_message_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePrivateMessageRequest.ProtoReflect.Descriptor instead.
func (*DeletePrivateMessageRequest) Descriptor() ([]byte, []int) {
	return file_internal_message_service_v1_private_message_proto_rawDescGZIP(), []int{5}
}

func (x *DeletePrivateMessageRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_internal_message_service_v1_private_message_proto protoreflect.FileDescriptor

const file_internal_message_service_v1_private_message_proto_rawDesc = "" +
	"\n" +
	"1internal_message/service/v1/private_message.proto\x12\x1binternal_message.service.v1\x1a$gnostic/openapi/v3/annotations.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a google/protobuf/field_mask.proto\x1a\x1epagination/v1/pagination.proto\x1a)internal_message/service/v1/message.proto\"\xca\b\n" +
	"\x0ePrivateMessage\x12&\n" +
	"\x02id\x18\x01 \x01(\rB\x11\xe0A\x01\xbaG\v\x92\x02\b消息IDH\x00R\x02id\x88\x01\x01\x12+\n" +
	"\asubject\x18\x02 \x01(\tB\f\xbaG\t\x92\x02\x06主题H\x01R\asubject\x88\x01\x01\x12+\n" +
	"\acontent\x18\x03 \x01(\tB\f\xbaG\t\x92\x02\x06内容H\x02R\acontent\x88\x01\x01\x12[\n" +
	"\x06status\x18\x04 \x01(\x0e2*.internal_message.service.v1.MessageStatusB\x12\xbaG\x0f\x92\x02\f消息状态H\x03R\x06status\x88\x01\x01\x129\n" +
	"\tsender_id\x18\n" +
	" \x01(\rB\x17\xbaG\x14\x92\x02\x11发送者用户IDH\x04R\bsenderId\x88\x01\x01\x12A\n" +
	"\vsender_name\x18\v \x01(\tB\x1b\xbaG\x18\x92\x02\x15发送者用户名称H\x05R\n" +
	"senderName\x88\x01\x01\x12E\n" +
	"\rsender_avatar\x18\f \x01(\tB\x1b\xbaG\x18\x92\x02\x15发送者用户头像H\x06R\fsenderAvatar\x88\x01\x01\x12=\n" +
	"\vreceiver_id\x18\x14 \x01(\rB\x17\xbaG\x14\x92\x02\x11接收者用户IDH\aR\n" +
	"receiverId\x88\x01\x01\x12E\n" +
	"\rreceiver_name\x18\x15 \x01(\tB\x1b\xbaG\x18\x92\x02\x15接收者用户名称H\bR\freceiverName\x88\x01\x01\x12I\n" +
	"\x0freceiver_avatar\x18\x16 \x01(\tB\x1b\xbaG\x18\x92\x02\x15接收者用户头像H\tR\x0ereceiverAvatar\x88\x01\x01\x12U\n" +
	"\vcreate_time\x18\xc8\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f创建时间H\n" +
	"R\n" +
	"createTime\x88\x01\x01\x12U\n" +
	"\vupdate_time\x18\xc9\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f更新时间H\vR\n" +
	"updateTime\x88\x01\x01\x12U\n" +
	"\vdelete_time\x18\xca\x01 \x01(\v2\x1a.google.protobuf.TimestampB\x12\xbaG\x0f\x92\x02\f删除时间H\fR\n" +
	"deleteTime\x88\x01\x01B\x05\n" +
	"\x03_idB\n" +
	"\n" +
	"\b_subjectB\n" +
	"\n" +
	"\b_contentB\t\n" +
	"\a_statusB\f\n" +
	"\n" +
	"_sender_idB\x0e\n" +
	"\f_sender_nameB\x10\n" +
	"\x0e_sender_avatarB\x0e\n" +
	"\f_receiver_idB\x10\n" +
	"\x0e_receiver_nameB\x12\n" +
	"\x10_receiver_avatarB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"u\n" +
	"\x1aListPrivateMessageResponse\x12A\n" +
	"\x05items\x18\x01 \x03(\v2+.internal_message.service.v1.PrivateMessageR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total\"*\n" +
	"\x18GetPrivateMessageRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"^\n" +
	"\x1bCreatePrivateMessageRequest\x12?\n" +
	"\x04data\x18\x01 \x01(\v2+.internal_message.service.v1.PrivateMessageR\x04data\"\x9c\x03\n" +
	"\x1bUpdatePrivateMessageRequest\x12?\n" +
	"\x04data\x18\x01 \x01(\v2+.internal_message.service.v1.PrivateMessageR\x04data\x12s\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskB6\xbaG3:\x16\x12\x14id,realname,username\x92\x02\x18要更新的字段列表R\n" +
	"updateMask\x12\xb4\x01\n" +
	"\rallow_missing\x18\x03 \x01(\bB\x89\x01\xbaG\x85\x01\x92\x02\x81\x01如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。H\x00R\fallowMissing\x88\x01\x01B\x10\n" +
	"\x0e_allow_missing\"-\n" +
	"\x1bDeletePrivateMessageRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id2\xfc\x03\n" +
	"\x15PrivateMessageService\x12\\\n" +
	"\x04List\x12\x19.pagination.PagingRequest\x1a7.internal_message.service.v1.ListPrivateMessageResponse\"\x00\x12k\n" +
	"\x03Get\x125.internal_message.service.v1.GetPrivateMessageRequest\x1a+.internal_message.service.v1.PrivateMessage\"\x00\x12\\\n" +
	"\x06Create\x128.internal_message.service.v1.CreatePrivateMessageRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\\\n" +
	"\x06Update\x128.internal_message.service.v1.UpdatePrivateMessageRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\\\n" +
	"\x06Delete\x128.internal_message.service.v1.DeletePrivateMessageRequest\x1a\x16.google.protobuf.Empty\"\x00B\xff\x01\n" +
	"\x1fcom.internal_message.service.v1B\x13PrivateMessageProtoP\x01Z=kratos-admin/api/gen/go/internal_message/service/v1;servicev1\xa2\x02\x03ISX\xaa\x02\x1aInternalMessage.Service.V1\xca\x02\x1aInternalMessage\\Service\\V1\xe2\x02&InternalMessage\\Service\\V1\\GPBMetadata\xea\x02\x1cInternalMessage::Service::V1b\x06proto3"

var (
	file_internal_message_service_v1_private_message_proto_rawDescOnce sync.Once
	file_internal_message_service_v1_private_message_proto_rawDescData []byte
)

func file_internal_message_service_v1_private_message_proto_rawDescGZIP() []byte {
	file_internal_message_service_v1_private_message_proto_rawDescOnce.Do(func() {
		file_internal_message_service_v1_private_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_private_message_proto_rawDesc), len(file_internal_message_service_v1_private_message_proto_rawDesc)))
	})
	return file_internal_message_service_v1_private_message_proto_rawDescData
}

var file_internal_message_service_v1_private_message_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_internal_message_service_v1_private_message_proto_goTypes = []any{
	(*PrivateMessage)(nil),              // 0: internal_message.service.v1.PrivateMessage
	(*ListPrivateMessageResponse)(nil),  // 1: internal_message.service.v1.ListPrivateMessageResponse
	(*GetPrivateMessageRequest)(nil),    // 2: internal_message.service.v1.GetPrivateMessageRequest
	(*CreatePrivateMessageRequest)(nil), // 3: internal_message.service.v1.CreatePrivateMessageRequest
	(*UpdatePrivateMessageRequest)(nil), // 4: internal_message.service.v1.UpdatePrivateMessageRequest
	(*DeletePrivateMessageRequest)(nil), // 5: internal_message.service.v1.DeletePrivateMessageRequest
	(MessageStatus)(0),                  // 6: internal_message.service.v1.MessageStatus
	(*timestamppb.Timestamp)(nil),       // 7: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),       // 8: google.protobuf.FieldMask
	(*v1.PagingRequest)(nil),            // 9: pagination.PagingRequest
	(*emptypb.Empty)(nil),               // 10: google.protobuf.Empty
}
var file_internal_message_service_v1_private_message_proto_depIdxs = []int32{
	6,  // 0: internal_message.service.v1.PrivateMessage.status:type_name -> internal_message.service.v1.MessageStatus
	7,  // 1: internal_message.service.v1.PrivateMessage.create_time:type_name -> google.protobuf.Timestamp
	7,  // 2: internal_message.service.v1.PrivateMessage.update_time:type_name -> google.protobuf.Timestamp
	7,  // 3: internal_message.service.v1.PrivateMessage.delete_time:type_name -> google.protobuf.Timestamp
	0,  // 4: internal_message.service.v1.ListPrivateMessageResponse.items:type_name -> internal_message.service.v1.PrivateMessage
	0,  // 5: internal_message.service.v1.CreatePrivateMessageRequest.data:type_name -> internal_message.service.v1.PrivateMessage
	0,  // 6: internal_message.service.v1.UpdatePrivateMessageRequest.data:type_name -> internal_message.service.v1.PrivateMessage
	8,  // 7: internal_message.service.v1.UpdatePrivateMessageRequest.update_mask:type_name -> google.protobuf.FieldMask
	9,  // 8: internal_message.service.v1.PrivateMessageService.List:input_type -> pagination.PagingRequest
	2,  // 9: internal_message.service.v1.PrivateMessageService.Get:input_type -> internal_message.service.v1.GetPrivateMessageRequest
	3,  // 10: internal_message.service.v1.PrivateMessageService.Create:input_type -> internal_message.service.v1.CreatePrivateMessageRequest
	4,  // 11: internal_message.service.v1.PrivateMessageService.Update:input_type -> internal_message.service.v1.UpdatePrivateMessageRequest
	5,  // 12: internal_message.service.v1.PrivateMessageService.Delete:input_type -> internal_message.service.v1.DeletePrivateMessageRequest
	1,  // 13: internal_message.service.v1.PrivateMessageService.List:output_type -> internal_message.service.v1.ListPrivateMessageResponse
	0,  // 14: internal_message.service.v1.PrivateMessageService.Get:output_type -> internal_message.service.v1.PrivateMessage
	10, // 15: internal_message.service.v1.PrivateMessageService.Create:output_type -> google.protobuf.Empty
	10, // 16: internal_message.service.v1.PrivateMessageService.Update:output_type -> google.protobuf.Empty
	10, // 17: internal_message.service.v1.PrivateMessageService.Delete:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_internal_message_service_v1_private_message_proto_init() }
func file_internal_message_service_v1_private_message_proto_init() {
	if File_internal_message_service_v1_private_message_proto != nil {
		return
	}
	file_internal_message_service_v1_message_proto_init()
	file_internal_message_service_v1_private_message_proto_msgTypes[0].OneofWrappers = []any{}
	file_internal_message_service_v1_private_message_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_private_message_proto_rawDesc), len(file_internal_message_service_v1_private_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_message_service_v1_private_message_proto_goTypes,
		DependencyIndexes: file_internal_message_service_v1_private_message_proto_depIdxs,
		MessageInfos:      file_internal_message_service_v1_private_message_proto_msgTypes,
	}.Build()
	File_internal_message_service_v1_private_message_proto = out.File
	file_internal_message_service_v1_private_message_proto_goTypes = nil
	file_internal_message_service_v1_private_message_proto_depIdxs = nil
}
