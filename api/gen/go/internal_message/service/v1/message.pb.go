// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: internal_message/service/v1/message.proto

package servicev1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 消息状态
type MessageStatus int32

const (
	MessageStatus_MessageStatus_Unknown MessageStatus = 0 // 未知状态
	MessageStatus_DRAFT                 MessageStatus = 1 // 草稿
	MessageStatus_PUBLISHED             MessageStatus = 2 // 已发布
	MessageStatus_SCHEDULED             MessageStatus = 3 // 定时发布
	MessageStatus_REVOKED               MessageStatus = 4 // 已撤销
	MessageStatus_ARCHIVED              MessageStatus = 5 // 已归档
	MessageStatus_DELETED               MessageStatus = 6 // 已删除
	MessageStatus_SENT                  MessageStatus = 7 // 已发送
	MessageStatus_RECEIVED              MessageStatus = 8 // 已接收
	MessageStatus_READ                  MessageStatus = 9 // 已接收
)

// Enum value maps for MessageStatus.
var (
	MessageStatus_name = map[int32]string{
		0: "MessageStatus_Unknown",
		1: "DRAFT",
		2: "PUBLISHED",
		3: "SCHEDULED",
		4: "REVOKED",
		5: "ARCHIVED",
		6: "DELETED",
		7: "SENT",
		8: "RECEIVED",
		9: "READ",
	}
	MessageStatus_value = map[string]int32{
		"MessageStatus_Unknown": 0,
		"DRAFT":                 1,
		"PUBLISHED":             2,
		"SCHEDULED":             3,
		"REVOKED":               4,
		"ARCHIVED":              5,
		"DELETED":               6,
		"SENT":                  7,
		"RECEIVED":              8,
		"READ":                  9,
	}
)

func (x MessageStatus) Enum() *MessageStatus {
	p := new(MessageStatus)
	*p = x
	return p
}

func (x MessageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_message_service_v1_message_proto_enumTypes[0].Descriptor()
}

func (MessageStatus) Type() protoreflect.EnumType {
	return &file_internal_message_service_v1_message_proto_enumTypes[0]
}

func (x MessageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageStatus.Descriptor instead.
func (MessageStatus) EnumDescriptor() ([]byte, []int) {
	return file_internal_message_service_v1_message_proto_rawDescGZIP(), []int{0}
}

var File_internal_message_service_v1_message_proto protoreflect.FileDescriptor

const file_internal_message_service_v1_message_proto_rawDesc = "" +
	"\n" +
	")internal_message/service/v1/message.proto\x12\x1binternal_message.service.v1*\x9d\x01\n" +
	"\rMessageStatus\x12\x19\n" +
	"\x15MessageStatus_Unknown\x10\x00\x12\t\n" +
	"\x05DRAFT\x10\x01\x12\r\n" +
	"\tPUBLISHED\x10\x02\x12\r\n" +
	"\tSCHEDULED\x10\x03\x12\v\n" +
	"\aREVOKED\x10\x04\x12\f\n" +
	"\bARCHIVED\x10\x05\x12\v\n" +
	"\aDELETED\x10\x06\x12\b\n" +
	"\x04SENT\x10\a\x12\f\n" +
	"\bRECEIVED\x10\b\x12\b\n" +
	"\x04READ\x10\tB\xf8\x01\n" +
	"\x1fcom.internal_message.service.v1B\fMessageProtoP\x01Z=kratos-admin/api/gen/go/internal_message/service/v1;servicev1\xa2\x02\x03ISX\xaa\x02\x1aInternalMessage.Service.V1\xca\x02\x1aInternalMessage\\Service\\V1\xe2\x02&InternalMessage\\Service\\V1\\GPBMetadata\xea\x02\x1cInternalMessage::Service::V1b\x06proto3"

var (
	file_internal_message_service_v1_message_proto_rawDescOnce sync.Once
	file_internal_message_service_v1_message_proto_rawDescData []byte
)

func file_internal_message_service_v1_message_proto_rawDescGZIP() []byte {
	file_internal_message_service_v1_message_proto_rawDescOnce.Do(func() {
		file_internal_message_service_v1_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_message_proto_rawDesc), len(file_internal_message_service_v1_message_proto_rawDesc)))
	})
	return file_internal_message_service_v1_message_proto_rawDescData
}

var file_internal_message_service_v1_message_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_internal_message_service_v1_message_proto_goTypes = []any{
	(MessageStatus)(0), // 0: internal_message.service.v1.MessageStatus
}
var file_internal_message_service_v1_message_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_internal_message_service_v1_message_proto_init() }
func file_internal_message_service_v1_message_proto_init() {
	if File_internal_message_service_v1_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_message_service_v1_message_proto_rawDesc), len(file_internal_message_service_v1_message_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_message_service_v1_message_proto_goTypes,
		DependencyIndexes: file_internal_message_service_v1_message_proto_depIdxs,
		EnumInfos:         file_internal_message_service_v1_message_proto_enumTypes,
	}.Build()
	File_internal_message_service_v1_message_proto = out.File
	file_internal_message_service_v1_message_proto_goTypes = nil
	file_internal_message_service_v1_message_proto_depIdxs = nil
}
