// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: internal_message/service/v1/notification_message.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NotificationMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NotificationMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotificationMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NotificationMessageMultiError, or nil if none found.
func (m *NotificationMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *NotificationMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Subject != nil {
		// no validation rules for Subject
	}

	if m.Content != nil {
		// no validation rules for Content
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.CategoryId != nil {
		// no validation rules for CategoryId
	}

	if m.CategoryName != nil {
		// no validation rules for CategoryName
	}

	if m.CreateBy != nil {
		// no validation rules for CreateBy
	}

	if m.UpdateBy != nil {
		// no validation rules for UpdateBy
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NotificationMessageValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NotificationMessageValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NotificationMessageValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NotificationMessageMultiError(errors)
	}

	return nil
}

// NotificationMessageMultiError is an error wrapping multiple validation
// errors returned by NotificationMessage.ValidateAll() if the designated
// constraints aren't met.
type NotificationMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotificationMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotificationMessageMultiError) AllErrors() []error { return m }

// NotificationMessageValidationError is the validation error returned by
// NotificationMessage.Validate if the designated constraints aren't met.
type NotificationMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotificationMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotificationMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotificationMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotificationMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotificationMessageValidationError) ErrorName() string {
	return "NotificationMessageValidationError"
}

// Error satisfies the builtin error interface
func (e NotificationMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotificationMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotificationMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotificationMessageValidationError{}

// Validate checks the field values on ListNotificationMessageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNotificationMessageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNotificationMessageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListNotificationMessageResponseMultiError, or nil if none found.
func (m *ListNotificationMessageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNotificationMessageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNotificationMessageResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNotificationMessageResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNotificationMessageResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListNotificationMessageResponseMultiError(errors)
	}

	return nil
}

// ListNotificationMessageResponseMultiError is an error wrapping multiple
// validation errors returned by ListNotificationMessageResponse.ValidateAll()
// if the designated constraints aren't met.
type ListNotificationMessageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNotificationMessageResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNotificationMessageResponseMultiError) AllErrors() []error { return m }

// ListNotificationMessageResponseValidationError is the validation error
// returned by ListNotificationMessageResponse.Validate if the designated
// constraints aren't met.
type ListNotificationMessageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNotificationMessageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNotificationMessageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNotificationMessageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNotificationMessageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNotificationMessageResponseValidationError) ErrorName() string {
	return "ListNotificationMessageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNotificationMessageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNotificationMessageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNotificationMessageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNotificationMessageResponseValidationError{}

// Validate checks the field values on GetNotificationMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNotificationMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNotificationMessageRequestMultiError, or nil if none found.
func (m *GetNotificationMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNotificationMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetNotificationMessageRequestMultiError(errors)
	}

	return nil
}

// GetNotificationMessageRequestMultiError is an error wrapping multiple
// validation errors returned by GetNotificationMessageRequest.ValidateAll()
// if the designated constraints aren't met.
type GetNotificationMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNotificationMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNotificationMessageRequestMultiError) AllErrors() []error { return m }

// GetNotificationMessageRequestValidationError is the validation error
// returned by GetNotificationMessageRequest.Validate if the designated
// constraints aren't met.
type GetNotificationMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNotificationMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNotificationMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNotificationMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNotificationMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNotificationMessageRequestValidationError) ErrorName() string {
	return "GetNotificationMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNotificationMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNotificationMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNotificationMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNotificationMessageRequestValidationError{}

// Validate checks the field values on CreateNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateNotificationMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateNotificationMessageRequestMultiError, or nil if none found.
func (m *CreateNotificationMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNotificationMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNotificationMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNotificationMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNotificationMessageRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNotificationMessageRequestMultiError(errors)
	}

	return nil
}

// CreateNotificationMessageRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateNotificationMessageRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNotificationMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNotificationMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNotificationMessageRequestMultiError) AllErrors() []error { return m }

// CreateNotificationMessageRequestValidationError is the validation error
// returned by CreateNotificationMessageRequest.Validate if the designated
// constraints aren't met.
type CreateNotificationMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNotificationMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNotificationMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNotificationMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNotificationMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNotificationMessageRequestValidationError) ErrorName() string {
	return "CreateNotificationMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNotificationMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNotificationMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNotificationMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNotificationMessageRequestValidationError{}

// Validate checks the field values on UpdateNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateNotificationMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateNotificationMessageRequestMultiError, or nil if none found.
func (m *UpdateNotificationMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNotificationMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotificationMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotificationMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotificationMessageRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNotificationMessageRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNotificationMessageRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNotificationMessageRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdateNotificationMessageRequestMultiError(errors)
	}

	return nil
}

// UpdateNotificationMessageRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateNotificationMessageRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNotificationMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNotificationMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNotificationMessageRequestMultiError) AllErrors() []error { return m }

// UpdateNotificationMessageRequestValidationError is the validation error
// returned by UpdateNotificationMessageRequest.Validate if the designated
// constraints aren't met.
type UpdateNotificationMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNotificationMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNotificationMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNotificationMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNotificationMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNotificationMessageRequestValidationError) ErrorName() string {
	return "UpdateNotificationMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNotificationMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNotificationMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNotificationMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNotificationMessageRequestValidationError{}

// Validate checks the field values on DeleteNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteNotificationMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteNotificationMessageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteNotificationMessageRequestMultiError, or nil if none found.
func (m *DeleteNotificationMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteNotificationMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteNotificationMessageRequestMultiError(errors)
	}

	return nil
}

// DeleteNotificationMessageRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteNotificationMessageRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteNotificationMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteNotificationMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteNotificationMessageRequestMultiError) AllErrors() []error { return m }

// DeleteNotificationMessageRequestValidationError is the validation error
// returned by DeleteNotificationMessageRequest.Validate if the designated
// constraints aren't met.
type DeleteNotificationMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteNotificationMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteNotificationMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteNotificationMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteNotificationMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteNotificationMessageRequestValidationError) ErrorName() string {
	return "DeleteNotificationMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteNotificationMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteNotificationMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteNotificationMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteNotificationMessageRequestValidationError{}
