// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: internal_message/service/v1/private_message.proto

package servicev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PrivateMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrivateMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrivateMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrivateMessageMultiError,
// or nil if none found.
func (m *PrivateMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *PrivateMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Subject != nil {
		// no validation rules for Subject
	}

	if m.Content != nil {
		// no validation rules for Content
	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.SenderId != nil {
		// no validation rules for SenderId
	}

	if m.SenderName != nil {
		// no validation rules for SenderName
	}

	if m.SenderAvatar != nil {
		// no validation rules for SenderAvatar
	}

	if m.ReceiverId != nil {
		// no validation rules for ReceiverId
	}

	if m.ReceiverName != nil {
		// no validation rules for ReceiverName
	}

	if m.ReceiverAvatar != nil {
		// no validation rules for ReceiverAvatar
	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrivateMessageValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrivateMessageValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrivateMessageValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrivateMessageValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrivateMessageValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrivateMessageValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrivateMessageValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrivateMessageValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrivateMessageValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PrivateMessageMultiError(errors)
	}

	return nil
}

// PrivateMessageMultiError is an error wrapping multiple validation errors
// returned by PrivateMessage.ValidateAll() if the designated constraints
// aren't met.
type PrivateMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrivateMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrivateMessageMultiError) AllErrors() []error { return m }

// PrivateMessageValidationError is the validation error returned by
// PrivateMessage.Validate if the designated constraints aren't met.
type PrivateMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrivateMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrivateMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrivateMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrivateMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrivateMessageValidationError) ErrorName() string { return "PrivateMessageValidationError" }

// Error satisfies the builtin error interface
func (e PrivateMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrivateMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrivateMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrivateMessageValidationError{}

// Validate checks the field values on ListPrivateMessageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPrivateMessageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPrivateMessageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPrivateMessageResponseMultiError, or nil if none found.
func (m *ListPrivateMessageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPrivateMessageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPrivateMessageResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPrivateMessageResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPrivateMessageResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListPrivateMessageResponseMultiError(errors)
	}

	return nil
}

// ListPrivateMessageResponseMultiError is an error wrapping multiple
// validation errors returned by ListPrivateMessageResponse.ValidateAll() if
// the designated constraints aren't met.
type ListPrivateMessageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPrivateMessageResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPrivateMessageResponseMultiError) AllErrors() []error { return m }

// ListPrivateMessageResponseValidationError is the validation error returned
// by ListPrivateMessageResponse.Validate if the designated constraints aren't met.
type ListPrivateMessageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPrivateMessageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPrivateMessageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPrivateMessageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPrivateMessageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPrivateMessageResponseValidationError) ErrorName() string {
	return "ListPrivateMessageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListPrivateMessageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPrivateMessageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPrivateMessageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPrivateMessageResponseValidationError{}

// Validate checks the field values on GetPrivateMessageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPrivateMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPrivateMessageRequestMultiError, or nil if none found.
func (m *GetPrivateMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPrivateMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetPrivateMessageRequestMultiError(errors)
	}

	return nil
}

// GetPrivateMessageRequestMultiError is an error wrapping multiple validation
// errors returned by GetPrivateMessageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPrivateMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPrivateMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPrivateMessageRequestMultiError) AllErrors() []error { return m }

// GetPrivateMessageRequestValidationError is the validation error returned by
// GetPrivateMessageRequest.Validate if the designated constraints aren't met.
type GetPrivateMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPrivateMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPrivateMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPrivateMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPrivateMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPrivateMessageRequestValidationError) ErrorName() string {
	return "GetPrivateMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPrivateMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPrivateMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPrivateMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPrivateMessageRequestValidationError{}

// Validate checks the field values on CreatePrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePrivateMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePrivateMessageRequestMultiError, or nil if none found.
func (m *CreatePrivateMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePrivateMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePrivateMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePrivateMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePrivateMessageRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreatePrivateMessageRequestMultiError(errors)
	}

	return nil
}

// CreatePrivateMessageRequestMultiError is an error wrapping multiple
// validation errors returned by CreatePrivateMessageRequest.ValidateAll() if
// the designated constraints aren't met.
type CreatePrivateMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePrivateMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePrivateMessageRequestMultiError) AllErrors() []error { return m }

// CreatePrivateMessageRequestValidationError is the validation error returned
// by CreatePrivateMessageRequest.Validate if the designated constraints
// aren't met.
type CreatePrivateMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePrivateMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePrivateMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePrivateMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePrivateMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePrivateMessageRequestValidationError) ErrorName() string {
	return "CreatePrivateMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePrivateMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePrivateMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePrivateMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePrivateMessageRequestValidationError{}

// Validate checks the field values on UpdatePrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePrivateMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePrivateMessageRequestMultiError, or nil if none found.
func (m *UpdatePrivateMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePrivateMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatePrivateMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatePrivateMessageRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatePrivateMessageRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatePrivateMessageRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatePrivateMessageRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatePrivateMessageRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AllowMissing != nil {
		// no validation rules for AllowMissing
	}

	if len(errors) > 0 {
		return UpdatePrivateMessageRequestMultiError(errors)
	}

	return nil
}

// UpdatePrivateMessageRequestMultiError is an error wrapping multiple
// validation errors returned by UpdatePrivateMessageRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdatePrivateMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePrivateMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePrivateMessageRequestMultiError) AllErrors() []error { return m }

// UpdatePrivateMessageRequestValidationError is the validation error returned
// by UpdatePrivateMessageRequest.Validate if the designated constraints
// aren't met.
type UpdatePrivateMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePrivateMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePrivateMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePrivateMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePrivateMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePrivateMessageRequestValidationError) ErrorName() string {
	return "UpdatePrivateMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePrivateMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePrivateMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePrivateMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePrivateMessageRequestValidationError{}

// Validate checks the field values on DeletePrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePrivateMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePrivateMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePrivateMessageRequestMultiError, or nil if none found.
func (m *DeletePrivateMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePrivateMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeletePrivateMessageRequestMultiError(errors)
	}

	return nil
}

// DeletePrivateMessageRequestMultiError is an error wrapping multiple
// validation errors returned by DeletePrivateMessageRequest.ValidateAll() if
// the designated constraints aren't met.
type DeletePrivateMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePrivateMessageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePrivateMessageRequestMultiError) AllErrors() []error { return m }

// DeletePrivateMessageRequestValidationError is the validation error returned
// by DeletePrivateMessageRequest.Validate if the designated constraints
// aren't met.
type DeletePrivateMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePrivateMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePrivateMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePrivateMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePrivateMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePrivateMessageRequestValidationError) ErrorName() string {
	return "DeletePrivateMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePrivateMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePrivateMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePrivateMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePrivateMessageRequestValidationError{}
