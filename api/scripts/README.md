# OpenAPI 文档修复脚本

## 问题背景

Goofish API 的 OpenAPI 文档由 `protoc-gen-openapi` 自动生成，但存在一个已知问题：该工具无法正确区分查询参数和请求体参数，导致所有参数都被放在 `requestBody` 中，而不是正确地分离到 `parameters` 数组中。

根据 Apifox 文档要求，所有 Goofish API 接口都需要包含三个必需的查询参数：
- `mch_id`: 货源平台商户ID（AppKey）
- `timestamp`: 当前时间戳（单位秒，5分钟内有效）
- `sign`: 签名MD5值（参考签名说明）

## 解决方案

我们创建了一个后处理脚本来修复生成的 OpenAPI 文档，该脚本会：

1. **识别需要修复的端点**：根据预定义的路径映射识别哪些端点需要查询参数
2. **提取参数定义**：从原始的 protobuf schema 中提取参数定义
3. **转换参数格式**：将 camelCase 属性名转换为 snake_case 查询参数名
4. **添加缺失参数**：自动添加三个必需的基础查询参数
5. **重构文档结构**：将查询参数移动到 `parameters` 数组，适当处理 `requestBody`

## 文件说明

### 核心脚本

- **`fix_openapi_clean.py`**: 主要的修复脚本，用于后处理 OpenAPI 文档
- **`build_openapi.sh`**: 自动化构建脚本，生成并修复 OpenAPI 文档
- **`verify_fix.py`**: 验证脚本，检查修复结果是否正确

### 测试和调试

- **`fix_openapi.py`**: 带调试信息的修复脚本（开发用）
- **`test_fix.py`**: 简单的测试脚本，检查特定端点的修复结果

## 使用方法

### 自动化构建（推荐）

```bash
cd api/scripts
./build_openapi.sh
```

这会：
1. 生成原始 OpenAPI 文档
2. 自动修复查询参数问题
3. 显示修复结果摘要

### 手动修复

```bash
cd api/scripts
python fix_openapi_clean.py \
    ../../app/admin/service/cmd/server/assets/goofish/openapi.json \
    ../../app/admin/service/cmd/server/assets/goofish/openapi.json
```

### 验证修复结果

```bash
cd api/scripts
python verify_fix.py
```

## 修复结果

修复后的 OpenAPI 文档具有以下特征：

### 完全查询参数化的端点
- `/goofish/goods/change/subscribe/list`: 7个查询参数，无 requestBody
- `/goofish/user/info`: 3个查询参数，无 requestBody

### 混合参数的端点
- `/goofish/goods/change/subscribe`: 3个查询参数 + requestBody
- `/goofish/goods/list`: 3个查询参数 + requestBody
- 其他业务端点：3个查询参数 + requestBody

### 参数说明

所有端点都包含以下基础查询参数：
- `mch_id` (string): 货源平台商户ID（AppKey）
- `timestamp` (integer): 当前时间戳（单位秒，5分钟内有效）
- `sign` (string): 签名MD5值（参考签名说明）

特定端点还包含业务相关的查询参数，如 `goods_type`, `goods_no`, `page_no`, `page_size` 等。

## 集成到构建流程

建议将 `build_openapi.sh` 脚本集成到项目的构建流程中，确保每次生成 OpenAPI 文档时都会自动应用修复。

## 技术细节

- **protobuf 到 HTTP 参数映射**: camelCase → snake_case
- **参数类型保持**: 保留原始的类型定义（string, integer, etc.）
- **描述信息**: 为基础查询参数添加中文描述
- **向后兼容**: 不影响现有的 gRPC 接口定义
