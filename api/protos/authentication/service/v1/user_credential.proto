syntax = "proto3";

package authentication.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

import "pagination/v1/pagination.proto";

// 用户认证服务
service UserCredentialService {
  // 查询列表
  rpc List (pagination.PagingRequest) returns (ListUserCredentialResponse) {}

  // 查询
  rpc Get (GetUserCredentialRequest) returns (UserCredential) {}
  rpc GetByIdentifier (GetUserCredentialByIdentifierRequest) returns (UserCredential) {}

  // 创建
  rpc Create (CreateUserCredentialRequest) returns (google.protobuf.Empty) {}

  // 更新
  rpc Update (UpdateUserCredentialRequest) returns (google.protobuf.Empty) {}

  // 删除
  rpc Delete (DeleteUserCredentialRequest) returns (google.protobuf.Empty) {}
  

  // 验证凭证
  rpc VerifyCredential (VerifyCredentialRequest) returns (VerifyCredentialResponse) {}

  // 修改凭证
  rpc ChangeCredential (ChangeCredentialRequest) returns (google.protobuf.Empty) {}

  // 重设凭证
  rpc ResetCredential (ResetCredentialRequest) returns (google.protobuf.Empty) {}
}

// 身份类型
enum IdentityType {
  USERNAME = 0; // 用户名
  USERID = 1; // 用户ID
  EMAIL = 2; // 邮箱地址
  PHONE = 3; // 手机号

  WECHAT = 100; // 微信
  QQ = 101; // QQ
  WEIBO = 102; // 微博
  DOUYIN = 103; // 抖音
  KUAISHOU = 104; // 快手
  BAIDU = 105; // 百度
  ALIPAY = 106; // 支付宝
  TAOBAO = 107; // 淘宝
  JD = 108; // 京东
  MEITUAN = 109; // 美团
  DINGTALK = 110; // 钉钉
  BILIBILI = 111; // 哔哩哔哩
  XIAOHONGSHU = 112; // 小红书

  GOOGLE = 200; // Google
  FACEBOOK = 201; // Facebook
  APPLE = 202; // Apple
  TELEGRAM = 203; // Telegram
  TWITTER = 204; // Twitter
  LINKEDIN = 205; // LinkedIn
  GITHUB = 206; // GitHub
  MICROSOFT = 207; // Microsoft
  DISCORD = 208; // Discord
  SLACK = 209; // Slack
  INSTAGRAM = 210; // Instagram
  TIKTOK = 211; // TikTok
  REDDIT = 212; // Reddit
  YOUTUBE = 213; // YouTube
  SPOTIFY = 214; // Spotify
  PINTEREST = 215; // Pinterest
  SNAPCHAT = 216; // Snapchat
  TUMBLR = 217; // Tumblr
  YAHOO = 218; // Yahoo
  WHATSAPP = 219; // WhatsApp
  LINE = 220; // LINE
}

// 凭证类型
enum CredentialType {
  PASSWORD_HASH = 0; // 加密密码
  ACCESS_TOKEN = 1; // 访问令牌
  REFRESH_TOKEN = 2; // 刷新令牌
  EMAIL_VERIFICATION_CODE = 3; // 邮箱验证码
  PHONE_VERIFICATION_CODE = 4; // 手机验证码
  OAUTH_TOKEN = 5; // OAuth令牌
  API_KEY = 6; // API密钥
  SSO_TOKEN = 7; // 单点登录令牌
  JWT = 8; // JSON Web Token
  SAML_ASSERTION = 9; // SAML断言
  OPENID_CONNECT_ID_TOKEN = 10; // OpenID Connect ID令牌
  SESSION_COOKIE = 11; // 会话Cookie
  TEMPORARY_CREDENTIAL = 12; // 临时凭证
  CUSTOM_CREDENTIAL = 13; // 自定义凭证类型
  BIOMETRIC_DATA = 14; // 生物识别数据（如指纹、面部识别等）
  SECURITY_KEY = 15; // 安全密钥（如FIDO2/WebAuthn等）
  OTP = 16; // 一次性密码（One-Time Password）
  SMART_CARD = 17; // 智能卡凭证
  CRYPTOGRAPHIC_CERTIFICATE = 18; // 加密证书
  BIOMETRIC_TOKEN = 19; // 生物识别令牌
  DEVICE_FINGERPRINT = 20; // 设备指纹
  HARDWARE_TOKEN = 21; // 硬件令牌
  SOFTWARE_TOKEN = 22; // 软件令牌
  SECURITY_QUESTION = 23; // 安全问题答案
  SECURITY_PIN = 24; // 安全PIN码
  TWO_FACTOR_AUTHENTICATION = 25; // 双因素认证
  MULTI_FACTOR_AUTHENTICATION = 26; // 多因素认证
  PASSWORDLESS_AUTHENTICATION = 27; // 无密码认证
  SOCIAL_LOGIN_TOKEN = 28; // 社交登录令牌
  SSO_SESSION = 29; // 单点登录会话
  API_SECRET = 30; // API密钥
  CUSTOM_TOKEN = 31; // 自定义令牌
  OAUTH2_CLIENT_CREDENTIALS = 32; // OAuth2客户端凭证
  OAUTH2_AUTHORIZATION_CODE = 33; // OAuth2授权码
  OAUTH2_IMPLICIT_GRANT = 34; // OAuth2隐式授权
  OAUTH2_PASSWORD_GRANT = 35; // OAuth2密码授权
  OAUTH2_REFRESH_GRANT = 36; // OAuth2刷新授权
}

// 用户凭证状态
enum UserCredentialStatus {
  DISABLED = 0; // 凭证被禁用，用户无法使用该凭证进行认证（如账号被冻结）。
  ENABLED = 1; // 凭证有效，用户可正常使用该凭证登录或注册。
  EXPIRED = 2; // 凭证已过期（如临时凭证超期）。
  UNVERIFIED = 3; // 凭证未验证（需用户完成验证流程后才能生效）。
  REMOVED = 4; // 凭证已删除（逻辑删除，非物理删除，保留审计记录）。
  BLOCKED = 5; // 凭证被锁定（通常因多次错误尝试触发安全机制）。
  TEMPORARY = 6; // 临时凭证（仅在特定时间段内有效）。
}

// 用户凭证
message UserCredential {
  uint32 id = 1; // 主键ID

  optional uint32 user_id = 2 [
    json_name = "userId", (gnostic.openapi.v3.property) = {description: "关联主表的用户ID"}
  ];  // 关联主表的用户ID
  optional uint32 tenant_id = 3 [
    json_name = "tenantId", (gnostic.openapi.v3.property) = {description: "租户ID"}
  ]; // 租户ID

  optional IdentityType identity_type = 10 [
    json_name = "identityType",
    (gnostic.openapi.v3.property) = {description: "认证方式类型，如用户名+密码、邮箱+密码、手机号+验证码、第三方平台认证等"}
  ]; // 认证方式类型
  optional string identifier = 11 [
    json_name = "identifier",
    (gnostic.openapi.v3.property) = {description: "身份唯一标识符，如果是密码登录，则是用户名；如果是邮箱登录，则是邮箱地址；如果是手机号登录，则是手机号；如果是第三方平台登录，则是第三方平台的唯一ID（如微信的OpenID）"}
  ]; // 身份唯一标识符

  optional CredentialType credential_type = 20 [
    json_name = "credentialType",
    (gnostic.openapi.v3.property) = {description: "凭证类型，如加密密码、访问令牌、刷新令牌等"}
  ]; // 凭证类型
  optional string credential = 21 [
    json_name = "credential",
    (gnostic.openapi.v3.property) = {description: "凭证，如果是密码登录，则是密码的hash值；如果是邮箱登录，则是邮箱的验证码；如果是手机号登录，则是手机号的验证码；如果是第三方平台登录，则是第三方平台的access_token"}
  ]; // 凭证

  optional bool is_primary = 30 [
    json_name = "isPrimary",
    (gnostic.openapi.v3.property) = { description: "是否主认证方式，如果用户同时绑定了邮箱和手机号，那么可以指定邮箱为主要认证方式。" }
  ]; // 是否主认证方式

  optional UserCredentialStatus status = 31 [
    json_name = "status",
    (gnostic.openapi.v3.property) = { description: "凭证状态" }
  ]; // 凭证状态

  optional string extra_info = 32 [
    json_name = "extraInfo",
    (gnostic.openapi.v3.property) = { description: "扩展信息，如果是第三方平台认证，可以记录第三方平台的用户信息。" }
  ]; // 扩展信息

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询列表 - 答复
message ListUserCredentialResponse {
  repeated UserCredential items = 1;
  uint32 total = 2;
}

// 更新 - 请求
message UpdateUserCredentialRequest {
  UserCredential data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 创建 - 请求
message CreateUserCredentialRequest {
  UserCredential data = 1;
}

// 删除 - 请求
message DeleteUserCredentialRequest {
  uint32 id = 1;
}

// 查询 - 请求
message GetUserCredentialRequest {
  uint32 id = 1;
}

// 查询 - 请求
message GetUserCredentialByIdentifierRequest {
  IdentityType identity_type = 1 [
    json_name = "identityType", (gnostic.openapi.v3.property) = {description: "身份类型"}
  ]; // 身份类型

  string identifier = 2 [
    json_name = "identifier", (gnostic.openapi.v3.property) = {description: "身份唯一标识符"}
  ]; // 身份唯一标识符
}

// 验证凭证 - 请求
message VerifyCredentialRequest {
  IdentityType identity_type = 1 [
    json_name = "identityType", (gnostic.openapi.v3.property) = {description: "身份类型"}
  ]; // 身份类型

  string identifier = 2 [
    json_name = "identifier", (gnostic.openapi.v3.property) = {description: "身份唯一标识符"}
  ]; // 身份唯一标识符

  string credential = 3 [
    json_name = "credential", (gnostic.openapi.v3.property) = {description: "凭证"}
  ]; // 凭证

  bool need_decrypt = 4 [
    json_name = "needDecrypt", (gnostic.openapi.v3.property) = {description: "是否需要解码"}
  ]; // 是否需要解码
}
// 验证凭证 - 答复
message VerifyCredentialResponse {
  bool success = 1;
}

// 修改凭证 - 请求
message ChangeCredentialRequest {
  IdentityType identity_type = 1 [
    json_name = "identityType", (gnostic.openapi.v3.property) = {description: "身份类型"}
  ]; // 身份类型

  string identifier = 2 [
    json_name = "identifier", (gnostic.openapi.v3.property) = {description: "身份唯一标识符"}
  ]; // 身份唯一标识符

  string old_credential = 3 [
    json_name = "oldCredential", (gnostic.openapi.v3.property) = {description: "旧凭证"}
  ]; // 旧凭证

  string new_credential = 4 [
    json_name = "newCredential", (gnostic.openapi.v3.property) = {description: "新凭证"}
  ]; // 新凭证

  bool need_decrypt = 5 [
    json_name = "needDecrypt", (gnostic.openapi.v3.property) = {description: "是否需要解码"}
  ]; // 是否需要解码
}

// 重设凭证 - 请求
message ResetCredentialRequest {
  IdentityType identity_type = 1 [
    json_name = "identityType", (gnostic.openapi.v3.property) = {description: "身份类型"}
  ]; // 身份类型

  string identifier = 2 [
    json_name = "identifier", (gnostic.openapi.v3.property) = {description: "身份唯一标识符"}
  ]; // 身份唯一标识符

  string new_credential = 3 [
    json_name = "newCredential", (gnostic.openapi.v3.property) = {description: "新凭证"}
  ]; // 新凭证

  bool need_decrypt = 4 [
    json_name = "needDecrypt", (gnostic.openapi.v3.property) = {description: "是否需要解码"}
  ]; // 是否需要解码
}
