syntax = "proto3";

package user.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 角色服务
service RoleService {
  // 查询角色列表
  rpc List (pagination.PagingRequest) returns (ListRoleResponse) {}

  // 查询角色详情
  rpc Get (GetRoleRequest) returns (Role) {}

  // 创建角色
  rpc Create (CreateRoleRequest) returns (google.protobuf.Empty) {}

  // 更新角色
  rpc Update (UpdateRoleRequest) returns (google.protobuf.Empty) {}

  // 删除角色
  rpc Delete (DeleteRoleRequest) returns (google.protobuf.Empty) {}

  // 批量创建角色
  rpc BatchCreate ( BatchCreateRolesRequest ) returns ( BatchCreateRolesResponse ) {}
}

// 角色
message Role {
  optional uint32 id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "角色ID"}
  ];  // 角色ID

  optional string name = 2 [json_name = "name", (gnostic.openapi.v3.property) = {description: "角色名称"}];  // 角色名称

  optional int32 sort_id = 3 [json_name = "sortId", (gnostic.openapi.v3.property) = {description: "排序编号"}];  // 排序编号

  optional string code = 4 [json_name = "code", (gnostic.openapi.v3.property) = {description: "角色值"}];  // 角色值

  optional string status = 5 [(gnostic.openapi.v3.property) = {
    description: "状态"
    default: { string: "ON" }
    enum: [{yaml: "ON"}, {yaml: "OFF"}]
  }];

  optional string remark = 6 [json_name = "remark", (gnostic.openapi.v3.property) = {description: "备注"}];  // 备注

  repeated uint32 menus = 7 [json_name = "menus", (gnostic.openapi.v3.property) = {description: "分配的菜单列表"}];  // 分配的菜单列表
  repeated uint32 apis = 8 [json_name = "apis", (gnostic.openapi.v3.property) = {description: "分配的API列表"}];  // 分配的API列表

  optional uint32 parent_id = 50 [json_name = "parentId", (gnostic.openapi.v3.property) = {description: "父节点ID"}];  // 父节点ID
  repeated Role children = 51 [json_name = "children", (gnostic.openapi.v3.property) = {description: "子节点树"}];  // 子节点树

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 角色列表 - 答复
message ListRoleResponse {
  repeated Role items = 1;
  uint32 total = 2;
}

// 角色数据 - 请求
message GetRoleRequest {
  uint32 id = 1;
}

// 创建角色 - 请求
message CreateRoleRequest {
  Role data = 1;
}

// 更新角色 - 请求
message UpdateRoleRequest {
  Role data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除角色 - 请求
message DeleteRoleRequest {
  uint32 id = 1;
}

message BatchCreateRolesRequest {
  repeated Role data = 1;
}
message BatchCreateRolesResponse {
  repeated Role data = 1;
}
