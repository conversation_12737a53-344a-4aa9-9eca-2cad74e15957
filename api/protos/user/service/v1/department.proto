syntax = "proto3";

package user.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 部门服务
service DepartmentService {
  // 查询部门列表
  rpc List (pagination.PagingRequest) returns (ListDepartmentResponse) {}

  // 查询部门详情
  rpc Get (GetDepartmentRequest) returns (Department) {}

  // 创建部门
  rpc Create (CreateDepartmentRequest) returns (google.protobuf.Empty) {}

  // 更新部门
  rpc Update (UpdateDepartmentRequest) returns (google.protobuf.Empty) {}

  // 删除部门
  rpc Delete (DeleteDepartmentRequest) returns (google.protobuf.Empty) {}

  // 批量创建部门
  rpc BatchCreate ( BatchCreateDepartmentsRequest ) returns ( BatchCreateDepartmentsResponse ) {}
}

// 部门
message Department {
  optional uint32 id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "部门ID"}
  ];  // 部门ID

  optional string name = 2 [
    json_name = "name",
    (gnostic.openapi.v3.property) = {description: "部门名称"}
  ];  // 部门名称

  optional int32 organization_id = 3 [
    json_name = "organizationId",
    (gnostic.openapi.v3.property) = {description: "所属组织ID"}
  ];  // 所属组织ID

  optional string organization_name = 4 [
    json_name = "organizationName",
    (gnostic.openapi.v3.property) = {description: "所属组织名称"}
  ];  // 所属组织名称

  optional int32 sort_id = 10 [
    json_name = "sortId",
    (gnostic.openapi.v3.property) = {description: "排序编号"}
  ];  // 排序编号

  optional string status = 11 [(gnostic.openapi.v3.property) = {
    description: "状态"
    default: {string: "ON"}
    enum: [{yaml: "ON"}, {yaml: "OFF"}]
  }]; // 状态

  optional string remark = 12 [json_name = "remark", (gnostic.openapi.v3.property) = {description: "备注"}];  // 备注

  optional uint32 parent_id = 50 [json_name = "parentId", (gnostic.openapi.v3.property) = {description: "父节点ID"}];  // 父节点ID
  repeated Department children = 51 [json_name = "children", (gnostic.openapi.v3.property) = {description: "子节点树"}];  // 子节点树

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}
// 部门列表 - 答复
message ListDepartmentResponse {
  repeated Department items = 1;
  uint32 total = 2;
}

// 部门数据 - 请求
message GetDepartmentRequest {
  uint32 id = 1;
}

// 创建部门 - 请求
message CreateDepartmentRequest {
  Department data = 1;
}

// 更新部门 - 请求
message UpdateDepartmentRequest {
  Department data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除部门 - 请求
message DeleteDepartmentRequest {
  uint32 id = 1;
}

message BatchCreateDepartmentsRequest {
  repeated Department data = 1;
}
message BatchCreateDepartmentsResponse {
  repeated Department data = 1;
}
