syntax = "proto3";

package user.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 职位服务
service PositionService {
  // 查询职位列表
  rpc List (pagination.PagingRequest) returns (ListPositionResponse) {}

  // 查询职位详情
  rpc Get (GetPositionRequest) returns (Position) {}

  // 创建职位
  rpc Create (CreatePositionRequest) returns (google.protobuf.Empty) {}

  // 更新职位
  rpc Update (UpdatePositionRequest) returns (google.protobuf.Empty) {}

  // 删除职位
  rpc Delete (DeletePositionRequest) returns (google.protobuf.Empty) {}

  // 批量创建职位
  rpc BatchCreate ( BatchCreatePositionsRequest ) returns ( BatchCreatePositionsResponse ) {}
}

// 职位
message Position {
  optional uint32 id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = {description: "职位ID"}
  ];  // 职位ID

  optional string name = 2 [json_name = "name", (gnostic.openapi.v3.property) = {description: "职位名称"}];  // 职位名称

  optional int32 sort_id = 3 [json_name = "sortId", (gnostic.openapi.v3.property) = {description: "排序编号"}];  // 排序编号

  optional string code = 4 [json_name = "code", (gnostic.openapi.v3.property) = {description: "职位值"}];  // 职位值

  optional string status = 5 [(gnostic.openapi.v3.property) = {
    description: "状态"
    default: { string: "ON" }
    enum: [{yaml: "ON"}, {yaml: "OFF"}]
  }]; // 状态

  optional string remark = 6 [json_name = "remark", (gnostic.openapi.v3.property) = {description: "备注"}];  // 备注

  optional uint32 parent_id = 50 [json_name = "parentId", (gnostic.openapi.v3.property) = {description: "父节点ID"}];  // 父节点ID
  repeated Position children = 51 [json_name = "children", (gnostic.openapi.v3.property) = {description: "子节点树"}];  // 子节点树

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 获取职位列表 - 答复
message ListPositionResponse {
  repeated Position items = 1;
  uint32 total = 2;
}

// 获取职位数据 - 请求
message GetPositionRequest {
  uint32 id = 1;
}

// 创建职位 - 请求
message CreatePositionRequest {
  Position data = 1;
}

// 更新职位 - 请求
message UpdatePositionRequest {
  Position data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除职位 - 请求
message DeletePositionRequest {
  uint32 id = 1;
}

message BatchCreatePositionsRequest {
  repeated Position data = 1;
}
message BatchCreatePositionsResponse {
  repeated Position data = 1;
}
