syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

import "pagination/v1/pagination.proto";

import "user/service/v1/department.proto";

// 部门管理服务
service DepartmentService {
  // 查询部门列表
  rpc List (pagination.PagingRequest) returns (user.service.v1.ListDepartmentResponse) {
    option (google.api.http) = {
      get: "/admin/v1/departments"
    };
  }

  // 查询部门详情
  rpc Get (user.service.v1.GetDepartmentRequest) returns (user.service.v1.Department) {
    option (google.api.http) = {
      get: "/admin/v1/departments/{id}"
    };
  }

  // 创建部门
  rpc Create (user.service.v1.CreateDepartmentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/departments"
      body: "*"
    };
  }

  // 更新部门
  rpc Update (user.service.v1.UpdateDepartmentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/departments/{data.id}"
      body: "*"
    };
  }

  // 删除部门
  rpc Delete (user.service.v1.DeleteDepartmentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/departments/{id}"
    };
  }
}
