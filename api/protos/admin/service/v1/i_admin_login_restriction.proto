syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 后台登录限制管理服务
service AdminLoginRestrictionService {
  // 查询后台登录限制列表
  rpc List (pagination.PagingRequest) returns (ListAdminLoginRestrictionResponse) {
    option (google.api.http) = {
      get: "/admin/v1/login-restrictions"
    };
  }

  // 查询后台登录限制详情
  rpc Get (GetAdminLoginRestrictionRequest) returns (AdminLoginRestriction) {
    option (google.api.http) = {
      get: "/admin/v1/login-restrictions/{id}"
    };
  }

  // 创建后台登录限制
  rpc Create (CreateAdminLoginRestrictionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/login-restrictions"
      body: "*"
    };
  }

  // 更新后台登录限制
  rpc Update (UpdateAdminLoginRestrictionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/login-restrictions/{data.id}"
      body: "*"
    };
  }

  // 删除后台登录限制
  rpc Delete (DeleteAdminLoginRestrictionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/login-restrictions/{id}"
    };
  }
}

// 后台登录限制类型
enum AdminLoginRestrictionType {
  LOGIN_RESTRICTION_TYPE_UNSPECIFIED = 0; // 未知
  BLACKLIST = 1; // 黑名单
  WHITELIST = 2; // 白名单
}

// 后台登录限制方式
enum AdminLoginRestrictionMethod {
  LOGIN_RESTRICTION_METHOD_UNSPECIFIED = 0; // 未知
  IP = 1; // IP地址限制
  MAC = 2; // MAC地址限制，绑定设备的MAC地址。
  REGION = 3; // 地区限制，根据地理位置（如国家、城市）限制登录。
  TIME = 4; // 时间限制，限制登录的时间段，例如只允许工作时间登录。
  DEVICE = 5; // 设备限制，限制登录设备的类型（如PC、手机）或特定设备ID。
}

// 后台登录限制
message AdminLoginRestriction {
  optional uint32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "后台登录限制ID"
    }
  ]; // 后台登录限制ID

  optional uint32 target_id = 2 [
    json_name = "targetId",
    (gnostic.openapi.v3.property) = {
      description: "目标用户ID"
    }
  ]; // 目标用户ID

  optional AdminLoginRestrictionType type = 3 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {
      description: "限制类型"
    }
  ]; // 限制类型

  optional AdminLoginRestrictionMethod method = 4 [
    json_name = "method",
    (gnostic.openapi.v3.property) = {
      description: "限制方式"
    }
  ]; // 限制方式

  optional string value = 5 [
    json_name = "value",
    (gnostic.openapi.v3.property) = {
      description: "限制值（如IP地址、MAC地址或地区代码）"
    }
  ]; // 限制值（如IP地址、MAC地址或地区代码）

  optional string reason = 6 [
    json_name = "reason",
    (gnostic.openapi.v3.property) = { description: "限制原因" }
  ]; // 限制原因

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询后台登录限制列表 - 回应
message ListAdminLoginRestrictionResponse {
  repeated AdminLoginRestriction items = 1;
  uint32 total = 2;
}

// 查询后台登录限制详情 - 请求
message GetAdminLoginRestrictionRequest {
  uint32 id = 1;
}

// 创建后台登录限制 - 请求
message CreateAdminLoginRestrictionRequest {
  AdminLoginRestriction data = 1;
}

// 更新后台登录限制 - 请求
message UpdateAdminLoginRestrictionRequest {
  AdminLoginRestriction data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除后台登录限制 - 请求
message DeleteAdminLoginRestrictionRequest {
  uint32 id = 1;
}
