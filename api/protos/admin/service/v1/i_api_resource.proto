syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";


// API资源管理服务
service ApiResourceService {
  // 查询API资源列表
  rpc List (pagination.PagingRequest) returns (ListApiResourceResponse) {
    option (google.api.http) = {
      get: "/admin/v1/api-resources"
    };
  }

  // 查询API资源详情
  rpc Get (GetApiResourceRequest) returns (ApiResource) {
    option (google.api.http) = {
      get: "/admin/v1/api-resources/{id}"
    };
  }

  // 创建API资源
  rpc Create (CreateApiResourceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/api-resources"
      body: "*"
    };
  }

  // 更新API资源
  rpc Update (UpdateApiResourceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/api-resources/{data.id}"
      body: "*"
    };
  }

  // 删除API资源
  rpc Delete (DeleteApiResourceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/api-resources/{id}"
    };
  }

  // 同步API资源
  rpc SyncApiResources (google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/api-resources/sync"
      body: "*"
    };
  }

  // 查询路由数据
  rpc GetWalkRouteData (google.protobuf.Empty) returns (ListApiResourceResponse) {
    option (google.api.http) = {
      get: "/admin/v1/api-resources/walk-route"
    };
  }
}


// API资源
message ApiResource {
  optional uint32 id = 1 [
    json_name = "id",
    (gnostic.openapi.v3.property) = { description: "资源ID" }
  ]; // 资源ID

  optional string operation = 2 [
    json_name = "operation",
    (gnostic.openapi.v3.property) = { description: "接口操作名" }
  ]; // 接口操作名

  optional string path = 3 [
    json_name = "path",
    (gnostic.openapi.v3.property) = { description: "接口路径" }
  ]; // 接口路径

  optional string method = 4 [
    json_name = "method",
    (gnostic.openapi.v3.property) = { description: "请求方法（GET/POST/PUT/DELETE）" }
  ]; // 请求方法

  optional string module = 5 [
    json_name = "module",
    (gnostic.openapi.v3.property) = { description: "所属业务模块（如 “用户管理”“支付系统”）" }
  ]; // 所属业务模块

  optional string module_description = 6 [
    json_name = "moduleDescription",
    (gnostic.openapi.v3.property) = { description: "模块描述" }
  ]; // 模块描述

  optional string description = 7 [
    json_name = "description",
    (gnostic.openapi.v3.property) = { description: "描述" }
  ]; // 描述

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询列表 - 回应
message ListApiResourceResponse {
  repeated ApiResource items = 1;
  uint32 total = 2;
}

// 查询 - 请求
message GetApiResourceRequest {
  uint32 id = 1;
}

// 创建 - 请求
message CreateApiResourceRequest {
  ApiResource data = 1;
}

// 更新 - 请求
message UpdateApiResourceRequest {
  ApiResource data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除 - 请求
message DeleteApiResourceRequest {
  uint32 id = 1;
}
