syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

import "pagination/v1/pagination.proto";

import "user/service/v1/user.proto";

// 用户管理服务
service UserService {
  // 获取用户列表
  rpc List (pagination.PagingRequest) returns (user.service.v1.ListUserResponse) {
    option (google.api.http) = {
      get: "/admin/v1/users"
    };
  }

  // 获取用户数据
  rpc Get (user.service.v1.GetUserRequest) returns (user.service.v1.User) {
    option (google.api.http) = {
      get: "/admin/v1/users/{id}"
    };
  }

  // 创建用户
  rpc Create (user.service.v1.CreateUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/users"
      body: "*"
    };
  }

  // 更新用户
  rpc Update (user.service.v1.UpdateUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/users/{data.id}"
      body: "*"
    };
  }

  // 删除用户
  rpc Delete (user.service.v1.DeleteUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/users/{id}"
    };
  }
}
