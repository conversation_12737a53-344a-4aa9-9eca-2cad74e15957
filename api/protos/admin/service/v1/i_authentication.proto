syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

import "user/service/v1/user.proto";
import "authentication/service/v1/authentication.proto";

// 用户后台登录认证服务
service AuthenticationService {
  // 登录
  rpc Login (authentication.service.v1.LoginRequest) returns (authentication.service.v1.LoginResponse) {
    option (google.api.http) = {
      post: "/admin/v1/login"
      body: "*"
    };

    option(gnostic.openapi.v3.operation) = {
      security: {}
    };
  }

  // 登出
  rpc Logout (google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/logout"
      body: "*"
    };
  }

  // 刷新认证令牌
  rpc RefreshToken (authentication.service.v1.LoginRequest) returns (authentication.service.v1.LoginResponse) {
    option (google.api.http) = {
      post: "/admin/v1/refresh_token"
      body: "*"
    };
  }

  // 修改用户密码
  rpc ChangePassword(authentication.service.v1.ChangePasswordRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/change_password"
      body: "*"
    };
  }
}
