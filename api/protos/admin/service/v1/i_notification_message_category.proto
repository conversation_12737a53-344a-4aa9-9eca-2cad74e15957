syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

import "pagination/v1/pagination.proto";

import "internal_message/service/v1/notification_message_category.proto";

// 通知消息分类管理服务
service NotificationMessageCategoryService {
  // 查询通知消息分类列表
  rpc List (pagination.PagingRequest) returns (internal_message.service.v1.ListNotificationMessageCategoryResponse) {
    option (google.api.http) = {
      get: "/admin/v1/notifications:categories"
    };
  }

  // 查询通知消息分类详情
  rpc Get (internal_message.service.v1.GetNotificationMessageCategoryRequest) returns (internal_message.service.v1.NotificationMessageCategory) {
    option (google.api.http) = {
      get: "/admin/v1/notifications:categories/{id}"
    };
  }

  // 创建通知消息分类
  rpc Create (internal_message.service.v1.CreateNotificationMessageCategoryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/notifications:categories"
      body: "*"
    };
  }

  // 更新通知消息分类
  rpc Update (internal_message.service.v1.UpdateNotificationMessageCategoryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/notifications:categories/{data.id}"
      body: "*"
    };
  }

  // 删除通知消息分类
  rpc Delete (internal_message.service.v1.DeleteNotificationMessageCategoryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/notifications:categories/{id}"
    };
  }
}
