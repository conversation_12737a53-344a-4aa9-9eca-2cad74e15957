syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

import "pagination/v1/pagination.proto";

import "internal_message/service/v1/private_message.proto";

// 私信消息管理服务
service PrivateMessageService {
  // 查询私信消息列表
  rpc List (pagination.PagingRequest) returns (internal_message.service.v1.ListPrivateMessageResponse) {
    option (google.api.http) = {
      get: "/admin/v1/private_messages"
    };
  }

  // 查询私信消息详情
  rpc Get (internal_message.service.v1.GetPrivateMessageRequest) returns (internal_message.service.v1.PrivateMessage) {
    option (google.api.http) = {
      get: "/admin/v1/private_messages/{id}"
    };
  }

  // 创建私信消息
  rpc Create (internal_message.service.v1.CreatePrivateMessageRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/private_messages"
      body: "*"
    };
  }

  // 更新私信消息
  rpc Update (internal_message.service.v1.UpdatePrivateMessageRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/private_messages/{data.id}"
      body: "*"
    };
  }

  // 删除私信消息
  rpc Delete (internal_message.service.v1.DeletePrivateMessageRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/private_messages/{id}"
    };
  }
}
