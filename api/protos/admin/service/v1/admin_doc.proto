syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";

option (gnostic.openapi.v3.document) = {
  info: {
    title: "Kratos Admin API";
    version: "1.0";
    description: "Kratos Admin API";
    contact: {
      name: "tx7do";
      url: "https://github.com/tx7do/kratos-admin";
      email: "<EMAIL>";
    }
    license: {
      name: "MIT License";
      url: "https://github.com/tx7do/kratos-admin/blob/master/LICENSE";
    }
  }

  security: [
    {
      additional_properties: [
        {
          name: "OAuth2PasswordBearer"; value: {}
        }
      ]
    }
  ]

  components: {
    security_schemes: {
      additional_properties: [
        {
          name: "OAuth2PasswordBearer";
          value: {
            security_scheme: {
              type: "oauth2";
              flows: {
                password: {
                  token_url: "/admin/v1/login";
                  refresh_url: "/admin/v1/refresh_token";
                  scopes: {}
                }
              }
            }
          }
        }
      ]
    }

    schemas: {
      additional_properties: [
        {
          name: "KratosStatus";
          value: {
            schema: {
              type: "object"
              description: "Kratos错误返回"
              properties: {
                additional_properties: [
                  {
                    name: "code"
                    value: {
                      schema: {
                        type: "number"
                        format: "int32"
                        description: "错误码"
                      }
                    }
                  },
                  {
                    name: "message"
                    value: {
                      schema: {
                        type: "string"
                        description: "错误消息"
                      }
                    }
                  },
                  {
                    name: "reason"
                    value: {
                      schema: {
                        type: "string"
                        description: "错误原因"
                      }
                    }
                  },
                  {
                    name: "metadata"
                    value: {
                      schema: {
                        type: "object"
                        description: "元数据"
                      }
                    }
                  }
                ]
              }
            }
          }
        }
      ]
    }

    responses: {
      additional_properties: [
        {
          name: "default"
          value: {
            response: {
              description: "default kratos response"
              content: {
                additional_properties: [
                  {
                    name: "application/json"
                    value: {
                      schema: {
                        reference: {
                          _ref: "#/components/schemas/KratosStatus"
                        }
                      }
                    }
                  }
                ]
              }
            }
          }
        }

        //        {
        //          name: "500"
        //          value: {
        //            response: {
        //              description: "default kratos response"
        //              content: {
        //                additional_properties: [
        //                  {
        //                    name: "application/json"
        //                    value: {
        //                      schema: {
        //                        reference: {
        //                          _ref: "#/components/schemas/KratosStatus"
        //                        }
        //                      }
        //                    }
        //                  }
        //                ]
        //              }
        //            }
        //          }
        //        }
      ]
    }
  }
};
