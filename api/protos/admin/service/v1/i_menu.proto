syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

import "admin/service/v1/i_router.proto";

// 后台菜单管理服务
service MenuService {
  // 查询菜单列表
  rpc List (pagination.PagingRequest) returns (ListMenuResponse) {
    option (google.api.http) = {
      get: "/admin/v1/menus"
    };
  }

  // 查询菜单详情
  rpc Get (GetMenuRequest) returns (Menu) {
    option (google.api.http) = {
      get: "/admin/v1/menus/{id}"
    };
  }

  // 创建菜单
  rpc Create (CreateMenuRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/menus"
      body: "*"
    };
  }

  // 更新菜单
  rpc Update (UpdateMenuRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/menus/{data.id}"
      body: "*"
    };
  }

  // 删除菜单
  rpc Delete (DeleteMenuRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/menus/{id}"
    };
  }
}

// 菜单类型
enum MenuType {
  FOLDER = 0;  // 菜单夹
  MENU = 1;  // 菜单项
  BUTTON = 2;  // 按钮
}

// 菜单状态
enum MenuStatus {
  OFF = 0;
  ON = 1;
}

// 菜单
message Menu {
  optional int32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "菜单ID"
    }
  ]; // 菜单ID

  optional MenuStatus status = 2 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {
      description: "菜单状态"
      default: {string: "ON"}
      enum: [{yaml: "ON"}, {yaml: "OFF"}]
    }
  ]; // 菜单状态

  optional MenuType type = 3 [
    json_name = "type",
    (gnostic.openapi.v3.property) = {
      description: "菜单类型"
      default: {string: "FOLDER"}
    }
  ]; // 菜单类型

  optional string path = 10 [
    json_name = "path",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "路由路径"
    }
  ]; // 路由路径

  optional string redirect = 11 [
    json_name = "redirect",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "重定向地址"
    }
  ]; // 重定向地址

  optional string alias = 12 [
    json_name = "alias",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "路由别名"
    }
  ]; // 路由别名

  optional string name = 13 [
    json_name = "name",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。"
    }
  ]; // 路由命名，然后我们可以使用 name 而不是 path 来传递 to 属性给 <router-link>。

  optional string component = 14 [
    json_name = "component",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "指向的组件"
    }
  ]; // 指向的组件

  optional RouteMeta meta = 15 [
    json_name = "meta",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "路由元信息"
    }
  ]; // 路由元信息

  optional int32 parent_id = 50 [json_name = "parentId", (gnostic.openapi.v3.property) = {description: "父节点ID"}];  // 父节点ID
  repeated Menu children = 51 [json_name = "children", (gnostic.openapi.v3.property) = {description: "子节点树"}];  // 子节点树

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}



// 查询菜单列表 - 回应
message ListMenuResponse {
  repeated Menu items = 1;
  uint32 total = 2;
}

// 查询菜单详情 - 请求
message GetMenuRequest {
  int32 id = 1;
}

// 创建菜单 - 请求
message CreateMenuRequest {
  Menu data = 1;
}

// 更新菜单 - 请求
message UpdateMenuRequest {
  Menu data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除菜单 - 请求
message DeleteMenuRequest {
  optional uint32 operator_id = 1 [
    (gnostic.openapi.v3.property) = {description: "操作用户ID", read_only: true},
    json_name = "operatorId"
  ]; // 操作用户ID

  int32 id = 2;
}
