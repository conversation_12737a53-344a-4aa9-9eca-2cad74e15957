syntax = "proto3";

package admin.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 字典管理服务
service DictService {
  // 查询字典列表
  rpc List (pagination.PagingRequest) returns (ListDictResponse) {
    option (google.api.http) = {
      get: "/admin/v1/dict"
    };
  }

  // 查询字典详情
  rpc Get (GetDictRequest) returns (Dict) {
    option (google.api.http) = {
      get: "/admin/v1/dict/{id}"
    };
  }

  // 创建字典
  rpc Create (CreateDictRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/admin/v1/dict"
      body: "*"
    };
  }

  // 更新字典
  rpc Update (UpdateDictRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/dict/{data.id}"
      body: "*"
    };
  }

  // 删除字典
  rpc Delete (DeleteDictRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/admin/v1/dict/{id}"
    };
  }
}

// 字典
message Dict {
  optional uint32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = {
      description: "字典ID"
    }
  ]; // 字典ID

  optional string category = 2 [
    json_name = "category",
    (gnostic.openapi.v3.property) = {
      description: "字典分类"
    }
  ]; // 字典分类

  optional string category_desc = 3 [
    json_name = "categoryDesc",
    (gnostic.openapi.v3.property) = {
      description: "字典分类名称"
    }
  ]; // 字典分类名称

  optional string key = 4 [
    json_name = "key",
    (gnostic.openapi.v3.property) = {
      description: "字典键"
    }
  ]; // 字典键

  optional string value = 5 [
    json_name = "value",
    (gnostic.openapi.v3.property) = {
      description: "字典值"
    }
  ]; // 字典值

  optional string value_desc = 6 [
    json_name = "valueDesc",
    (gnostic.openapi.v3.property) = {
      description: "字典值名称"
    }
  ]; // 字典值名称

  optional string value_data_type = 7 [
    json_name = "valueDataType",
    (gnostic.openapi.v3.property) = {
      description: "字典值数据类型"
    }
  ]; // 字典值数据类型

  optional string status = 10 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {
      description: "字典状态"
      default: {string: "ON"}
      enum: [{yaml: "ON"}, {yaml: "OFF"}]
    }
  ]; // 字典状态

  optional int32 sort_id = 11 [
    json_name = "sortId",
    (gnostic.openapi.v3.property) = {
      description: "排序编号"
    }
  ]; // 排序编号

  optional string remark = 12 [
    json_name = "remark",
    (gnostic.openapi.v3.property) = {
      description: "备注"
    }
  ]; // 备注

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询字典列表 - 回应
message ListDictResponse {
  repeated Dict items = 1;
  uint32 total = 2;
}

// 查询字典详情 - 请求
message GetDictRequest {
  uint32 id = 1;
}

// 创建字典 - 请求
message CreateDictRequest {
  Dict data = 1;
}

// 更新字典 - 请求
message UpdateDictRequest {
  Dict data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除字典 - 请求
message DeleteDictRequest {
  uint32 id = 1;
}
