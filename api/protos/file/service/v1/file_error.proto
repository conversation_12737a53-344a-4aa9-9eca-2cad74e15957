syntax = "proto3";

package file.service.v1;

import "errors/errors.proto";

// FileErrorReason 是文件系统错误定义
enum FileErrorReason {
    option (errors.default_code) = 500;

    // 400
    BAD_REQUEST = 0 [(errors.code) = 400]; // 错误请求

    // 401
    UNAUTHORIZED = 100 [(errors.code) = 401]; // 未授权

    // 402
    PAYMENT_REQUIRED = 200 [(errors.code) = 402]; // 需要支付

    // 403
    FORBIDDEN = 300 [(errors.code) = 403]; // 禁止访问

    // 404
    NOT_FOUND = 400 [(errors.code) = 404]; // 找不到资源
    FILE_NOT_FOUND = 401 [(errors.code) = 404]; // 文件不存在

    // 405
    METHOD_NOT_ALLOWED = 500 [(errors.code) = 405]; // 方法不允许

    // 406
    NOT_ACCEPTABLE = 600 [(errors.code) = 406]; // 不可接受的请求

    // 407
    PROXY_AUTHENTICATION_REQUIRED = 700 [(errors.code) = 407]; // 代理身份验证需要

    // 408
    REQUEST_TIMEOUT = 800 [(errors.code) = 408]; // 请求超时

    // 409
    CONFLICT = 900 [(errors.code) = 409];                   // 冲突

    // 410
    GONE = 1000 [(errors.code) = 410];                       // 已删除

    // 411
    LENGTH_REQUIRED = 1010 [(errors.code) = 411];            // 需要Content-Length

    // 412
    PRECONDITION_FAILED = 1020 [(errors.code) = 412];        // 前置条件失败

    // 413
    PAYLOAD_TOO_LARGE = 1030 [(errors.code) = 413];          // 负载过大
    FILE_TOO_LARGE = 1031 [(errors.code) = 413]; // 文件过大

    // 414
    URI_TOO_LONG = 1040 [(errors.code) = 414];               // URI过长

    // 415
    UNSUPPORTED_MEDIA_TYPE = 1050 [(errors.code) = 415];     // 不支持的媒体类型

    // 416
    RANGE_NOT_SATISFIABLE = 1060 [(errors.code) = 416];      // 请求范围无法满足

    // 417
    EXPECTATION_FAILED = 1070 [(errors.code) = 417];         // 期望失败

    // 418
    IM_A_TEAPOT = 1080 [(errors.code) = 418];                // 我是茶壶 (RFC 2324)

    // 421
    MISDIRECTED_REQUEST = 1090 [(errors.code) = 421];        // 错误的请求

    // 422
    UNPROCESSABLE_ENTITY = 1100 [(errors.code) = 422];       // 不可处理的实体

    // 423
    LOCKED = 1110 [(errors.code) = 423];                     // 已锁定

    // 424
    FAILED_DEPENDENCY = 1120 [(errors.code) = 424];          // 依赖失败

    // 425
    TOO_EARLY = 1130 [(errors.code) = 425];                  // 请求过早

    // 426
    UPGRADE_REQUIRED = 1140 [(errors.code) = 426];           // 需要升级

    // 428
    PRECONDITION_REQUIRED = 1150 [(errors.code) = 428];      // 需要前置条件

    // 429
    TOO_MANY_REQUESTS = 1160 [(errors.code) = 429];          // 请求过多

    // 431
    REQUEST_HEADER_FIELDS_TOO_LARGE = 1170 [(errors.code) = 431]; // 请求头字段过大

    // 451
    UNAVAILABLE_FOR_LEGAL_REASONS = 1180 [(errors.code) = 451]; // 因法律原因不可用


    // 500
    INTERNAL_SERVER_ERROR = 2000  [(errors.code) = 500];        // 内部服务器错误
    UPLOAD_FAILED = 2001 [(errors.code) = 500];
    DOWNLOAD_FAILED = 2002 [(errors.code) = 500];

    // 501
    NOT_IMPLEMENTED = 2100 [(errors.code) = 501];              // 未实现

    // 502
    BAD_GATEWAY = 2200 [(errors.code) = 502];                  // 错误网关

    // 503
    SERVICE_UNAVAILABLE = 2300 [(errors.code) = 503];          // 服务不可用

    // 504
    GATEWAY_TIMEOUT = 2400 [(errors.code) = 504];              // 网关超时

    // 505
    HTTP_VERSION_NOT_SUPPORTED = 2500 [(errors.code) = 505];   // HTTP版本不支持

    // 506
    VARIANT_ALSO_NEGOTIATES = 2600 [(errors.code) = 506];      // 变体也协商

    // 507
    INSUFFICIENT_STORAGE = 2700 [(errors.code) = 507];         // 存储空间不足

    // 508
    LOOP_DETECTED = 2800 [(errors.code) = 508];                // 检测到循环

    // 510
    NOT_EXTENDED = 2900 [(errors.code) = 510];                 // 未扩展

    // 511
    NETWORK_AUTHENTICATION_REQUIRED = 3000 [(errors.code) = 511]; // 需要网络认证


    // 非标准状态码

    // 598
    NETWORK_READ_TIMEOUT_ERROR = 3100 [(errors.code) = 598];   // 网络读取超时

    // 599
    NETWORK_CONNECT_TIMEOUT_ERROR = 3200 [(errors.code) = 599]; // 网络连接超时
}
