syntax = "proto3";

package file.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 文件服务
service FileService {
  // 获取文件列表
  rpc List (pagination.PagingRequest) returns (ListFileResponse) {}

  // 获取文件数据
  rpc Get (GetFileRequest) returns (File) {}

  // 创建文件
  rpc Create (CreateFileRequest) returns (google.protobuf.Empty) {}

  // 更新文件
  rpc Update (UpdateFileRequest) returns (google.protobuf.Empty) {}

  // 删除文件
  rpc Delete (DeleteFileRequest) returns (google.protobuf.Empty) {}
}

// OSS供应商
enum OSSProvider {
  MINIO = 0;
  ALIYUN = 1;
  AWS = 2;
  AZURE = 3;
  BAIDU = 4;
  QINIU = 5;
  TENCENT = 6;
  GOOGLE = 7;
  HUAWEI = 8;
  QCLOUD = 9;
  LOCAL = 10;
}

// 文件
message File {
  optional uint32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "文件ID" }
  ]; // 文件ID

  optional OSSProvider provider = 2 [
    json_name = "provider",
    (gnostic.openapi.v3.property) = { description: "OSS供应商" }
  ];  // OSS供应商

  optional string bucket_name = 3 [
    json_name = "bucketName",
    (gnostic.openapi.v3.property) = { description: "存储桶名称" }
  ];  // 存储桶名称

  optional string file_directory = 4 [
    json_name = "fileDirectory",
    (gnostic.openapi.v3.property) = { description: "文件目录" }
  ];  // 文件目录

  optional string file_guid = 5 [
    json_name = "fileGuid",
    (gnostic.openapi.v3.property) = { description: "文件Guid" }
  ];  // 文件Guid

  optional string save_file_name = 6 [
    json_name = "saveFileName",
    (gnostic.openapi.v3.property) = { description: "保存文件名" }
  ];  // 保存文件名

  optional string file_name = 7 [
    json_name = "fileName",
    (gnostic.openapi.v3.property) = { description: "文件名" }
  ];  // 文件名

  optional string extension = 8 [
    json_name = "extension",
    (gnostic.openapi.v3.property) = { description: "文件扩展名" }
  ];  // 文件扩展名

  optional uint64 size = 9 [
    json_name = "size",
    (gnostic.openapi.v3.property) = { description: "文件字节长度" }
  ];  // 文件字节长度

  optional string size_format = 10 [
    json_name = "sizeFormat",
    (gnostic.openapi.v3.property) = { description: "文件大小格式化" }
  ];  // 文件大小格式化

  optional string link_url = 11 [
    json_name = "linkUrl",
    (gnostic.openapi.v3.property) = { description: "链接地址" }
  ];  // 链接地址

  optional string md5 = 12 [
    json_name = "md5",
    (gnostic.openapi.v3.property) = { description: "md5码，防止上传重复文件" }
  ];  // md5码，防止上传重复文件

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询列表 - 回应
message ListFileResponse {
  repeated File items = 1;
  uint32 total = 2;
}

// 查询 - 请求
message GetFileRequest {
  uint32 id = 1;
}

// 创建 - 请求
message CreateFileRequest {
  File data = 1;
}

// 更新 - 请求
message UpdateFileRequest {
  File data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除 - 请求
message DeleteFileRequest {
  uint32 id = 1;
}
