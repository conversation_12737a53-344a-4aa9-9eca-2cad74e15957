syntax = "proto3";

package file.service.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";

// OSS服务
service OssService {
  // 获取对象存储（OSS）上传链接
  rpc OssUploadUrl (OssUploadUrlRequest) returns (OssUploadUrlResponse) {}

  // 获取对象存储（OSS）下载链接
  rpc GetDownloadUrl (GetDownloadUrlRequest) returns (GetDownloadUrlResponse) {}

  // 获取文件夹下面的文件列表
  rpc ListOssFile (ListOssFileRequest) returns (ListOssFileResponse) {}

  // 删除一个文件
  rpc DeleteOssFile (DeleteOssFileRequest) returns (DeleteOssFileResponse) {}

  // 上传文件
  rpc UploadOssFile (UploadOssFileRequest) returns (UploadOssFileResponse) {}
}

// 前端上传文件所用的HTTP方法
enum UploadMethod {
  Put = 0;
  Post = 1;
}

// 获取对象存储上传链接 - 请求
message OssUploadUrlRequest {
  UploadMethod method = 1 [
    json_name = "method",
    (gnostic.openapi.v3.property) = { description: "上传文件所用的HTTP方法，支持POST和PUT" }
  ];  // 上传文件所用的HTTP方法

  optional string content_type = 2 [
    json_name = "contentType",
    (gnostic.openapi.v3.property) = { description: "文件的MIME类型" }
  ];  // 文件的MIME类型

  optional string bucket_name = 3 [
    json_name = "bucketName",
    (gnostic.openapi.v3.property) = { description: "文件桶名称，如果不填写，将会根据文件名或者MIME类型进行自动解析" }
  ]; // 文件桶名称，如果不填写，将会根据文件名或者MIME类型进行自动解析。

  optional string file_path = 4 [
    json_name = "filePath",
    (gnostic.openapi.v3.property) = { description: "远端的文件路径，可以不填写" }
  ]; // 远端的文件路径，可以不填写。

  optional string file_name = 5 [
    json_name = "fileName",
    (gnostic.openapi.v3.property) = { description: "文件名，如果不填写，则会生成UUID，有同名文件也会改为UUID" }
  ]; // 文件名，如果不填写，则会生成UUID，有同名文件也会改为UUID。
}

// 获取对象存储上传链接 - 回应
message OssUploadUrlResponse {
  string upload_url = 1 [
    json_name = "uploadUrl",
    (gnostic.openapi.v3.property) = { description: "文件的上传链接，默认1个小时的过期时间" }
  ]; // 文件的上传链接，默认1个小时的过期时间。

  string download_url = 2 [
    json_name = "downloadUrl",
    (gnostic.openapi.v3.property) = { description: "文件的下载链接" }
  ]; // 文件的下载链接

  optional string bucket_name = 3 [
    json_name = "bucketName",
    (gnostic.openapi.v3.property) = { description: "文件桶名称" }
  ]; // 文件桶名称

  string object_name = 4 [
    json_name = "objectName",
    (gnostic.openapi.v3.property) = { description: "文件名" }
  ];  // 文件名

  map<string, string> form_data = 5 [
    json_name = "formData",
    (gnostic.openapi.v3.property) = { description: "表单数据，使用POST方法时填写" }
  ];
}

message GetDownloadUrlRequest {

}

message GetDownloadUrlResponse {
  
}

message ListOssFileRequest {
  optional string bucket_name = 1 [
    json_name = "bucketName",
    (gnostic.openapi.v3.property) = { description: "文件桶名称" }
  ]; // 文件桶名称

  optional string folder = 2 [
    json_name = "folder",
    (gnostic.openapi.v3.property) = { description: "文件夹名称" }
  ]; // 文件夹名称

  optional bool recursive = 3 [
    json_name = "recursive",
    (gnostic.openapi.v3.property) = { description: "是否递归文件夹" }
  ]; // 是否递归文件夹
}

message ListOssFileResponse {
  repeated string files = 1 [
    json_name = "files",
    (gnostic.openapi.v3.property) = { description: "文件列表" }
  ];
}

message DeleteOssFileRequest {
  optional string bucket_name = 1 [
    json_name = "bucketName",
    (gnostic.openapi.v3.property) = { description: "文件桶名称" }
  ]; // 文件桶名称
  optional string object_name = 2 [
    json_name = "objectName",
    (gnostic.openapi.v3.property) = { description: "文件名" }
  ]; // 文件名
}

message DeleteOssFileResponse {

}

message UploadOssFileRequest {
  optional string bucket_name = 1 [
    json_name = "bucketName",
    (gnostic.openapi.v3.property) = { description: "文件桶名称" }
  ]; // 文件桶名称

  optional string object_name = 2 [
    json_name = "objectName",
    (gnostic.openapi.v3.property) = { description: "文件名" }
  ]; // 文件名

  optional bytes file = 3 [
    json_name = "file",
    (gnostic.openapi.v3.property) = { description: "文件内容" }
  ]; // 文件内容

  optional string source_file_name = 4 [
    json_name = "sourceFileName",
    (gnostic.openapi.v3.property) = { description: "原文件文件名" }
  ]; // 原文件文件名

  optional string mime = 5 [
    json_name = "mime",
    (gnostic.openapi.v3.property) = { description: "文件的MIME类型" }
  ]; // 文件的MIME类型
}
message UploadOssFileResponse {
  string url = 1;
}
