syntax = "proto3";

package internal_message.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

// 通知消息分类服务
service NotificationMessageCategoryService {
  // 查询通知消息分类列表
  rpc List (pagination.PagingRequest) returns (ListNotificationMessageCategoryResponse) {}

  // 查询通知消息分类详情
  rpc Get (GetNotificationMessageCategoryRequest) returns (NotificationMessageCategory) {}

  // 创建通知消息分类
  rpc Create (CreateNotificationMessageCategoryRequest) returns (google.protobuf.Empty) {}

  // 更新通知消息分类
  rpc Update (UpdateNotificationMessageCategoryRequest) returns (google.protobuf.Empty) {}

  // 删除通知消息分类
  rpc Delete (DeleteNotificationMessageCategoryRequest) returns (google.protobuf.Empty) {}
}

// 通知消息分类
message NotificationMessageCategory {
  optional uint32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "分类ID" }
  ]; // 分类ID

  optional string name = 2 [
    json_name = "name",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "名称" }
  ]; // 名称

  optional string code = 3 [
    json_name = "code",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "编码" }
  ]; // 编码

  optional int32 sort_id = 4 [
    json_name = "sortId",
    (gnostic.openapi.v3.property) = {description: "排序编号"}
  ];  // 排序编号

  optional bool enable = 5 [
    json_name = "enable",
    (gnostic.openapi.v3.property) = {description: "是否启用"}
  ];  // 是否启用

  optional uint32 parent_id = 50 [
    json_name = "parentId",
    (gnostic.openapi.v3.property) = {description: "父节点ID"}
  ];  // 父节点ID
  repeated NotificationMessageCategory children = 51 [
    json_name = "children",
    (gnostic.openapi.v3.property) = {description: "子节点树"}
  ];  // 子节点树

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询通知消息分类列表 - 回应
message ListNotificationMessageCategoryResponse {
  repeated NotificationMessageCategory items = 1;
  uint32 total = 2;
}

// 查询通知消息分类详情 - 请求
message GetNotificationMessageCategoryRequest {
  uint32 id = 1;
}

// 创建通知消息分类 - 请求
message CreateNotificationMessageCategoryRequest {
  NotificationMessageCategory data = 1;
}

// 更新通知消息分类 - 请求
message UpdateNotificationMessageCategoryRequest {
  NotificationMessageCategory data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除通知消息分类 - 请求
message DeleteNotificationMessageCategoryRequest {
  uint32 id = 1;
}
