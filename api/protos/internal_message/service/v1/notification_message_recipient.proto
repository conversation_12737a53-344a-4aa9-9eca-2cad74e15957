syntax = "proto3";

package internal_message.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

import "internal_message/service/v1/message.proto";

// 通知消息接收者服务
service NotificationMessageRecipientService {
  // 查询通知消息接收者列表
  rpc List (pagination.PagingRequest) returns (ListNotificationMessageRecipientResponse) {}

  // 查询通知消息接收者详情
  rpc Get (GetNotificationMessageRecipientRequest) returns (NotificationMessageRecipient) {}

  // 创建通知消息接收者
  rpc Create (CreateNotificationMessageRecipientRequest) returns (google.protobuf.Empty) {}

  // 更新通知消息接收者
  rpc Update (UpdateNotificationMessageRecipientRequest) returns (google.protobuf.Empty) {}

  // 删除通知消息接收者
  rpc Delete (DeleteNotificationMessageRecipientRequest) returns (google.protobuf.Empty) {}
}

// 通知消息接收者
message NotificationMessageRecipient {
  optional uint32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "记录ID" }
  ]; // 记录ID

  optional uint32 message_id = 2 [
    json_name = "messageId",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "群发消息ID" }
  ]; // 群发消息ID

  optional uint32 recipient_id = 3 [
    json_name = "recipientId",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "接收者用户ID" }
  ]; // 接收者用户ID

  optional MessageStatus status = 4 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {
      description: "消息状态"
    }
  ]; // 消息状态

  optional uint32 create_by = 100 [json_name = "createBy", (gnostic.openapi.v3.property) = {description: "创建者ID"}]; // 创建者ID
  optional uint32 update_by = 101 [json_name = "updateBy", (gnostic.openapi.v3.property) = {description: "更新者ID"}]; // 更新者ID

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询通知消息接收者列表 - 回应
message ListNotificationMessageRecipientResponse {
  repeated NotificationMessageRecipient items = 1;
  uint32 total = 2;
}

// 查询通知消息接收者详情 - 请求
message GetNotificationMessageRecipientRequest {
  uint32 id = 1;
}

// 创建通知消息接收者 - 请求
message CreateNotificationMessageRecipientRequest {
  NotificationMessageRecipient data = 1;
}

// 更新通知消息接收者 - 请求
message UpdateNotificationMessageRecipientRequest {
  NotificationMessageRecipient data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除通知消息接收者 - 请求
message DeleteNotificationMessageRecipientRequest {
  uint32 id = 1;
}
