syntax = "proto3";

package internal_message.service.v1;

import "gnostic/openapi/v3/annotations.proto";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

import "pagination/v1/pagination.proto";

import "internal_message/service/v1/message.proto";

// 私信消息服务
service PrivateMessageService {
  // 查询私信消息列表
  rpc List (pagination.PagingRequest) returns (ListPrivateMessageResponse) {}

  // 查询私信消息详情
  rpc Get (GetPrivateMessageRequest) returns (PrivateMessage) {}

  // 创建私信消息
  rpc Create (CreatePrivateMessageRequest) returns (google.protobuf.Empty) {}

  // 更新私信消息
  rpc Update (UpdatePrivateMessageRequest) returns (google.protobuf.Empty) {}

  // 删除私信消息
  rpc Delete (DeletePrivateMessageRequest) returns (google.protobuf.Empty) {}
}

// 私信消息
message PrivateMessage {
  optional uint32 id = 1 [
    json_name = "id",
    (google.api.field_behavior) = OPTIONAL,
    (gnostic.openapi.v3.property) = { description: "消息ID" }
  ]; // 消息ID

  optional string subject = 2 [
    json_name = "subject",
    (gnostic.openapi.v3.property) = { description: "主题" }
  ]; // 主题

  optional string content = 3 [
    json_name = "content",
    (gnostic.openapi.v3.property) = { description: "内容" }
  ]; // 内容

  optional MessageStatus status = 4 [
    json_name = "status",
    (gnostic.openapi.v3.property) = {
      description: "消息状态"
    }
  ]; // 消息状态

  optional uint32 sender_id = 10 [
    json_name = "senderId",
    (gnostic.openapi.v3.property) = { description: "发送者用户ID" }
  ]; // 发送者用户ID

  optional string sender_name = 11 [
    json_name = "senderName",
    (gnostic.openapi.v3.property) = {
      description: "发送者用户名称"
    }
  ]; // 发送者用户名称

  optional string sender_avatar = 12 [
    json_name = "senderAvatar",
    (gnostic.openapi.v3.property) = {
      description: "发送者用户头像"
    }
  ]; // 发送者用户头像

  optional uint32 receiver_id = 20 [
    json_name = "receiverId",
    (gnostic.openapi.v3.property) = { description: "接收者用户ID" }
  ]; // 接收者用户ID

  optional string receiver_name = 21 [
    json_name = "receiverName",
    (gnostic.openapi.v3.property) = {
      description: "接收者用户名称"
    }
  ]; // 接收者用户名称

  optional string receiver_avatar = 22 [
    json_name = "receiverAvatar",
    (gnostic.openapi.v3.property) = {
      description: "接收者用户头像"
    }
  ]; // 接收者用户头像

  optional google.protobuf.Timestamp create_time = 200 [json_name = "createTime", (gnostic.openapi.v3.property) = {description: "创建时间"}];// 创建时间
  optional google.protobuf.Timestamp update_time = 201 [json_name = "updateTime", (gnostic.openapi.v3.property) = {description: "更新时间"}];// 更新时间
  optional google.protobuf.Timestamp delete_time = 202 [json_name = "deleteTime", (gnostic.openapi.v3.property) = {description: "删除时间"}];// 删除时间
}

// 查询私信消息列表 - 回应
message ListPrivateMessageResponse {
  repeated PrivateMessage items = 1;
  uint32 total = 2;
}

// 查询私信消息详情 - 请求
message GetPrivateMessageRequest {
  uint32 id = 1;
}

// 创建私信消息 - 请求
message CreatePrivateMessageRequest {
  PrivateMessage data = 1;
}

// 更新私信消息 - 请求
message UpdatePrivateMessageRequest {
  PrivateMessage data = 1;

  google.protobuf.FieldMask update_mask = 2 [
    (gnostic.openapi.v3.property) = {
      description: "要更新的字段列表",
      example: {yaml : "id,realname,username"}
    },
    json_name = "updateMask"
  ]; // 要更新的字段列表

  optional bool allow_missing = 3 [
    (gnostic.openapi.v3.property) = {description: "如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。"},
    json_name = "allowMissing"
  ]; // 如果设置为true的时候，资源不存在则会新增(插入)，并且在这种情况下`updateMask`字段将会被忽略。
}

// 删除私信消息 - 请求
message DeletePrivateMessageRequest {
  uint32 id = 1;
}
