edition: 3.0.0
name: goofish-api
access: "tdid"
vars:
  region: "cn-shanghai"
resources:
  goofish-api:
    component: fc3
    props:
      region: ${vars.region}
      description: 闲管家虚拟货源 API
      # layers: 
      #   - "acs:fc:cn-shanghai:official:layers/Go1/versions/1"
      environmentVariables:
        PATH: PATH=/opt/go1/bin:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/opt/bin
        TZ: Asia/Shanghai
        CGO_ENABLED: "1"
      vpcConfig:
        securityGroupId: sg-uf63h58x0cojg6eutqnf
        vpcId: vpc-uf6v6g274feupv6noxj32
        vSwitchIds:
          - vsw-uf6n7jmd6rex8jcnpbg1b
          - vsw-uf6qsib9g2qrznlq1rfph
      runtime: custom.debian12
      timeout: 1200
      cpu: 1
      instanceConcurrency: 5
      tags:
        - Value: 起号助手
          Key: project
        - Value: test
          Key: depoy_env
      memorySize: 1024
      diskSize: 512
      customRuntimeConfig:
        port: 7788
        command:
          - /bin/bash
          - /code/start.sh
      functionName: "goofish-api"
      code: .
      logConfig: auto
      triggers:
        - triggerName: httpTrigger
          triggerType: http
          triggerConfig:
            authType: anonymous
            methods:
              - GET
              - POST
              - PUT
              - DELETE
      customDomain:
        domainName: auto
        protocol: HTTP
        route:
          path: /*
          qualifier: LATEST
